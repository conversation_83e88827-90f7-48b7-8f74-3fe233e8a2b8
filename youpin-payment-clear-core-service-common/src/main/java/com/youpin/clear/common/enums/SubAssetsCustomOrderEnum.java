package com.youpin.clear.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金处理顺序枚举
 */
@Getter
@AllArgsConstructor
public enum SubAssetsCustomOrderEnum {

    PRE_SALE_DEPOSIT(SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, List.of(UserAssetsTypeEnum.TYPE_219.getTypeId(),
            UserAssetsTypeEnum.TYPE_212.getTypeId(), UserAssetsTypeEnum.TYPE_213.getTypeId(), UserAssetsTypeEnum.TYPE_220.getTypeId(), UserAssetsTypeEnum.TYPE_214.getTypeId(), UserAssetsTypeEnum.TYPE_215.getTypeId(), UserAssetsTypeEnum.TYPE_218.getTypeId(),
            UserAssetsTypeEnum.TYPE_247.getTypeId(), UserAssetsTypeEnum.TYPE_256.getTypeId(), UserAssetsTypeEnum.TYPE_248.getTypeId(), UserAssetsTypeEnum.TYPE_257.getTypeId(), UserAssetsTypeEnum.TYPE_211.getTypeId())),

    FRONT_NORMAL_GOOD(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, List.of(UserAssetsTypeEnum.TYPE_3.getTypeId(), UserAssetsTypeEnum.TYPE_221.getTypeId(), UserAssetsTypeEnum.TYPE_4.getTypeId(), UserAssetsTypeEnum.TYPE_222.getTypeId(), UserAssetsTypeEnum.TYPE_211.getTypeId(), UserAssetsTypeEnum.TYPE_83.getTypeId(), UserAssetsTypeEnum.TYPE_5.getTypeId(), UserAssetsTypeEnum.TYPE_181.getTypeId(), UserAssetsTypeEnum.TYPE_154.getTypeId(), UserAssetsTypeEnum.TYPE_155.getTypeId(),
            UserAssetsTypeEnum.TYPE_247.getTypeId(), UserAssetsTypeEnum.TYPE_256.getTypeId(), UserAssetsTypeEnum.TYPE_248.getTypeId(), UserAssetsTypeEnum.TYPE_257.getTypeId(), UserAssetsTypeEnum.TYPE_211.getTypeId())),


    MONEY_TRANSFER_PURCHASE(SubBusTypeFrontEnum.MONEY_TRANSFER_PURCHASE, List.of(UserAssetsTypeEnum.TYPE_97.getTypeId(), UserAssetsTypeEnum.TYPE_98.getTypeId())),
    PURCHASE_TRANSFER_MONEY(SubBusTypeFrontEnum.PURCHASE_TRANSFER_MONEY, List.of(UserAssetsTypeEnum.TYPE_100.getTypeId(), UserAssetsTypeEnum.TYPE_99.getTypeId())),


    ;


    private final SubBusTypeFrontEnum subBusTypeFrontEnum;
    /**
     * 资金类型
     */
    private final List<Integer> customOrderList;


    private static final Map<SubBusTypeFrontEnum, List<Integer>> MAP = new HashMap<>();

    static {
        for (SubAssetsCustomOrderEnum item : SubAssetsCustomOrderEnum.values()) {
            MAP.put(item.getSubBusTypeFrontEnum(), item.getCustomOrderList());
        }
    }

    public static List<Integer> getByTypeCode(SubBusTypeFrontEnum subBusTypeFrontEnum) {
        if (subBusTypeFrontEnum == null || !MAP.containsKey(subBusTypeFrontEnum)) {
            return Collections.emptyList();
        }
        return MAP.getOrDefault(subBusTypeFrontEnum, Collections.emptyList());
    }

}
