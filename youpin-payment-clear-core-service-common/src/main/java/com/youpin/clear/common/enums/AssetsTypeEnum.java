package com.youpin.clear.common.enums;

public enum AssetsTypeEnum {

    /**
     * 默认
     */
    DEFAULT,
    /**
     * 平台出金
     */
    PLATFORM_OUT,
    /**
     * 平台手续费
     */
    PLATFORM_FEE,

    /**
     * 退款
     */
    REFUND;


    public static AssetsTypeEnum getCodeEnum(String code) {
        for (AssetsTypeEnum item : AssetsTypeEnum.values()) {
            if (item.name().equals(code)) return item;
        }
        return null;
    }
}
