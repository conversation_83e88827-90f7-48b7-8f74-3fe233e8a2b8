package com.youpin.clear.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum AdjustmentApplyStatusEnum {
    APPLY_ING(1, "申请中"),
    APPLY_REJECT(2, "审核拒绝"),
    APPLY_PROCESSING(3, "处理中"),
    APPLY_PROCESS_SUCCESS(4, "处理成功"),
    APPLY_PROCESS_FAIL(5, "处理失败"),
    APPLY_REVOKE(6,"申请撤销"),
    ;


    private final int status;

    private final String desc;

    private static final Map<Integer, AdjustmentApplyStatusEnum> map = new HashMap<>();

    AdjustmentApplyStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    static {
        for (AdjustmentApplyStatusEnum e : AdjustmentApplyStatusEnum.values()) {
            map.put(e.getStatus(), e);
        }
    }

    public static AdjustmentApplyStatusEnum getByStatus(int status) {
        return map.get(status);
    }


}
