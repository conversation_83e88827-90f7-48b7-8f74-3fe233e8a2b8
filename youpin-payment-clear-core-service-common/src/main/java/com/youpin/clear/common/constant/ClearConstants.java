package com.youpin.clear.common.constant;

import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;

import java.util.List;

/**
 * 通用常量信息
 */
public class ClearConstants {


    public static final Long TOTAL_SHARD = 1024L;

    public static final Integer MERCHANT_ID = 10001;

    public static final String COMMA = ",";
    public static final Long CONSTANT_LONG_0 = 0L;
    public static final Long CONSTANT_LONG_1 = 1L;
    public static final Long CONSTANT_LONG_7 = 7L;
    public static final Long CONSTANT_LONG_12 = 12L;
    public static final Long CONSTANT_LONG_24 = 24L;

    public static final int CONSTANT_INT_0 = 0;

    public static final Integer CONSTANT_INTEGER_0 = 0;
    public static final Integer CONSTANT_INTEGER_1 = 1;

    public static final Integer CONSTANT_INTEGER_2 = 2;
    public static final Integer CONSTANT_INTEGER_3 = 3;
    public static final Integer CONSTANT_INTEGER_4 = 4;
    public static final Integer CONSTANT_INTEGER_5 = 5;
    public static final Integer CONSTANT_INTEGER_6 = 6;
    public static final Integer CONSTANT_INTEGER_10 = 10;
    public static final Integer CONSTANT_INTEGER_21 = 21;
    public static final Integer CONSTANT_INTEGER_22 = 22;
    public static final Integer CONSTANT_INTEGER_43 = 43;
    public static final Integer CONSTANT_INTEGER_44 = 44;
    public static final Integer CONSTANT_INTEGER_58 = 58;

    public static final Integer CONSTANT_INTEGER_82 = 82;
    public static final Integer CONSTANT_INTEGER_90 = 90;

    public static final Integer CONSTANT_INTEGER_98 = 98;
    public static final Integer CONSTANT_INTEGER_100 = 100;
    public static final Integer CONSTANT_INTEGER_189 = 189;


    public static final Integer CONSTANT_INTEGER_111 = 111;
    public static final Integer CONSTANT_INTEGER_146 = 146;
    public static final Integer CONSTANT_INTEGER_169 = 169;
    public static final Integer CONSTANT_INTEGER_181 = 181;
    public static final Integer CONSTANT_INTEGER_182 = 182;
    public static final Integer CONSTANT_INTEGER_191 = 191;
    public static final Integer CONSTANT_INTEGER_201 = 201;
    public static final Integer CONSTANT_INTEGER_202 = 202;
    public static final Integer CONSTANT_INTEGER_211 = 211;
    public static final Integer CONSTANT_INTEGER_212 = 212;

    public static final Integer CONSTANT_INTEGER_219 = 219;
    public static final Integer CONSTANT_INTEGER_220 = 220;
    public static final Integer CONSTANT_INTEGER_221 = 221;
    public static final Integer CONSTANT_INTEGER_222 = 222;

    public static final Integer CONSTANT_INTEGER_1000 = 1000;

    public static final Integer CONSTANT_INTEGER_1002 = 1002;
    public static final Integer CONSTANT_INTEGER_83 = 83;
    public static final Integer CONSTANT_INTEGER_227 = 227;
    public static final Integer CONSTANT_INTEGER_229 = 229;
    public static final Integer CONSTANT_INTEGER_230 = 230;
    public static final Integer CONSTANT_INTEGER_231 = 231;
    public static final Integer CONSTANT_INTEGER_232 = 232;
    public static final Integer CONSTANT_INTEGER_233 = 233;
    public static final Integer CONSTANT_INTEGER_235 = 235;


    /**
     * 不发送账单大类
     */
    public static final List<SubBusTypeFrontEnum> NOT_SEND_BILL_ACCOUNT_MQ = List.of(
            SubBusTypeFrontEnum.MONEY_TRANSFER_PURCHASE,
            SubBusTypeFrontEnum.PURCHASE_TRANSFER_MONEY,
            SubBusTypeFrontEnum.WALLET_RECHARGE_WITHDRAW,
            SubBusTypeFrontEnum.PURCHASE_RECHARGE_WITHDRAW,
            SubBusTypeFrontEnum.RECHARGE_KEEP_PROMISE,
            SubBusTypeFrontEnum.KEEP_RECHARGE
    );

    /**
     * 不发送账单
     */
    public static final List<Integer> NOT_SEND_BILL_ACCOUNT_MQ_BY_TYPE_ID = List.of(
            UserAssetsTypeEnum.TYPE_21.getTypeId(),
            UserAssetsTypeEnum.TYPE_22.getTypeId(),
            UserAssetsTypeEnum.TYPE_33.getTypeId(),
            UserAssetsTypeEnum.TYPE_58.getTypeId(),
            UserAssetsTypeEnum.TYPE_65.getTypeId(),
            UserAssetsTypeEnum.TYPE_66.getTypeId(),
            UserAssetsTypeEnum.TYPE_90.getTypeId(),
            UserAssetsTypeEnum.TYPE_95.getTypeId(),
            UserAssetsTypeEnum.TYPE_102.getTypeId(),
            UserAssetsTypeEnum.TYPE_108.getTypeId(),
            UserAssetsTypeEnum.TYPE_111.getTypeId(),
            UserAssetsTypeEnum.TYPE_112.getTypeId(),
            UserAssetsTypeEnum.TYPE_113.getTypeId(),
            UserAssetsTypeEnum.TYPE_116.getTypeId(),
            UserAssetsTypeEnum.TYPE_117.getTypeId(),
            UserAssetsTypeEnum.TYPE_119.getTypeId(),
            UserAssetsTypeEnum.TYPE_121.getTypeId(),
            UserAssetsTypeEnum.TYPE_122.getTypeId(),
            UserAssetsTypeEnum.TYPE_125.getTypeId(),
            UserAssetsTypeEnum.TYPE_134.getTypeId(),
            UserAssetsTypeEnum.TYPE_137.getTypeId(),
            UserAssetsTypeEnum.TYPE_154.getTypeId(),
            UserAssetsTypeEnum.TYPE_158.getTypeId(),
            UserAssetsTypeEnum.TYPE_162.getTypeId(),
            UserAssetsTypeEnum.TYPE_163.getTypeId(),
            UserAssetsTypeEnum.TYPE_174.getTypeId(),
            UserAssetsTypeEnum.TYPE_176.getTypeId(),
            UserAssetsTypeEnum.TYPE_189.getTypeId(),
            UserAssetsTypeEnum.TYPE_211.getTypeId(),
            UserAssetsTypeEnum.TYPE_218.getTypeId(),
            UserAssetsTypeEnum.TYPE_221.getTypeId(),
            UserAssetsTypeEnum.TYPE_222.getTypeId(),
            UserAssetsTypeEnum.TYPE_227.getTypeId(),
            UserAssetsTypeEnum.TYPE_229.getTypeId(),
            UserAssetsTypeEnum.TYPE_230.getTypeId(),
            UserAssetsTypeEnum.TYPE_232.getTypeId(),
            UserAssetsTypeEnum.TYPE_235.getTypeId(),
            UserAssetsTypeEnum.TYPE_237.getTypeId(),
            UserAssetsTypeEnum.TYPE_243.getTypeId()
    );

    public static final List<Integer> TYPE_211_BALANCE_COMPENSATION_RELATIONS = List.of(
            UserAssetsTypeEnum.TYPE_4.getTypeId(),
            UserAssetsTypeEnum.TYPE_11.getTypeId(),
            UserAssetsTypeEnum.TYPE_133.getTypeId(),
            UserAssetsTypeEnum.TYPE_151.getTypeId(),
            UserAssetsTypeEnum.TYPE_180.getTypeId(),
            UserAssetsTypeEnum.TYPE_191.getTypeId(),
            UserAssetsTypeEnum.TYPE_193.getTypeId(),
            UserAssetsTypeEnum.TYPE_195.getTypeId(),
            UserAssetsTypeEnum.TYPE_200.getTypeId(),
            UserAssetsTypeEnum.TYPE_210.getTypeId(),
            UserAssetsTypeEnum.TYPE_217.getTypeId(),
            UserAssetsTypeEnum.TYPE_220.getTypeId(),
            UserAssetsTypeEnum.TYPE_213.getTypeId(),
            UserAssetsTypeEnum.TYPE_101002.getTypeId(),
            UserAssetsTypeEnum.TYPE_102002.getTypeId(),
            UserAssetsTypeEnum.TYPE_243.getTypeId(),
            UserAssetsTypeEnum.TYPE_250.getTypeId()

    );


    /**
     * 243 过滤
     */
    public static final List<Integer> FILTER_243_TYPE_ID = List.of(1, 43, 3, 10, 15, 21, 24, 33, 85, 89, 95, 102, 108, 109, 114, 120, 126, 132, 140, 141, 142, 148, 156, 158, 160, 165, 177, 185, 190, 194, 199, 205, 207, 209, 216, 228, 1001, 101001, 102001);
}
