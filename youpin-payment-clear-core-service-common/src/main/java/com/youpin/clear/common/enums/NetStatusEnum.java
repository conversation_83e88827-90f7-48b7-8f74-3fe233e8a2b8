package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum NetStatusEnum {

    FAIL(0, "失败"), SUCCESS(1, "成功"), PROCESSING(2, "进行中");

    private static final Map<Integer, NetStatusEnum> MAP = new HashMap<>();

    static {
        for (NetStatusEnum item : NetStatusEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String name;

    public static NetStatusEnum getNetStatusEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
