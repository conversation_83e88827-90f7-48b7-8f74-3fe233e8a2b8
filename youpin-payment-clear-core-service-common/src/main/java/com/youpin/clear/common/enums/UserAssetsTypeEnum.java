package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum UserAssetsTypeEnum {
    TYPE_1("充值", 1, FundingDirectionEnum.RECHARGE, AssetsTypeEnum.DEFAULT),
    TYPE_2("提现", 2, FundingDirectionEnum.CASH_WITHDRAWAL, AssetsTypeEnum.DEFAULT),
    TYPE_43("求购账户充值", 43, FundingDirectionEnum.RECHARGE, AssetsTypeEnum.DEFAULT),
    TYPE_23("提现退款", 23, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_44("求购账户提现", 44, FundingDirectionEnum.CASH_WITHDRAWAL, AssetsTypeEnum.DEFAULT),
    TYPE_96("求购提现退款", 96, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_82("平台账户充值", 82, FundingDirectionEnum.RECHARGE, AssetsTypeEnum.DEFAULT),

    TYPE_3("购买", 3, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_4("退款", 4, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_5("出售", 5, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_83("退还买家资金", 83, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_45("求购处罚", 45, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_46("求购补偿", 46, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_94("私密出售补贴", 94, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    /**
     * 私密出售支出 平台户账户
     */
    TYPE_95("私密出售支出", 95, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_97("转出到求购账户余额", 97, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    /**
     * 钱包转出到求购账户余额
     */
    TYPE_98("钱包-转出到求购账户余额", 98, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    /**
     * 求购账户余额-转出到钱包
     */
    TYPE_100("求购账户余额-转出到钱包", 100, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_99("转出到钱包", 99, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    /**
     * 交易服务费
     */
    TYPE_181("交易服务费", 181, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_182("退还交易服务费", 182, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_190("购买CDK", 190, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_191("CDK退款", 191, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_194("支付出租大会员", 194, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_195("退还出租大会员", 195, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_201("优惠券抵扣", 201, FundingDirectionEnum.INVARIANT_SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_202("退还优惠券折扣", 202, FundingDirectionEnum.INVARIANT_ADDITION, AssetsTypeEnum.REFUND),


    TYPE_209("支付免押增值服务", 209, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_210("退还免押增值服务", 210, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_216("购买服饰", 216, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_217("服饰退款", 217, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_132("支付极速发货", 132, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_133("退还极速发货", 133, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    //预售业务
    TYPE_213("解冻预售保证金", 213, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_214("扣除违约金", 214, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_215("补贴违约金", 215, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_218("预售违约手续费", 218, FundingDirectionEnum.INVARIANT_ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_219("支付预售保证金", 219, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_212("冻结预售保证金", 212, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_220("退还预售保证金", 220, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.REFUND),

    TYPE_103("支付押金保障费", 103, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    //租赁过户
    TYPE_148("支付租赁过户", 148, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_151("退还租赁过户", 151, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_149("收取租赁过户", 149, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_150("支付租赁过户手续费", 150, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_173("租赁过户返现", 173, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_172("收取待结租金", 172, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_174("支付租赁过户返现", 174, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_187("租赁服务费", 187, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),


    TYPE_192("支付赠送服务费", 192, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_193("退还赠送服务费", 193, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_203("冻结赠送服务费", 203, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_204("解冻赠送服务费", 204, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),

    TYPE_13("冻结押金", 13, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_88("冻结安心租", 88, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_183("冻结信用超能力", 183, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_197("冻结省钱会员", 197, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),


    TYPE_14("解冻押金", 14, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_91("安心租解冻", 91, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_184("解冻信用超能力", 184, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_198("解冻省钱会员", 198, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),

    TYPE_74("收取逾期资金", 74, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_15("支付租金", 15, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_89("支付安心租", 89, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    // 平台户和用户都会使用
    TYPE_185("支付信用超能力", 185, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_186("信用超能力退还", 186, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_33("支付补贴租金", 33, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_90("安心租收取", 90, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_92("安心租退还", 92, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_16("收取租金", 16, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_199("支付省钱会员", 199, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_200("退还省钱会员", 200, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),


    TYPE_47("支付买断资金", 47, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_48("收取买断资金", 48, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_49("退还买断资金", 49, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_55("收取赔偿租金", 55, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_56("支付赔付资金", 56, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_57("支付赔偿资金", 57, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_58("押金额外扣除", 58, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_54("收取赔付资金", 54, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_111("暂收买断资金", 111, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_21("垫付资金", 21, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_22("追回资金", 22, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_75("收取额外赔偿", 75, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_76("支付逾期资金", 76, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_77("支付额外赔偿", 77, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_24("支付续租资金", 24, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_25("收取续租资金", 25, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_78("退还续租资金", 78, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_71("退还守约资金", 71, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    /**
     * 支付期数资金
     */
    TYPE_120("支付期数资金", 120, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    /**
     * 暂收期数资金
     */
    TYPE_121("暂收期数资金", 121, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_136("收取附加价值赔付", 136, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_137("赔饰品差额调拨", 137, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_177("支付解封服务费", 177, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_180("退还解封服务费", 180, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_189("赔饰品追回", 189, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),


    TYPE_211("余额补偿入金", 211, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),


    TYPE_64("安全保障费用", 64, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_65("预收安全保障费用", 65, FundingDirectionEnum.INVARIANT_ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_66("追回安全保障费用", 66, FundingDirectionEnum.INVARIANT_ADDITION, AssetsTypeEnum.DEFAULT),

    //还价
    TYPE_10("还价支付", 10, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_11("还价退款", 11, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_170("还价补偿", 170, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_169("还价处罚", 169, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_171("退还还价处罚", 171, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    //平台户 4316621
    TYPE_221("支出支付立减补贴", 221, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_222("退还支付立减补贴", 222, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),


    TYPE_6("交易补偿", 6, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_17("购买冻结", 17, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_18("购买解冻", 18, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),


    TYPE_72("收取延期资金", 72, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_73("支付延期资金", 73, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_85("支付安心购", 85, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_86("安心购退款", 86, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_101("安心租赔付", 101, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_104("退还押金保障费", 104, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_105("安心购解冻", 105, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_106("安心购冻结", 106, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_107("租4免1活动补贴", 107, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_109("支付安心涨", 109, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_110("退还安心涨", 110, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    //平台收取
    TYPE_113("收取安心涨", 113, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_114("支付红锁无忧", 114, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_115("退还红锁无忧", 115, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_118("红锁无忧赔付", 118, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_123("退还期数资金", 123, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_124("自动到账服务费", 124, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_126("支付采购服务费", 126, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_128("私密租赁补贴", 128, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_135("转账充值到账", 135, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_138("冻结数码租赁押金", 138, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_139("解冻数码租赁押金", 139, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_140("支付数码租金", 140, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_141("支付数码逾期违约金", 141, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_142("支付数码强制买断", 142, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_143("退还数码租金", 143, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_144("退还数码逾期违约金", 144, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_145("退还数码强制买断", 145, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_146("立减金补贴", 146, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_147("退还立减金补贴", 147, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.REFUND),

    TYPE_155("返现补贴", 155, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_154("支付返现补贴", 154, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_156("支付手机充值", 156, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_157("退还手机充值", 157, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_159("收取开箱出金", 159, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_160("支付活动资金", 160, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_161("退还活动资金", 161, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_164("退还求购处罚", 164, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_165("支付官箱自开", 165, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_166("收取官箱自开", 166, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_167("支出官箱自开", 167, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_168("退还官箱自开", 168, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_175("任务返现补贴", 175, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_188("退还租赁服务费", 188, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_205("支付自动发货", 205, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_206("收取自动发货", 206, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_208("退还自动发货", 208, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_10003("红锁无忧冻结", 10003, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_10004("红锁无忧解冻", 10004, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_10006("极速发货冻结", 10006, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_10007("极速发货解冻", 10007, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_116("收取红锁无忧", 116, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_125("收取自动到账服务费", 125, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_102("支出安心租", 102, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_112("处理买断资金", 112, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_122("处理期数资金", 122, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_108("108租4免1活动支出", 108, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_117("支出红锁无忧退费", 117, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.REFUND),
    TYPE_119("支出红锁无忧赔付", 119, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_158("支出开箱出金", 158, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_176("支付任务返现补贴", 176, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_129("私密租赁补贴支出", 129, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_162("支付提现券资金", 162, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_134("转账充值支出", 134, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_207("支出自动发货", 207, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_93("交易处罚", 93, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_196("赔付服务费", 196, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_81("转租服务费", 81, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_34("退还补贴租金", 34, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_87("安心购收取", 87, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_127("收取采购服务费", 127, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_163("退还提现券资金", 163, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_1001("数据修复减", 1001, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_1000("数据修复加", 1000, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
    TYPE_1002("司法划拨", 1002, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_227("收取交易服务费", 227, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_228("担保交易购买", 228, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_229("担保户进账", 229, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_230("担保户支出", 230, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_231("担保交易出售", 231, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_232("担保户退款支出", 232, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_233("担保交易退款", 233, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_234("担保交易服务费", 234, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_235("担保服务费收入", 235, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),


    TYPE_236("退还赔付服务费", 236, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    TYPE_237("预售返还余额", 237, FundingDirectionEnum.INVARIANT_SUBTRACTION, AssetsTypeEnum.DEFAULT),

    TYPE_243("支付服务费", 243, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_244("支付服务费退款", 244, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_245("钱包充值退款", 245, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.REFUND),
    TYPE_246("求购充值退款", 246, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.REFUND),

    //新版资金明细ID
    /**
     * 前二位 类型 10
     * 1 支出 ,2 收入 ,3 冻结 ,4 解冻
     * 第三位 商户类型
     * 1.本户 2.外户
     */
    TYPE_102001("支付多游戏SDK购买", 102001, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_102002("退还多游戏SDK购买", 102002, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    //OP-80231
    TYPE_101001("支付出售大会员", 101001, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_101002("退还出售大会员", 101002, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),

    TYPE_247("结算冻结", 247, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),

    TYPE_248("结算解冻", 248, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_249("扣除结算金额-撤回报价", 249, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_250("退款-撤回报价", 250, FundingDirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
    //平台户
    TYPE_251("诚信卖家垫付", 251, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    TYPE_252("诚信卖家守约", 252, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    //平台户
    TYPE_253("追回诚信卖家垫付", 253, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_254("扣除私密出售补贴", 254, FundingDirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
    //平台户
    TYPE_255("私密出售补贴退款", 255, FundingDirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),

    TYPE_256("私密出售补贴冻结", 256, FundingDirectionEnum.FREEZE, AssetsTypeEnum.DEFAULT),
    TYPE_257("私密出售补贴解冻", 257, FundingDirectionEnum.UNFREEZE, AssetsTypeEnum.DEFAULT),

    /**
     * 资金交易转提现
     */
    TYPE_270("资金交易转提现", 270, FundingDirectionEnum.TRANSFER, AssetsTypeEnum.DEFAULT),

    ;
    private final String name;
    /**
     * 资金类型
     */
    private final Integer typeId;
    /**
     * 资金方向
     */
    private final FundingDirectionEnum directionEnum;

    /**
     * 资金类型
     */
    private final AssetsTypeEnum assetsTypeEnum;


    public static final Map<Integer, UserAssetsTypeEnum> MAP = new HashMap<>();

    static {
        for (UserAssetsTypeEnum item : UserAssetsTypeEnum.values()) {
            MAP.put(item.typeId, item);
        }
    }


    public static UserAssetsTypeEnum getByTypeCode(Integer typeCode) {
        if (typeCode == null || !MAP.containsKey(typeCode)) {
            return null;
        }
        return MAP.get(typeCode);
    }
}
