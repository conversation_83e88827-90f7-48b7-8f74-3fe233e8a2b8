package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum UserAssetsReferenceRelationEnum {


    T_5_221(5, 221);


    /**
     * code
     */
    private final Integer code;

    /**
     * relationCode
     */
    private final Integer relationCode;


    private static final Map<Integer, UserAssetsReferenceRelationEnum> MAP = new HashMap<>();


    static {
        for (UserAssetsReferenceRelationEnum item : UserAssetsReferenceRelationEnum.values()) {
            MAP.put(item.code, item);
        }
    }


    public static UserAssetsReferenceRelationEnum getRelationEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
