//package com.youpin.clear.common.enums;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Getter
//@AllArgsConstructor
//public enum TypeRelationEnum {
//
//
//    TYPE_5(UserAssetsEnum.TYPE_5, List.of(UserAssetsEnum.TYPE_3, UserAssetsEnum.TYPE_10)),
//
//    TYPE_23(UserAssetsEnum.TYPE_23, List.of(UserAssetsEnum.TYPE_2)),
//
//
//    TYPE_124(UserAssetsEnum.TYPE_124, List.of(UserAssetsEnum.TYPE_3)),
//    TYPE_125(UserAssetsEnum.TYPE_125, List.of(UserAssetsEnum.TYPE_124)),
//
//
//    TYPE_94(UserAssetsEnum.TYPE_94, List.of(UserAssetsEnum.TYPE_95)), // 求购补偿
//    TYPE_46(UserAssetsEnum.TYPE_46, List.of(UserAssetsEnum.TYPE_45)),
//
//    TYPE_149(UserAssetsEnum.TYPE_149, List.of(UserAssetsEnum.TYPE_148)),
//
//    TYPE_172(UserAssetsEnum.TYPE_172, List.of(UserAssetsEnum.TYPE_148)),
//
//    TYPE_173(UserAssetsEnum.TYPE_173, List.of(UserAssetsEnum.TYPE_174)),
//    TYPE_4(UserAssetsEnum.TYPE_4, List.of(UserAssetsEnum.TYPE_3, UserAssetsEnum.TYPE_1, UserAssetsEnum.TYPE_10)),
//    TYPE_83(UserAssetsEnum.TYPE_83, List.of(UserAssetsEnum.TYPE_3, UserAssetsEnum.TYPE_10)),
//
//
//    TYPE_211(UserAssetsEnum.TYPE_211, List.of(UserAssetsEnum.TYPE_3, UserAssetsEnum.TYPE_1, UserAssetsEnum.TYPE_10)),
//
//    TYPE_182(UserAssetsEnum.TYPE_182, List.of(UserAssetsEnum.TYPE_181)),
//    TYPE_193(UserAssetsEnum.TYPE_193, List.of(UserAssetsEnum.TYPE_192)),
//    TYPE_164(UserAssetsEnum.TYPE_164, List.of(UserAssetsEnum.TYPE_45)),
//    TYPE_11(UserAssetsEnum.TYPE_11, List.of(UserAssetsEnum.TYPE_10)),
//    TYPE_171(UserAssetsEnum.TYPE_171, List.of(UserAssetsEnum.TYPE_169)),
//
//    TYPE_170(UserAssetsEnum.TYPE_170, List.of(UserAssetsEnum.TYPE_169)),
//
//
//    TYPE_151(UserAssetsEnum.TYPE_151, List.of(UserAssetsEnum.TYPE_148)),
//
//    TYPE_169(UserAssetsEnum.TYPE_169, List.of(UserAssetsEnum.TYPE_10)),
//
//    TYPE_191(UserAssetsEnum.TYPE_191, List.of(UserAssetsEnum.TYPE_190)),
//
//    TYPE_18(UserAssetsEnum.TYPE_18, List.of(UserAssetsEnum.TYPE_17)),
//
//    TYPE_155(UserAssetsEnum.TYPE_155, List.of(UserAssetsEnum.TYPE_154)),
//
//    TYPE_204(UserAssetsEnum.TYPE_204, List.of(UserAssetsEnum.TYPE_203)),
//
//    TYPE_107(UserAssetsEnum.TYPE_107, List.of(UserAssetsEnum.TYPE_108)),
//
//    TYPE_175(UserAssetsEnum.TYPE_175, List.of(UserAssetsEnum.TYPE_176)),
//    TYPE_14(UserAssetsEnum.TYPE_14, List.of(UserAssetsEnum.TYPE_13)),
//
//    TYPE_121(UserAssetsEnum.TYPE_121, List.of(UserAssetsEnum.TYPE_120)),
//
//    TYPE_25(UserAssetsEnum.TYPE_25, List.of(UserAssetsEnum.TYPE_24)),
//    TYPE_72(UserAssetsEnum.TYPE_72, List.of(UserAssetsEnum.TYPE_73)),
//
//    TYPE_22(UserAssetsEnum.TYPE_22, List.of(UserAssetsEnum.TYPE_15, UserAssetsEnum.TYPE_77, UserAssetsEnum.TYPE_76, UserAssetsEnum.TYPE_56, UserAssetsEnum.TYPE_57, UserAssetsEnum.TYPE_112)),
//
//    TYPE_16(UserAssetsEnum.TYPE_16, List.of(UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_15, UserAssetsEnum.TYPE_33, UserAssetsEnum.TYPE_201, UserAssetsEnum.TYPE_122)),
//    TYPE_74(UserAssetsEnum.TYPE_74, List.of(UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_33, UserAssetsEnum.TYPE_122)),
//    TYPE_75(UserAssetsEnum.TYPE_75, List.of(UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_33, UserAssetsEnum.TYPE_122)),
//
//    TYPE_187(UserAssetsEnum.TYPE_187, List.of(UserAssetsEnum.TYPE_16, UserAssetsEnum.TYPE_25, UserAssetsEnum.TYPE_48, UserAssetsEnum.TYPE_54, UserAssetsEnum.TYPE_55, UserAssetsEnum.TYPE_72, UserAssetsEnum.TYPE_74, UserAssetsEnum.TYPE_172)),
//    TYPE_81(UserAssetsEnum.TYPE_81, List.of(UserAssetsEnum.TYPE_16, UserAssetsEnum.TYPE_25, UserAssetsEnum.TYPE_48, UserAssetsEnum.TYPE_54, UserAssetsEnum.TYPE_55, UserAssetsEnum.TYPE_72, UserAssetsEnum.TYPE_74, UserAssetsEnum.TYPE_172)),
//
//    TYPE_103(UserAssetsEnum.TYPE_103, List.of(UserAssetsEnum.TYPE_16, UserAssetsEnum.TYPE_25, UserAssetsEnum.TYPE_48, UserAssetsEnum.TYPE_54, UserAssetsEnum.TYPE_55, UserAssetsEnum.TYPE_72, UserAssetsEnum.TYPE_74, UserAssetsEnum.TYPE_172)),
//
//    //UserAssetsEnum.TYPE_75,
//    TYPE_189(UserAssetsEnum.TYPE_189, List.of(UserAssetsEnum.TYPE_112, UserAssetsEnum.TYPE_57)),
//    TYPE_58(UserAssetsEnum.TYPE_58, List.of(UserAssetsEnum.TYPE_112, UserAssetsEnum.TYPE_57, UserAssetsEnum.TYPE_56)),
//
//    TYPE_196(UserAssetsEnum.TYPE_196, List.of(UserAssetsEnum.TYPE_48, UserAssetsEnum.TYPE_54)),
//
//
//    TYPE_111(UserAssetsEnum.TYPE_111, List.of(UserAssetsEnum.TYPE_47, UserAssetsEnum.TYPE_201, UserAssetsEnum.TYPE_122)),
//    TYPE_54(UserAssetsEnum.TYPE_54, List.of(UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_201, UserAssetsEnum.TYPE_122)),
//    TYPE_128(UserAssetsEnum.TYPE_128, List.of(UserAssetsEnum.TYPE_129)),
//
////    TYPE_15(UserAssetsEnum.TYPE_15, List.of(UserAssetsEnum.TYPE_22)),
////    TYPE_76(UserAssetsEnum.TYPE_76, List.of(UserAssetsEnum.TYPE_22)),
////    TYPE_77(UserAssetsEnum.TYPE_77, List.of(UserAssetsEnum.TYPE_22)),
//
//    TYPE_48(UserAssetsEnum.TYPE_48, List.of(UserAssetsEnum.TYPE_21)),
//
////    TYPE_112(UserAssetsEnum.TYPE_112, List.of(UserAssetsEnum.TYPE_111)),
//
//    TYPE_90(UserAssetsEnum.TYPE_90, List.of(UserAssetsEnum.TYPE_89)),
//
//
//    TYPE_98(UserAssetsEnum.TYPE_98, List.of(UserAssetsEnum.TYPE_97)),
//    TYPE_99(UserAssetsEnum.TYPE_99, List.of(UserAssetsEnum.TYPE_100)),
//
//
//    TYPE_181(UserAssetsEnum.TYPE_181, List.of(UserAssetsEnum.TYPE_5)),
//
////    TYPE_93(UserAssetsEnum.TYPE_93, List.of(UserAssetsEnum.TYPE_3,UserAssetsEnum.TYPE_4)),
//
//    TYPE_150(UserAssetsEnum.TYPE_150, List.of(UserAssetsEnum.TYPE_149)),
//    TYPE_163(UserAssetsEnum.TYPE_163, List.of(UserAssetsEnum.TYPE_162)),
//
//    TYPE_147(UserAssetsEnum.TYPE_147, List.of(UserAssetsEnum.TYPE_146)),
//
//    TYPE_135(UserAssetsEnum.TYPE_135, List.of(UserAssetsEnum.TYPE_134)),
//
//    TYPE_113(UserAssetsEnum.TYPE_113, List.of(UserAssetsEnum.TYPE_109)),
//    TYPE_206(UserAssetsEnum.TYPE_206, List.of(UserAssetsEnum.TYPE_205)),
//    TYPE_208(UserAssetsEnum.TYPE_208, List.of(UserAssetsEnum.TYPE_207)),
//    TYPE_210(UserAssetsEnum.TYPE_210, List.of(UserAssetsEnum.TYPE_209)),
//
//    TYPE_1000(UserAssetsEnum.TYPE_1000, List.of(UserAssetsEnum.TYPE_1001, UserAssetsEnum.TYPE_3, UserAssetsEnum.TYPE_10, UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_33)),
//
//    TYPE_49(UserAssetsEnum.TYPE_49, List.of(UserAssetsEnum.TYPE_47)),
//
//    TYPE_202(UserAssetsEnum.TYPE_202, List.of(UserAssetsEnum.TYPE_201)),
//    TYPE_34(UserAssetsEnum.TYPE_34, List.of(UserAssetsEnum.TYPE_33)),
//    TYPE_55(UserAssetsEnum.TYPE_55, List.of(UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_201, UserAssetsEnum.TYPE_122)),
//    TYPE_71(UserAssetsEnum.TYPE_71, List.of(UserAssetsEnum.TYPE_15, UserAssetsEnum.TYPE_76)),
//
//    TYPE_78(UserAssetsEnum.TYPE_78, List.of(UserAssetsEnum.TYPE_24)),
//
//    TYPE_86(UserAssetsEnum.TYPE_86, List.of(UserAssetsEnum.TYPE_85)),
//    TYPE_87(UserAssetsEnum.TYPE_87, List.of(UserAssetsEnum.TYPE_85)),
//    TYPE_92(UserAssetsEnum.TYPE_92, List.of(UserAssetsEnum.TYPE_89)),
//    TYPE_96(UserAssetsEnum.TYPE_96, List.of(UserAssetsEnum.TYPE_44)),
//    TYPE_101(UserAssetsEnum.TYPE_101, List.of(UserAssetsEnum.TYPE_102)),
//
//    TYPE_104(UserAssetsEnum.TYPE_104, List.of(UserAssetsEnum.TYPE_103)),
//    TYPE_110(UserAssetsEnum.TYPE_110, List.of(UserAssetsEnum.TYPE_109)),
//
//    TYPE_115(UserAssetsEnum.TYPE_115, List.of(UserAssetsEnum.TYPE_117)),
//    TYPE_116(UserAssetsEnum.TYPE_116, List.of(UserAssetsEnum.TYPE_114)),
//    TYPE_118(UserAssetsEnum.TYPE_118, List.of(UserAssetsEnum.TYPE_119)),
//    TYPE_123(UserAssetsEnum.TYPE_123, List.of(UserAssetsEnum.TYPE_122)),
//    TYPE_127(UserAssetsEnum.TYPE_127, List.of(UserAssetsEnum.TYPE_126)),
//    TYPE_133(UserAssetsEnum.TYPE_133, List.of(UserAssetsEnum.TYPE_132)),
//    TYPE_136(UserAssetsEnum.TYPE_136, List.of(UserAssetsEnum.TYPE_21, UserAssetsEnum.TYPE_201, UserAssetsEnum.TYPE_122)),
//    TYPE_137(UserAssetsEnum.TYPE_137, List.of(UserAssetsEnum.TYPE_57)),
//
//    TYPE_143(UserAssetsEnum.TYPE_143, List.of(UserAssetsEnum.TYPE_140)),
//    TYPE_139(UserAssetsEnum.TYPE_139, List.of(UserAssetsEnum.TYPE_138)),
//    TYPE_144(UserAssetsEnum.TYPE_144, List.of(UserAssetsEnum.TYPE_141)),
//    TYPE_145(UserAssetsEnum.TYPE_145, List.of(UserAssetsEnum.TYPE_142)),
//    TYPE_157(UserAssetsEnum.TYPE_157, List.of(UserAssetsEnum.TYPE_156)),
//
//    TYPE_159(UserAssetsEnum.TYPE_159, List.of(UserAssetsEnum.TYPE_158)),
//    TYPE_161(UserAssetsEnum.TYPE_161, List.of(UserAssetsEnum.TYPE_160)),
//    TYPE_166(UserAssetsEnum.TYPE_166, List.of(UserAssetsEnum.TYPE_165)),
//    TYPE_168(UserAssetsEnum.TYPE_168, List.of(UserAssetsEnum.TYPE_165)),
//
//    TYPE_180(UserAssetsEnum.TYPE_180, List.of(UserAssetsEnum.TYPE_177)),
//    TYPE_186(UserAssetsEnum.TYPE_186, List.of(UserAssetsEnum.TYPE_185)),
//    TYPE_188(UserAssetsEnum.TYPE_188, List.of(UserAssetsEnum.TYPE_187)),
//
//    TYPE_195(UserAssetsEnum.TYPE_195, List.of(UserAssetsEnum.TYPE_194)),
//    TYPE_200(UserAssetsEnum.TYPE_200, List.of(UserAssetsEnum.TYPE_199)),
//    TYPE_10004(UserAssetsEnum.TYPE_10004, List.of(UserAssetsEnum.TYPE_10003)),
//    TYPE_10007(UserAssetsEnum.TYPE_10007, List.of(UserAssetsEnum.TYPE_10006)),
//
//    TYPE_217(UserAssetsEnum.TYPE_217, List.of(UserAssetsEnum.TYPE_216)),
//
//    TYPE_91(UserAssetsEnum.TYPE_91, List.of(UserAssetsEnum.TYPE_88)),
//    TYPE_105(UserAssetsEnum.TYPE_105, List.of(UserAssetsEnum.TYPE_106)),
//    TYPE_184(UserAssetsEnum.TYPE_184, List.of(UserAssetsEnum.TYPE_183)),
//    TYPE_198(UserAssetsEnum.TYPE_198, List.of(UserAssetsEnum.TYPE_197)),
//
//
//    ;
//
//
//    public static final Map<UserAssetsEnum, List<UserAssetsEnum>> MAP = Arrays.stream(TypeRelationEnum.values())
//            .collect(Collectors.toMap(TypeRelationEnum::getTypeEnum, TypeRelationEnum::getReverseTypeEnumList));
//
//
//    private final UserAssetsEnum typeEnum;
//    private final List<UserAssetsEnum> reverseTypeEnumList;
//
//    public static List<UserAssetsEnum> getByTypeCode(UserAssetsEnum codeEnum) {
//        if (codeEnum == null || !MAP.containsKey(codeEnum)) {
//            return null;
//        }
//        return MAP.get(codeEnum);
//    }
//}
