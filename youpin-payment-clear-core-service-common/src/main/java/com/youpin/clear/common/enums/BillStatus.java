package com.youpin.clear.common.enums;

/**
 * 
 * @ClassName: BillStatus.java
 * @Package: com.youpin.payment.account.client.enums
 * @Description: 账单状态
 *
 * @version: v1.0.0
 * @author: x<PERSON><PERSON>
 * @date: 2023年8月4日 上午11:22:55 
 *
 */
public enum BillStatus {
	
	/**
	 * 初始化
	 */
	INIT(1, "初始化"),

	/**
	 * 进行中
	 */
	ING(2, "进行中"),

	/**
	 * 成功
	 */
	SUCCESS(3, "成功"),

	/**
	 * 失败
	 */
	FAILED(4, "失败")
	
	;

	private final Integer code;

	private final String message;

	BillStatus(Integer code, String message) {
		this.code = code;
		this.message = message;
	}


	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static BillStatus getByNetStatus(Integer code) {
		BillStatus billStatus = BillStatus.SUCCESS;
		if(code.equals(NetStatusEnum.PROCESSING.getCode())){
			billStatus = BillStatus.ING;
		}
		if(code.equals(NetStatusEnum.FAIL.getCode())){
			billStatus = BillStatus.FAILED;
		}
		return billStatus;
	}
}
