package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    /**
     * 余额
     */
    BALANCE(0, "余额"),
    /**
     * 余额1
     */
    BALANCE_1(1, "余额1"),

    /**
     * 余额1可交易
     */
    BALANCE_TRADER_1(11, "余额1可交易"),
    /**
     * 余额1可提现
     */
    BALANCE_WITHDRAW_1(12, "余额1可提现"),

    /**
     * 余额2
     */
    BALANCE_2(2, "余额2"),

    /**
     * 余额2可交易
     */
    BALANCE_TRADER_2(21, "余额2可交易"),
    /**
     * 余额2可提现
     */
    BALANCE_WITHDRAW_2(22, "余额2可提现"),
    /**
     * 求购余额
     */
    PURCHASE_BALANCE(3, "求购余额"),
    /**
     * 求购充值余额1 --- 求购充值1可提现
     */
    PURCHASE_BALANCE_RECHARGE_1(4, "求购充值1"),
    /**
     * 求购充值余额2 --- 求购充值2可提现
     */
    PURCHASE_BALANCE_RECHARGE_2(5, "求购充值2"),
    /**
     * 求购转入余额1
     */
    PURCHASE_BALANCE_TRANSFER_1(6, "求购转入1"),

    /**
     * 求购转入1可交易
     */
    PURCHASE_BALANCE_TRANSFER_TRADER_1(61, "求购转入1可交易"),

    /**
     * 求购转入1可提现
     */
    PURCHASE_BALANCE_TRANSFER_WITHDRAW_1(62, "求购转入1可提现"),

    /**
     * 求购转入余额2
     */
    PURCHASE_BALANCE_TRANSFER_2(7, "求购转入2"),

    /**
     * 求购转入2可交易
     */
    PURCHASE_BALANCE_TRANSFER_TRADER_2(71, "求购转入2可交易"),
    /**
     * 求购转入2可提现
     */
    PURCHASE_BALANCE_TRANSFER_WITHDRAW_2(72, "求购转入2可提现"),

    /**
     * 余额1可提现121
     */
    BALANCE_WITHDRAW_121(121, "余额1可提现临时"),

    /**
     * 余额2可提现122
     */
    BALANCE_WITHDRAW_122(122, "余额2可提现临时"),

    ;

    private static final Map<Integer, AccountTypeEnum> MAP = new HashMap<>();

    static {
        for (AccountTypeEnum item : AccountTypeEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String name;

    public static AccountTypeEnum getAccountTypeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }

}