package com.youpin.clear.common.constant;


public interface MQConfig {

    /**
     * 开发环境是通用2
     */
    String DEFAULT_PAYMENT_MQ = "rocketmq";

    /**
     * 开发环境是 通用1 测试环境是 通用1
     */
    String PAYMENT_CLEAR_MQ = "rocketmq1";

    /**
     *
     */
    String PAYMENT_ACCOUNT_MQ = "rocketmq3";


    /**
     * 资金明细消费组
     */
    String USER_ACCOUNT_RECORD_CONSUMER_GROUP = "USER_ACCOUNT_RECORD_CONSUMER_GROUP";

    /**
     * 资金明细消费组-拆解
     */
    String USER_ACCOUNT_RECORD_CONSUMER_TWO_GROUP = "USER_ACCOUNT_RECORD_CONSUMER_TWO_GROUP";


    /**
     * 资金明细TOPIC
     */
    String USER_ACCOUNT_RECORD_TOPIC = "USER_ACCOUNT_RECORD_TOPIC";

    /**
     * 资金明细TAG
     */
    String USER_ACCOUNT_RECORD_TAG = "SETTLEACCOUNTS";

    /**
     * 通用 topic 结合 tag 使用
     */
    String PAYMENT_CLEAR_COMMON_TOPIC = "PAYMENT_CLEAR_COMMON_TOPIC";

    /**
     * 用户资金余额对账
     */
    String USER_BALANCE_ASSETS_CHECK_TAG = "USER_BALANCE_ASSETS_CHECK_TAG";

    /**
     * 用户资金余额对账_消费组
     */
    String USER_BALANCE_ASSETS_CHECK_TAG_GROUP = "USER_BALANCE_ASSETS_CHECK_TAG_GROUP";


    /**
     * 用户资金渠道对账_消费组
     */
    String USER_BALANCE_CHANNEL_CHECK_TAG_GROUP = "USER_BALANCE_CHANNEL_CHECK_TAG_GROUP";


    /**
     * 用户开户 Topic
     */
    String USER_REGISTER_SUCCESS_TOPIC = "USER_REGISTER_SUCCESS_TOPIC";


    /**
     * 用户开户 tag
     */
    String USER_REGISTER_TAG = "USER_REGISTER_TAG";


    /**
     * 用户开户 Group
     */
    String USER_REGISTER_ACCOUNT_GROUP = "USER_REGISTER_ACCOUNT_GROUP";


    /**
     * 资金流水同步TOPIC
     */
    String USER_ACCOUNT_RECORD_SYNC_TOPIC = "USER_ACCOUNT_RECORD_SYNC_TOPIC";
    String USER_ACCOUNT_RECORD_SYNC_TAG = "USER_ACCOUNT_RECORD_SYNC_TAG";
    String USER_ACCOUNT_RECORD_CONSUMER_SYNC_GROUP = "USER_ACCOUNT_RECORD_CONSUMER_SYNC_GROUP";


    String SYNC_BILL_FROM_PAYCENTER_TOPIC = "USER-BILL-TOPIC";


    /**
     * 平台户交易流水-缓冲记账TOPIC
     */
    String PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TOPIC = "PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TOPIC";
    String PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TAG = "PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TAG";
    String PLATFORM_ACCOUNT_TRANSACTION_RECORDS_GROUP = "PLATFORM_ACCOUNT_TRANSACTION_RECORDS_GROUP";
    /**
     * net平台户资金异步缓冲顺序处理-过度
     */
    String NET_PLATFORM_USER_RECORDS_TOPIC = "NET_PLATFORM_USER_RECORDS_TOPIC";
    String NET_PLATFORM_USER_RECORDS_TAG = "NET_PLATFORM_USER_RECORDS_TAG";
    String NET_PLATFORM_USER_RECORDS_GROUP = "NET_PLATFORM_USER_RECORDS_GROUP";

}
