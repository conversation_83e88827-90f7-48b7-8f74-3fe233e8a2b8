package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum MowMoneyEnum {

    This_Money(1, "This_Money"),

    This_Only_Withdraw_Money(2, "This_Only_Withdraw_Money"),

    lease_money(3, "lease_money"),


    Special_Mow_Money(4, "Special_Mow_Money"),
    ;


    private static final Map<Integer, MowMoneyEnum> MAP = new HashMap<>();

    static {
        for (MowMoneyEnum item : MowMoneyEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String name;

    public static MowMoneyEnum getCodeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
