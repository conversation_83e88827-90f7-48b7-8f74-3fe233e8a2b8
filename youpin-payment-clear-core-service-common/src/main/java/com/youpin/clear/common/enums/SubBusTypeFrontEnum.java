package com.youpin.clear.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 子业务类型
 */

@Getter
@AllArgsConstructor
public enum SubBusTypeFrontEnum {

    /***
     * 子业务类型-特殊逻辑
     */
    SPECIAL_LOGIC(10, "特殊逻辑"),

    /***
     * 子业务类型-出售-求购供应
     */
    PURCHASE_SUPPLY(1300, "求购-供应"),

    /***
     * 子业务类型-出售-安心涨增值
     */
    AN_XIN_RISING(2000, "出售-安心涨增值"),

    /***
     * 子业务类型-出售-手机充值
     */
    MOBILE_RECHARGE(2100, "出售-手机充值"),

    /***
     * 子业务类型-出售-出售-zft-扫码-私密交易
     */
    PRIVATE_TRADE_ZFT_QRCODE(3000, "出售-zft-扫码-私密交易"),

    /***
     * 子业务类型-出售-红锁无忧增值
     */
    RED_LOCK_WORRY_FREE(4000, "出售-红锁无忧增值"),
    /***
     * 出售-自助解封
     */
    SELL_AUTO_UNBLOCK(4100, "出售-自助解封"),
    /***
     * 子业务类型-出售-赠送
     */
    SELL_GIVE(5000, "出售-赠送"),

    /***
     * 子业务类型-充值-钱包
     */
    RECHARGE_WALLET(7000, "充值-钱包"),

    /***
     * 子业务类型-充值-求购
     */
    RECHARGE_BUY(7100, "充值-求购"),

    /***
     * 子业务类型-充值-求购发布
     */
    RECHARGE_BUY_PUBLISH(7200, "充值-求购发布"),

    /***
     * 子业务类型-充值-立即守约
     */
    RECHARGE_KEEP_PROMISE(7300, "充值-立即守约"),

    /***
     * 子业务类型-充值-奖励发放活动
     */
    RECHARGE_REWARD_ACTIVITY(7400, "充值-奖励发放活动"),


    /**
     * 子业务类型-出售-还价支付
     */
    COUNTER_PAYMENT(8000, "出售-还价支付"),

    /***
     * 子业务类型-出售-极速发货
     */
    FAST_SHIP(9000, "出售-极速发货"),


    /**
     * 子业务类型-数码租赁-信用授权
     */
    DIGITAL_RENT_AUTH(10000, "数码租赁-信用授权"),

    /**
     * 子业务类型-数码租赁-主动支付
     */
    DIGITAL_RENT_PAYMENT(11000, "数码租赁-主动支付"),

    /**
     * 子业务类型-出售-租赁过户
     */
    SELL_LEASE_TRANSFER(12000, "出售-租赁过户"),

    /**
     * 子业务类型-租赁-出金返现
     */
    CASH_BACK(13000, "租赁-出金返现"),

    /**
     * 子业务类型-三方-管箱自开
     */
    THIRD_OPEN_BOX(14000, "三方-管箱自开"),

    /**
     * 还价
     */
    COUNTER_OFFER(15000, "新还价"),

    /**
     * 私密出售
     */
    PRIVATE_SALE(16000, "私密出售"),

    /***
     * 租赁
     */
    RENT_OUT(17000, "租赁-租赁"),


    /***
     * 充值-求购
     */
    FRONT_RECHARGE_BUY(18000, "充值-求购"),

    /***
     * 充值-求购发布
     */
    FRONT_RECHARGE_BUY_PUBLISH(18100, "充值-求购发布"),

    /***
     * 出售-普通商品
     */
    FRONT_NORMAL_GOOD(19000, "出售-普通商品"),

    /***
     * 出售-批量购买
     */
    FRONT_NORMAL_GOOD_BATCH(20000, "出售-批量购买"),


    /***
     * 出售-赠送
     */
    FRONT_SELL_GIVE(21000, "出售-赠送"),

    /**
     * 钱包提现
     */
    WALLET_RECHARGE_WITHDRAW(22000, "钱包提现"),

    /**
     * 求购提现
     */
    PURCHASE_RECHARGE_WITHDRAW(23000, "求购提现"),
    /**
     * CDK
     */
    SELL_CDK(24000, "CDK"),
    /**
     * 省钱会员
     */
    COST_SAVING_MEMBER(26000, "省钱会员"),

    /**
     * 出租大会员
     */
    SELL_BIG_VIP(25000, "出租大会员"),

    /**
     * 自动发货 已经凉了
     */
    @Deprecated
    AUTO_DELIVER(28000, "自动发货"),

    /**
     * 免押中心
     */
    DEPOSIT_FREE_CENTER(29000, "免押中心"),

    /**
     * 有品服饰
     */
    YOUPIN_CLOTHING(30000, "有品服饰"),

    /**
     * 预售保证金
     */
    PRE_SALE_DEPOSIT(31000, "预售保证金"),

    /**
     * 租赁续租
     */
    RE_LET(32000, "租赁续租"),
    /**
     * 极速发货
     */
    FRONT_FAST_SHIP(33000, "极速发货"),

    /**
     * 租赁-守约充值
     */
    KEEP_RECHARGE(34000, "守约充值"),

    /**
     * 钱包转入求购
     */
    MONEY_TRANSFER_PURCHASE(35000, "钱包转入求购"),

    /**
     * 求购转入钱包
     */
    PURCHASE_TRANSFER_MONEY(36000, "求购转入钱包"),

    /**
     * 租赁-免押
     */
    LEASE_CREDIT(37000, "租赁免押"),


    /**
     * 出售大会员
     */
    SELL_BIG_VIP_CENTER(38000, "出售大会员"),


    /**
     * 租赁买断
     */
    LEASE_BUYOUT(39000, "租赁买断"),

    /**
     * 租赁-守约支付
     */
    KEEP_PROMISE(40000, "租赁守约"),

    /**
     * 流放之路2
     */
    POE2(41000, "流放之路2"),

    /**
     * 充值待退货款
     */
    RECHARGE_WAIT_REFUND_GOODS(42000,"充值待退货款"),

    /**
     * 多游戏SDK-购买
     */
    MULTI_GAME_SDK_BUY(50000, "多游戏SDK-购买"),


    ;


    /**
     * CODE
     */
    private final Integer code;
    /**
     * NAME
     */
    private final String name;


    public static final Map<Integer, SubBusTypeFrontEnum> MAP = Arrays.stream(SubBusTypeFrontEnum.values()).collect(Collectors.toMap(SubBusTypeFrontEnum::getCode, t -> t));

    /**
     * 根据code获取枚举值
     */
    public static SubBusTypeFrontEnum getByCode(Integer code) {

        if (code == null) {
            return null;
        }

        for (SubBusTypeFrontEnum e : SubBusTypeFrontEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

}
