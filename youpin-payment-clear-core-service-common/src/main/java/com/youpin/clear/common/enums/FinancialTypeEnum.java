package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum FinancialTypeEnum {

    /**
     * 支付
     */
    Pay(1, "支付"),
    /**
     * 退款
     */
    Refund(2, "退款"),
    /**
     * 结算
     */
    Settlement(3, "结算"),
    /**
     * 特殊资金处理
     */
    Special_Settlement(4, "特殊资金处理"),
    /**
     * 提现
     */
    Withdrawal(5, "提现"),


    ;

    private static final Map<Integer, FinancialTypeEnum> MAP = new HashMap<>();

    static {
        for (FinancialTypeEnum item : FinancialTypeEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String name;

    public static FinancialTypeEnum getTypeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
