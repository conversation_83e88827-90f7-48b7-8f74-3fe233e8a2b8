//package com.youpin.clear.common.enums;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 数据库连带关系查找
// */
//@Getter
//@AllArgsConstructor
//public enum DBAssociatedAdditionEnum {
//
//
//    TYPE_4(UserAssetsEnum.TYPE_4.getTypeId(), List.of(UserAssetsEnum.TYPE_4.getTypeId())),
//    TYPE_5(UserAssetsEnum.TYPE_5.getTypeId(), List.of(UserAssetsEnum.TYPE_5.getTypeId())),
//
//    TYPE_188(UserAssetsEnum.TYPE_188.getTypeId(), List.of(UserAssetsEnum.TYPE_188.getTypeId())),
//    TYPE_104(UserAssetsEnum.TYPE_104.getTypeId(), List.of(UserAssetsEnum.TYPE_104.getTypeId())),
//    TYPE_96(UserAssetsEnum.TYPE_96.getTypeId(), List.of(UserAssetsEnum.TYPE_96.getTypeId())),
//
//
//    ;
//
//    public static final Map<Integer, List<Integer>> MAP = new HashMap<>();
//
//    static {
//        for (DBAssociatedAdditionEnum item : DBAssociatedAdditionEnum.values()) {
//            MAP.put(item.typeId, item.typeIdList);
//        }
//    }
//
//    private final Integer typeId;
//    private final List<Integer> typeIdList;
//
//    public static List<Integer> getByTypeCode(Integer typeId) {
//        if (typeId == null || !MAP.containsKey(typeId)) {
//            return null;
//        }
//        return MAP.get(typeId);
//    }
//
//
//}
