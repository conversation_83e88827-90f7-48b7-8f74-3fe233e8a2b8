package com.youpin.clear.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SellOrderSettleTypeEnum {

    SELL_ORDER_BUY_FREEZE(1, "购买冻结"),
    SELL_ORDER_SAFE_BUY_FREEZE(2, "安心购冻结"),
    SELL_ORDER_BUY_UNFREEZE(3, "购买解冻"),
    SELL_ORDER_SAFE_BUY_UNFREEZE(4, "安心购解冻"),
    SELL_ORDER_BUY_ER_PUNISH(5, "交易处罚"),
    SELL_ORDER_BUY_PAY(6, "购买支付"),
    SELL_ORDER_SAFE_BUY_PAY(7, "安心购支付"),
    SELL_ORDER_RECEIVE(8, "购买收取"),
    SELL_ORDER_GIVEBACK(9, "退还金额"),
    SELL_ORDER_SERVICE_FEE(10, "手续费"),
    SELL_COMMISSION(18, "交易服务费"),
    SELL_COMMISSION_REFUND(19, "退还交易服务费"),
    SELL_ORDER_SAFE_BUY_RECEIVE(11, "安心购收取"),
    SELL_ORDER_REFUND(12, "购买退款"),
    SELL_ORDER_SAFE_BUY_REFUND(13, "安心购退款"),
    COUNTER_OFFER_ORDER_BUYER_PUNISH(16, "还价处罚"),
    GIVE_SERVICE_CHARGE(20, "赠送服务费"),
    REFUND_GIVE_SERVICE_CHARGE(21, "退还赠送服务费"),
    DEDUCT_BUYER_FINE(214, "扣除买家违约金"),
    SUBSIDY_SELLER_FINE(215, "补贴卖家违约金"),
    PLATFORM_DEFAULT_FEE(218, "预售违约手续费");



    private final Integer code;
    private final String desc;


}

