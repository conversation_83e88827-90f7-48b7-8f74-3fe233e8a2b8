package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum AmountTypeEnum {

    /**
     * 可用
     */
    AVAILABLE(1, "可用"),
    /**
     * 冻结
     */
    FREEZE(2, "冻结"),


    ;

    private static final Map<Integer, AmountTypeEnum> MAP = new HashMap<>();

    static {
        for (AmountTypeEnum item : AmountTypeEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String name;

    public static AmountTypeEnum getAmountTypeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}