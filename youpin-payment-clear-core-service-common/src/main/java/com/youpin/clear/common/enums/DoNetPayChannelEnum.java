package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum DoNetPayChannelEnum {

    /**
     * 未知
     */
    None(-1, "不选"),

    /**
     * 余额
     */
    Balance(0, "余额"),

    /**
     * 微信
     */
    Wx(1, "微信"),

    /**
     * 支付宝
     */
    Alipay(2, "支付宝"),

    /**
     * 积分
     */
    Integral(3, "积分"),

    /**
     * 额度
     */
    Quota(4, "额度"),


    /**
     * 押金卡
     */
    DepositCard(5, "押金卡"),


    /**
     * 临时额度
     */
    TemporaryQuota(6, "临时额度"),

    /**
     * 固定额度
     */
    FixedQuota(7, "押金卡"),

    /**
     * 求购余额
     */
    PurchaseBalance(8, "求购余额"),


    /**
     * 支付宝原路退还
     */
    AlipayRefund(9, "支付宝原路退还"),

    /**
     * 支付宝记账本
     */
    AlipayFundAccountBook(10, "支付宝记账本"),

    /**
     * 京东
     */
    JD(11, "京东"),

    /**
     * 京东原路退还
     */
    JDRefund(12, "京东原路退还"),

    DyPay(13, "抖音"),

    DyRefund(14, "抖音支付原路退还"),

    YBWithdraw(15, "易宝提现");


    private static final Map<Integer, DoNetPayChannelEnum> MAP = new HashMap<>();

    static {
        for (DoNetPayChannelEnum item : DoNetPayChannelEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String text;

    public static DoNetPayChannelEnum getByPayChannel(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}