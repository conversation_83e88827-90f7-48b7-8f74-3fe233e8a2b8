package com.youpin.clear.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum AccountPayChannelEnum {

    BALANCE(0, "余额"),
    ALI_PAY(2, "支付宝"),
    PURCHASE_BALANCE(8, "求购余额"),
    DY_PAY(13, "抖音"),
    JD_PAY(11, "京东"),
    YEE_PAY(15, "易宝"),

    ;

    private final int code;

    private final String desc;

    private static final Map<Integer, AccountPayChannelEnum> map = new HashMap<>();

    AccountPayChannelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static {
        for (AccountPayChannelEnum accountPayChannelEnum : AccountPayChannelEnum.values()) {
            map.put(accountPayChannelEnum.getCode(), accountPayChannelEnum);
        }
    }

    public static AccountPayChannelEnum getByCode(int code) {
        return map.get(code);
    }
}
