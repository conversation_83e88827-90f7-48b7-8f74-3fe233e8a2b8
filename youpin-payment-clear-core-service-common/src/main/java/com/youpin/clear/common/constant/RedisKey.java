package com.youpin.clear.common.constant;

public interface RedisKey {

    /**
     * 基础key
     */
    String BASE_KEY = "youpin:clear:BaseKey:";

    /**
     * 资产类型缓存
     */
    String REDIS_KEY_ASSETS_TYPE_MAP_CACHE = BASE_KEY + "assets_type_map_cache";

    /**
     * 资产类型关联关系缓存
     */
    String REDIS_KEY_ASSETS_TYPE_RELATE_MAP_CACHE = BASE_KEY + "assets_type_relate_map_cache";


    /**
     * 用户标签缓存
     */
    String REDIS_KEY_USER_TAG_MAP_CACHE = BASE_KEY + "user_tag_map_cache:";

    /**
     * 服务费流水总金额
     */
    String TRANSACTION_SERVICE_FEE_STATEMENT_SUM_CACHE = BASE_KEY + "service_fee_sum_cache:";


    /**
     * 服务费流水总80总金额
     */
    String TRANSACTION_SERVICE_FEE_STATEMENT_CACHE = BASE_KEY + "service_fee_cache:";


    /**
     *
     */
    String MONEY1_TO_MONEY2_CACHE = BASE_KEY + "money1ToMoney2:";

    /**
     *
     */
    String MONEY1_TO_MONEY2_MSG_CACHE = BASE_KEY + "money1ToMoney2_msg:";


    /**
     * 服务费流水总金额
     */
    String TRANSACTION_SERVICE_FEE_END_USER_CACHE = BASE_KEY + "selectEndUserId";

    /**
     * 财务日汇总
     */
    String TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE = BASE_KEY + "financeSummaryDay:";

    /**
     * 财务日汇总邮件
     */
    String TRANSACTION_SERVICE_FEE_SUMMARY_DAY_EMAIL_CACHE = BASE_KEY + "financeSummaryDay:email:";



}
