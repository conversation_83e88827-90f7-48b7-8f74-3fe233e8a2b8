package com.youpin.clear.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum UU898UserSubAccountType {

    /**
     * 可提现账户
     * 虚拟账户，通过userAssetInfo.money - 可交易账户金额计算
     */
    WITHDRAW(1, "可提现账户"),

    /**
     * 可冻结账户
     */
    FREEZE(2, "可冻结账户"),

    /**
     * 可交易账户
     */
    TRADE(3, "可交易账户"),

    ;


    private final Integer type;

    private final String desc;


    UU898UserSubAccountType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private static Map<Integer, UU898UserSubAccountType> map = new HashMap<>();

    static {
        for (UU898UserSubAccountType UU898UserSubAccountType : UU898UserSubAccountType.values()) {
            map.put(UU898UserSubAccountType.getType(), UU898UserSubAccountType);
        }
    }

    public static UU898UserSubAccountType getByCode(Integer code) {
        return map.get(code);
    }
}
