package com.youpin.clear.common.enums;

import java.util.List;

/**
 * 支付渠道
 *
 * <AUTHOR> dingtao
 * @date : 2022-8-12
 */
public enum PayChannelEnum {

    /**
     * 未知
     */
    UNKNOWN(0, "未知"),

    /**
     * 余额
     */
    ACCOUNT_BALANCE(100, "余额"),

    /**
     * 支付宝
     */
    ALIPAY(200, "支付宝"),

    /**
     * 微信
     */
    WECHAT(300, "微信"),
    /**
     * 京东
     */
    JD_PAY(400, "京东"),
    /**
     * 抖音
     */
    DY_PAY(500, "抖音"),

    ;
    private final Integer code;
    private final String name;

    PayChannelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PayChannelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PayChannelEnum e : PayChannelEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static PayChannelEnum getPayChannelByNetPayChannel(DoNetPayChannelEnum netPayChannel) {
        PayChannelEnum payChannelEnum = PayChannelEnum.UNKNOWN;
        //List<DoNetPayChannelEnum> payChannelBalance = new List<PayChannel>() { PayChannel.Balance, PayChannel.PurchaseBalance };
        List<DoNetPayChannelEnum> payChannelBalance = List.of(DoNetPayChannelEnum.Balance,DoNetPayChannelEnum.PurchaseBalance);
        if (payChannelBalance.contains(netPayChannel))
        {
            payChannelEnum = PayChannelEnum.ACCOUNT_BALANCE;
        }
        //List<PayChannel> payChannelAli = new List<PayChannel>() { PayChannel.AlipayRefund, PayChannel.Alipay, PayChannel.AlipayFundAccountbook };
        List<DoNetPayChannelEnum> payChannelAli = List.of(DoNetPayChannelEnum.Alipay, DoNetPayChannelEnum.AlipayRefund,DoNetPayChannelEnum.AlipayFundAccountBook);
        if (payChannelAli.contains(netPayChannel))
        {
            payChannelEnum = PayChannelEnum.ALIPAY;
        }

       // List<DoNetPayChannelEnum> paychannelJD = new List<PayChannel>() { PayChannel.JD, PayChannel.JDRefund };
        List<DoNetPayChannelEnum> paychannelJD = List.of(DoNetPayChannelEnum.JD, DoNetPayChannelEnum.JDRefund);
        if (paychannelJD.contains(netPayChannel))
        {
            payChannelEnum = PayChannelEnum.JD_PAY;
        }

       // List<PayChannel> pcDouyinpay = new List<PayChannel>() { PayChannel.Douyinpay };

        List<DoNetPayChannelEnum> pcDouyinpay = List.of(DoNetPayChannelEnum.DyPay);
        if (pcDouyinpay.contains(pcDouyinpay))
        {
            payChannelEnum = PayChannelEnum.DY_PAY;
        }

        return payChannelEnum;
    }
}
