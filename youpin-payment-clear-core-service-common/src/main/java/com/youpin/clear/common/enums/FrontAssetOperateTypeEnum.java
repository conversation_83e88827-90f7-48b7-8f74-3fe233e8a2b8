package com.youpin.clear.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum FrontAssetOperateTypeEnum {
    EXPENSE(0, "查询支出类型", List.of(AssetOperateTypeEnum.EXPENSE.getCode())),
    INCOME(1, "查询收入类型", List.of(AssetOperateTypeEnum.INCOME.getCode())),
    KEEP(2, "资金不变", List.of(AssetOperateTypeEnum.KEEP.getCode())),
    NON_EXPENSE(3, "查询非支出类型", List.of(AssetOperateTypeEnum.INCOME.getCode(), AssetOperateTypeEnum.KEEP.getCode())),
    NON_INCOME(4, "查询非收入类型", List.of(AssetOperateTypeEnum.EXPENSE.getCode(), AssetOperateTypeEnum.KEEP.getCode()));

    private final int code;

    private final String desc;

    private final List<Integer> innerOperateTypeList;

    private static final Map<Integer, FrontAssetOperateTypeEnum> MAP = new HashMap<>();

    FrontAssetOperateTypeEnum(int code, String desc, List<Integer> innerOperateTypeList) {
        this.code = code;
        this.desc = desc;
        this.innerOperateTypeList = innerOperateTypeList;
    }

    static {
        for (FrontAssetOperateTypeEnum type : FrontAssetOperateTypeEnum.values()) {
            MAP.put(type.getCode(), type);
        }
    }

    public static FrontAssetOperateTypeEnum getByCode(int code) {
        return MAP.get(code);
    }
}
