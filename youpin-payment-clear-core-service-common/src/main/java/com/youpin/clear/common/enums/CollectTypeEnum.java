package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CollectTypeEnum {

    /**
     * 收单类型 1 默认
     */
    DEFAULT(0, "默认"),

    /**
     * 收单类型  1 自有
     */
    OWN(1, "自有"),
    /**
     * 收单类型  2 监管
     */
    SUPERVISION(2, "监管");


    static final Map<Integer, CollectTypeEnum> MAP = new HashMap<>();

    static {
        for (CollectTypeEnum item : CollectTypeEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    private final Integer code;
    private final String name;

    public static CollectTypeEnum getCollectTypeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
