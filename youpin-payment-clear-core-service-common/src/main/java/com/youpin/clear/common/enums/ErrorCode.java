package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成功：0，错误码 8001 | 400 500 600 | xx
 * x01 参数校验
 * x02 外部接口调用异常
 * x03 数据库操作异常
 * x04 流程处理异常
 * x05 redis操作失败
 * x06 mq操作失败
 * x90 其他异常
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ErrorCode {

    /***
     * 处理成功
     */
    SUCCESS(0, "处理成功"),

    //-------------800----------------------

    ID_FAIL(800110101, "获取统一id异常"),

    SNOW_ID(800110102, "采取雪花算法生成唯一Id"),

    SYSTEM_ERROR(800140100, "系统异常"),

    REQUEST_PARAM_ERROR(800140101, "请求参数错误"),

    OPERATE_FREQUENT_ERROR(800140104, "操作频繁"),


    CALL_COMMODITY_REMOTE_ERROR(800150201, "远程调用商品服务异常"),

    CALL_SERVICE_USER_REMOTE_ERROR(800150202, "远程调用用户服务异常"),

    CALL_PAY_CENTER_REMOTE_ERROR(800150203, "远程调用支付网关异常"),

    REMOVE_CACHE_BLOCKS_ERROR(800150501, "redis删除黑名单异常"),

    INSERT_CACHE_BLOCKS_ERROR(800150502, "redis新增黑名单异常"),

    REDIS_FAILED(800150503, "操作redis失败,请重试"),

    REDIS_LOCK_FAILED(*********, "redis获取锁失败"),

    MQ_SEND_FAILED(*********, "mq发送失败"),

    MQ_CONSUMER_FAILED(*********, "mq消费失败"),

    LOCK_PAY_ORDER_TRY_LOCK_FAIL(*********, "锁支付单获取redis锁失败"),

    LOCK_PAY_ORDER_HANDLE_ERROR(*********, "锁支付单执行异常"),

    APOLLO_PARAM_ERROR(*********, "Apollo配置解析异常"),

    /**
     * 不支持的逻辑处理
     */
    NOT_SUPPORT_LOGIC_ERROR(*********, "不支持的逻辑处理"),


    OPEN_ACCOUNT_ERROR(*********, "账户开户重复"),


    //资金映射关系不存在
    ACCOUNT_MAPPING_NOT_EXIST(*********, "资金映射关系不存在"),

    //资金明细订单号不存在
    ACCOUNT_RECORD_ORDER_NO_NOT_EXIST(*********, "资金明细订单号不存在"),

    /**
     * 资金方向映射工厂类不存在
     */
    ACCOUNT_DIRECTION_MAPPING_FACTORY_NOT_EXIST(*********, "资金方向映射工厂类不存在"),

    /**
     * 账户余额同步失败
     */
    ACCOUNT_SYNC_FAIL(*********, "账户余额同步失败"),
    /**
     * 账户不存在
     */
    ACCOUNT_NOT_EXIST(*********, "账户不存在"),
    /**
     * 账户余额扣减不足
     */
    ACCOUNT_NOT_ENOUGH(*********, "账户余额扣减不足"),
    /**
     * 关系查询失败
     */
    RELATION_QUERY_FAIL(*********, "关系查询失败"),

    /**
     * 金额校验失败
     */
    AMOUNT_CHECK_FAIL(*********, "金额校验失败"),
    /**
     * 分账金额计算失败
     */
    AMOUNT_CALCULATE_FAIL(*********, "分账金额计算失败"),
    // 账户分账记录已存在
    ACCOUNT_RECORD_EXIST(*********, "账户分账记录已存在"),

    /**
     * 账户金额更新失败
     */
    ACCOUNT_UPDATE_FAIL(*********, "账户金额更新失败"),

    /**
     * 分账失败
     */
    ACCOUNT_DISTRIBUTION_FAIL(*********, "账户分账失败"),

    /**
     * 金额不能小于0
     */
    AMOUNT_NOT_LESS_ZERO(*********, "金额不能小于0"),

    /**
     * 账户余额不足
     */
    ACCOUNT_BALANCE_NOT_ENOUGH(*********, "账户余额不足"),

    /**
     * 账单状态消费顺序异常
     */
    BILL_STATUS_ORDER_ERROR(*********, "账单状态消费顺序异常"),

    /**
     * 异常测试
     */
    EXCEPTION_TEST(*********, "异常测试"),

    /**
     * 账户冻结余额不足
     */
    ACCOUNT_FROZEN_BALANCE_NOT_ENOUGH(*********, "账户冻结余额不足"),
    /**
     * 冻结金额校验失败
     */
    FROZEN_AMOUNT_CHECK_FAIL(*********, "金额校验失败"),

    /**
     * 补偿保存失败
     */
    COMPENSATION_SAVE_FAIL(*********, "补偿保存失败"),

    /**
     * 业务工厂不存在
     */
    BUSINESS_FACTORY_NOT_EXIST(*********, "业务工厂不存在"),

    /**
     * 用户资金信息为空
     */
    USER_ACCOUNT_INFO_IS_NULL(*********, "用户资金信息为空"),

    /**
     * 余额不足
     */
    BALANCE_NOT_ENOUGH(*********, "余额不足"),

    /**
     * 资金处理异常
     */
    ACCOUNT_PROCESS_ERROR(*********, "资金处理异常"),

    /**
     * 求购余额不足
     */
    PURCHASE_BALANCE_NOT_ENOUGH(*********, "求购余额不足"),

    /**
     * 结算金额大于支付金额
     */
    SETTLEMENT_AMOUNT_GREATER_THAN_PAY_AMOUNT(*********, "结算金额大于支付金额"),

    /**
     * 冻结余额不足
     */
    BLOCK_BALANCE_NOT_ENOUGH(*********, "冻结余额不足"),

    /**
     * 余额没有进行中或错误这个状态
     */
    BALANCE_PAY_NOT_STATUS(*********, "余额没有进行中或错误这个状态"),


    /**
     * 退款金额 大于 支出金额
     */
    REFUND_AMOUNT_GREATER_THAN_EXPEND_AMOUNT(*********, "退款金额大于支出金额"),

    /**
     * 账户余额不足告警
     */
    MARKETING_SAVE_WARNING(*********, "账户余额不足告警"),


    MOCK_ERROR(*********, "mock异常"),


    /**
     * 提现金额大于求购可用提现金额
     */
    PURCHASE_MONEY_GREATER_AVAILABLE_MONEY_ERROR(*********, "提现金额大于求购可用提现金额"),

    /**
     * 已结算解冻金额大于待结算解冻金额
     */
    SETTLEMENT_FROZEN_GREATER_SETTLEMENT_FROZEN_ERROR(*********, "结算解冻金额大于剩余结算冻结金额"),

    /**
     * 余额策略错误
     */
    BALANCE_STRATEGY_ERROR(*********, "结算策略错误"),

    /**
     * 调用业务账户服务异常
     */
    CALL_BUSINESS_ACCOUNT_SERVICE_ERROR(*********, "调用业务账户服务异常"),

    ;


    /***
     * 错误码code
     */
    private final Integer code;

    /***
     * 错误码描述
     */
    private final String message;
}
