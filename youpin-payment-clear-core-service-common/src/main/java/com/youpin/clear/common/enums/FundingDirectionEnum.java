package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum FundingDirectionEnum {
    /**
     * 充值
     */
    RECHARGE("RECHARGE", "充值", 0),

    /**
     * 冻结
     */
    FREEZE("FREEZE", "冻结", 1),

    /**
     * 解冻
     */
    UNFREEZE("UNFREEZE", "解冻", 2),

    /**
     * 收入
     */
    ADDITION("ADDITION", "加", 3),

    /**
     * 支出
     */
    SUBTRACTION("SUBTRACTION", "减", 4),

    /**
     * 退款
     */
    REFUND("REFUND", "退款", 3),

    /**
     * 资金不变  -
     */
    INVARIANT_SUBTRACTION("INVARIANT", "资金不变-减", 5),

    /**
     * 资金不变  +
     */
    INVARIANT_ADDITION("INVARIANT", "资金不变-加", 5),
    
    /**
     * 提现
     */
    CASH_WITHDRAWAL("CASH_WITHDRAWAL", "提现", 6),


    /**
     * 划拨（从另一个账户转移到另一个账户）
     */
    TRANSFER("TRANSFER", "划拨", 7),
    ;


    private final String code;
    private final String name;
    private final Integer sort;


    private static final Map<String, FundingDirectionEnum> MAP = new HashMap<>();

    static {
        for (FundingDirectionEnum item : FundingDirectionEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    public static FundingDirectionEnum getCodeEnum(String code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
