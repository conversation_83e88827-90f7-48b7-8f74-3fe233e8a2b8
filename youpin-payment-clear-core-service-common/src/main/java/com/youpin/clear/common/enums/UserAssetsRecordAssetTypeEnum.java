package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 资产类型枚举
 */
@Getter
@AllArgsConstructor
public enum UserAssetsRecordAssetTypeEnum {
    /**
     * 账户余额
     */
    Balance(1),

    /**
     * 积分
     */
    Integral(10),

    /**
     * 冻结金额
     */
    BlockMoney(20),

    /**
     * 求购金额
     */
    PurchaseMoney(30),

    /**
     * 只可提现金额
     */
    OnlyWithDrawMoney(40),
    ;


    /**
     * code
     */
    private final Integer code;


    private static final Map<Integer, UserAssetsRecordAssetTypeEnum> MAP = new HashMap<>();

    static {
        for (UserAssetsRecordAssetTypeEnum item : UserAssetsRecordAssetTypeEnum.values()) {
            MAP.put(item.code, item);
        }
    }


    public static UserAssetsRecordAssetTypeEnum getAccountTypeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}