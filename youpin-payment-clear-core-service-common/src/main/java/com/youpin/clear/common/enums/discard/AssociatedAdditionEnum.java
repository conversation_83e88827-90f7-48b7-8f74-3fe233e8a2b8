//package com.youpin.clear.common.enums;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 入参连带关系查找
// */
//@Getter
//@AllArgsConstructor
//public enum AssociatedAdditionEnum {
//
//    TYPE_149(UserAssetsEnum.TYPE_149.getTypeId(), List.of(UserAssetsEnum.TYPE_172.getTypeId())),
//
//    TYPE_172(UserAssetsEnum.TYPE_172.getTypeId(), List.of(UserAssetsEnum.TYPE_149.getTypeId())),
//
//    TYPE_83(UserAssetsEnum.TYPE_83.getTypeId(), List.of(UserAssetsEnum.TYPE_5.getTypeId())),
//
//    TYPE_16(UserAssetsEnum.TYPE_16.getTypeId(), List.of(UserAssetsEnum.TYPE_74.getTypeId(), UserAssetsEnum.TYPE_75.getTypeId(), UserAssetsEnum.TYPE_123.getTypeId())),
//
//    TYPE_123(UserAssetsEnum.TYPE_123.getTypeId(), List.of(UserAssetsEnum.TYPE_16.getTypeId())),
//
//
//    TYPE_74(UserAssetsEnum.TYPE_74.getTypeId(), List.of(UserAssetsEnum.TYPE_16.getTypeId(), UserAssetsEnum.TYPE_75.getTypeId())),
//    TYPE_75(UserAssetsEnum.TYPE_75.getTypeId(), List.of(UserAssetsEnum.TYPE_16.getTypeId(), UserAssetsEnum.TYPE_74.getTypeId())),
//
//    TYPE_22(UserAssetsEnum.TYPE_22.getTypeId(), List.of(UserAssetsEnum.TYPE_58.getTypeId(), UserAssetsEnum.TYPE_189.getTypeId())),
//
//    TYPE_58(UserAssetsEnum.TYPE_58.getTypeId(), List.of(UserAssetsEnum.TYPE_22.getTypeId(), UserAssetsEnum.TYPE_189.getTypeId())),
//
//    TYPE_189(UserAssetsEnum.TYPE_189.getTypeId(), List.of(UserAssetsEnum.TYPE_58.getTypeId(), UserAssetsEnum.TYPE_22.getTypeId())),
//
//
//    TYPE_48(UserAssetsEnum.TYPE_48.getTypeId(), List.of(UserAssetsEnum.TYPE_136.getTypeId())),
//
//    TYPE_136(UserAssetsEnum.TYPE_136.getTypeId(), List.of(UserAssetsEnum.TYPE_48.getTypeId(), UserAssetsEnum.TYPE_55.getTypeId())),
//
//    TYPE_55(UserAssetsEnum.TYPE_55.getTypeId(), List.of(UserAssetsEnum.TYPE_136.getTypeId())),
//
//    TYPE_187(UserAssetsEnum.TYPE_187.getTypeId(), List.of(UserAssetsEnum.TYPE_81.getTypeId())),
//    TYPE_81(UserAssetsEnum.TYPE_81.getTypeId(), List.of(UserAssetsEnum.TYPE_187.getTypeId())),
//
//    ;
//
//    public static final Map<Integer, List<Integer>> MAP = new HashMap<>();
//
//    static {
//        for (AssociatedAdditionEnum item : AssociatedAdditionEnum.values()) {
//            MAP.put(item.typeId, item.typeIdList);
//        }
//    }
//
//    private final Integer typeId;
//    private final List<Integer> typeIdList;
//
//    public static List<Integer> getByTypeCode(Integer typeId) {
//        if (typeId == null || !MAP.containsKey(typeId)) {
//            return null;
//        }
//        return MAP.get(typeId);
//    }
//
//
//}
