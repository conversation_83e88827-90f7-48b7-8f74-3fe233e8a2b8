package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


@Getter
@AllArgsConstructor
public enum AccountAssetsTypeRelateEnum {
    INCOME_FROM_EXPENDITURE("INCOME_FROM_EXPENDITURE_", "支出转收入"),
    ASSOCIATED_ADDITION("ASSOCIATED_ADDITION_", "关联加"),
    DB_ASSOCIATED_ADDITION("DB_ASSOCIATED_ADDITION_", "数据库关联加"),
    SPECIAL("SPECIAL_", "特殊"),
    ;
    /**
     * 前缀
     */
    private final String prefix;

    private final String desc;

    private static final Map<String, AccountAssetsTypeRelateEnum> MAP = new HashMap<>();

    static {
        for (AccountAssetsTypeRelateEnum item : AccountAssetsTypeRelateEnum.values()) {
            MAP.put(item.prefix, item);
        }
    }


    public static AccountAssetsTypeRelateEnum getAccountTypeEnum(String code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}
