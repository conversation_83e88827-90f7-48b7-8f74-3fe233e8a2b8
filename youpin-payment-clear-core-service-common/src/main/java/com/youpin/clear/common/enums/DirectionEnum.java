package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DirectionEnum {

    /**
     * 充值
     */
    RECHARGE("REC<PERSON>R<PERSON>", "充值", 0),

    /**
     * 冻结+
     */
    FREEZE_ADDITION("FREEZE_ADDITION", "冻结+", 1),
    /**
     * 冻结退还
     */
    FREEZE_REFUND("FREEZE_REFUND", "冻结退还", 2),

    /**
     * 退款
     */
    REFUND("REFUND", "退款", 2),

    /**
     * 减
     */
    SUBTRACTION("SUBTRACTION", "减", 5),

    /**
     * 加
     */
    ADDITION("ADDITION", "加", 4),


    /**
     * 渠道冻结
     */
    CHANNEL_FREEZE("CHANNEL_FREEZE", "渠道冻结", 3),

    /**
     * 渠道解冻
     */
    CHANNEL_UNFREEZE("CHANNEL_UNFREEZE", "渠道解冻", 4),


    /**
     * 提现
     */
    CASH_WITHDRAWAL("CASH_WITHDRAWAL", "提现", 6),

    /**
     * 不变
     */
    INVARIANT("INVARIANT", "不变", 8),


    /**
     * 入参查找
     */
    PARAM_FIND("PARAM_FIND", "入参查找", 9),

    /**
     * 特殊处理
     */
    TYPE_SPECIAL("TYPE_SPECIAL", "特殊处理", 9),


    ;
    private final String code;
    private final String name;
    private final Integer sort;


    private static final Map<String, DirectionEnum> MAP = new HashMap<>();

    static {
        for (DirectionEnum item : DirectionEnum.values()) {
            MAP.put(item.code, item);
        }
    }

    public static DirectionEnum getCodeEnum(String code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }

}