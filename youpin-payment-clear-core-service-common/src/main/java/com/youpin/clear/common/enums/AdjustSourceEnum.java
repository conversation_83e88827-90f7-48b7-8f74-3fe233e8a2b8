package com.youpin.clear.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum AdjustSourceEnum {

    RISK(1,"风控"),

    FINANCE(2,"财务"),

    BUSINESS(3,"运营"),

    PM_RD(4,"产研"),

    ;

    private final Integer code;

    private final String desc;

    private static final Map<Integer, AdjustSourceEnum> MAP = new HashMap<>();

    static {
        for (AdjustSourceEnum adjustSourceEnum : AdjustSourceEnum.values()) {
            MAP.put(adjustSourceEnum.getCode(), adjustSourceEnum);
        }
    }


    AdjustSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AdjustSourceEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
