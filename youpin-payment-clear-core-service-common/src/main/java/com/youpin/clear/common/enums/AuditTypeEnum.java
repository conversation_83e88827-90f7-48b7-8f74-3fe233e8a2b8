package com.youpin.clear.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum AuditTypeEnum {

    PASS(1, "通过"),
    REJECT(2, "拒绝"),
    ;

    private final int code;

    private final String msg;

    private static final Map<Integer, AuditTypeEnum> map = new HashMap<>();

    AuditTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    static {
        for (AuditTypeEnum item : AuditTypeEnum.values()) {
            map.put(item.getCode(), item);
        }
    }

    public static AuditTypeEnum getEnum(int code) {
        return map.get(code);
    }

}
