package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssetsErrorCode {

    /***
     * 处理成功
     */
    SUCCESS(0, "处理成功"),

    /***
     * 处理失败
     */
    ERROR(9999, "处理失败"),


    /**
     * 资金已处理
     */
    ALREADY_PROCESSED(6003, "资金已处理"),


    ;

    /***
     * 错误码code
     */
    private final Integer code;

    /***
     * 错误码描述
     */
    private final String message;
}
