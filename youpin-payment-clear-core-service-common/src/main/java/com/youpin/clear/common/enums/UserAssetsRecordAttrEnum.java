package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum UserAssetsRecordAttrEnum {
    /**
     * 默认
     */
    None(0),

    /**
     * 余额是否变动
     */
    BalanceIsChange(1),

    /**
     * 求购余额是否变动
     */
    PurchaseBalanceIsChange(2),
    ;


    /**
     * code
     *
     */
    private final Integer code;


    private static final Map<Integer, UserAssetsRecordAttrEnum> MAP = new HashMap<>();

    static {
        for (UserAssetsRecordAttrEnum item : UserAssetsRecordAttrEnum.values()) {
            MAP.put(item.code, item);
        }
    }


    public static UserAssetsRecordAttrEnum getAccountTypeEnum(Integer code) {
        if (code == null || !MAP.containsKey(code)) {
            return null;
        }
        return MAP.get(code);
    }
}