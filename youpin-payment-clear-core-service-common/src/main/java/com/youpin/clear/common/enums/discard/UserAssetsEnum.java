//package com.youpin.clear.common.enums.discard;
//
//import com.youpin.clear.common.enums.AssetsTypeEnum;
//import com.youpin.clear.common.enums.DirectionEnum;
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//
//import java.util.Arrays;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//
//@Getter
//@AllArgsConstructor
//public enum UserAssetsEnum {
//    /**
//     * 充值
//     */
//    TYPE_1("充值", 1, DirectionEnum.RECHARGE, AssetsTypeEnum.DEFAULT),
//    /**
//     * 提现
//     */
//    TYPE_2("提现", 2, DirectionEnum.CASH_WITHDRAWAL, AssetsTypeEnum.DEFAULT),
//
//    TYPE_3("购买", 3, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//    /**
//     * 退款
//     */
//    TYPE_4("退款", 4, DirectionEnum.REFUND, AssetsTypeEnum.REFUND),
//
//    TYPE_5("出售", 5, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    /**
//     * 交易补偿
//     */
//    TYPE_6("交易补偿", 6, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_10("还价支付", 10, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_11("还价退款", 11, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_13("冻结押金", 13, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_14("解冻押金", 14, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_15("支付租金", 15, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_16("收取租金", 16, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_17("购买冻结", 17, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_18("购买解冻", 18, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_24("支付续租资金", 24, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_25("收取续租资金", 25, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_23("提现退款", 23, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_43("求购账户充值", 43, DirectionEnum.RECHARGE, AssetsTypeEnum.DEFAULT),
//    TYPE_44("求购账户提现", 44, DirectionEnum.CASH_WITHDRAWAL, AssetsTypeEnum.DEFAULT),
//    TYPE_96("求购提现退款", 96, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    TYPE_45("求购处罚", 45, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_46("求购补偿", 46, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_47("支付买断资金", 47, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    /**
//     * 收取买断资金
//     */
//    TYPE_48("收取买断资金", 48, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_49("退还买断资金", 49, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    TYPE_54("收取赔付资金", 54, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_55("收取赔偿租金", 55, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_56("支付赔付资金", 56, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_57("支付赔偿资金", 57, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_71("退还守约资金", 71, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_72("收取延期资金", 72, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_73("支付延期资金", 73, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_74("收取逾期资金", 74, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_75("收取额外赔偿", 75, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_76("支付逾期资金", 76, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_77("支付额外赔偿", 77, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_78("退还续租资金", 78, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//
//    TYPE_83("退还金额", 83, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    TYPE_85("支付安心购", 85, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_86("安心购退款", 86, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_88("冻结安心租", 88, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_89("支付安心租", 89, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_91("安心租解冻", 91, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_92("安心租退还", 92, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_94("私密出售补贴", 94, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_97("转出到求购账户余额", 97, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    /**
//     * 钱包转出到求购账户余额
//     */
//    TYPE_98("钱包-转出到求购账户余额", 98, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_99("转出到钱包", 99, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    /**
//     * 求购账户余额-转出到钱包
//     */
//    TYPE_100("求购账户余额-转出到钱包", 100, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_101("安心租赔付", 101, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_104("退还押金保障费", 104, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_105("安心购解冻", 105, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_106("安心购冻结", 106, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_107("租4免1活动补贴", 107, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_109("支付安心涨", 109, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_110("退还安心涨", 110, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_114("支付红锁无忧", 114, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_115("退还红锁无忧", 115, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_118("红锁无忧赔付", 118, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_120("支付期数资金", 120, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_123("退还期数资金", 123, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_124("自动到账服务费", 124, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_126("支付采购服务费", 126, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_128("私密租赁补贴", 128, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_132("支付极速发货", 132, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_133("退还极速发货", 133, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_135("转账充值到账", 135, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_136("收取附加价值赔付", 136, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_138("冻结数码租赁押金", 138, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_139("解冻数码租赁押金", 139, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_140("支付数码租金", 140, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_141("支付数码逾期违约金", 141, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_142("支付数码强制买断", 142, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_143("退还数码租金", 143, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_144("退还数码逾期违约金", 144, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_145("退还数码强制买断", 145, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    TYPE_146("立减金补贴", 146, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_147("退还立减金补贴", 147, DirectionEnum.REFUND, AssetsTypeEnum.REFUND),
//    TYPE_148("支付租赁过户", 148, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_151("退还租赁过户", 151, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_155("返现补贴", 155, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_156("支付手机充值", 156, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_157("退还手机充值", 157, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_159("收取开箱出金", 159, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_160("支付活动资金", 160, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_161("退还活动资金", 161, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_164("退还求购处罚", 164, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_165("支付官箱自开", 165, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_166("收取官箱自开", 166, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_167("支出官箱自开", 167, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_168("退还官箱自开", 168, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//
//    TYPE_170("还价补偿", 170, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_171("退还还价处罚", 171, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_149("收取租赁过户", 149, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_172("收取待结租金", 172, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_173("租赁过户返现", 173, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_175("任务返现补贴", 175, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_177("支付解封服务费", 177, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_180("退还解封服务费", 180, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_182("退还交易服务费", 182, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_183("冻结信用超能力", 183, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_184("解冻信用超能力", 184, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_186("信用超能力退还", 186, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_188("退还租赁服务费", 188, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_190("购买CDK", 190, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_191("CDK退款", 191, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_192("支付赠送服务费", 192, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_193("退还赠送服务费", 193, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_194("支付出租大会员", 194, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_195("退还出租大会员", 195, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_197("冻结省钱会员", 197, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_198("解冻省钱会员", 198, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_199("支付省钱会员", 199, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_200("退还省钱会员", 200, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    TYPE_201("优惠券抵扣", 201, DirectionEnum.INVARIANT, AssetsTypeEnum.DEFAULT),
//    TYPE_202("退还优惠券折扣", 202, DirectionEnum.INVARIANT, AssetsTypeEnum.DEFAULT),
//
//    TYPE_203("冻结赠送服务费", 203, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_204("解冻赠送服务费", 204, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_205("支付自动发货", 205, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_206("收取自动发货", 206, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_208("退还自动发货", 208, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//    TYPE_10003("红锁无忧冻结", 10003, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_10004("红锁无忧解冻", 10004, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_10006("极速发货冻结", 10006, DirectionEnum.CHANNEL_FREEZE, AssetsTypeEnum.DEFAULT),
//    TYPE_10007("极速发货解冻", 10007, DirectionEnum.CHANNEL_UNFREEZE, AssetsTypeEnum.DEFAULT),
//
//    //平台资金
//    TYPE_22("追回资金", 22, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_58("押金额外扣除", 58, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_90("安心租收取", 90, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_111("暂收买断资金", 111, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_113("收取安心涨", 113, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_116("收取红锁无忧", 116, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_125("收取自动到账服务费", 125, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_137("赔饰品差额调拨", 137, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_185("支付信用超能力", 185, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_189("赔饰品追回", 189, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    //平台出金
//    TYPE_21("垫付资金", 21, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_33("支付补贴租金", 33, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_102("支出安心租", 102, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//
//    TYPE_112("处理买断资金", 112, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//
//    TYPE_122("处理期数资金", 122, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_154("支付返现补贴", 154, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_108("108租4免1活动支出", 108, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_117("支出红锁无忧退费", 117, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_119("支出红锁无忧赔付", 119, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_158("支出开箱出金", 158, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_174("支付租赁过户返现", 174, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_176("支付任务返现补贴", 176, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_129("私密租赁补贴支出", 129, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_95("私密出售支出", 95, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_162("支付提现券资金", 162, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//    TYPE_134("转账充值支出", 134, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//
//    TYPE_207("支出自动发货", 207, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_OUT),
//
//
//    TYPE_93("交易处罚", 93, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//
//    //手续费
//    TYPE_181("交易服务费", 181, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_FEE),
//    TYPE_103("支付押金保障费（特殊处理）", 103, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_FEE),
//    TYPE_150("支付租赁过户手续费", 150, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_FEE),
//    TYPE_196("赔付服务费", 196, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_FEE),
//    TYPE_187("租赁服务费", 187, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_FEE),
//    TYPE_81("转租服务费", 81, DirectionEnum.SUBTRACTION, AssetsTypeEnum.PLATFORM_FEE),
//
//    //特殊处理
//    TYPE_169("还价处罚", 169, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_34("退还补贴租金", 34, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_82("平台账户充值", 82, DirectionEnum.RECHARGE, AssetsTypeEnum.DEFAULT),
//
//    TYPE_87("安心购收取", 87, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_121("暂收期数资金", 121, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//    TYPE_127("收取采购服务费", 127, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_163("退还提现券资金", 163, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_209("支付免押增值服务", 209, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_210("退还免押增值服务", 210, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    //后面匹配逻辑
//    TYPE_211("余额补偿入金", 211, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_1001("数据修复减", 1001, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//    TYPE_1000("数据修复加", 1000, DirectionEnum.ADDITION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_1002("司法划拨", 1002, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//
//    TYPE_216("购买服饰", 216, DirectionEnum.SUBTRACTION, AssetsTypeEnum.DEFAULT),
//
//    TYPE_217("服饰退款", 217, DirectionEnum.ADDITION, AssetsTypeEnum.REFUND),
//
//    ;
//
//
//    public static final Map<Integer, UserAssetsEnum> MAP = Arrays.stream(UserAssetsEnum.values())
//            .collect(Collectors.toMap(UserAssetsEnum::getTypeId, item -> item));
//
//    private final String name;
//    /**
//     * 资金类型
//     */
//    private final Integer typeId;
//    /**
//     * 资金方向
//     */
//    private final DirectionEnum directionEnum;
//    /**
//     * 资金类型
//     */
//    private final AssetsTypeEnum assetsTypeEnum;
//
//    public static UserAssetsEnum getByTypeCode(Integer typeCode) {
//        if (typeCode == null || !MAP.containsKey(typeCode)) {
//            return null;
//        }
//        return MAP.get(typeCode);
//    }
//
//
//}
