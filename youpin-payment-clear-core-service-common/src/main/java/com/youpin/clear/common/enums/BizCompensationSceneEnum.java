package com.youpin.clear.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BizCompensationSceneEnum {

    ASSET(1, "资金分账", true),
    ASSET_SPLIT(2, "数据拆解", true),
    SYNC(3, "数据同步", true),
    NET_PLATFORM_USER_RECORD(5, "net缓冲记账", true),

    TRANSACTION_SERVICE_FEE_STATEMENT_TOPIC(14, "交易服务费流水发送", true),
    TRANSACTION_SERVICE_FEE_STATEMENT_GROUP(15, "交易服务费流水消费", true),

    HANDLE_MONEY_ONLY_WITHDRAW_EXCEPTION(16, "仅提现异常", true);




    public static final Map<Integer, BizCompensationSceneEnum> MAP = Arrays.stream(BizCompensationSceneEnum.values()).collect(Collectors.toMap(BizCompensationSceneEnum::getCode, t -> t));
    private final Integer code;
    private final String msg;
    private final Boolean retry;


}
