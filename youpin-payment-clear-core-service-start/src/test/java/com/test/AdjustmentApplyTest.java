package com.test;

import com.uu898.youpin.commons.utils.JacksonUtils;
import com.youpin.clear.Application;
import com.youpin.clear.app.service.AdjustmentApplyService;
import com.youpin.clear.client.request.*;
import com.youpin.clear.client.response.AdjustApplyListResponse;
import com.youpin.clear.client.response.AdjustDetailResponse;
import com.youpin.clear.client.response.AdjustmentApplyConfigResponse;
import com.youpin.clear.common.enums.AccountPayChannelEnum;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class AdjustmentApplyTest {

    @Resource
    private AdjustmentApplyService adjustmentApplyService;

    @Resource
    private UserAccountRecordGateway userAccountRecordGateway;

    /**
     * 提交申请
     */
    @Test
    public void testSubmitApply() {
        AdjustAccountRequest incAccountInfo = new AdjustAccountRequest();
        incAccountInfo.setUserId(1L);
        incAccountInfo.setAssetType(4L);
        incAccountInfo.setAccountPayChannelType(AccountPayChannelEnum.BALANCE.getCode());
        incAccountInfo.setServiceFee(null);

        List<AdjustApplyItemRequest> itemList = new ArrayList<>();
        AdjustApplyItemRequest itemRequest = new AdjustApplyItemRequest();
        itemRequest.setAdjustAmount(new BigDecimal(10.99));
        itemRequest.setAdjustAccountType(AccountTypeEnum.BALANCE_1.getCode());
        itemList.add(itemRequest);
        incAccountInfo.setItemList(itemList);

        AdjustAccountRequest decAccountInfo = new AdjustAccountRequest();
        decAccountInfo.setUserId(2L);
        decAccountInfo.setAssetType(5L);
        decAccountInfo.setAccountPayChannelType(AccountPayChannelEnum.BALANCE.getCode());
        decAccountInfo.setServiceFee(null);

        List<AdjustApplyItemRequest> decItemList = new ArrayList<>();
        AdjustApplyItemRequest decItemRequest = new AdjustApplyItemRequest();
        decItemRequest.setAdjustAmount(new BigDecimal(10.99));
        decItemRequest.setAdjustAccountType(AccountTypeEnum.BALANCE_1.getCode());
        decItemList.add(decItemRequest);
        decAccountInfo.setItemList(decItemList);


        AdjustApplyRequest request = new AdjustApplyRequest();
        request.setRelatedOrderNo("123");
        request.setPayOrderNo("234");
        request.setApplyRemark("调账申请");
        request.setApplyBy("阿三");
        request.setIncAccountInfo(incAccountInfo);
        request.setDecAccountInfo(decAccountInfo);

        Long id = adjustmentApplyService.submitApply(request);
        System.out.println(id);
    }


    /**
     * 查询列表
     */
    @Test
    public void testAdjustApplyList() {
        AdjustApplyListQueryRequest request = new AdjustApplyListQueryRequest();
        request.setPage(1L);
        request.setPageSize(20L);
        request.setStartTime(LocalDateTime.now().minusDays(1L));
        request.setEndTime(LocalDateTime.now().plusDays(1L));
        AdjustApplyListResponse response = adjustmentApplyService.queryList(request);
        System.out.println(JacksonUtils.writeValueAsString(response));
    }


    @Test
    public void testApplyDetailQuery() {
        AdjustDetailResponse adjustDetailResponse = adjustmentApplyService.queryDetail(3L);
        System.out.println(JacksonUtils.writeValueAsString(adjustDetailResponse));
    }


    @Test
    public void testSendSmsCode() {
        ApplySendVerifyCodeRequest request = new ApplySendVerifyCodeRequest();
        request.setApplyIdList(List.of(3L));
        request.setAuditBy("阿三2");
        adjustmentApplyService.sendVerifyCode(request);
    }

    @Test
    public void auditPass() {
        AdjustApplyAuditRequest request = new AdjustApplyAuditRequest();
        request.setAuditType(1);
        request.setApplyId(3L);
        request.setAuditRemark("测试通过审核");
        request.setVerifyCode("311666");
        request.setAuditBy("阿三2");
        adjustmentApplyService.applyAudit(request);
    }


    @Test
    public void auditReject() {
        AdjustApplyAuditRequest request = new AdjustApplyAuditRequest();
        request.setAuditType(2);
        request.setApplyId(3L);
        request.setAuditRemark("测试通过审核");
        request.setVerifyCode("311666");
        request.setAuditBy("阿三2");
        adjustmentApplyService.applyAudit(request);

    }


    @Test
    public void testQueryApplyTypeConfig() {
        AdjustmentApplyTypeConfigQueryRequest request = new AdjustmentApplyTypeConfigQueryRequest();
        request.setOperateType(0);
        AdjustmentApplyConfigResponse adjustmentApplyConfigResponse = adjustmentApplyService.queryApplyTypeConfig();
        System.out.println(JacksonUtils.writeValueAsString(adjustmentApplyConfigResponse));

        request.setOperateType(1);
        adjustmentApplyConfigResponse = adjustmentApplyService.queryApplyTypeConfig();
        System.out.println(JacksonUtils.writeValueAsString(adjustmentApplyConfigResponse));

        request.setOperateType(2);
        adjustmentApplyConfigResponse = adjustmentApplyService.queryApplyTypeConfig();
        System.out.println(JacksonUtils.writeValueAsString(adjustmentApplyConfigResponse));

        request.setOperateType(3);
        adjustmentApplyConfigResponse = adjustmentApplyService.queryApplyTypeConfig();
        System.out.println(JacksonUtils.writeValueAsString(adjustmentApplyConfigResponse));

        request.setOperateType(4);
        adjustmentApplyConfigResponse = adjustmentApplyService.queryApplyTypeConfig();
        System.out.println(JacksonUtils.writeValueAsString(adjustmentApplyConfigResponse));
    }


    @Test
    public void adjustmentHandle() {
        adjustmentApplyService.adjustmentHandle();
    }




}
