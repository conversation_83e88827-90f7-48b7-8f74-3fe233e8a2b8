package com.youpin.clear.financial;


import com.youpin.clear.Application;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.financial.part.AssetsPartServiceTest;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 赠送资金测试
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class SellGiveTest {


    @Autowired
    AssetsPartServiceTest assetsPartServiceTest;


    /**
     * 清理数据
     */
    public void deleteByTest(Long userId, String orderNo, String payOrderNo) {
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_3);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_4);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_192);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_5);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_83);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_181);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_193);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_209);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_210);


    }

    /**
     * 场景1
     * 余额-支付,退款
     */
    @Test
    public void test_01() {
        Long buyerUserId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        BigDecimal money221 = assetsPartServiceTest.getRandomMoneyByInt(5, null);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        //余额支付
        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_192.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, assetInfoList_pay);

        //退款
        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_193.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_93.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_refund.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, NetStatusEnum.SUCCESS, assetInfoList_refund);
    }

    /**
     * 场景1
     * 渠道-支付,退款失败  余额补偿
     */
    @Test
    public void test_011() {
        Long buyerUserId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        BigDecimal money221 = assetsPartServiceTest.getRandomMoneyByInt(5, null);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        //余额支付
        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        //支付赠送服务费
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_192.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        //支出支付立减补贴
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, assetInfoList_pay);

        //退款
        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        // 退款
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        // 退还赠送服务费
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_193.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        // 交易处罚
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_93.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        // 退还支付立减补贴
        assetInfoList_refund.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        // 余额补偿入金
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_211.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //渠道退款失败
        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, NetStatusEnum.FAIL, assetInfoList_refund);
    }


    @Test
    public void test_012() {
        Long buyerUserId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        BigDecimal money221 = assetsPartServiceTest.getRandomMoneyByInt(5, null);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        //余额支付
        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //支付赠送服务费
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_192.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //支出支付立减补贴
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, assetInfoList_pay);

        //退款
        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        // 退款
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        // 退还赠送服务费
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_193.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        // 交易处罚
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_93.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        // 退还支付立减补贴
        assetInfoList_refund.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        // 余额补偿入金
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_211.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //渠道退款失败
        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, NetStatusEnum.FAIL, assetInfoList_refund);
    }

    /**
     * 场景2
     * 支付 结算
     */
    @Test
    public void test_02() {
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_SELL_GIVE);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        //余额支付
        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_192.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, assetInfoList_pay);

        List<ClearAssetInfoRequest> assetInfoList_settlement = new ArrayList<>();
        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_5.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 4, UserAssetsTypeEnum.TYPE_181.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_SELL_GIVE, assetInfoList_settlement);
    }


    /**
     * 免押中心
     */
    @Test
    public void test_03() {
        Long buyerUserId = 174024L;
        //卖家用户ID
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.DEPOSIT_FREE_CENTER);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.DEPOSIT_FREE_CENTER);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        //余额支付
        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_209.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.DEPOSIT_FREE_CENTER, assetInfoList_pay);


        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_210.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.DEPOSIT_FREE_CENTER, NetStatusEnum.SUCCESS, assetInfoList_refund);

        assetsPartServiceTest.doubleRefundCheck(tm -> assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.DEPOSIT_FREE_CENTER, NetStatusEnum.SUCCESS, assetInfoList_refund));


    }


}
