package com.youpin.clear.financial.part;

import com.youpin.clear.app.converter.FinancialTransactionConvertor;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.client.request.financial.PayFinancialRequest;
import com.youpin.clear.client.request.financial.RefundFinancialRequest;
import com.youpin.clear.client.request.financial.SettlementFinancialRequest;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.factory.SubBusFinancialProcessorFactory;
import com.youpin.clear.domain.gateway.UU898UserAssetsInfoGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.infrastructure.config.AssetsTypePlatformUserIdProperties;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

@Service
@Slf4j
public class AssetsPartServiceTest {


    @Autowired
    SubBusFinancialProcessorFactory subBusFinancialProcessorFactory;

    @Autowired
    UU898UserAssetsRecordGateway uu898UserAssetsRecordGateway;

    @Autowired
    UU898UserAssetsInfoGateway uu898UserAssetsInfoGateway;

    @Autowired
    AssetsTypePlatformUserIdProperties assetsTypePlatformUserIdProperties;


    /**
     * 多次退款判断
     */
    public void doubleRefundCheck(Consumer<Void> consumer) {
        log.info("多次退款判断");
        try {
            consumer.accept(null);
        } catch (PaymentClearBusinessException e) {
            log.error("多次退款: 业务异常. error,{}", ExceptionUtils.getStackTrace(e));
            if (Objects.equals(e.getCode(), ErrorCode.REFUND_AMOUNT_GREATER_THAN_EXPEND_AMOUNT.getCode())) {
                log.info("多次退款: 符合预期判断 :退款金额大于支出金额");
            } else {
                log.error("多次退款:  错误: error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("多次退款:  未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }
    }


    /**
     * 支付
     */
    public void payChannelTestPay(Long userId, String orderNo, String payOrderNo, String serialNo, DoNetPayChannelEnum payChannel, BigDecimal money, SubBusTypeFrontEnum subBusType, UserAssetsTypeEnum userAssetsTypeEnum) {

        UserAssetsInfoDTO userAssetsInfo_start = null, userAssetsInfo_pay = null, userAssetsInfo_refund = null;

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }
        PayFinancialRequest payFinancialRequest = new PayFinancialRequest();
        payFinancialRequest.setMerchantId(10001);
        payFinancialRequest.setBusinessType(1);
        payFinancialRequest.setCollectType(2);

        payFinancialRequest.setSubBusType(subBusType.getCode());
        payFinancialRequest.setSerialNo(serialNo);
        payFinancialRequest.setOrderNo(orderNo);
        payFinancialRequest.setPayOrderNo(payOrderNo);
        payFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        payFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), userAssetsTypeEnum.getTypeId(), payChannel.getCode(),null)));

        FinancialResponse financialResponse = pay(payFinancialRequest);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-支付成功", subBusType.getName());
        }

        //重复性判断
        checkPayAlreadyProcessed(payFinancialRequest);

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_pay = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
            if (userAssetsInfo_start.getMoney().subtract(money).compareTo(userAssetsInfo_pay.getMoney()) != 0) {
                log.error("{}-余额-支付失败", subBusType.getName());
                throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "余额-支付校验失败");
            }
        }
    }


    /**
     * 支付
     */
    public void payTest(String orderNo, String payOrderNo, String serialNo, SubBusTypeFrontEnum subBusType, List<ClearAssetInfoRequest> assetInfoList) {


        PayFinancialRequest payFinancialRequest = new PayFinancialRequest();
        payFinancialRequest.setMerchantId(10001);
        payFinancialRequest.setBusinessType(1);
        payFinancialRequest.setCollectType(2);

        payFinancialRequest.setSubBusType(subBusType.getCode());
        payFinancialRequest.setSerialNo(serialNo);
        payFinancialRequest.setOrderNo(orderNo);
        payFinancialRequest.setPayOrderNo(payOrderNo);
        payFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        payFinancialRequest.setAssetInfoList(assetInfoList);
        FinancialResponse financialResponse = pay(payFinancialRequest);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-支付成功", subBusType.getName());
        }
//        //重复性判断
//        checkPayAlreadyProcessed(payFinancialRequest);
//

    }

    /**
     * 退款
     */
    public void refundTest(String orderNo, String payOrderNo, String serialNo, SubBusTypeFrontEnum subBusType, NetStatusEnum netStatusEnum, List<ClearAssetInfoRequest> assetInfoList) {


        // 退款
        RefundFinancialRequest refund_processing = new RefundFinancialRequest();
        refund_processing.setMerchantId(10001);
        refund_processing.setBusinessType(1);
        refund_processing.setCollectType(2);
        refund_processing.setSubBusType(subBusType.getCode());
        refund_processing.setSerialNo(serialNo);
        refund_processing.setOrderNo(orderNo);
        refund_processing.setPayOrderNo(payOrderNo);

        refund_processing.setStatus(netStatusEnum.getCode());

        refund_processing.setAssetInfoList(assetInfoList);


        FinancialResponse financialResponse_refund_success = refund(refund_processing);
        if (Objects.equals(financialResponse_refund_success.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-退款成功", subBusType.getName());
        }


    }

    /**
     * 退款
     */
    public void payChannelTestRefund(Long userId, String orderNo, String payOrderNo, String serialNo, DoNetPayChannelEnum payChannel, BigDecimal money, SubBusTypeFrontEnum subBusType, UserAssetsTypeEnum userAssetsTypeEnum,
                                     Boolean isBalance211Refund) {

        UserAssetsInfoDTO userAssetsInfo_start = null, userAssetsInfo_refund = null;

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }

        // 退款
        RefundFinancialRequest refund_processing = new RefundFinancialRequest();
        refund_processing.setMerchantId(10001);
        refund_processing.setBusinessType(1);
        refund_processing.setCollectType(2);
        refund_processing.setSubBusType(subBusType.getCode());
        refund_processing.setSerialNo(serialNo);
        refund_processing.setOrderNo(orderNo);
        refund_processing.setPayOrderNo(payOrderNo);
        refund_processing.setStatus(NetStatusEnum.SUCCESS.getCode());

        if (Boolean.TRUE.equals(isBalance211Refund)) {
            refund_processing.setStatus(NetStatusEnum.FAIL.getCode());
            refund_processing.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), userAssetsTypeEnum.getTypeId(), payChannel.getCode(),null),
                    new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_211.getTypeId(), payChannel.getCode(),null)));
        } else {
            refund_processing.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), userAssetsTypeEnum.getTypeId(), payChannel.getCode(),null)));
        }


        FinancialResponse financialResponse_refund_success = refund(refund_processing);
        if (Objects.equals(financialResponse_refund_success.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-退款成功", subBusType.getName());
        }
        checkRefundAlreadyProcessed(refund_processing);


        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_refund = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            if (userAssetsInfo_start.getMoney().add(money).compareTo(userAssetsInfo_refund.getMoney()) != 0) {
                log.error("{}-余额-退款失败", subBusType.getName());
                throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "余额-退款校验失败");
            }
        }

    }





    /**
     * 结算
     */
    public void settlementTest(String orderNo, String payOrderNo, String serialNo, SubBusTypeFrontEnum subBusType, List<ClearAssetInfoRequest> assetInfoList) {

        SettlementFinancialRequest request = new SettlementFinancialRequest();
        request.setMerchantId(10001);
        request.setBusinessType(1);
        request.setCollectType(2);

        request.setSubBusType(subBusType.getCode());
        request.setSerialNo(serialNo);
        request.setOrderNo(orderNo);
        request.setPayOrderNo(payOrderNo);
        request.setStatus(NetStatusEnum.SUCCESS.getCode());
        request.setAssetInfoList(assetInfoList);

        FinancialResponse financialResponse = settlement(request);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-结算成功", subBusType.getName());
        }
    }


    public void specialSettlementTest(String orderNo, String payOrderNo, String serialNo, SubBusTypeFrontEnum subBusType, List<ClearAssetInfoRequest> assetInfoList) {

        SettlementFinancialRequest request = new SettlementFinancialRequest();
        request.setMerchantId(10001);
        request.setBusinessType(1);
        request.setCollectType(2);

        request.setSubBusType(subBusType.getCode());
        request.setSerialNo(serialNo);
        request.setOrderNo(orderNo);
        request.setPayOrderNo(payOrderNo);
        request.setStatus(NetStatusEnum.SUCCESS.getCode());
        request.setAssetInfoList(assetInfoList);

        FinancialResponse financialResponse = specialSettlement(request);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-结算成功", subBusType.getName());
        }
    }


    private FinancialResponse pay(PayFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTO(request, FinancialTypeEnum.Pay);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    private FinancialResponse refund(RefundFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOByRefund(request, FinancialTypeEnum.Refund);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    private FinancialResponse settlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Settlement);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    private FinancialResponse specialSettlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Special_Settlement);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    public int deleteByTest(Long userId, String orderNo, String payOrderNo, String serialNo, UserAssetsTypeEnum userAssetsTypeEnum) {
        Integer deleteSum = uu898UserAssetsRecordGateway.deleteByTest(userId, serialNo, orderNo, payOrderNo, userAssetsTypeEnum.getTypeId());
        log.info("  清理 {}:{} userId:{} deleteSum:{}", userId, userAssetsTypeEnum.getTypeId(), userAssetsTypeEnum.getName(), deleteSum);
        return deleteSum;
    }

    private void checkRefundAlreadyProcessed(RefundFinancialRequest refundProcessing) {

        try {
            refund(refundProcessing);
        } catch (PaymentClearBusinessException e) {
            if (Objects.equals(e.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode())) {
                log.info("重复操作 退款 符合预期判断");
            } else {
                log.error("重复操作. 退款 error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("重复操作退款 未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }
    }

    private void checkPayAlreadyProcessed(PayFinancialRequest payFinancialRequest) {
        try {
            pay(payFinancialRequest);
        } catch (PaymentClearBusinessException e) {
            if (Objects.equals(e.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode())) {
                log.info("重复性校验: 支付 符合预期判断 :重复操作");
            } else {
                log.error("重复性校验: 支付 支付错误: error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("重复性校验: 支付 未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }
    }

    public String getSerialNo() {
        //根据日期生成流水号
        try {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + RandomUtils.nextInt(100, 999);
        } catch (Exception e) {
            log.error("获取流水号失败: error,{}", ExceptionUtils.getStackTrace(e));
        }
        return "";
    }

    public static final String ORDER_NO = "0088";
    public static final String PAY_ORDER_NO = "0077";

    public String getOrderNo(SubBusTypeFrontEnum subBusTypeFrontEnum) {
        return String.format("%s%s%d%d", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")),
                ORDER_NO,
                subBusTypeFrontEnum.getCode(),
                RandomUtils.nextInt(1, 1000000));
    }

    public String getPayOrderNo(SubBusTypeFrontEnum subBusTypeFrontEnum) {
        return String.format("%s%s%d%d", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")),
                PAY_ORDER_NO,
                subBusTypeFrontEnum.getCode(),
                RandomUtils.nextInt(1, 1000000));
    }

    public BigDecimal getRandomMoneyByInt(Integer maxMoney, BigDecimal defMoney) {

        return getRandomMoney(BigDecimal.valueOf(maxMoney), defMoney);
    }

    public BigDecimal getRandomMoney(BigDecimal maxMoney, BigDecimal defMoney) {
        if (defMoney != null && defMoney.compareTo(BigDecimal.ZERO) >= 0) {
            return defMoney;
        }
        return new BigDecimal(RandomUtils.nextInt(1, maxMoney.intValue()));
    }
}
