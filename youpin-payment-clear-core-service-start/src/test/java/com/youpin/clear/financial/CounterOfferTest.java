package com.youpin.clear.financial;


import com.youpin.clear.Application;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.financial.part.AssetsPartServiceTest;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 还价资金测试
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class CounterOfferTest {


    @Autowired
    AssetsPartServiceTest assetsPartServiceTest;


    /**
     * 清理数据
     */
    public void deleteByTest(Long userId, String orderNo, String payOrderNo) {
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_3);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_4);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_211);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_5);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_83);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_181);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_221);
    }


    /**
     * 场景1
     * 余额-支付,退款
     */
    @Test
    public void test_01() {
        Long userId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        //余额支付
        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_10);
        //退款
        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_11, false);

    }

    /**
     * 场景2
     * 余额-支付,超额退款
     */
    @Test
    public void test_02() {
        Long userId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        //余额支付
        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_10);
        //退款
        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money.subtract(BigDecimal.ONE), SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_11, false);

        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_11, false);

    }


    /**
     * 场景3
     * 渠道支付,退款
     */
    @Test
    public void test_03() {
        Long userId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        //余额支付
        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_10);
        //退款
        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_11, false);
    }

    /**
     * 场景4
     * 支付  退款+还价处罚
     * TYPE_170("还价补偿", 170, FundingDirectionEnum.ADDITION),
     * TYPE_169("还价处罚", 169, FundingDirectionEnum.SUBTRACTION),
     * TYPE_171("退还还价处罚", 171, FundingDirectionEnum.ADDITION),
     */
    @Test
    public void test_04() {
        Long userId = 174024L;
        Long sellerUserId = 174025L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.COUNTER_OFFER);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        //余额支付

        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COUNTER_OFFER, UserAssetsTypeEnum.TYPE_10);

        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        assetInfoList_refund.add(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_refund.add(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_169.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.COUNTER_OFFER, NetStatusEnum.SUCCESS, assetInfoList_refund);

        List<ClearAssetInfoRequest> assetInfoList_specialSettlement = new ArrayList<>();
        assetInfoList_specialSettlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_170.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.COUNTER_OFFER, assetInfoList_specialSettlement);

        assetInfoList_specialSettlement.clear();
        assetInfoList_specialSettlement.add(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_171.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //正常退款
        //assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.COUNTER_OFFER, assetInfoList_specialSettlement);

        //超出退款
        assetInfoList_specialSettlement.clear();
        assetInfoList_specialSettlement.add(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_171.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.COUNTER_OFFER, assetInfoList_specialSettlement);

        //重复判断
        //assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.COUNTER_OFFER, assetInfoList_specialSettlement);

    }


}
