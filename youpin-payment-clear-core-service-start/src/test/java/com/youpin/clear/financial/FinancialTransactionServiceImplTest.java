package com.youpin.clear.financial;

import com.youpin.clear.Application;
import com.youpin.clear.app.converter.FinancialTransactionConvertor;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.client.request.financial.PayFinancialRequest;
import com.youpin.clear.client.request.financial.RefundFinancialRequest;
import com.youpin.clear.client.request.financial.SettlementFinancialRequest;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.factory.SubBusFinancialProcessorFactory;
import com.youpin.clear.domain.gateway.UU898UserAssetsInfoGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.infrastructure.config.AssetsTypePlatformUserIdProperties;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class FinancialTransactionServiceImplTest {


    @Autowired
    SubBusFinancialProcessorFactory subBusFinancialProcessorFactory;

    @Autowired
    UU898UserAssetsRecordGateway uu898UserAssetsRecordGateway;

    @Autowired
    UU898UserAssetsInfoGateway uu898UserAssetsInfoGateway;

    @Autowired
    AssetsTypePlatformUserIdProperties assetsTypePlatformUserIdProperties;


    private FinancialResponse pay(PayFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTO(request, FinancialTypeEnum.Pay);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    private FinancialResponse refund(RefundFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOByRefund(request, FinancialTypeEnum.Refund);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    private FinancialResponse settlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Settlement);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    private FinancialResponse specialSettlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Special_Settlement);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }


    /**
     * 退服务费
     */
    private void refundServiceFeeTest(Long userId, String orderNo, String payOrderNo, String serialNo, BigDecimal money, SubBusTypeFrontEnum subBusTypeFrontEnum) {

        RefundFinancialRequest refundFinancialRequest = new RefundFinancialRequest();
        refundFinancialRequest.setMerchantId(10001);
        refundFinancialRequest.setCollectType(2);
        refundFinancialRequest.setSubBusType(subBusTypeFrontEnum.getCode());
        refundFinancialRequest.setSerialNo(serialNo);
        refundFinancialRequest.setOrderNo(orderNo);
        refundFinancialRequest.setPayOrderNo(payOrderNo);
        refundFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        refundFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), 182, DoNetPayChannelEnum.Balance.getCode(), null)));

        FinancialResponse financialResponse_refund = refund(refundFinancialRequest);
        if (Objects.equals(financialResponse_refund.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("服务费退款成功");
        }
    }


    private void privateSettlementTest(Long payUserId, Long settlementUserId, String orderNo, String payOrderNo, String serialNo, BigDecimal money, BigDecimal serviceFee) {

        SettlementFinancialRequest settlementFinancialRequest = new SettlementFinancialRequest();
        settlementFinancialRequest.setMerchantId(10001);
        settlementFinancialRequest.setSubBusType(SubBusTypeFrontEnum.PRIVATE_SALE.getCode());
        settlementFinancialRequest.setSerialNo(serialNo);
        settlementFinancialRequest.setOrderNo(orderNo);
        settlementFinancialRequest.setPayOrderNo(payOrderNo);
        settlementFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());

        if (serviceFee.compareTo(BigDecimal.ZERO) > 0) {
            settlementFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money), 6, DoNetPayChannelEnum.Balance.getCode(), null), new ClearAssetInfoRequest(settlementUserId, AmountUtils.convertToCent(money), 8, DoNetPayChannelEnum.Balance.getCode(), null), new ClearAssetInfoRequest(settlementUserId, AmountUtils.convertToCent(serviceFee), 181, DoNetPayChannelEnum.Balance.getCode(), null)));
        } else {
            settlementFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money), 6, DoNetPayChannelEnum.Balance.getCode(), null), new ClearAssetInfoRequest(settlementUserId, AmountUtils.convertToCent(money), 8, DoNetPayChannelEnum.Balance.getCode(), null)));
        }

        FinancialResponse financialResponse_settlement = settlement(settlementFinancialRequest);
        if (Objects.equals(financialResponse_settlement.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("私密结算成功");
        }
    }


    private void privateRefundTest(Long payUserId, Long settlementUserId, String orderNo, String payOrderNo, String serialNo, BigDecimal money) {

        RefundFinancialRequest refundFinancialRequest = new RefundFinancialRequest();
        refundFinancialRequest.setMerchantId(10001);
        refundFinancialRequest.setSubBusType(SubBusTypeFrontEnum.PRIVATE_SALE.getCode());
        refundFinancialRequest.setCollectType(2);
        refundFinancialRequest.setSerialNo(serialNo);
        refundFinancialRequest.setOrderNo(orderNo);
        refundFinancialRequest.setPayOrderNo(payOrderNo);
        refundFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        refundFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money), 12, DoNetPayChannelEnum.Balance.getCode(), null)));


        FinancialResponse financialResponse_refund = refund(refundFinancialRequest);
        if (Objects.equals(financialResponse_refund.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("私密退款成功");
        }
    }

    private void privatePayTest(Long payUserId, Long settlementUserId, String orderNo, String payOrderNo, String serialNo, BigDecimal money) {

        PayFinancialRequest payFinancialRequest = new PayFinancialRequest();
        payFinancialRequest.setMerchantId(10001);
        payFinancialRequest.setSubBusType(SubBusTypeFrontEnum.PRIVATE_SALE.getCode());
        payFinancialRequest.setSerialNo(serialNo);
        payFinancialRequest.setOrderNo(orderNo);
        payFinancialRequest.setPayOrderNo(payOrderNo);
        payFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        payFinancialRequest.setCollectType(2);
        payFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money), 6, DoNetPayChannelEnum.Balance.getCode(), null)));

        FinancialResponse financialResponse = pay(payFinancialRequest);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("私密支付成功");
        }
    }

    private void privateSubsidyTest(Long subsidyUserId, String orderNo, String payOrderNo, String serialNo, BigDecimal money) {

        SettlementFinancialRequest settlementFinancialRequest = new SettlementFinancialRequest();
        settlementFinancialRequest.setMerchantId(10001);
        settlementFinancialRequest.setSubBusType(SubBusTypeFrontEnum.PRIVATE_SALE.getCode());
        settlementFinancialRequest.setSerialNo(serialNo);
        settlementFinancialRequest.setOrderNo(orderNo);
        settlementFinancialRequest.setPayOrderNo(payOrderNo);
        settlementFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        settlementFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(subsidyUserId, AmountUtils.convertToCent(money), 1, DoNetPayChannelEnum.Balance.getCode(), null)));

        FinancialResponse financialResponse = specialSettlement(settlementFinancialRequest);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("私密支付成功");
        }
    }


    /**
     * 私密测试-支付 退款
     */
    @Test
    public void privatePayAndRefundTest() {

        UserAssetsInfoDTO userAssetsInfo_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(174061L);

        Long payUserId = 174061L;
        String orderNo = "20250218110299117256";
        String payOrderNo = "202502181767546679";
        BigDecimal money = new BigDecimal("1.1");


        privateRefundTest(payUserId, null, orderNo, payOrderNo, "2025021819218553061", new BigDecimal("0.1"));

        //privatePayTest(payUserId, null, orderNo, payOrderNo, "2025021819218553060", money);

//        privateRefundTest(payUserId, null, orderNo, payOrderNo, "2025021819218553061", new BigDecimal("0.1"));

//        privateRefundTest(payUserId, null, orderNo, payOrderNo, "2025021819218553061", new BigDecimal("0.1"));





//        privateRefundTest(payUserId, null, orderNo, payOrderNo, "2025021819218553061", money);


//        UserAssetsInfoDTO userAssetsInfo_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(payUserId);
//
//        if (userAssetsInfo_start.getMoney().compareTo(userAssetsInfo_end.getMoney()) == 0) {
//            log.info("私密支付退款数据-资金-测试无误 {} {}", userAssetsInfo_start.getMoney(), userAssetsInfo_end.getMoney());
//        } else {
//            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付退款数据-资金-测试异常");
//        }
//
//        List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOS = uu898UserAssetsRecordGateway.queryUserAssetsRecordDTOList(payUserId, null, orderNo, payOrderNo, null, null);
//        if (null != uu898UserAssetsRecordDTOS && uu898UserAssetsRecordDTOS.size() == 2 && uu898UserAssetsRecordDTOS.stream().allMatch(e -> Objects.equals(e.getStatus(), NetStatusEnum.SUCCESS.getCode(),null))) {
//            log.info("私密支付退款数据-资金明细-测试无误");
//        } else {
//            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付退款数据-资金明细-测试异常");
//        }
//
//        Integer deletePaySum = uu898UserAssetsRecordGateway.deleteByTest(payUserId, "2025021819218553060", orderNo, payOrderNo, null);
//        Integer deleteRefundSum = uu898UserAssetsRecordGateway.deleteByTest(payUserId, "2025021819218553061", orderNo, payOrderNo, null);
//
//        log.info("私密支付退款数据 清理 deletePaySum:{},deleteRefundSum:{} ", deletePaySum, deleteRefundSum);
//
//        if (deletePaySum != 1 || deleteRefundSum != 1) {
//            log.error("私密支付退款数据 清理失败");
//            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付退款数据 清理失败");
//        }
    }

    /**
     * 私密测试-支付 结算(含服务费) 补贴
     */
    @Test
    public void privatePayAndSettlementTest() {
        Long payUserId = 174061L;
        //结算用户
        Long settlementUserId = 174024L;
        String orderNo = "20250218110299117256";
        String payOrderNo = "202502181767546679";
        //支付金额
        BigDecimal money = new BigDecimal("1.2");
        //服务费
        BigDecimal serviceFee = new BigDecimal("0.01");


        UserAssetsInfoDTO userAssetsInfo_pay_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(payUserId);
        UserAssetsInfoDTO userAssetsInfo_settlement_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(settlementUserId);

        privatePayTest(payUserId, null, orderNo, payOrderNo, "2025021819218553060", money);
        privateSettlementTest(payUserId, settlementUserId, orderNo, payOrderNo, "2025021819218553062", money, serviceFee);

        BigDecimal subsidyMoney = new BigDecimal("0.21");
        privateSubsidyTest(payUserId, orderNo, payOrderNo, "2025021819218553063", subsidyMoney);


        UserAssetsInfoDTO userAssetsInfo_pay_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(payUserId);
        UserAssetsInfoDTO userAssetsInfo_settlement_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(settlementUserId);

        if (userAssetsInfo_pay_start.getMoney().subtract(money).add(subsidyMoney).compareTo(userAssetsInfo_pay_end.getMoney()) == 0) {
            log.info("私密支付结算数据-资金-测试无误 {} - {} + {} = {}", userAssetsInfo_pay_start.getMoney(), money, subsidyMoney, userAssetsInfo_pay_end.getMoney());
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付结算数据-资金-测试异常");
        }

        if (userAssetsInfo_settlement_start.getMoney().add(money).subtract(serviceFee).compareTo(userAssetsInfo_settlement_end.getMoney()) == 0) {
            log.info("私密支付结算数据-资金-测试无误 {} + {} - {} = {}", userAssetsInfo_settlement_start.getMoney(), money, serviceFee, userAssetsInfo_settlement_end.getMoney());
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付结算数据-资金-测试异常");
        }

        //退服务费
        refundServiceFeeTest(settlementUserId, orderNo, "2025021819218553064", "2025021819218553064", serviceFee, SubBusTypeFrontEnum.PRIVATE_SALE);
        userAssetsInfo_settlement_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(settlementUserId);

        if (userAssetsInfo_settlement_start.getMoney().add(money).compareTo(userAssetsInfo_settlement_end.getMoney()) == 0) {
            log.info("私密支付结算数据-资金-测试无误 {} + {} + {} = {}", userAssetsInfo_settlement_start.getMoney(), money, serviceFee, userAssetsInfo_settlement_end.getMoney());
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付结算数据-资金-测试异常");
        }

        Integer deletePaySum = uu898UserAssetsRecordGateway.deleteByTest(payUserId, "2025021819218553060", orderNo, payOrderNo, null);
        Integer deleteSettlementSum = uu898UserAssetsRecordGateway.deleteByTest(settlementUserId, "2025021819218553062", orderNo, payOrderNo, null);
        Integer deleteSubsidySum = uu898UserAssetsRecordGateway.deleteByTest(null, "2025021819218553063", orderNo, payOrderNo, null);
        Integer deleteRefundServiceFeeSum = uu898UserAssetsRecordGateway.deleteByTest(settlementUserId, "2025021819218553064", orderNo, "2025021819218553064", UserAssetsTypeEnum.TYPE_182.getTypeId());

        log.info("私密支付结算数据 清理 deletePaySum:{},deleteSettlementSum:{}  deleteSubsidySum:{} deleteRefundServiceFeeSum:{}", deletePaySum, deleteSettlementSum, deleteSubsidySum, deleteRefundServiceFeeSum);

        if (deletePaySum != 1 || deleteSettlementSum > 2 || deleteSettlementSum <= 0 || deleteSubsidySum > 2 || deleteSubsidySum <= 0 || deleteRefundServiceFeeSum != 1) {
            log.info("私密支付结算补贴数据 清理失败");
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "私密支付结算补贴数据 清理失败");
        }

    }






    /**
     * 守约充值,充值 ,退款中 退款成功
     */
    @Test
    public void keepRechargeTest() {

        Long userId = 174061L;
        String orderNo = "600000000003385016";
        String payOrderNo = "202504081006781240";
        Integer deleteSum = uu898UserAssetsRecordGateway.deleteByTest(userId, null, orderNo, payOrderNo, null);

        //分
        BigDecimal money = new BigDecimal(8);

        PayFinancialRequest recharge = new PayFinancialRequest();
        recharge.setMerchantId(10001);
        recharge.setBusinessType(3);
        recharge.setSubBusType(SubBusTypeFrontEnum.KEEP_RECHARGE.getCode());
        recharge.setSerialNo("202504081001012377");
        recharge.setOrderNo(orderNo);
        recharge.setPayOrderNo(payOrderNo);
        recharge.setStatus(NetStatusEnum.SUCCESS.getCode());
        recharge.setCollectType(2);
        recharge.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, money.longValue(), UserAssetsTypeEnum.TYPE_1.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(), null)));


        //查询用户资金
        UserAssetsInfoDTO userAssetsInfo_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);

        FinancialResponse financialResponse = pay(recharge);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("守约充值成功");
        }

        RefundFinancialRequest refund_processing = new RefundFinancialRequest();
        refund_processing.setMerchantId(10001);
        refund_processing.setCollectType(2);
        refund_processing.setBusinessType(3);
        refund_processing.setSubBusType(SubBusTypeFrontEnum.KEEP_RECHARGE.getCode());
        refund_processing.setSerialNo("202504081001012416");
        refund_processing.setOrderNo(orderNo);
        refund_processing.setPayOrderNo(payOrderNo);
        refund_processing.setStatus(NetStatusEnum.PROCESSING.getCode());
        //充值退款金额  必须是负数  要符合 uu898 老逻辑
        refund_processing.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, money.negate().longValue(), UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(), null)));

        FinancialResponse financialResponse_refund = refund(refund_processing);
        if (Objects.equals(financialResponse_refund.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("守约充值退款进行中");
        }

//        refund_processing.setStatus(NetStatusEnum.SUCCESS.getCode(),null);
//        FinancialResponse financialResponse_refund_success = refund(refund_processing);
//        if (Objects.equals(financialResponse_refund_success.getCode(),null, ClearConstants.CONSTANT_INTEGER_0)) {
//            log.info("守约充值退款成功");
//        }

        UserAssetsInfoDTO userAssetsInfo_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);

        if (userAssetsInfo_start.getMoney().compareTo(userAssetsInfo_end.getMoney()) == 0) {
            log.info("守约充值并退款-资金-测试无误 {} {}", userAssetsInfo_start.getMoney(), userAssetsInfo_end.getMoney());
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "守约充值并退款-资金-测试异常");
        }

        List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOS = uu898UserAssetsRecordGateway.queryUserAssetsRecordDTOList(userId, null, orderNo, payOrderNo, null, null);
        if (null != uu898UserAssetsRecordDTOS && uu898UserAssetsRecordDTOS.size() == 2 && uu898UserAssetsRecordDTOS.stream().allMatch(e -> Objects.equals(e.getStatus(), NetStatusEnum.SUCCESS.getCode()))) {
            log.info("守约充值并退款-资金明细-测试无误");
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "守约充值并退款-资金明细-测试异常");
        }


//         deleteSum = uu898UserAssetsRecordGateway.deleteByTest(userId, null, orderNo, payOrderNo, null);
//        log.info("守约充值退款数据 清理 deleteSum:{}", deleteSum);
//
//        if (deleteSum != 2) {
//            log.info("守约充值退款数据 清理失败");
//            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "守约充值退款数据 清理失败");
//        }
    }

    /**
     * 租赁守约
     */
    @Test
    public void keepPromiseTest() {

        Long userId = 174061L;
        //平台出金的账户
        Long platformUserId = assetsTypePlatformUserIdProperties.getConfigMap().get(Long.valueOf(UserAssetsTypeEnum.TYPE_58.getTypeId()));
        log.info("平台出金的账户:{}", platformUserId);
        String orderNo = "306317519208017920";
        String serialNo = "202504081001012479";
        BigDecimal money = new BigDecimal("1");

        PayFinancialRequest payFinancialRequest = new PayFinancialRequest();
        payFinancialRequest.setMerchantId(10001);
        payFinancialRequest.setSubBusType(SubBusTypeFrontEnum.KEEP_PROMISE.getCode());
        payFinancialRequest.setSerialNo(serialNo);
        payFinancialRequest.setOrderNo(orderNo);
        payFinancialRequest.setPayOrderNo(null);
        payFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        payFinancialRequest.setCollectType(2);
        payFinancialRequest.setAssetInfoList(List.of(
                //支付赔付资金
                new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_56.getTypeId(), DoNetPayChannelEnum.Balance.getCode(), null),
                //押金额外扣除
                new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_58.getTypeId(), DoNetPayChannelEnum.Balance.getCode(), null)));

        UserAssetsInfoDTO userAssetsInfo_56_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        UserAssetsInfoDTO userAssetsInfo_58_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(platformUserId);


        FinancialResponse financialResponse = pay(payFinancialRequest);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("租赁守约成功");
        }

        try {
            pay(payFinancialRequest);
        } catch (PaymentClearBusinessException e) {
            if (Objects.equals(e.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode())) {
                log.info("租赁守约-重复支付 符合预期判断");
            } else {
                log.error("业务异常. error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }

        UserAssetsInfoDTO userAssetsInfo_56_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        UserAssetsInfoDTO userAssetsInfo_58_end = uu898UserAssetsInfoGateway.getUserAssetsInfo(platformUserId);

        if (userAssetsInfo_56_start.getMoney().subtract(money).compareTo(userAssetsInfo_56_end.getMoney()) == 0) {
            log.info("租赁守约-资金-用户-测试无误 {} {}", userAssetsInfo_56_start.getMoney(), userAssetsInfo_56_end.getMoney());
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "租赁守约-资金-平台户-测试异常");
        }

        if (userAssetsInfo_58_start.getMoney().add(money).compareTo(userAssetsInfo_58_end.getMoney()) == 0) {
            log.info("租赁守约-资金-平台户-测试无误 {} {}", userAssetsInfo_58_start.getMoney(), userAssetsInfo_58_end.getMoney());
        } else {
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "租赁守约-资金-平台户-测试异常");
        }

        Integer deleteSum = uu898UserAssetsRecordGateway.deleteByTest(userId, serialNo, orderNo, null, UserAssetsTypeEnum.TYPE_56.getTypeId());
        log.info("租赁守约数据 清理 56 userId:{} deleteSum:{}", userId, deleteSum);
        if (deleteSum != 1) {
            log.error("租赁守约数据 清理 56 userId:{} deleteSum:{}", userId, deleteSum);
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "租赁守约数据 清理 56 userId:{} deleteSum:{}");
        }
        //平台户 数据删除
        Integer deleteSum_58 = uu898UserAssetsRecordGateway.deleteByTest(platformUserId, serialNo, orderNo, null, UserAssetsTypeEnum.TYPE_58.getTypeId());
        log.info("租赁守约数据 清理 平台户数据删除 58 deleteSum:{}", deleteSum);

        if (deleteSum_58 != 1) {
            log.error("租赁守约数据 平台户数据删除 清理失败");
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "租赁守约数据 平台户数据删除 清理失败");
        }
    }


    /**
     * 极速发货
     */
    @Test
    public void expressFastTest() {

    }


    /**
     * 省钱会员
     */
    @Test
    public void costSavingMemberTest() {
        Long userId = 174024L;
        String orderNo = "2025040810000245679";
        String payOrderNo = "202504081006781306";
        //元
        BigDecimal money = new BigDecimal(8);

//        //场景1 余额 - 支付 - 退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012533", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012534", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        //清理数据
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012533", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012534", UserAssetsTypeEnum.TYPE_200);
//
//        //场景2 支付宝 - 支付 - 退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012535", DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012536", DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012535", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012536", UserAssetsTypeEnum.TYPE_200);
//
//        //场景3 京东 - 支付 - 退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012537", DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012538", DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012537", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012538", UserAssetsTypeEnum.TYPE_200);
//
//        //场景4 抖音 - 支付 - 退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012539", DoNetPayChannelEnum.DyPay, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012540", DoNetPayChannelEnum.DyPay, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012539", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012540", UserAssetsTypeEnum.TYPE_200);
//
//        //场景5 渠道支付-余额退款 支付宝支付 余额退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012541", DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012542", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012541", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012542", UserAssetsTypeEnum.TYPE_200);
//
//        //场景6 渠道支付-余额退款 京东支付 余额退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012543", DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012544", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012543", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012544", UserAssetsTypeEnum.TYPE_200);
//
//
//        //场景6 渠道支付-余额退款 抖音支付 余额退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012545", DoNetPayChannelEnum.DyPay, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012546", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012545", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012546", UserAssetsTypeEnum.TYPE_200);
//
//        //场景7 余额支付 ,部分退款
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012547", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012548", DoNetPayChannelEnum.Balance, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012549", DoNetPayChannelEnum.Balance, money.subtract(money.divide(new BigDecimal(2), 2, RoundingMode.DOWN)), SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012547", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012548", UserAssetsTypeEnum.TYPE_200);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012549", UserAssetsTypeEnum.TYPE_200);
//
//
//        //场景8 余额支付 超退
//        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012547", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012548", DoNetPayChannelEnum.Balance, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012549", DoNetPayChannelEnum.Balance, money.subtract(money.divide(new BigDecimal(2), 2, RoundingMode.DOWN)), SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//
//        try {
//            payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012550", DoNetPayChannelEnum.Balance, money.subtract(money.divide(new BigDecimal(2), 2, RoundingMode.DOWN)), SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200,Boolean.FALSE);
//        } catch (PaymentClearBusinessException e) {
//            if (Objects.equals(e.getCode(),null, ErrorCode.INCOME_REFUND_AMOUNT_GREATER_THAN_EXPEND_AMOUNT.getCode(),null)) {
//                log.info("收入/退款金额大于支出金额  符合预期判断");
//            } else {
//                log.error("收入/退款金额大于支出金额.  error,{}", ExceptionUtils.getStackTrace(e));
//            }
//        } catch (Exception e) {
//            log.error("收入/退款金额大于支出金额. 未知异常. error,{}", ExceptionUtils.getStackTrace(e));
//        }
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012547", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012548", UserAssetsTypeEnum.TYPE_200);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012549", UserAssetsTypeEnum.TYPE_200);

        //场景9 渠道支付,退款失败 增加211 余额补偿入金
        payChannelTestPay(userId, orderNo, payOrderNo, "202504081001012551", DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_199);
        //余额补偿入金
        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012552", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200, Boolean.TRUE);
        //余额退款
        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012553", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200, Boolean.FALSE);
        //余额渠道退款
        payChannelTestRefund(userId, orderNo, payOrderNo, "202504081001012554", DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.COST_SAVING_MEMBER, UserAssetsTypeEnum.TYPE_200, Boolean.FALSE);

//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012547", UserAssetsTypeEnum.TYPE_199);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012552", UserAssetsTypeEnum.TYPE_200);
//        deleteByTest(userId, orderNo, payOrderNo, "202504081001012552", UserAssetsTypeEnum.TYPE_211);


    }


    /**
     * 出租大会员
     */
    @Test
    public void sellBigVip() {


    }

    /**
     * 极速发货
     */
    @Test
    public void frontFastShip() {

    }


    /**
     * 支付
     */
    public void payChannelTestPay(Long userId, String orderNo, String payOrderNo, String serialNo, DoNetPayChannelEnum payChannel, BigDecimal money, SubBusTypeFrontEnum subBusType, UserAssetsTypeEnum userAssetsTypeEnum) {

        UserAssetsInfoDTO userAssetsInfo_start = null, userAssetsInfo_pay = null, userAssetsInfo_refund = null;

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }
        PayFinancialRequest payFinancialRequest = new PayFinancialRequest();
        payFinancialRequest.setMerchantId(10001);
        payFinancialRequest.setBusinessType(1);
        payFinancialRequest.setCollectType(2);

        payFinancialRequest.setSubBusType(subBusType.getCode());
        payFinancialRequest.setSerialNo(serialNo);
        payFinancialRequest.setOrderNo(orderNo);
        payFinancialRequest.setPayOrderNo(payOrderNo);
        payFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        payFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), userAssetsTypeEnum.getTypeId(), payChannel.getCode(), null)));

        FinancialResponse financialResponse = pay(payFinancialRequest);
        if (Objects.equals(financialResponse.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-支付成功", subBusType.getName());
        }

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_pay = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }

        //重复性判断
        checkPayAlreadyProcessed(payFinancialRequest);

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            if (userAssetsInfo_start.getMoney().subtract(money).compareTo(userAssetsInfo_pay.getMoney()) != 0) {
                log.error("{}-余额-支付失败", subBusType.getName());
                throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "余额-支付校验失败");
            }
        }

    }

    /**
     * 退款
     */
    private void payChannelTestRefund(Long userId, String orderNo, String payOrderNo, String serialNo, DoNetPayChannelEnum payChannel, BigDecimal money, SubBusTypeFrontEnum subBusType, UserAssetsTypeEnum userAssetsTypeEnum, Boolean isBalanceRefund) {

        UserAssetsInfoDTO userAssetsInfo_start = null, userAssetsInfo_refund = null;

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_start = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }

        // 退款
        RefundFinancialRequest refund_processing = new RefundFinancialRequest();
        refund_processing.setMerchantId(10001);
        refund_processing.setBusinessType(1);
        refund_processing.setCollectType(2);
        refund_processing.setSubBusType(subBusType.getCode());
        refund_processing.setSerialNo(serialNo);
        refund_processing.setOrderNo(orderNo);
        refund_processing.setPayOrderNo(payOrderNo);


        if (isBalanceRefund) {
            refund_processing.setStatus(NetStatusEnum.FAIL.getCode());
            refund_processing.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), userAssetsTypeEnum.getTypeId(), payChannel.getCode(), null), new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_211.getTypeId(), payChannel.getCode(), null)));
        } else {
            refund_processing.setStatus(NetStatusEnum.SUCCESS.getCode());
            refund_processing.setAssetInfoList(List.of(new ClearAssetInfoRequest(userId, AmountUtils.convertToCent(money), userAssetsTypeEnum.getTypeId(), payChannel.getCode(), null)));
        }


        FinancialResponse financialResponse_refund_success = refund(refund_processing);
        if (Objects.equals(financialResponse_refund_success.getCode(), ClearConstants.CONSTANT_INTEGER_0)) {
            log.info("{}-退款成功", subBusType.getName());
        }
        checkRefundAlreadyProcessed(refund_processing);


        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            userAssetsInfo_refund = uu898UserAssetsInfoGateway.getUserAssetsInfo(userId);
        }

        if (payChannel.equals(DoNetPayChannelEnum.Balance)) {
            if (userAssetsInfo_start.getMoney().add(money).compareTo(userAssetsInfo_refund.getMoney()) != 0) {
                log.error("{}-余额-退款失败", subBusType.getName());
                throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "余额-退款校验失败");
            }
        }

    }

    private void checkRefundAlreadyProcessed(RefundFinancialRequest refundProcessing) {

        try {
            refund(refundProcessing);
        } catch (PaymentClearBusinessException e) {
            if (Objects.equals(e.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode())) {
                log.info("重复操作 退款 符合预期判断");
            } else {
                log.error("重复操作. 退款 error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("重复操作退款 未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }
    }

    private void checkPayAlreadyProcessed(PayFinancialRequest payFinancialRequest) {
        try {
            pay(payFinancialRequest);
        } catch (PaymentClearBusinessException e) {
            if (Objects.equals(e.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode())) {
                log.info("重复操作 支付 符合预期判断");
            } else {
                log.error("重复操作. 支付 error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("重复操作 支付 未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }
    }

    private void deleteByTest(Long userId, String orderNo, String payOrderNo, String serialNo, UserAssetsTypeEnum userAssetsTypeEnum) {
        Integer deleteSum = uu898UserAssetsRecordGateway.deleteByTest(userId, serialNo, orderNo, payOrderNo, userAssetsTypeEnum.getTypeId());
        log.info("  清理 {}:{} userId:{} deleteSum:{}", userId, userAssetsTypeEnum.getTypeId(), userAssetsTypeEnum.getName(), deleteSum);
        if (deleteSum != 1) {
            log.error("  清理 199 userId:{} deleteSum:{}", userId, deleteSum);
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "省钱会员 支付 清理 199 userId:{} deleteSum:{}");
        }
    }


}