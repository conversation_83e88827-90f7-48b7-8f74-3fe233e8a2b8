package com.youpin.clear.financial;


import com.youpin.clear.Application;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.financial.part.AssetsPartServiceTest;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 出售资金测试
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class SellTest {


    @Autowired
    AssetsPartServiceTest assetsPartServiceTest;


    /**
     * 清理数据
     */
    public void deleteByTest(Long userId, String orderNo, String payOrderNo) {
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_3);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_4);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_211);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_5);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_83);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_181);
        assetsPartServiceTest.deleteByTest(userId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_221);
    }




    /**
     * 场景1
     * 余额-支付,退款
     */
    @Test
    public void test_01() {
        Long userId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        //余额支付
        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);
        //退款
        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_4,  false);

    }


    /**
     * 场景2
     * 渠道支付,多次退款
     */
    @Test
    public void test_02() {
        Long userId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.DyPay, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);
        //退款
        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.DyPay, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_4,  false);

        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.DyPay, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_4,  false);


    }


    /**
     * 场景3
     * 渠道支付,渠道退款失败,退回余额 211 余额入金补偿
     */
    @Test
    public void test_03() {
        Long userId = 174024L;
        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(userId, orderNo, payOrderNo);
        assetsPartServiceTest.payChannelTestPay(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.DyPay, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);
        //退款
        assetsPartServiceTest.payChannelTestRefund(userId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.DyPay, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_4,  true);

    }


    /**
     * 场景4
     * 老版 - 余额支付,结算
     * -- [
     * AssetInfoRequest(userId=10608794, money=24587, typeId=8, payChannel=0, isChannelSuccess=true),  //出售8--> 5
     * AssetInfoRequest(userId=959779, money=13, typeId=9, payChannel=0, isChannelSuccess=true),      //退还买家资金处理9--> 83
     * AssetInfoRequest(userId=10608794, money=246, typeId=18, payChannel=0, isChannelSuccess=true), // 交易服务费18--> 181
     * AssetInfoRequest(userId=959779, money=24600, typeId=6, payChannel=11, isChannelSuccess=true)  //支出6--> 3
     * ]
     */
    @Test
    public void test_04() {

        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);

        assetsPartServiceTest.payChannelTestPay(buyerUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);
        //结算
        List<ClearAssetInfoRequest> assetInfoList = new ArrayList<>();
        //6-3
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), SellOrderSettleTypeEnum.SELL_ORDER_BUY_PAY.getCode(), DoNetPayChannelEnum.Balance.getCode(),null));
        //8-5
        assetInfoList.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 2, SellOrderSettleTypeEnum.SELL_ORDER_RECEIVE.getCode(), DoNetPayChannelEnum.Balance.getCode(),null));
        //9-83
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 3, SellOrderSettleTypeEnum.SELL_ORDER_GIVEBACK.getCode(), DoNetPayChannelEnum.Balance.getCode(),null));
        //18 -181
        assetInfoList.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 3, SellOrderSettleTypeEnum.SELL_COMMISSION.getCode(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList);

    }

    /**
     * 场景5
     * 余额支付,结算
     * 新版
     */
    @Test
    public void test_05() {
        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);

        assetsPartServiceTest.payChannelTestPay(buyerUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);
        //结算
        List<ClearAssetInfoRequest> assetInfoList = new ArrayList<>();
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_5.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_83.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_181.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList);

        assetInfoList = assetInfoList.stream().filter(clearAssetInfoRequest -> Objects.equals(clearAssetInfoRequest.getTypeId(), UserAssetsTypeEnum.TYPE_83.getTypeId())).collect(Collectors.toList());
        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList);


    }

    /**
     * 场景6
     * 余额支付 退款  违约金
     * [
     * {"isChannelSuccess":true,"money":4200,"payChannel":2,"typeId":12,"userId":10375512}, 12 -->4
     * {"isChannelSuccess":true,"money":210,"payChannel":2,"typeId":214,"userId":10375512},
     * {"isChannelSuccess":true,"money":168,"payChannel":2,"typeId":215,"userId":8681633},
     * {"isChannelSuccess":true,"money":42,"payChannel":2,"typeId":218,"userId":3231589}]
     */
    @Test
    public void test_06() {

        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        //平台户可以不传

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);

        assetsPartServiceTest.payChannelTestPay(buyerUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);


        List<ClearAssetInfoRequest> assetInfoList = new ArrayList<>();
        //12---4
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), SellOrderSettleTypeEnum.SELL_ORDER_REFUND.getCode(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_214.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_215.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money) / 4, UserAssetsTypeEnum.TYPE_218.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, NetStatusEnum.SUCCESS,assetInfoList);
    }

    /**
     * 场景7
     * 渠道支付, 退款, 违约金
     */
    @Test
    public void test_07() {

        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        //平台户可以不传

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);

        assetsPartServiceTest.payChannelTestPay(buyerUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, UserAssetsTypeEnum.TYPE_3);


        List<ClearAssetInfoRequest> assetInfoList = new ArrayList<>();
        //12---4
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), SellOrderSettleTypeEnum.SELL_ORDER_REFUND.getCode(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_214.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_215.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money) / 4, UserAssetsTypeEnum.TYPE_218.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));

        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, NetStatusEnum.SUCCESS,assetInfoList);
    }

    /**
     * 场景8
     * 支付 补贴  退款
     * <p>
     * {"isChannelSuccess":true,"money":285,"payChannel":2,"typeId":6,"userId":3415002},
     * {"isChannelSuccess":true,"money":2,"payChannel":2,"typeId":221,"userId":0}
     */
    @Test
    public void test_08() {

        //买家用户ID
        Long buyerUserId = 174024L;
        //平台户可以不传
        Long platformUserId = 4316621L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        BigDecimal money221 = assetsPartServiceTest.getRandomMoneyByInt(5, null);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(platformUserId, orderNo, payOrderNo);


        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_pay);


        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, NetStatusEnum.SUCCESS,assetInfoList_refund);

        assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_refund);

    }


//

    /**
     * 场景9
     * 支付 补贴  结算
     * [
     * AssetInfoRequest(userId=7560553, money=243, typeId=8, payChannel=0, isChannelSuccess=true),
     * AssetInfoRequest(userId=7560553, money=2, typeId=18, payChannel=0, isChannelSuccess=true),
     * AssetInfoRequest(userId=0, money=2, typeId=221, payChannel=0, isChannelSuccess=true),
     * AssetInfoRequest(userId=3415002, money=243, typeId=6, payChannel=2, isChannelSuccess=true)]
     */
    @Test
    public void test_09() {
        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        //平台户可以不传
        Long platformUserId = 4316621L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = assetsPartServiceTest.getRandomMoneyByInt(10, null);
        BigDecimal money221 = assetsPartServiceTest.getRandomMoneyByInt(5, null);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);
        deleteByTest(platformUserId, orderNo, payOrderNo);


        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_pay);


        List<ClearAssetInfoRequest> assetInfoList_settlement = new ArrayList<>();
        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) + AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_5.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 4, UserAssetsTypeEnum.TYPE_181.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_settlement);


    }

    /**
     * 场景10
     * 支付 补贴  结算 并退补贴
     */
    @Test
    public void test_10() {
        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        //平台户可以不传
        Long platformUserId = 4316621L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = new BigDecimal(10);
        BigDecimal money221 = new BigDecimal(4);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);
        deleteByTest(platformUserId, orderNo, payOrderNo);


        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_pay);


        List<ClearAssetInfoRequest> assetInfoList_settlement = new ArrayList<>();
        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(BigDecimal.valueOf(1)), UserAssetsTypeEnum.TYPE_83.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetInfoList_settlement.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(BigDecimal.valueOf(1)), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) + AmountUtils.convertToCent(BigDecimal.valueOf(2)), UserAssetsTypeEnum.TYPE_5.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 10, UserAssetsTypeEnum.TYPE_181.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));


        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_settlement);


    }


    /**
     * 场景11
     * 支付 结算  补贴
     */
    @Test
    public void test_11() {
        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        //平台户可以不传
        Long platformUserId = 4316621L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = new BigDecimal(10);
        BigDecimal money221 = new BigDecimal(4);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);
        deleteByTest(platformUserId, orderNo, payOrderNo);


        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Alipay.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_pay);


        List<ClearAssetInfoRequest> assetInfoList_settlement = new ArrayList<>();
        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(BigDecimal.valueOf(1)), UserAssetsTypeEnum.TYPE_83.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(BigDecimal.valueOf(1)), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) + AmountUtils.convertToCent(BigDecimal.valueOf(2)), UserAssetsTypeEnum.TYPE_5.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_settlement.add(new ClearAssetInfoRequest(sellerUserId, AmountUtils.convertToCent(money) / 10, UserAssetsTypeEnum.TYPE_181.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_settlement);


        List<ClearAssetInfoRequest> assetInfoList_settlement_special = new ArrayList<>();
        assetInfoList_settlement_special.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money)/10, UserAssetsTypeEnum.TYPE_155.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_settlement_special);


        assetsPartServiceTest.specialSettlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_settlement_special);

    }

    @Test
    public void test_12() {
        //买家用户ID
        Long buyerUserId = 174024L;
        //卖家用户ID
        Long sellerUserId = 174025L;
        //平台户可以不传
        Long platformUserId = 4316621L;

        String orderNo = assetsPartServiceTest.getOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        String payOrderNo = assetsPartServiceTest.getPayOrderNo(SubBusTypeFrontEnum.FRONT_NORMAL_GOOD);
        //元
        BigDecimal money = new BigDecimal(10);
        BigDecimal money221 = new BigDecimal(4);

        //清理数据
        deleteByTest(buyerUserId, orderNo, payOrderNo);
        deleteByTest(sellerUserId, orderNo, payOrderNo);
        deleteByTest(platformUserId, orderNo, payOrderNo);


        List<ClearAssetInfoRequest> assetInfoList_pay = new ArrayList<>();
        assetInfoList_pay.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_3.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_pay.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_221.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.payTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, assetInfoList_pay);

        List<ClearAssetInfoRequest> assetInfoList_refund = new ArrayList<>();
        assetInfoList_refund.add(new ClearAssetInfoRequest(buyerUserId, AmountUtils.convertToCent(money)+1, UserAssetsTypeEnum.TYPE_4.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetInfoList_refund.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money221), UserAssetsTypeEnum.TYPE_222.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetsPartServiceTest.refundTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.FRONT_NORMAL_GOOD, NetStatusEnum.SUCCESS,assetInfoList_refund);


    }



}
