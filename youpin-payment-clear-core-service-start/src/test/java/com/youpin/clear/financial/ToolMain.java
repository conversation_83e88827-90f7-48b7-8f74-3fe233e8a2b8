package com.youpin.clear.financial;

import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class ToolMain {


    public static void main(String[] args) {
        List<Integer> list = new ArrayList<>();
        UserAssetsTypeEnum.MAP.forEach((k, v) -> {
            if (v.getDirectionEnum().equals(FundingDirectionEnum.SUBTRACTION)||v.getDirectionEnum().equals(FundingDirectionEnum.RECHARGE)) {
                list.add(v.getTypeId());
            }
        });

        list.sort(Comparator.comparingInt(o -> o));
        list.forEach(v -> System.out.println(",UserAssetsTypeEnum.TYPE_" + v + ".getTypeId()"));


    }
}
