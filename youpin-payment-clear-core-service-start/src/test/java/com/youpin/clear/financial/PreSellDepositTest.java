package com.youpin.clear.financial;


import com.youpin.clear.Application;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.financial.part.AssetsPartServiceTest;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 预售保证金测试
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
@Slf4j
public class PreSellDepositTest {


    @Autowired
    AssetsPartServiceTest assetsPartServiceTest;

    /**
     * 预售保证金测试-场景1
     * 余额-支付,退款
     */
    @Test
    public void preSellDepositTestOne() {
        Long payUserId = 174024L;
        String orderNo = "2025040810000245679";
        String payOrderNo = "202504081006781306";
        //元
        BigDecimal money = new BigDecimal(8);

        int deletePaySum_212 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_212);
        log.info("212:删除的支付记录数：{}", deletePaySum_212);
        int deletePaySum_213 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_213);
        log.info("213:删除的支付记录数：{}", deletePaySum_213);
        //冻结预售保证金
        //余额支付
        assetsPartServiceTest.payChannelTestPay(payUserId, orderNo, payOrderNo, "202504081001012551", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_212);
        //退款
        assetsPartServiceTest.payChannelTestRefund(payUserId, orderNo, payOrderNo, "202504081001012552", DoNetPayChannelEnum.Balance, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_213, false);

    }

    /**
     * 预售保证金测试-场景2
     * 京东-支付,退款
     */
    @Test
    public void preSellDepositTestTwo() {
        Long payUserId = 174024L;
        String orderNo = "2025009008801";
        String payOrderNo = "2025009007701";
        //元
        BigDecimal money = new BigDecimal(RandomUtils.nextInt(1, 10));

        int deletePaySum_212 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_212);
        int deletePaySum_219 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_219);
        //解冻预售保证金
        int deletePaySum_213 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_213);
        //退还预售保证金
        int deletePaySum_220 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_220);

        //支付
        assetsPartServiceTest.payChannelTestPay(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_212);
        //退款
        assetsPartServiceTest.payChannelTestRefund(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_213, false);

    }


    /**
     * 预售保证金测试-场景2
     * 京东-支付,多次退款
     */
    @Test
    public void preSellDepositTestTwo_Refund() {
        Long payUserId = 174024L;
        String orderNo = "2025009008801";
        String payOrderNo = "2025009007701";
        //元
        BigDecimal money = new BigDecimal(RandomUtils.nextInt(1, 10));

        int deletePaySum_212 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_212);
        int deletePaySum_219 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_219);
        //解冻预售保证金
        int deletePaySum_213 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_213);
        //退还预售保证金
        int deletePaySum_220 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_220);

        //支付
        assetsPartServiceTest.payChannelTestPay(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.JD, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_212);
        //退款
        assetsPartServiceTest.payChannelTestRefund(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.JD, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_213, false);
        //退款
        assetsPartServiceTest.payChannelTestRefund(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.JD, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_213, false);
        //退款
        try {
            assetsPartServiceTest.payChannelTestRefund(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.JD, money.divide(new BigDecimal(2), 2, RoundingMode.DOWN), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_213, false);
        } catch (PaymentClearBusinessException e) {
            log.error("多次退款: 业务异常. error,{}", ExceptionUtils.getStackTrace(e));
            if (Objects.equals(e.getCode(), ErrorCode.REFUND_AMOUNT_GREATER_THAN_EXPEND_AMOUNT.getCode())) {
                log.info("多次退款: 符合预期判断 :退款金额大于支出金额");
            } else {
                log.error("多次退款:  错误: error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("多次退款:  未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }

    }


    /**
     * 预售保证金测试-场景3
     * 余额  支付 结算 多次结算
     */
    @Test
    public void preSellDepositTestThree() {
        Long payUserId = 174024L;
        String orderNo = "2025009008802";
        String payOrderNo = "2025009007702";
        //元
        BigDecimal money = new BigDecimal(RandomUtils.nextInt(1, 10));

        int deletePaySum_212 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_212);
        int deletePaySum_213 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_213);
        int deletePaySum_214 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_214);
        int deletePaySum_215 = assetsPartServiceTest.deleteByTest(174025L, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_215);
        int deletePaySum_218 = assetsPartServiceTest.deleteByTest(3231589L, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_218);
        int deletePaySum_219 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_219);

        //支付
        assetsPartServiceTest.payChannelTestPay(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_212);
        //结算
        List<ClearAssetInfoRequest> assetInfoList = new ArrayList<>();
        //扣除违约金
        assetInfoList.add(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_214.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //补贴违约金
        assetInfoList.add(new ClearAssetInfoRequest(174025L, AmountUtils.convertToCent(money) / 2 / 2, UserAssetsTypeEnum.TYPE_215.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //预售违约手续费
        assetInfoList.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money) / 2 / 2, UserAssetsTypeEnum.TYPE_218.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, assetInfoList);

        //二次结算
        log.info("二次结算");
        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, assetInfoList);


    }


    /**
     * 预售保证金测试-场景4
     * 余额  支付 结算 超出结算
     */
    @Test
    public void preSellDepositTestFour() {
        Long payUserId = 174024L;
        String orderNo = "2025009008802";
        String payOrderNo = "2025009007702";
        //元
        BigDecimal money = new BigDecimal(RandomUtils.nextInt(1, 10));

        int deletePaySum_212 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_212);
        int deletePaySum_213 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_213);
        int deletePaySum_214 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_214);
        int deletePaySum_215 = assetsPartServiceTest.deleteByTest(174025L, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_215);
        int deletePaySum_218 = assetsPartServiceTest.deleteByTest(3231589L, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_218);
        int deletePaySum_219 = assetsPartServiceTest.deleteByTest(payUserId, orderNo, payOrderNo, null, UserAssetsTypeEnum.TYPE_219);

        //支付
        assetsPartServiceTest.payChannelTestPay(payUserId, orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), DoNetPayChannelEnum.Alipay, money, SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, UserAssetsTypeEnum.TYPE_212);
        //结算
        List<ClearAssetInfoRequest> assetInfoList = new ArrayList<>();
        //扣除违约金
        assetInfoList.add(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_214.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //补贴违约金
        assetInfoList.add(new ClearAssetInfoRequest(174025L, AmountUtils.convertToCent(money) / 2, UserAssetsTypeEnum.TYPE_215.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //预售违约手续费
        assetInfoList.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_218.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, assetInfoList);

        assetInfoList = new ArrayList<>();
        //扣除违约金
        assetInfoList.add(new ClearAssetInfoRequest(payUserId, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_214.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));
        //补贴违约金
        Assert.assertTrue(assetInfoList.add(new ClearAssetInfoRequest(174025L, AmountUtils.convertToCent(money), UserAssetsTypeEnum.TYPE_215.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null)));
        //预售违约手续费
        assetInfoList.add(new ClearAssetInfoRequest(null, AmountUtils.convertToCent(money) / 3, UserAssetsTypeEnum.TYPE_218.getTypeId(), DoNetPayChannelEnum.Balance.getCode(),null));

        log.info("二次结算");
        try {
            assetsPartServiceTest.settlementTest(orderNo, payOrderNo, assetsPartServiceTest.getSerialNo(), SubBusTypeFrontEnum.PRE_SALE_DEPOSIT, assetInfoList);
        } catch (PaymentClearBusinessException e) {
            log.error("二次结算: 业务异常. error,{}", ExceptionUtils.getStackTrace(e));
            if (Objects.equals(e.getCode(), ErrorCode.SETTLEMENT_AMOUNT_GREATER_THAN_PAY_AMOUNT.getCode())) {
                log.info("二次结算: 符合预期判断 :结算金额大于支付金额");
            } else {
                log.error("二次结算:  错误: error,{}", ExceptionUtils.getStackTrace(e));
            }
        } catch (Exception e) {
            log.error("二次结算:  未知异常. error,{}", ExceptionUtils.getStackTrace(e));
        }

    }

}
