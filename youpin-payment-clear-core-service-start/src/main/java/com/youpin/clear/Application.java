package com.youpin.clear;

import com.uu898.youpin.commons.Logger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableFeignClients(value = {
        "com.youpin.leaf",
        "com.youpin.payment.gateway",
        "com.youpin.clear.infrastructure.feign",
        "com.youpin.trade",
        "com.youpin.asset"
}
)
@SpringBootApplication(scanBasePackages = {
        "com.youpin",
        "com.uu898.youpin"
})
public class Application {

    public static void main(String[] args) {

        SpringApplication.run(Application.class, args);
        Logger.info("{}", "启动完成");
    }
}
