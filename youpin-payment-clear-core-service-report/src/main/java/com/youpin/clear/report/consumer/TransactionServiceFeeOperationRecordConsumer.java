//package com.youpin.clear.report.consumer;
//
//import com.alibaba.fastjson.JSON;
//import com.youpin.clear.common.constant.MQConfig;
//import com.youpin.clear.common.enums.BizCompensationSceneEnum;
//import com.youpin.clear.common.enums.ErrorCode;
//import com.youpin.clear.domain.exception.PaymentClearBusinessException;
//import com.youpin.clear.domain.gateway.CompensationRecordGateway;
//import com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord;
//import com.youpin.clear.app.service.TransactionServiceFeeStatementRecordService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.exception.ExceptionUtils;
//import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
//import youpin.commons.rocketmq.core.RocketMQListener;
//
///**
// * 交易服务费流水-顺序消费-用户分组
// */
//@Slf4j
//@Component
//@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ, topic = MQConfig.TRANSACTION_SERVICE_FEE_STATEMENT_TOPIC, expression = MQConfig.TRANSACTION_SERVICE_FEE_STATEMENT_TAG, consumerGroup = MQConfig.TRANSACTION_SERVICE_FEE_STATEMENT_GROUP, filterExpressionType = FilterExpressionType.TAG)
//public class TransactionServiceFeeOperationRecordConsumer implements RocketMQListener<String> {
//
//    @Autowired
//    CompensationRecordGateway compensationRecordGateway;
//
//    @Autowired
//    TransactionServiceFeeStatementRecordService transactionServiceFeeStatementRecordService;
//
//    @Override
//    public void onMessage(String msg) {
//        if (StringUtils.isEmpty(msg)) {
//            log.error("[交易服务费流水] 监听消息:接收到了空消息");
//            return;
//        }
//        log.info("[交易服务费流水] 收到消息:{}", msg);
//        TransactionServiceFeeStatementRecord record = JSON.parseObject(msg, TransactionServiceFeeStatementRecord.class);
//        try {
//            transactionServiceFeeStatementRecordService.transactionServiceFeeStatement(record);
//        } catch (Exception e) {
//            log.error("[交易服务费流水] 错误 保存补偿表错误 Error:{}", ExceptionUtils.getStackTrace(e));
//            saveCompensationRecord(record);
//        }
//    }
//
//    /**
//     * 保存异常记录
//     */
//    private void saveCompensationRecord(TransactionServiceFeeStatementRecord dto) {
//        String uniqueKey = String.format("TSFS_GROUP_%s_%s", dto.getUserId(), dto.getUserAssetsRecordId());
//        try {
//            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(dto), BizCompensationSceneEnum.TRANSACTION_SERVICE_FEE_STATEMENT_GROUP);
//        } catch (Exception e) {
//            log.error("[交易服务费流水] {} 错误 保存补偿表错误 Error:{}", uniqueKey, ExceptionUtils.getStackTrace(e));
//            throw new PaymentClearBusinessException(ErrorCode.COMPENSATION_SAVE_FAIL);
//        }
//        log.info("[交易服务费流水] 保存补偿表 完成:{}", uniqueKey);
//
//
//    }
//}