package com.youpin.clear.application.example;

import com.youpin.clear.adapter.job.CalcTradeMoneyUtils;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 退款明细计算示例
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundDetailExample {
    
    private static final Integer TRANSFER_IN_JOURNAL_TYPE = 97;
    private static final Integer TRANSFER_OUT_JOURNAL_TYPE = 99;
    
    /**
     * 演示退款明细计算
     */
    public void demonstrateRefundDetails() {
        log.info("=== 退款明细计算演示 ===");
        
        // 示例1：第一次转出3元的退款明细
        demonstrateFirstTransferOut();
        
        // 示例2：第二次转出5元的退款明细
        demonstrateSecondTransferOut();
        
        // 示例3：第三次转出3元的退款明细
        demonstrateThirdTransferOut();
        
        log.info("=== 演示完成 ===");
    }
    
    /**
     * 第一次转出：退款明细应该是从转入订单10、9、8各退1元
     */
    private void demonstrateFirstTransferOut() {
        log.info("\n--- 第一次转出3元的退款明细 ---");
        
        // 历史流水：只有转入记录，没有转出记录
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1")
        );
        
        CalcTradeMoneyUtils.RefundResult result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, new BigDecimal("3"));
        
        log.info("总退款金额：{} 元", result.getTotalRefundAmount());
        log.info("退款明细：");
        for (CalcTradeMoneyUtils.RefundDetail detail : result.getRefundDetails()) {
            log.info("  从 {} 退款 {} 元", detail.getTransferInOrderNo(), detail.getRefundAmount());
        }
        
        // 预期：从转入订单10退1元，转入订单9退1元，转入订单8退1元
        log.info("预期：从转入订单10退1元，转入订单9退1元，转入订单8退1元");
    }
    
    /**
     * 第二次转出：已有历史转出3元，本次转出5元
     */
    private void demonstrateSecondTransferOut() {
        log.info("\n--- 第二次转出5元的退款明细 ---");
        
        // 历史流水：包含第一次转出记录
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1")
        );
        
        CalcTradeMoneyUtils.RefundResult result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, new BigDecimal("5"));
        
        log.info("总退款金额：{} 元", result.getTotalRefundAmount());
        log.info("退款明细：");
        for (CalcTradeMoneyUtils.RefundDetail detail : result.getRefundDetails()) {
            log.info("  从 {} 退款 {} 元", detail.getTransferInOrderNo(), detail.getRefundAmount());
        }
        
        // 预期：从转入订单11退1元，转入订单8退2元，转入订单7退1元，转入订单6退1元
        log.info("预期：从转入订单11退1元，转入订单8退2元，转入订单7退1元，转入订单6退1元");
    }
    
    /**
     * 第三次转出：已有历史转出8元，本次转出3元
     */
    private void demonstrateThirdTransferOut() {
        log.info("\n--- 第三次转出3元的退款明细 ---");
        
        // 历史流水：包含前两次转出记录
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-5"),
            createRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), TRANSFER_IN_JOURNAL_TYPE, "3")
        );
        
        CalcTradeMoneyUtils.RefundResult result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, new BigDecimal("3"));
        
        log.info("总退款金额：{} 元", result.getTotalRefundAmount());
        log.info("退款明细：");
        for (CalcTradeMoneyUtils.RefundDetail detail : result.getRefundDetails()) {
            log.info("  从 {} 退款 {} 元", detail.getTransferInOrderNo(), detail.getRefundAmount());
        }
        
        // 预期：从转入订单12退3元
        log.info("预期：从转入订单12退3元");
    }
    
    /**
     * 演示核心逻辑说明
     */
    public void explainCoreLogic() {
        log.info("=== 退款明细分配逻辑说明 ===");
        log.info("1. 历史转出按FIFO（先进先出）原则扣减转入记录");
        log.info("2. 本次退款按LIFO（后进先出）原则从剩余转入记录中分配");
        log.info("3. 优先从最新的转入订单开始退款，直到退款金额分配完毕");
        log.info("4. 返回详细的退款明细，包含每笔转入订单的退款金额");
        log.info("===============================");
    }
    
    /**
     * 创建测试记录
     */
    private UU898UserSubAccountFlowRecordDTO createRecord(String orderNo, LocalDateTime createTime, 
                                                         Integer journalType, String balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(new BigDecimal(balanceChange))
                .build();
    }
    
    /**
     * 运行所有演示
     */
    public void runAllDemonstrations() {
        explainCoreLogic();
        demonstrateRefundDetails();
    }
}
