package com.youpin.clear.application.example;

import com.youpin.clear.adapter.job.CalcTradeMoneyUtils;
import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 简化的仅可交易余额计算示例
 * <AUTHOR>
 */
@Slf4j
@Component
public class SimplifiedTradeBalanceExample {
    
    private static final Integer TRANSFER_IN_JOURNAL_TYPE = 97;
    private static final Integer TRANSFER_OUT_JOURNAL_TYPE = 99;
    
    /**
     * 演示简化的计算逻辑
     */
    public void demonstrateSimplifiedLogic() {
        log.info("=== 简化的仅可交易余额计算逻辑演示 ===");
        
        // 示例1：第一次转出计算逻辑 - 退款3元
        demonstrateFirstTransferOut();
        
        // 示例2：第二次转出计算逻辑 - 退款5元  
        demonstrateSecondTransferOut();
        
        // 示例3：第三次转出计算逻辑 - 退款3元
        demonstrateThirdTransferOut();
        
        log.info("=== 演示完成 ===");
    }
    
    /**
     * 第一次转出计算逻辑：退款3元
     */
    private void demonstrateFirstTransferOut() {
        log.info("\n--- 第一次转出计算逻辑 ---");
        
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3")
        );
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);
        
        log.info("计算结果：");
        log.info("  退款仅可交易金额：{} 元", result.getChangeMoney());
        log.info("  剩余仅可交易金额：{} 元", result.getTradeChangeMoney());
        log.info("  预期：退款3元，剩余5元");
        
        // 验证结果
        if (result.getChangeMoney().compareTo(new BigDecimal("3")) == 0 && 
            result.getTradeChangeMoney().compareTo(new BigDecimal("5")) == 0) {
            log.info("✅ 计算结果正确！");
        } else {
            log.error("❌ 计算结果不符合预期！");
        }
    }
    
    /**
     * 第二次转出计算逻辑：退款5元
     */
    private void demonstrateSecondTransferOut() {
        log.info("\n--- 第二次转出计算逻辑 ---");
        
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-5")
        );
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);
        
        log.info("计算结果：");
        log.info("  退款仅可交易金额：{} 元", result.getChangeMoney());
        log.info("  剩余仅可交易金额：{} 元", result.getTradeChangeMoney());
        log.info("  预期：退款5元，剩余1元");
        
        // 验证结果
        if (result.getChangeMoney().compareTo(new BigDecimal("5")) == 0 && 
            result.getTradeChangeMoney().compareTo(new BigDecimal("1")) == 0) {
            log.info("✅ 计算结果正确！");
        } else {
            log.error("❌ 计算结果不符合预期！");
        }
    }
    
    /**
     * 第三次转出计算逻辑：退款3元
     */
    private void demonstrateThirdTransferOut() {
        log.info("\n--- 第三次转出计算逻辑 ---");
        
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-5"),
            createRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3")
        );
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);
        
        log.info("计算结果：");
        log.info("  退款仅可交易金额：{} 元", result.getChangeMoney());
        log.info("  剩余仅可交易金额：{} 元", result.getTradeChangeMoney());
        log.info("  预期：退款3元，剩余1元");
        
        // 验证结果
        if (result.getChangeMoney().compareTo(new BigDecimal("3")) == 0 && 
            result.getTradeChangeMoney().compareTo(new BigDecimal("1")) == 0) {
            log.info("✅ 计算结果正确！");
        } else {
            log.error("❌ 计算结果不符合预期！");
        }
    }
    
    /**
     * 演示核心逻辑说明
     */
    public void explainCoreLogic() {
        log.info("=== 简化逻辑核心说明 ===");
        log.info("1. 核心逻辑：退款仅可交易金额 = 最后一次转出金额的绝对值");
        log.info("2. 剩余仅可交易金额 = 总转入金额 - 总转出金额（不能为负数）");
        log.info("3. 只需要按时间排序，找到最后一次转出记录即可");
        log.info("4. 不需要复杂的FIFO扣减逻辑，大大简化了计算过程");
        log.info("=========================");
    }
    
    /**
     * 创建测试记录
     */
    private UU898UserSubAccountFlowRecordDTO createRecord(String orderNo, LocalDateTime createTime, 
                                                         Integer journalType, String balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(new BigDecimal(balanceChange))
                .build();
    }
    
    /**
     * 运行所有演示
     */
    public void runAllDemonstrations() {
        explainCoreLogic();
        demonstrateSimplifiedLogic();
    }
}
