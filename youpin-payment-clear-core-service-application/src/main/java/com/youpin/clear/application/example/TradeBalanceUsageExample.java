package com.youpin.clear.application.example;

import com.youpin.clear.adapter.job.CalcTradeMoneyUtils;
import com.youpin.clear.application.service.TradeBalanceCalculationService;
import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 仅可交易余额计算使用示例
 * <AUTHOR>
 */
@Slf4j
@Component
public class TradeBalanceUsageExample {
    
    @Autowired
    private TradeBalanceCalculationService tradeBalanceService;
    
    /**
     * 演示完整的仅可交易余额计算流程
     */
    public void demonstrateTradeBalanceCalculation() {
        Long userId = 12345L;
        Integer accountType = 2; // 假设2代表可交易账户类型
        
        log.info("=== 开始演示仅可交易余额计算 ===");
        
        // 1. 查询用户当前的仅可交易余额
        BigDecimal currentBalance = tradeBalanceService.getCurrentTradeBalance(userId, accountType);
        log.info("用户 {} 当前仅可交易余额：{}", userId, currentBalance);
        
        // 2. 模拟转出操作前的检查
        BigDecimal transferOutAmount = new BigDecimal("100.00");
        TradeBalanceCalculationService.TradeBalanceCheckResult checkResult = 
                tradeBalanceService.checkTransferOutAmount(userId, accountType, transferOutAmount);
        
        log.info("转出检查结果：");
        log.info("  是否可以转出：{}", checkResult.isCanTransfer());
        log.info("  当前余额：{}", checkResult.getCurrentBalance());
        log.info("  转出金额：{}", checkResult.getTransferAmount());
        log.info("  超出金额：{}", checkResult.getExcessAmount());
        log.info("  检查消息：{}", checkResult.getMessage());
        
        // 3. 计算退款仅可交易金额
        if (checkResult.isCanTransfer()) {
            ChangeMoneyDTO refundResult = tradeBalanceService.calculateRefundTradeAmount(
                    userId, accountType, transferOutAmount);
            
            log.info("退款计算结果：");
            log.info("  退款仅可交易金额：{}", refundResult.getChangeMoney());
            log.info("  剩余仅可交易金额：{}", refundResult.getTradeChangeMoney());
        } else {
            log.warn("转出金额超过可用余额，无法执行转出操作");
            
            // 计算部分转出的情况
            BigDecimal availableAmount = checkResult.getCurrentBalance();
            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("尝试计算部分转出（金额：{}）", availableAmount);
                ChangeMoneyDTO partialRefundResult = tradeBalanceService.calculateRefundTradeAmount(
                        userId, accountType, availableAmount);
                
                log.info("部分转出计算结果：");
                log.info("  退款仅可交易金额：{}", partialRefundResult.getChangeMoney());
                log.info("  剩余仅可交易金额：{}", partialRefundResult.getTradeChangeMoney());
            }
        }
        
        // 4. 打印详细的计算过程（用于调试）
        log.info("=== 打印详细计算过程 ===");
        tradeBalanceService.printCalculationDetails(userId, accountType, transferOutAmount);
        
        log.info("=== 演示完成 ===");
    }
    
    /**
     * 演示批量用户的仅可交易余额查询
     */
    public void demonstrateBatchTradeBalanceQuery() {
        log.info("=== 开始演示批量查询仅可交易余额 ===");
        
        Long[] userIds = {12345L, 12346L, 12347L, 12348L, 12349L};
        Integer accountType = 2;
        
        for (Long userId : userIds) {
            try {
                BigDecimal balance = tradeBalanceService.getCurrentTradeBalance(userId, accountType);
                log.info("用户 {} 的仅可交易余额：{}", userId, balance);
                
                // 模拟不同的转出金额检查
                BigDecimal[] testAmounts = {
                    new BigDecimal("50.00"),
                    new BigDecimal("100.00"),
                    new BigDecimal("200.00")
                };
                
                for (BigDecimal testAmount : testAmounts) {
                    TradeBalanceCalculationService.TradeBalanceCheckResult result = 
                            tradeBalanceService.checkTransferOutAmount(userId, accountType, testAmount);
                    
                    log.info("  转出 {} 元：{} ({})", 
                            testAmount, 
                            result.isCanTransfer() ? "可以" : "不可以", 
                            result.getMessage());
                }
                
            } catch (Exception e) {
                log.error("查询用户 {} 的仅可交易余额时发生异常", userId, e);
            }
        }
        
        log.info("=== 批量查询完成 ===");
    }
    
    /**
     * 演示验证示例计算逻辑
     */
    public void demonstrateExampleValidation() {
        log.info("=== 开始验证示例计算逻辑 ===");
        
        // 调用工具类的验证方法
        CalcTradeMoneyUtils.validateExampleCalculation();
        
        log.info("=== 示例验证完成 ===");
    }
    
    /**
     * 演示异常情况处理
     */
    public void demonstrateExceptionHandling() {
        log.info("=== 开始演示异常情况处理 ===");
        
        // 1. 不存在的用户
        Long nonExistentUserId = 999999L;
        Integer accountType = 2;
        
        log.info("测试不存在的用户：{}", nonExistentUserId);
        BigDecimal balance = tradeBalanceService.getCurrentTradeBalance(nonExistentUserId, accountType);
        log.info("不存在用户的余额：{}", balance);
        
        // 2. 无效的转出金额
        Long validUserId = 12345L;
        BigDecimal invalidAmount = new BigDecimal("-100.00");
        
        log.info("测试无效的转出金额：{}", invalidAmount);
        ChangeMoneyDTO result = tradeBalanceService.calculateRefundTradeAmount(
                validUserId, accountType, invalidAmount);
        log.info("无效金额的计算结果：退款={}, 剩余={}", 
                result.getChangeMoney(), result.getTradeChangeMoney());
        
        // 3. 零金额转出
        BigDecimal zeroAmount = BigDecimal.ZERO;
        log.info("测试零金额转出：{}", zeroAmount);
        ChangeMoneyDTO zeroResult = tradeBalanceService.calculateRefundTradeAmount(
                validUserId, accountType, zeroAmount);
        log.info("零金额的计算结果：退款={}, 剩余={}", 
                zeroResult.getChangeMoney(), zeroResult.getTradeChangeMoney());
        
        log.info("=== 异常情况处理演示完成 ===");
    }
    
    /**
     * 演示性能测试
     */
    public void demonstratePerformanceTest() {
        log.info("=== 开始性能测试 ===");
        
        Long userId = 12345L;
        Integer accountType = 2;
        BigDecimal transferAmount = new BigDecimal("100.00");
        
        int testCount = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            tradeBalanceService.calculateRefundTradeAmount(userId, accountType, transferAmount);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / testCount;
        
        log.info("性能测试结果：");
        log.info("  测试次数：{}", testCount);
        log.info("  总耗时：{} ms", totalTime);
        log.info("  平均耗时：{} ms", avgTime);
        log.info("  QPS：{}", Math.round(1000.0 / avgTime));
        
        log.info("=== 性能测试完成 ===");
    }
    
    /**
     * 运行所有演示
     */
    public void runAllDemonstrations() {
        try {
            demonstrateTradeBalanceCalculation();
            Thread.sleep(1000);
            
            demonstrateBatchTradeBalanceQuery();
            Thread.sleep(1000);
            
            demonstrateExampleValidation();
            Thread.sleep(1000);
            
            demonstrateExceptionHandling();
            Thread.sleep(1000);
            
            demonstratePerformanceTest();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("演示过程被中断", e);
        } catch (Exception e) {
            log.error("演示过程中发生异常", e);
        }
    }
}
