package com.youpin.clear.application.service;

import com.youpin.clear.adapter.job.CalcTradeMoneyUtils;
import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import com.youpin.clear.domain.gateway.UU898UserSubAccountGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 仅可交易余额计算服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class TradeBalanceCalculationService {
    
    @Autowired
    private UU898UserSubAccountGateway subAccountGateway;
    
    /**
     * 计算用户的退款仅可交易金额
     * 
     * @param userId 用户ID
     * @param accountType 账户类型
     * @param transferOutAmount 转出金额
     * @return 计算结果
     */
    public ChangeMoneyDTO calculateRefundTradeAmount(Long userId, Integer accountType, BigDecimal transferOutAmount) {
        log.info("开始计算用户 {} 的退款仅可交易金额，账户类型：{}，转出金额：{}", 
                userId, accountType, transferOutAmount);
        
        try {
            // 1. 获取用户的子账户流水记录
            List<UU898UserSubAccountFlowRecordDTO> flowRecords = getSubAccountFlowRecords(userId, accountType);
            
            if (flowRecords.isEmpty()) {
                log.warn("用户 {} 的子账户流水记录为空", userId);
                return ChangeMoneyDTO.builder()
                        .changeMoney(transferOutAmount) // 没有转入记录，全部作为退款
                        .tradeChangeMoney(BigDecimal.ZERO)
                        .build();
            }
            
            // 2. 执行计算
            ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(flowRecords, transferOutAmount);
            
            log.info("用户 {} 计算完成 - 退款仅可交易：{}，剩余仅可交易：{}", 
                    userId, result.getChangeMoney(), result.getTradeChangeMoney());
            
            return result;
            
        } catch (Exception e) {
            log.error("计算用户 {} 的退款仅可交易金额时发生异常", userId, e);
            // 异常情况下返回保守结果
            return ChangeMoneyDTO.builder()
                    .changeMoney(transferOutAmount)
                    .tradeChangeMoney(BigDecimal.ZERO)
                    .build();
        }
    }
    
    /**
     * 查询用户当前的仅可交易余额
     * 
     * @param userId 用户ID
     * @param accountType 账户类型
     * @return 当前仅可交易余额
     */
    public BigDecimal getCurrentTradeBalance(Long userId, Integer accountType) {
        log.info("查询用户 {} 当前的仅可交易余额，账户类型：{}", userId, accountType);
        
        try {
            // 获取用户的子账户流水记录
            List<UU898UserSubAccountFlowRecordDTO> flowRecords = getSubAccountFlowRecords(userId, accountType);
            
            if (flowRecords.isEmpty()) {
                log.info("用户 {} 没有子账户流水记录，当前仅可交易余额为0", userId);
                return BigDecimal.ZERO;
            }
            
            // 计算当前余额
            ChangeMoneyDTO result = CalcTradeMoneyUtils.calcCurrentTradeBalance(flowRecords);
            BigDecimal currentBalance = result.getTradeChangeMoney();
            
            log.info("用户 {} 当前仅可交易余额：{}", userId, currentBalance);
            return currentBalance;
            
        } catch (Exception e) {
            log.error("查询用户 {} 当前仅可交易余额时发生异常", userId, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 检查转出金额是否超过可用的仅可交易余额
     * 
     * @param userId 用户ID
     * @param accountType 账户类型
     * @param transferOutAmount 转出金额
     * @return 检查结果
     */
    public TradeBalanceCheckResult checkTransferOutAmount(Long userId, Integer accountType, BigDecimal transferOutAmount) {
        log.info("检查用户 {} 转出金额 {} 是否超过可用余额", userId, transferOutAmount);
        
        try {
            BigDecimal currentBalance = getCurrentTradeBalance(userId, accountType);
            
            if (transferOutAmount.compareTo(currentBalance) <= 0) {
                return TradeBalanceCheckResult.builder()
                        .canTransfer(true)
                        .currentBalance(currentBalance)
                        .transferAmount(transferOutAmount)
                        .excessAmount(BigDecimal.ZERO)
                        .message("转出金额在可用范围内")
                        .build();
            } else {
                BigDecimal excessAmount = transferOutAmount.subtract(currentBalance);
                return TradeBalanceCheckResult.builder()
                        .canTransfer(false)
                        .currentBalance(currentBalance)
                        .transferAmount(transferOutAmount)
                        .excessAmount(excessAmount)
                        .message("转出金额超过可用余额 " + excessAmount + " 元")
                        .build();
            }
            
        } catch (Exception e) {
            log.error("检查用户 {} 转出金额时发生异常", userId, e);
            return TradeBalanceCheckResult.builder()
                    .canTransfer(false)
                    .message("检查过程中发生异常：" + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取用户的子账户流水记录
     * 可以根据需要添加时间范围、分页等参数
     */
    private List<UU898UserSubAccountFlowRecordDTO> getSubAccountFlowRecords(Long userId, Integer accountType) {
        // 这里可以根据业务需求添加时间范围限制
        // 例如：只查询最近30天的记录
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(30);

        // 构建子账户DTO
        UU898UserSubAccountDTO subAccountDTO = new UU898UserSubAccountDTO();
        subAccountDTO.setUserId(userId);
        subAccountDTO.setAccountType(accountType);

        // 获取转入和转出的流水类型
        List<Integer> journalTypeList = Arrays.asList(97, 99); // 97-转入, 99-转出

        // 调用网关获取流水记录
        return subAccountGateway.listSubAccountRecord(subAccountDTO, journalTypeList, startTime, endTime);
    }
    
    /**
     * 打印计算过程详情（用于调试）
     */
    public void printCalculationDetails(Long userId, Integer accountType, BigDecimal transferOutAmount) {
        log.info("=== 打印用户 {} 的计算详情 ===", userId);
        
        List<UU898UserSubAccountFlowRecordDTO> flowRecords = getSubAccountFlowRecords(userId, accountType);
        CalcTradeMoneyUtils.printCalculationDetails(flowRecords, transferOutAmount);
    }
    
    /**
     * 仅可交易余额检查结果
     */
    public static class TradeBalanceCheckResult {
        private boolean canTransfer;           // 是否可以转出
        private BigDecimal currentBalance;     // 当前可用余额
        private BigDecimal transferAmount;     // 转出金额
        private BigDecimal excessAmount;       // 超出金额
        private String message;                // 检查结果消息
        
        public static TradeBalanceCheckResultBuilder builder() {
            return new TradeBalanceCheckResultBuilder();
        }
        
        // Getters
        public boolean isCanTransfer() { return canTransfer; }
        public BigDecimal getCurrentBalance() { return currentBalance; }
        public BigDecimal getTransferAmount() { return transferAmount; }
        public BigDecimal getExcessAmount() { return excessAmount; }
        public String getMessage() { return message; }
        
        // Builder
        public static class TradeBalanceCheckResultBuilder {
            private boolean canTransfer;
            private BigDecimal currentBalance;
            private BigDecimal transferAmount;
            private BigDecimal excessAmount;
            private String message;
            
            public TradeBalanceCheckResultBuilder canTransfer(boolean canTransfer) {
                this.canTransfer = canTransfer;
                return this;
            }
            
            public TradeBalanceCheckResultBuilder currentBalance(BigDecimal currentBalance) {
                this.currentBalance = currentBalance;
                return this;
            }
            
            public TradeBalanceCheckResultBuilder transferAmount(BigDecimal transferAmount) {
                this.transferAmount = transferAmount;
                return this;
            }
            
            public TradeBalanceCheckResultBuilder excessAmount(BigDecimal excessAmount) {
                this.excessAmount = excessAmount;
                return this;
            }
            
            public TradeBalanceCheckResultBuilder message(String message) {
                this.message = message;
                return this;
            }
            
            public TradeBalanceCheckResult build() {
                TradeBalanceCheckResult result = new TradeBalanceCheckResult();
                result.canTransfer = this.canTransfer;
                result.currentBalance = this.currentBalance;
                result.transferAmount = this.transferAmount;
                result.excessAmount = this.excessAmount;
                result.message = this.message;
                return result;
            }
        }
    }
}
