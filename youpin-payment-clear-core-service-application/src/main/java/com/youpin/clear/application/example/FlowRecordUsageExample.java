package com.youpin.clear.application.example;

import com.youpin.clear.application.service.PaymentDeductionApplicationService;
import com.youpin.clear.domain.aggregate.uu898.DeductionResult;
import com.youpin.clear.domain.aggregate.uu898.UserSubAccountType;
import com.youpin.clear.domain.aggregate.uu898.UserSubAccountFlowRecord;
import com.youpin.clear.domain.gateway.UserSubAccountFlowRecordGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * FlowRecordId使用示例
 * 展示如何在扣款操作后获取和使用流水记录ID
 * <AUTHOR>
 */
@Slf4j
@Component
public class FlowRecordUsageExample {
    
    @Autowired
    private PaymentDeductionApplicationService paymentService;
    
    @Autowired
    private UserSubAccountFlowRecordGateway flowRecordGateway;
    
    /**
     * 完整的扣款流程示例，展示flowRecordId的使用
     */
    public void demonstrateFlowRecordUsage() {
        Long userId = 12345L;
        BigDecimal amount = new BigDecimal("100.00");
        String orderNo = "ORDER_" + System.currentTimeMillis();
        String payOrderNo = "PAY_" + System.currentTimeMillis();
        
        log.info("=== 开始演示FlowRecordId的使用 ===");
        
        // 1. 执行购买商品扣款
        DeductionResult result = paymentService.purchaseProduct(userId, amount, orderNo, payOrderNo);
        
        if (!result.isSuccess()) {
            log.error("扣款失败，无法演示：{}", result.getErrorMessage());
            return;
        }
        
        // 2. 获取所有流水记录ID
        List<Long> allFlowRecordIds = result.getDeductionDetails().stream()
                .map(DeductionResult.SubAccountDeductionDetail::getFlowRecordId)
                .filter(id -> id != null)
                .collect(Collectors.toList());
        
        log.info("本次扣款生成的流水记录ID：{}", allFlowRecordIds);
        
        // 3. 按账户类型分别获取流水记录ID
        List<Long> tradeFlowIds = getFlowRecordIdsByAccountType(result, UserSubAccountType.TRADE);
        List<Long> withdrawFlowIds = getFlowRecordIdsByAccountType(result, UserSubAccountType.WITHDRAW);
        
        log.info("可交易账户流水记录ID：{}", tradeFlowIds);
        log.info("可提现账户流水记录ID：{}", withdrawFlowIds);
        
        // 4. 查询具体的流水记录详情
        queryFlowRecordDetails(allFlowRecordIds);
        
        // 5. 验证流水记录的业务信息
        validateBusinessInfo(allFlowRecordIds, orderNo, payOrderNo);
        
        // 6. 演示如何根据订单号查询相关流水
        queryFlowRecordsByOrderNo(orderNo);
    }
    
    /**
     * 根据账户类型获取流水记录ID
     */
    private List<Long> getFlowRecordIdsByAccountType(DeductionResult result, UserSubAccountType accountType) {
        return result.getDeductionDetails().stream()
                .filter(detail -> detail.getAccountType() == accountType)
                .map(DeductionResult.SubAccountDeductionDetail::getFlowRecordId)
                .filter(id -> id != null)
                .collect(Collectors.toList());
    }
    
    /**
     * 查询流水记录详情
     */
    private void queryFlowRecordDetails(List<Long> flowRecordIds) {
        log.info("=== 查询流水记录详情 ===");
        
        flowRecordIds.forEach(flowRecordId -> {
            try {
                UserSubAccountFlowRecord flowRecord = flowRecordGateway.getById(flowRecordId);
                if (flowRecord != null) {
                    log.info("流水记录 {}：", flowRecordId);
                    log.info("  账户编号：{}", flowRecord.getAccountNo());
                    log.info("  账户类型：{}", flowRecord.getAccountType());
                    log.info("  用户ID：{}", flowRecord.getUserId());
                    log.info("  订单号：{}", flowRecord.getOrderNo());
                    log.info("  支付单号：{}", flowRecord.getPayOrderNo());
                    log.info("  变更前余额：{}", flowRecord.getBalanceBefore());
                    log.info("  余额变更值：{}", flowRecord.getBalanceChange());
                    log.info("  变更后余额：{}", flowRecord.getBalanceAfter());
                    log.info("  创建时间：{}", flowRecord.getCreateTime());
                } else {
                    log.error("流水记录 {} 不存在！", flowRecordId);
                }
            } catch (Exception e) {
                log.error("查询流水记录 {} 时发生异常：{}", flowRecordId, e.getMessage());
            }
        });
    }
    
    /**
     * 验证流水记录的业务信息
     */
    private void validateBusinessInfo(List<Long> flowRecordIds, String expectedOrderNo, String expectedPayOrderNo) {
        log.info("=== 验证流水记录业务信息 ===");
        
        flowRecordIds.forEach(flowRecordId -> {
            try {
                UserSubAccountFlowRecord flowRecord = flowRecordGateway.getById(flowRecordId);
                if (flowRecord != null) {
                    boolean orderNoMatch = expectedOrderNo.equals(flowRecord.getOrderNo());
                    boolean payOrderNoMatch = expectedPayOrderNo.equals(flowRecord.getPayOrderNo());
                    
                    log.info("流水记录 {} 验证结果：", flowRecordId);
                    log.info("  订单号匹配：{} (期望：{}，实际：{})", orderNoMatch, expectedOrderNo, flowRecord.getOrderNo());
                    log.info("  支付单号匹配：{} (期望：{}，实际：{})", payOrderNoMatch, expectedPayOrderNo, flowRecord.getPayOrderNo());
                    
                    if (!orderNoMatch || !payOrderNoMatch) {
                        log.warn("流水记录 {} 的业务信息不匹配！", flowRecordId);
                    }
                }
            } catch (Exception e) {
                log.error("验证流水记录 {} 时发生异常：{}", flowRecordId, e.getMessage());
            }
        });
    }
    
    /**
     * 根据订单号查询相关流水记录
     */
    private void queryFlowRecordsByOrderNo(String orderNo) {
        log.info("=== 根据订单号查询流水记录 ===");
        
        try {
            List<UserSubAccountFlowRecord> flowRecords = flowRecordGateway.getByOrderNo(orderNo);
            log.info("订单号 {} 关联的流水记录数量：{}", orderNo, flowRecords.size());
            
            flowRecords.forEach(record -> {
                log.info("流水记录ID：{}，账户类型：{}，变更金额：{}", 
                        record.getId(), record.getAccountType(), record.getBalanceChange());
            });
        } catch (Exception e) {
            log.error("根据订单号查询流水记录时发生异常：{}", e.getMessage());
        }
    }
    
    /**
     * 演示退款操作中flowRecordId的使用
     */
    public void demonstrateRefundFlowRecordUsage() {
        Long userId = 12345L;
        BigDecimal refundAmount = new BigDecimal("50.00");
        String orderNo = "ORDER_REFUND_" + System.currentTimeMillis();
        String refundNo = "REFUND_" + System.currentTimeMillis();
        
        log.info("=== 开始演示退款FlowRecordId的使用 ===");
        
        // 执行退款操作
        DeductionResult refundResult = paymentService.refundProduct(userId, refundAmount, orderNo, refundNo);
        
        if (refundResult.isSuccess()) {
            // 获取退款流水记录ID
            List<Long> refundFlowIds = refundResult.getDeductionDetails().stream()
                    .map(DeductionResult.SubAccountDeductionDetail::getFlowRecordId)
                    .filter(id -> id != null)
                    .collect(Collectors.toList());
            
            log.info("退款操作生成的流水记录ID：{}", refundFlowIds);
            
            // 验证退款流水记录的金额为正数
            refundFlowIds.forEach(flowRecordId -> {
                try {
                    UserSubAccountFlowRecord flowRecord = flowRecordGateway.getById(flowRecordId);
                    if (flowRecord != null) {
                        BigDecimal balanceChange = flowRecord.getBalanceChange();
                        if (balanceChange.compareTo(BigDecimal.ZERO) > 0) {
                            log.info("退款流水记录 {} 验证通过，退款金额：{}", flowRecordId, balanceChange);
                        } else {
                            log.warn("退款流水记录 {} 异常，金额应为正数，实际：{}", flowRecordId, balanceChange);
                        }
                    }
                } catch (Exception e) {
                    log.error("验证退款流水记录 {} 时发生异常：{}", flowRecordId, e.getMessage());
                }
            });
        } else {
            log.error("退款失败：{}", refundResult.getErrorMessage());
        }
    }
}
