package com.youpin.clear.app.service;

import com.youpin.clear.domain.dto.AccountAssetsTypeDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AccountAssetsTypeService {
    Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsType();

    Map<String, List<Integer>> gatAllAccountAssetsTypeRelate();

    void initAccountAssetsTypeCache();

    Map<Integer, String> gatAccountAssetsTypeByCode(Set<Integer> assetsCodeList);
}
