package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.service.UU898UserAccountService;
import com.youpin.clear.client.request.UU898SubAccountInitRequest;
import com.youpin.clear.domain.gateway.UU898UserSubAccountGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class UU898UserSubAccountServiceImpl implements UU898UserAccountService {

    @Autowired
    private UU898UserSubAccountGateway uu898UserSubAccountGateway;


    /**
     * 初始化用户子账户
     *
     * @param request request
     */
    @Override
    public void initUserSubAccount(UU898SubAccountInitRequest request) {
        if (CollectionUtils.isEmpty(request.getSubAccountTypeList())) {
            return;
        }
        Long minUserId = request.getMinUserId();
        Long maxUserId = request.getMaxUserId();
        //
        List<Long> userIds = new ArrayList<>(Math.toIntExact(maxUserId - minUserId));
        // 遍历userId
        for (Long userId = minUserId; userId <= maxUserId; userId++) {
            userIds.add(userId);
        }
        // 保存
        uu898UserSubAccountGateway.addSubAccount(userIds, request.getSubAccountTypeList());
        //
        log.info("[initUserSubAccount]:初始化用户子账户成功,minUserId:{},maxUserId:{}", minUserId, maxUserId);
    }
}
