package com.youpin.clear.app.converter;


import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * 转换类
 *
 * <AUTHOR>
 */
public class TypeConversionWorker {

    public static LocalDateTime netDateStrToLocalDateTime(String netDateStr) {
        if (StringUtils.isNotBlank(netDateStr)) {
            if (netDateStr.contains("+")) {
                return LocalDateTime.parse(netDateStr.split("\\+")[0]);
            } else {
                return LocalDateTime.parse(netDateStr);
            }
        }
        return null;
    }


}
