package com.youpin.clear.app.service;

import com.youpin.clear.client.request.FinancialStatementJobRequest;
import com.youpin.clear.client.response.FinancialStatementJobResponse;

public interface UU898UserAssetsRecordService {
    FinancialStatementJobResponse countByAddTime(FinancialStatementJobRequest request);

    FinancialStatementJobResponse selectByAddTimePage(FinancialStatementJobRequest request);

    FinancialStatementJobResponse selectByIdInterval(FinancialStatementJobRequest request);

    FinancialStatementJobResponse selectMaxId();

    void handleUserAssetsRecordId(FinancialStatementJobRequest request);

}
