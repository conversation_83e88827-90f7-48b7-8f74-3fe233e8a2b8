package com.youpin.clear.app.service;

import com.youpin.clear.client.request.TransactionServiceFeeStatementRequest;
import com.youpin.clear.client.response.TransactionServiceFeeStatementResponse;

public interface TransactionServiceFeeStatementRecordService {

    TransactionServiceFeeStatementResponse selectMaxId();

    TransactionServiceFeeStatementResponse updateSerialNo(TransactionServiceFeeStatementRequest request);

    void handleByJobId(TransactionServiceFeeStatementRequest request);

    void handleByJobIdRetry();

    void financeSummary(TransactionServiceFeeStatementRequest request);

    void financeSummaryClear(TransactionServiceFeeStatementRequest request);
}
