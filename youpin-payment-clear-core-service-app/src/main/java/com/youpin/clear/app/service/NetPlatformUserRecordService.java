package com.youpin.clear.app.service;

import com.youpin.clear.client.request.UserAssetsRecordByTypeRequest;
import com.youpin.clear.client.response.UserAssetsRecordByTypeResponse;
import com.youpin.clear.domain.dto.NetPlatformUserRecordMessage;

public interface NetPlatformUserRecordService {
    void uu898UserAssetsRecordNetBufferAccountingProcess(UserAssetsRecordByTypeRequest request);

    void bufferAccountingProcess(NetPlatformUserRecordMessage netPlatformUserRecordMessage);

    UserAssetsRecordByTypeResponse selectMaxId(UserAssetsRecordByTypeRequest request);
}
