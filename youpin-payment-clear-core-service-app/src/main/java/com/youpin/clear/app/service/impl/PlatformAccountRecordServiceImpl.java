package com.youpin.clear.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.AccountAssetsTypeService;
import com.youpin.clear.app.service.PlatformAccountRecordService;
import com.youpin.clear.client.request.PlatformAccountRecordJobRequest;
import com.youpin.clear.client.response.PlatformAccountRecordJobResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.ClearUserAssetsRecordGateway;
import com.youpin.clear.domain.gateway.PlatformAccountRecordGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsInfoGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class PlatformAccountRecordServiceImpl implements PlatformAccountRecordService {


    @Autowired
    PlatformAccountRecordGateway platformAccountRecordGateway;

    @Autowired
    UU898UserAssetsInfoGateway uu898UserAssetsInfoGateway;

    @Autowired
    UU898UserAssetsRecordGateway uu898UserAssetsRecordGateway;

    @Autowired
    ClearUserAssetsRecordGateway clearUserAssetsRecordGateway;

    @Autowired
    AccountAssetsTypeService accountAssetsTypeService;


    @Override
    public PlatformAccountRecordJobResponse selectPageSizeByStatusAndCreateTime(PlatformAccountRecordJobRequest request) {
        List<Long> ids = platformAccountRecordGateway.selectPageSizeByStatusAndCreateTime(request.getShardIndex(), request.getStatus(), request.getCreateTime(), request.getPageSize());
        return PlatformAccountRecordJobResponse.builder().ids(ids).build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bufferAccountingProcessMq(PlatformAccountRecordDTO dto) {
        UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO = uu898UserAssetsRecordGateway.selectById(dto.getUserAssetsRecordId());

        //查询数据是否存在
        PlatformAccountRecordDTO dtoMqDb = platformAccountRecordGateway.selectById(dto.getId());
        if (null == dtoMqDb) {
            //说明数据丢失 查询资金明细 存在则插入数据
            //查询资金明细数据
            if (null == uu898UserAssetsRecordDTO) {
                log.error("[缓冲记账]流水数据丢失 用户资金明细查询失败,数据为null :{}", JSON.toJSONString(dto));
                return;
            }
            if (!uu898UserAssetsRecordDTO.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
                log.warn("[缓冲记账] 流水数据丢失 用户资金明细状态异常 :{}", JSON.toJSONString(uu898UserAssetsRecordDTO));
                return;
            }
            //补充数据
            log.info("[缓冲记账]补充数据 :{}", JSON.toJSONString(dto));
            Boolean saveFlag = platformAccountRecordGateway.save(dto);
            if (!saveFlag) {
                log.error("[缓冲记账]用户资金记录插入失败 :{}", JSON.toJSONString(dto));
                return;
            }
            return;
        }
        //状态判断
        if (!PlatformAccountRecordStatusEnum.PENDING.getCode().equals(dtoMqDb.getStatus())) {
            log.warn("[缓冲记账]数据已处理 :{}", JSON.toJSONString(dto));
            return;
        }
        bufferAccountingProcess(dto);


    }

    /**
     * 缓冲记账处理
     */
    @Override
    public void bufferAccountingProcess(PlatformAccountRecordDTO dto) {

        if (null == dto.getUserAssetsRecordId() || null == dto.getId() || null == dto.getUserId() || StringUtils.isBlank(dto.getTreadNo())) {
            log.warn("[缓冲记账]用户资金记录ID为空 :{}", JSON.toJSONString(dto));
            return;
        }
        //查询资金明细数据 二次验证
        UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO = uu898UserAssetsRecordGateway.selectById(dto.getUserAssetsRecordId());
        if (null == uu898UserAssetsRecordDTO) {
            log.error("[缓冲记账]用户资金明细查询失败,数据为null :{}", JSON.toJSONString(dto));
            return;
        }
        //uu898资金处理
        handleUserAssetsRecord(dto, uu898UserAssetsRecordDTO);
        //账务资金处理
        handleClearUserAssetsRecord(uu898UserAssetsRecordDTO);
        //数据更新成功
        Boolean flag = platformAccountRecordGateway.updateStatusById(dto.getId(), PlatformAccountRecordStatusEnum.SUCCESS.getCode());
        if (!flag) {
            log.warn("[缓冲记账]流水日志更新失败 :{}", JSON.toJSONString(dto));
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "流水日志更新失败,操作回滚");
        }
    }

    private void handleClearUserAssetsRecord(UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO) {
        //同步数据
        ClearUserAssetsRecordDTO clearUserAssetsRecordDTO = clearUserAssetsRecordGateway.selectUserAssetsRecordById(uu898UserAssetsRecordDTO.getUserId(), uu898UserAssetsRecordDTO.getId());
        if (null == clearUserAssetsRecordDTO) {
            Map<Integer, String> typeIdNameList = accountAssetsTypeService.gatAccountAssetsTypeByCode(Set.of(uu898UserAssetsRecordDTO.getTypeId()));
            //插入数据
            ClearUserAssetsRecordDTO clearUserAssetsRecordDTOInsert = UserAssetsRecordConvertor.MAPPER.toClearUserAssetsRecordDTOByUU898(uu898UserAssetsRecordDTO);
            clearUserAssetsRecordDTOInsert.setTypeName(typeIdNameList.get(uu898UserAssetsRecordDTO.getTypeId()));
            clearUserAssetsRecordDTOInsert.setCollectType(CollectTypeEnum.OWN.getCode());
            clearUserAssetsRecordDTOInsert.setMerchantId(ClearConstants.MERCHANT_ID);
            clearUserAssetsRecordDTOInsert.setStatus(uu898UserAssetsRecordDTO.getStatus());
            clearUserAssetsRecordDTOInsert.setBlockMoney(uu898UserAssetsRecordDTO.getBlockMoney());
            clearUserAssetsRecordDTOInsert.setAfterBlockMoney(uu898UserAssetsRecordDTO.getAfterBlockMoney());
            clearUserAssetsRecordDTOInsert.setMoney(uu898UserAssetsRecordDTO.getMoney());
            clearUserAssetsRecordDTOInsert.setAfterMoney(uu898UserAssetsRecordDTO.getAfterMoney());
            clearUserAssetsRecordDTOInsert.setPurchaseMoney(uu898UserAssetsRecordDTO.getPurchaseMoney());
            clearUserAssetsRecordDTOInsert.setAfterPurchaseMoney(uu898UserAssetsRecordDTO.getAfterPurchaseMoney());
            clearUserAssetsRecordDTOInsert.setCompleteTime(uu898UserAssetsRecordDTO.getCompleteTime());
            clearUserAssetsRecordGateway.insertSelective(clearUserAssetsRecordDTOInsert);
            log.info("[缓冲记账] 账务资金明细插入成功 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());
        } else {
            //更新数据
            clearUserAssetsRecordDTO.setBlockMoney(uu898UserAssetsRecordDTO.getBlockMoney());
            clearUserAssetsRecordDTO.setAfterBlockMoney(uu898UserAssetsRecordDTO.getAfterBlockMoney());
            clearUserAssetsRecordDTO.setMoney(uu898UserAssetsRecordDTO.getMoney());
            clearUserAssetsRecordDTO.setAfterMoney(uu898UserAssetsRecordDTO.getAfterMoney());
            clearUserAssetsRecordDTO.setPurchaseMoney(uu898UserAssetsRecordDTO.getPurchaseMoney());
            clearUserAssetsRecordDTO.setAfterPurchaseMoney(uu898UserAssetsRecordDTO.getAfterPurchaseMoney());
            clearUserAssetsRecordDTO.setStatus(uu898UserAssetsRecordDTO.getStatus());
            clearUserAssetsRecordDTO.setCompleteTime(uu898UserAssetsRecordDTO.getCompleteTime());
            clearUserAssetsRecordGateway.updateByPrimaryKeySelectiveByUserId(clearUserAssetsRecordDTO);
            log.info("[缓冲记账] 账务资金明细更新成功 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());
        }
    }

    private void handleUserAssetsRecord(PlatformAccountRecordDTO dto, UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO) {
        //状态判断 进行中才处理
        if (!uu898UserAssetsRecordDTO.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
            log.warn("[缓冲记账]用户资金明细数据已处理 :{}", JSON.toJSONString(dto));
            return;
        }
        //查询用户资金信息
        UserAssetsInfoDTO originUserAssetsInfoDTO = uu898UserAssetsInfoGateway.getUserAssetsInfo(dto.getUserId());
        if (originUserAssetsInfoDTO == null) {
            log.error("[缓冲记账]用户资金信息查询失败 :{}", JSON.toJSONString(dto));
            throw new PaymentClearBusinessException(ErrorCode.USER_ACCOUNT_INFO_IS_NULL);
        }
        BigDecimal beforeMoney = originUserAssetsInfoDTO.getMoney();
        BigDecimal beforeBlockMoney = originUserAssetsInfoDTO.getBlockMoney();
        BigDecimal beforePurchaseMoney = originUserAssetsInfoDTO.getPurchaseMoney();

        uu898UserAssetsRecordDTO.setMoney(beforeMoney);
        uu898UserAssetsRecordDTO.setBlockMoney(beforeBlockMoney);
        uu898UserAssetsRecordDTO.setPurchaseMoney(beforePurchaseMoney);


        //判断资金是否需要加减
        UserAssetsTypeEnum userAssetsTypeEnum = UserAssetsTypeEnum.getByTypeCode(uu898UserAssetsRecordDTO.getTypeId());

        if (userAssetsTypeEnum.getDirectionEnum().equals(FundingDirectionEnum.INVARIANT_ADDITION) || userAssetsTypeEnum.getDirectionEnum().equals(FundingDirectionEnum.INVARIANT_SUBTRACTION)) {

            uu898UserAssetsRecordDTO.setAfterMoney(beforeMoney);
            uu898UserAssetsRecordDTO.setAfterBlockMoney(beforeBlockMoney);
            uu898UserAssetsRecordDTO.setAfterPurchaseMoney(beforePurchaseMoney);

        } else {
            //判断修改金额类型
            if (PlatformAccountRecordMoneyTypeEnum.BALANCE.getCode().equals(dto.getMoneyType())) {
                BigDecimal afterMoney = beforeMoney.add(dto.getMoney());
                BigDecimal afterBlockMoney = beforeBlockMoney.add(dto.getBlockMoney());
                //校验
                if (afterMoney.compareTo(BigDecimal.ZERO) < 0 || afterBlockMoney.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("[缓冲记账]余额不足/冻结余额不足 :{}  {} {}", afterMoney, afterBlockMoney, JSON.toJSONString(dto));
                    throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
                }

                UserAssetsInfoDTO toUpdateUserAssetsInfoDTO = new UserAssetsInfoDTO();
                toUpdateUserAssetsInfoDTO.setMoney(afterMoney);
                toUpdateUserAssetsInfoDTO.setBlockMoney(afterBlockMoney);
                toUpdateUserAssetsInfoDTO.setLastUserRecordId(dto.getUserAssetsRecordId());
                toUpdateUserAssetsInfoDTO.setUpdateTime(LocalDateTime.now());

                boolean updateUserBalanceAndBlockMoneyFlag = uu898UserAssetsInfoGateway.updateUserBalanceAndBlockMoney(originUserAssetsInfoDTO, toUpdateUserAssetsInfoDTO) > 0;
                if (!updateUserBalanceAndBlockMoneyFlag) {
                    log.error("[缓冲记账]用户资金余额更新失败 :{}", JSON.toJSONString(dto));
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "用户资金余额更新失败,操作回滚");
                }
                uu898UserAssetsRecordDTO.setAfterMoney(afterMoney);
                uu898UserAssetsRecordDTO.setAfterBlockMoney(afterBlockMoney);

            } else if (PlatformAccountRecordMoneyTypeEnum.PURCHASE_BALANCE.getCode().equals(dto.getMoneyType())) {
                BigDecimal afterPurchaseMoney = beforePurchaseMoney.add(dto.getMoney());
                BigDecimal afterPurchaseMoneyFromMoney = originUserAssetsInfoDTO.getPurchaseMoneyFromMoney();

                // 求购余额小于钱包转入求购金额
                if (afterPurchaseMoney.compareTo(afterPurchaseMoneyFromMoney) < 0) {
                    // 设置钱包转入求购金额为求购余额
                    afterPurchaseMoneyFromMoney = afterPurchaseMoney;
                }
                //校验余额
                if (afterPurchaseMoney.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("[缓冲记账]求购余额不足 :{}", JSON.toJSONString(dto));
                    throw new PaymentClearBusinessException(ErrorCode.PURCHASE_BALANCE_NOT_ENOUGH);
                }

                UserAssetsInfoDTO toUpdateUserAssetsInfoDTO = new UserAssetsInfoDTO();
                toUpdateUserAssetsInfoDTO.setPurchaseMoney(afterPurchaseMoney);
                toUpdateUserAssetsInfoDTO.setPurchaseMoneyFromMoney(afterPurchaseMoneyFromMoney);
                toUpdateUserAssetsInfoDTO.setLastUserRecordId(dto.getUserAssetsRecordId());
                toUpdateUserAssetsInfoDTO.setUpdateTime(LocalDateTime.now());

                boolean updateUserPurchaseBalanceFlag = uu898UserAssetsInfoGateway.updateUserPurchaseBalance(originUserAssetsInfoDTO, toUpdateUserAssetsInfoDTO) > 0;
                if (!updateUserPurchaseBalanceFlag) {
                    log.error("[缓冲记账]用户资金求购余额更新失败 :{}", JSON.toJSONString(dto));
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "用户资金求购余额更新失败,操作回滚");
                }
                uu898UserAssetsRecordDTO.setAfterPurchaseMoney(afterPurchaseMoney);

            } else {
                log.error("[缓冲记账]金额类型异常 :{}", JSON.toJSONString(dto));
            }
        }
        //更新用户资金明细
        uu898UserAssetsRecordDTO.setStatus(NetStatusEnum.SUCCESS.getCode());
        uu898UserAssetsRecordDTO.setCompleteTime(LocalDateTime.now());
        boolean userAssetsRecordUpdateFlag = uu898UserAssetsRecordGateway.update(uu898UserAssetsRecordDTO) > 0;
        if (!userAssetsRecordUpdateFlag) {
            log.error("[缓冲记账]用户资金明细更新失败 :{}", JSON.toJSONString(dto));
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "用户资金明细更新失败,操作回滚");
        }
        log.info("[缓冲记账] 用户资金明细更新成功 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handle(PlatformAccountRecordJobRequest request) {
        if (null == request || null == request.getId()) {
            log.warn("流水日志JOB处理失败,参数异常 {} ", request);
            return;
        }
        //查询数据是否存在
        PlatformAccountRecordDTO dtoMqDb = platformAccountRecordGateway.selectById(request.getId());
        if (null == dtoMqDb) {
            log.warn("流水日志JOB处理失败,流水不存在 {} ", request);
            return;
        }
        if (!PlatformAccountRecordStatusEnum.PENDING.getCode().equals(dtoMqDb.getStatus())) {
            log.warn("流水日志JOB处理失败,流水已处理 {} ", request);
            return;
        }
        this.bufferAccountingProcess(dtoMqDb);
    }

}
