package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.service.UU898UserAssetsRecordService;
import com.youpin.clear.client.request.FinancialStatementJobRequest;
import com.youpin.clear.client.response.FinancialStatementJobResponse;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Service
@Slf4j
public class UU898UserAssetsRecordServiceImpl implements UU898UserAssetsRecordService {

    @Autowired
    UU898UserAssetsRecordGateway uu898UserAssetsRecordGateway;

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Override
    public FinancialStatementJobResponse countByAddTime(FinancialStatementJobRequest request) {
        Long count = uu898UserAssetsRecordGateway.countByAddTime(request.getLastId(), request.getStartTime(), request.getEndTime());
        FinancialStatementJobResponse response = new FinancialStatementJobResponse();
        response.setCount(count);
        return response;
    }

    @Override
    public FinancialStatementJobResponse selectByAddTimePage(FinancialStatementJobRequest request) {
        List<Long> idList = uu898UserAssetsRecordGateway.selectByAddTimePage(request.getLastId(), request.getStartTime(), request.getEndTime(), request.getPageIndex(), request.getPageSize());
        FinancialStatementJobResponse response = new FinancialStatementJobResponse();
        response.setIds(idList);
        return response;
    }

    @Override
    public FinancialStatementJobResponse selectByIdInterval(FinancialStatementJobRequest request) {
        List<Long> idList = uu898UserAssetsRecordGateway.selectByIdInterval(request.getStartId(), request.getEndId(), request.getStartTime(), request.getEndTime(), request.getPageIndex(), request.getPageSize());
        FinancialStatementJobResponse response = new FinancialStatementJobResponse();
        response.setIds(idList);
        return response;
    }

    @Override
    public FinancialStatementJobResponse selectMaxId() {
        Long maxId = uu898UserAssetsRecordGateway.selectMaxId();
        FinancialStatementJobResponse response = new FinancialStatementJobResponse();
        response.setMaxId(maxId);
        return response;
    }


    @Override
    public void handleUserAssetsRecordId(FinancialStatementJobRequest request) {
        //资金Id
        Long userAssetsRecordId = request.getId();
        log.info("[资金对账] 资金Id:{}", userAssetsRecordId);
        //查询资金明细数据
        UU898UserAssetsRecordDTO userAssetsRecordDTO = uu898UserAssetsRecordGateway.selectById(userAssetsRecordId);
        if (null == userAssetsRecordDTO) {
            log.error("[资金对账] 资金Id:{} 资金明细不存在", userAssetsRecordId);
            return;
        }
        //用户id
        Long userId = userAssetsRecordDTO.getUserId();
        //查询分账信息
        List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(userId, userAssetsRecordId, null);
        if (userAccountRecordMemberList.isEmpty()) {
            log.error("[资金对账] 资金Id:{} 用户Id:{} 分账信息不存在", request.getId(), userId);
            return;
        }
        //比对分账信息
        //金额
        BigDecimal thisMoneyA = userAssetsRecordDTO.getThisMoney(), thisMoneyB = BigDecimal.ZERO;
        //求购金额
        BigDecimal thisPurchaseMoneyA = userAssetsRecordDTO.getThisPurchaseMoney(), thisPurchaseMoneyB = BigDecimal.ZERO;
        //渠道
        Integer payChannelA = userAssetsRecordDTO.getPayChannel(), payChannelB = null;
        //状态
        Integer statusA = userAssetsRecordDTO.getStatus(), statusB = null;

        for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberList) {
            if (userAccountRecordMember.getAccountType().equals(AccountTypeEnum.BALANCE_1.getCode()) || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.BALANCE_2.getCode())) {
                thisMoneyB = thisMoneyB.add(userAccountRecordMember.getBalanceChange());
            }
            if (userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode())
                    || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode())
                    || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode())
                    || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode())) {
                thisPurchaseMoneyB = thisPurchaseMoneyB.add(userAccountRecordMember.getBalanceChange());
            }
            if (userAccountRecordMember.getPayChannel() != null) {
                payChannelB = userAccountRecordMember.getPayChannel();
            }
            if (userAccountRecordMember.getStatus() != null) {
                statusB = userAccountRecordMember.getStatus();
            }
        }
        //数据比对
        if (thisMoneyA.compareTo(thisMoneyB) != 0) {
            log.error("[资金对账] 资金Id:{} 用户Id:{} 资金明细金额不匹配,资金明细金额:{},分账金额:{}", request.getId(), userId, thisMoneyA, thisMoneyB);
        }
        if (thisPurchaseMoneyA.compareTo(thisPurchaseMoneyB) != 0) {
            log.error("[资金对账] 资金Id:{} 用户Id:{} 求购金额不匹配,资金明细金额:{},分账金额:{}", request.getId(), userId, thisPurchaseMoneyA, thisPurchaseMoneyB);
        }
        if (payChannelB != null && payChannelA.compareTo(payChannelB) != 0) {
            log.warn("[资金对账] 资金Id:{} 用户Id:{} 渠道不匹配,资金明细渠道:{},分账渠道:{}", request.getId(), userId, payChannelA, payChannelB);
        }
        if (statusB != null && statusA.compareTo(statusB) != 0) {
            log.warn("[资金对账] 资金Id:{} 用户Id:{} 状态不匹配,资金明细状态:{},分账状态:{}", request.getId(), userId, statusA, statusB);
        }
    }


}
