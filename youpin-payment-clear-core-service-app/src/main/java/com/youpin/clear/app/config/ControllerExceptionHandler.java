package com.youpin.clear.app.config;

import com.uu898.youpin.commons.Logger;
import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.List;
import java.util.Objects;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@SuppressWarnings({"rawtypes"})
public class ControllerExceptionHandler {
    /**
     * 未知的异常类
     *
     * @param ex ex
     * @return Result
     */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Exception.class)
    public Result handleException(Exception ex) {
        Logger.error("ControllerExceptionHandler UNKNOWN_ERROR {} {}", ex.getClass(), ex.getMessage());
        return Result.fail(Status.UNKNOWN);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result handleMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        return Result.fail(Status.NOT_SUPPORT);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({PaymentClearBusinessException.class, BusinessException.class})
    public Result noStackTraceExceptionHandler(BusinessException e) {
        Logger.error("业务异常 error 详情：[{}]", e.getMessage());
        return Result.fail(e.getCode(), e.getMessage());
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Result handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        Logger.error("param analysis error", e);
        return Result.fail(Status.REQUEST_PARAM_ERROR);
    }

    /**
     * 接收参数校验不通过
     *
     * @param ex MethodArgumentNotValidException
     * @return Result
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
        StringBuilder errorMessage = new StringBuilder();
        allErrors.forEach(x -> errorMessage.append(x.getDefaultMessage()).append(","));
        errorMessage.deleteCharAt(errorMessage.length() - 1);
        String error = errorMessage.toString();
        return Result.fail(Status.REQUEST_PARAM_ERROR.getCode(),
                StringUtils.isEmpty(error) ? Status.REQUEST_PARAM_ERROR.getMsg() : error);
    }

    /**
     * 接受参数类型转换报错处理
     *
     * @param e MethodArgumentTypeMismatchException
     * @return Result
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public Result handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        Logger.error("参数转换失败，方法：" + Objects.requireNonNull(e.getParameter().getMethod()).getName() + "，参数：" +
                e.getName() + "，信息：" + e.getLocalizedMessage());
        return Result.fail(Status.REQUEST_PARAM_TYPE_ERROR.getCode(), e.getMessage());
    }
}
