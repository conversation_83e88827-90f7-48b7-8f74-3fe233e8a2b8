package com.youpin.clear.app.converter;

import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.client.response.financial.UserAssetsRecordResponse;
import com.youpin.clear.common.enums.FinancialTypeEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.domain.dto.FinancialProcessorAssetInfoDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface FinancialTransactionConvertor {
    FinancialTransactionConvertor MAPPER = Mappers.getMapper(FinancialTransactionConvertor.class);

    FinancialResponse toFinancialResponse(FinancialProcessorResultDTO processorResultDTO);

    default FinancialProcessorDTO toFinancialProcessorResultDTO(PayFinancialRequest request, FinancialTypeEnum financialTypeEnum) {
        FinancialProcessorDTO dto = new FinancialProcessorDTO();
        dto.setOrderNo(request.getOrderNo());
        dto.setPayOrderNo(request.getPayOrderNo());
        dto.setSerialNo(request.getSerialNo());
        dto.setNetStatusEnum(NetStatusEnum.getNetStatusEnum(request.getStatus()));
        dto.setCollectType(request.getCollectType());
        dto.setMerchantId(request.getMerchantId());
        dto.setCurrency(request.getCurrency());
        dto.setSubBusTypeFrontEnum(SubBusTypeFrontEnum.getByCode(request.getSubBusType()));
        dto.setFinancialTypeEnum(financialTypeEnum);
        List<ClearAssetInfoRequest> assetInfoList = request.getAssetInfoList();
        List<FinancialProcessorAssetInfoDTO> assetInfoDTOList = new ArrayList<>();
        for (ClearAssetInfoRequest item : assetInfoList) {
            FinancialProcessorAssetInfoDTO dtoInfo = new FinancialProcessorAssetInfoDTO();
            dtoInfo.setUserId(item.getUserId());
            dtoInfo.setMoney(item.getMoney());
            dtoInfo.setTypeId(item.getTypeId());
            dtoInfo.setPayChannel(item.getPayChannel());
            dtoInfo.setBalanceStrategy(item.getBalanceStrategy());

            assetInfoDTOList.add(dtoInfo);
        }
        dto.setAssetInfoDTOList(assetInfoDTOList);
        return dto;
    }

    default FinancialProcessorDTO toFinancialProcessorResultDTOByRefund(RefundFinancialRequest request, FinancialTypeEnum financialTypeEnum) {
        FinancialProcessorDTO dto = new FinancialProcessorDTO();
        dto.setOrderNo(request.getOrderNo());
        dto.setPayOrderNo(request.getPayOrderNo());
        dto.setSerialNo(request.getSerialNo());
        dto.setNetStatusEnum(NetStatusEnum.getNetStatusEnum(request.getStatus()));
        dto.setCollectType(request.getCollectType());
        dto.setMerchantId(request.getMerchantId());
        dto.setCurrency(request.getCurrency());
        dto.setSubBusTypeFrontEnum(SubBusTypeFrontEnum.getByCode(request.getSubBusType()));
        dto.setFinancialTypeEnum(financialTypeEnum);
        dto.setIsChannelSuccess(request.getIsChannelSuccess());

        List<ClearAssetInfoRequest> assetInfoList = request.getAssetInfoList();
        List<FinancialProcessorAssetInfoDTO> assetInfoDTOList = new ArrayList<>();
        for (ClearAssetInfoRequest item : assetInfoList) {
            FinancialProcessorAssetInfoDTO dtoInfo = new FinancialProcessorAssetInfoDTO();
            dtoInfo.setUserId(item.getUserId());
            dtoInfo.setMoney(item.getMoney());
            dtoInfo.setTypeId(item.getTypeId());
            dtoInfo.setPayChannel(item.getPayChannel());
            dtoInfo.setBalanceStrategy(item.getBalanceStrategy());

            assetInfoDTOList.add(dtoInfo);
        }
        dto.setAssetInfoDTOList(assetInfoDTOList);
        return dto;
    }

    default FinancialProcessorDTO toFinancialProcessorResultDTOBySettlement(SettlementFinancialRequest request, FinancialTypeEnum financialTypeEnum) {
        FinancialProcessorDTO dto = new FinancialProcessorDTO();
        dto.setOrderNo(request.getOrderNo());
        dto.setPayOrderNo(request.getPayOrderNo());
        dto.setSerialNo(request.getSerialNo());
        dto.setNetStatusEnum(NetStatusEnum.getNetStatusEnum(request.getStatus()));
        dto.setCollectType(request.getCollectType());
        dto.setMerchantId(request.getMerchantId());
        dto.setCurrency(request.getCurrency());
        dto.setSubBusTypeFrontEnum(SubBusTypeFrontEnum.getByCode(request.getSubBusType()));
        dto.setFinancialTypeEnum(financialTypeEnum);
        if (Boolean.TRUE.equals(request.getHardProcess())) {
            dto.setHardProcess(request.getHardProcess());
        }
        dto.setSettleAccountType(request.getSettleAccountType());

        List<ClearAssetInfoRequest> assetInfoList = request.getAssetInfoList();
        List<FinancialProcessorAssetInfoDTO> assetInfoDTOList = new ArrayList<>();
        for (ClearAssetInfoRequest item : assetInfoList) {
            FinancialProcessorAssetInfoDTO dtoInfo = new FinancialProcessorAssetInfoDTO();
            dtoInfo.setUserId(item.getUserId());
            dtoInfo.setMoney(item.getMoney());
            dtoInfo.setTypeId(item.getTypeId());
            dtoInfo.setPayChannel(item.getPayChannel());
            dtoInfo.setBalanceStrategy(item.getBalanceStrategy());
            dtoInfo.setBalanceStrategy(item.getBalanceStrategy());

            assetInfoDTOList.add(dtoInfo);
        }
        dto.setAssetInfoDTOList(assetInfoDTOList);
        return dto;
    }

    List<UserAssetsRecordResponse> listToQueryUserAssetsRecordResponse(List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOList);

    UserAssetsRecordResponse toQueryUserAssetsRecordResponse(UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO);

    default FinancialProcessorDTO toFinancialProcessorResultDTOByWithdrawal(WithdrawalFinancialRequest request, FinancialTypeEnum financialTypeEnum) {
        FinancialProcessorDTO dto = new FinancialProcessorDTO();
        dto.setOrderNo(request.getOrderNo());
        dto.setPayOrderNo(request.getPayOrderNo());
        dto.setSerialNo(request.getSerialNo());
        dto.setNetStatusEnum(NetStatusEnum.getNetStatusEnum(request.getStatus()));
        dto.setCollectType(request.getCollectType());
        dto.setMerchantId(request.getMerchantId());
        dto.setCurrency(request.getCurrency());
        dto.setSubBusTypeFrontEnum(SubBusTypeFrontEnum.getByCode(request.getSubBusType()));
        dto.setFinancialTypeEnum(financialTypeEnum);

        List<ClearAssetInfoRequest> assetInfoList = request.getAssetInfoList();
        List<FinancialProcessorAssetInfoDTO> assetInfoDTOList = new ArrayList<>();
        for (ClearAssetInfoRequest item : assetInfoList) {
            FinancialProcessorAssetInfoDTO dtoInfo = new FinancialProcessorAssetInfoDTO();
            dtoInfo.setUserId(item.getUserId());
            dtoInfo.setMoney(item.getMoney());
            dtoInfo.setTypeId(item.getTypeId());
            dtoInfo.setPayChannel(item.getPayChannel());
            assetInfoDTOList.add(dtoInfo);
        }
        dto.setAssetInfoDTOList(assetInfoDTOList);
        return dto;
    }
}
