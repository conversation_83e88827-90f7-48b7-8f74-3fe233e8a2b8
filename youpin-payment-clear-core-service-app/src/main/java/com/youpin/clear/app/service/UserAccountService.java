package com.youpin.clear.app.service;

import com.youpin.clear.client.request.CheckAccountRequest;
import com.youpin.clear.client.request.OpenAccountRequest;
import com.youpin.clear.client.request.UserAccountInfoRequest;
import com.youpin.clear.client.response.CheckAccountResponse;
import com.youpin.clear.client.response.UserAccountInfoResponse;

import java.util.List;

public interface UserAccountService {
    void createUserAccount(OpenAccountRequest request);

    void syncUserAccount(OpenAccountRequest request);

    UserAccountInfoResponse queryUserAccount(UserAccountInfoRequest request);

    List<CheckAccountResponse> checkBatchUserAccount(CheckAccountRequest request);

    Boolean checkUserAccount(CheckAccountRequest request);
    Boolean checkUserAccount2(CheckAccountRequest request);


    void transferAccountMoney1ToMoney2();

    /**
     * 补全用户账户
     *
     * @param request 请求
     */
    void completionUserAccount(OpenAccountRequest request);


    void initUserWithdrawMoney(OpenAccountRequest request);
}
