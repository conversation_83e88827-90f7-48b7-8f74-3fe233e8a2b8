package com.youpin.clear.app.converter;

import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import com.youpin.clear.domain.dto.UserAssetsRecordMessageTest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(componentModel = "spring", uses = {TypeConversionWorker.class})
public interface UserAssetsRecordConvertor {

    UserAssetsRecordConvertor MAPPER = Mappers.getMapper(UserAssetsRecordConvertor.class);

    @Mapping(target = "addTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getAddTime()))")
    @Mapping(target = "completeTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getCompleteTime()))")
    @Mapping(target = "payWaitExpireTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getPayWaitExpireTime()))")
    UU898UserAssetsRecordDTO toUserAssetsRecordDTO(UserAssetsRecordMessage userAssetsRecordMessage);


    List<UU898UserAssetsRecordDTO> toUserAssetsRecordDTOList(List<UserAssetsRecordMessage> userAssetsRecordMessageList);


    @Mapping(target = "addTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getAddTime()))")
    @Mapping(target = "completeTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getCompleteTime()))")
    @Mapping(target = "payWaitExpireTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getPayWaitExpireTime()))")
    UU898UserAssetsRecordDTO toUserAssetsRecordDTOTest(UserAssetsRecordMessageTest userAssetsRecordMessage);

    List<UU898UserAssetsRecordDTO> toUserAssetsRecordDTOListTest(List<UserAssetsRecordMessageTest> requestList);

    List<ClearUserAssetsRecordDTO> toClearUserAssetsRecordDTOList(List<UserAssetsRecordMessage> userAssetsRecordMessageList);

    @Mapping(target = "addTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getAddTime()))")
    @Mapping(target = "completeTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getCompleteTime()))")
    @Mapping(target = "payWaitExpireTime", expression = "java(TypeConversionWorker.netDateStrToLocalDateTime(userAssetsRecordMessage.getPayWaitExpireTime()))")
    @Mapping(target = "userAssetsRecordId", source = "id")
    @Mapping(target = "userAssetsRecordNo", source = "id")
    ClearUserAssetsRecordDTO toClearUserAssetsRecordDTO(UserAssetsRecordMessage userAssetsRecordMessage);

    @Mapping(target = "userAssetsRecordNo", source = "id")
    @Mapping(target = "userAssetsRecordId", source = "id")
    ClearUserAssetsRecordDTO toClearUserAssetsRecordDTOByUU898(UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO);
}

