package com.youpin.clear.app.consumer;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.UserBalanceChannelCheckService;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.UserBalanceAssetsCheckMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
import youpin.commons.rocketmq.core.RocketMQListener;

import java.util.List;

/**
 * 用户资金渠道对账
 * 消费
 */
@Slf4j
@Component
@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ,topic = MQConfig.PAYMENT_CLEAR_COMMON_TOPIC, expression = MQConfig.USER_BALANCE_ASSETS_CHECK_TAG, consumerGroup = MQConfig.USER_BALANCE_CHANNEL_CHECK_TAG_GROUP, filterExpressionType = FilterExpressionType.TAG)
public class UserBalanceChannelCheckConsumer implements RocketMQListener<String> {


    @Autowired
    UserBalanceChannelCheckService userBalanceChannelCheckService;

    @Override
    public void onMessage(String message) {
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[用户资金渠道对账] 监听消息:接收到了空消息");
            }
            log.info("[用户资金渠道对账] 监听消息:接收到了消息:{}", message);
            List<UserBalanceAssetsCheckMessage> userBalanceAssetsCheckMessageList = JSON.parseArray(message, UserBalanceAssetsCheckMessage.class);
            userBalanceChannelCheckService.handle(userBalanceAssetsCheckMessageList);
        } catch (Exception e) {
            log.error("[用户资金渠道对账] 用户资金渠道对账 异常:{}", ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_CONSUMER_FAILED);
        }
    }
}
