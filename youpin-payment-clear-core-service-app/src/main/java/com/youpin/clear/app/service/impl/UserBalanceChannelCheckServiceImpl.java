package com.youpin.clear.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.UserBalanceChannelCheckService;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.member.BillItemMemberExtension;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.UserBalanceAssetsCheckMessage;
import com.youpin.clear.domain.feign.YeePayFeignService;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import com.youpin.clear.domain.servcie.DingTalkService;
import com.youpin.payment.gateway.client.request.yee.PaySystemQueryDTO;
import com.youpin.payment.gateway.client.response.yee.PaySystemQueryRemitOrderQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class UserBalanceChannelCheckServiceImpl implements UserBalanceChannelCheckService {

    @Autowired
    YeePayFeignService yeePayFeignService;

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Autowired
    DingTalkService dingTalkService;

    final static List<Integer> payChannel = List.of(
            DoNetPayChannelEnum.Alipay.getCode(),
            DoNetPayChannelEnum.AlipayFundAccountBook.getCode(),
            DoNetPayChannelEnum.DyPay.getCode(),
            DoNetPayChannelEnum.AlipayRefund.getCode(),
            DoNetPayChannelEnum.DyRefund.getCode(),
            DoNetPayChannelEnum.JD.getCode(),
            DoNetPayChannelEnum.JDRefund.getCode(),
            DoNetPayChannelEnum.YBWithdraw.getCode());

    @Override
    public void handle(List<UserBalanceAssetsCheckMessage> userBalanceAssetsCheckMessageList) {
        for (UserBalanceAssetsCheckMessage userBalanceAssetsCheckMessage : userBalanceAssetsCheckMessageList) {
            if (!payChannel.contains(userBalanceAssetsCheckMessage.getPayChannel())) {
                continue;
            }
            //目前只做易宝
            if (DoNetPayChannelEnum.YBWithdraw.getCode().equals(userBalanceAssetsCheckMessage.getPayChannel())) {
                boolean ybWithdrawFlag = ybWithdraw(userBalanceAssetsCheckMessage);
                if (ybWithdrawFlag) {
                    log.info("[用户资金渠道对账] 易宝提现校验成功:{}", userBalanceAssetsCheckMessage);
                } else {
                    sendDingTalkMessage(userBalanceAssetsCheckMessage);
                }
            }
        }
    }

    private void sendDingTalkMessage(UserBalanceAssetsCheckMessage userBalanceAssetsCheckMessage) {

        StringBuilder msg = new StringBuilder();
        Long userId = userBalanceAssetsCheckMessage.getUserId();
        Long userAssetsRecordId = userBalanceAssetsCheckMessage.getUserAssetsRecordId();
        msg.append("用户资金渠道对账: 失败").append("\n");
        msg.append("用户ID: ").append(userId).append("\n");
        msg.append("资金ID: ").append(userAssetsRecordId).append("\n");
        msg.append("订单号: ").append(userBalanceAssetsCheckMessage.getOrderNo()).append("\n");
        msg.append("渠道: ").append(DoNetPayChannelEnum.getByPayChannel(userBalanceAssetsCheckMessage.getPayChannel())).append("\n");
        dingTalkService.sendDingTalkMessage("userBalanceAssetsCheck", msg.toString());
    }


    boolean ybWithdraw(UserBalanceAssetsCheckMessage userBalanceAssetsCheckMessage) {
        Long userId = userBalanceAssetsCheckMessage.getUserId();
        Long userAssetsRecordId = userBalanceAssetsCheckMessage.getUserAssetsRecordId();
        boolean statusFlag = userBalanceAssetsCheckMessage.getStatus().equals(NetStatusEnum.SUCCESS.getCode());
        if (!statusFlag) {
            log.info("[用户资金渠道对账]  易宝提现 状态不是进行中 跳出 用户id:{} 资金id:{}", userId, userAssetsRecordId);
            return false;
        }

        List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(userId, userAssetsRecordId, null);
        if (userAccountRecordMemberList.isEmpty()) {
            log.error("[用户资金渠道对账] 易宝提现 分账信息不存在 用户id:{} 资金id:{}", userId, userAssetsRecordId);
            return false;
        }

        //总金额
        BigDecimal totalMoney = BigDecimal.ZERO;
        //服务费金额
        BigDecimal chargeMoney = BigDecimal.ZERO;
        for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberList) {
            totalMoney = totalMoney.add(userAccountRecordMember.getBalanceChange());
            BillItemMemberExtension billItemMemberExtension = userAccountRecordMember.getBillItemMemberExtension();
            if (null != billItemMemberExtension && null != billItemMemberExtension.getChargeMoney()) {
                chargeMoney = chargeMoney.add(billItemMemberExtension.getChargeMoney());
            }
        }
        //实际比对金额
        BigDecimal actualMoney = totalMoney.abs().subtract(chargeMoney.abs());

        //支付单号
        String payOrderNo = userAccountRecordMemberList.get(0).getPayOrderNo();
        if (StringUtils.isBlank(payOrderNo) || totalMoney.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("[用户资金渠道对账] 易宝提现 对账数据错误 用户id:{} 资金id:{} 金额:{},支付单号:{}", userId, userAssetsRecordId, totalMoney, payOrderNo);
            return false;
        }
        PaySystemQueryDTO dto = new PaySystemQueryDTO();
        dto.setRequestNo(payOrderNo);
        PaySystemQueryRemitOrderQueryResponse ybResult = yeePayFeignService.paySystemQuery(dto);
        //易宝状态
        String ybStatus = ybResult.getStatus();
        //易宝金额
        BigDecimal ybReceiveAmount = ybResult.getReceiveAmount();

        if (ybStatus.equals("SUCCESS")) {
            if (actualMoney.compareTo(ybReceiveAmount) == 0) {
                log.info("[用户资金渠道对账] 易宝提现 对账成功 用户id:{} 资金id:{} 资金明细金额:{},服务费:{},实际到账金额:{}", userId, userAssetsRecordId, totalMoney, chargeMoney, ybReceiveAmount);
            } else {
                log.error("[用户资金渠道对账] 易宝提现 对账失败 用户id:{} 资金id:{} 资金明细金额:{},服务费:{},实际到账金额:{}", userId, userAssetsRecordId, totalMoney, chargeMoney, ybReceiveAmount);
                return false;
            }
        } else {
            log.error("[用户资金渠道对账] 易宝提现 对账失败 用户id:{} 资金id:{} 资金明细金额:{},服务费:{},易宝状态:{} 易宝返回:{}", userId, totalMoney, chargeMoney, userAssetsRecordId, ybStatus, JSON.toJSONString(ybResult));
            return false;
        }
        return true;
    }
}
