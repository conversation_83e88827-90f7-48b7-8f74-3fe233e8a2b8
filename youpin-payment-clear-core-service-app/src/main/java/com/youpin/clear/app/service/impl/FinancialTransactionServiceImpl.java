package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.converter.FinancialTransactionConvertor;
import com.youpin.clear.app.service.FinancialTransactionService;
import com.youpin.clear.client.request.QueryAssetInfoMoneyRequest;
import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.AssetInfoMoney;
import com.youpin.clear.client.response.QueryAssetInfoMoneyResponse;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.client.response.financial.SettleResponse;
import com.youpin.clear.client.response.financial.UserAssetsRecordResponse;
import com.youpin.clear.common.enums.FinancialTypeEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.factory.SubBusFinancialProcessorFactory;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinancialTransactionServiceImpl implements FinancialTransactionService {

    @Autowired
    SubBusFinancialProcessorFactory subBusFinancialProcessorFactory;


    @Autowired
    UU898UserAssetsRecordGateway uu898UserAssetsRecordGateway;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    /**
     * 支付
     */
    @Override
    public FinancialResponse pay(PayFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTO(request, FinancialTypeEnum.Pay);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    /**
     * 退款
     */
    @Override
    public FinancialResponse refund(RefundFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOByRefund(request, FinancialTypeEnum.Refund);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    /**
     * 结算
     */
    @Override
    public FinancialResponse settlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Settlement);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        FinancialResponse financialResponse = FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
        SettleResponse settleResponse = SettleResponse.builder()
                .assetInfoMoneyList(buildAssetList(financialProcessorDTO))
                .build();
        financialResponse.setSettleResponse(settleResponse);
        return financialResponse;
    }

    /**
     * 特殊结算
     */
    @Override
    public FinancialResponse specialSettlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Special_Settlement);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        FinancialResponse financialResponse = FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
        SettleResponse settleResponse = SettleResponse.builder()
                .assetInfoMoneyList(buildAssetList(financialProcessorDTO))
                .build();
        financialResponse.setSettleResponse(settleResponse);
        return financialResponse;
    }

    /**
     * 查询
     */
    @Override
    public List<UserAssetsRecordResponse> query(QueryFinancialRequest request) {
        List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOList = uu898UserAssetsRecordGateway.queryUserAssetsRecordDTOList(request.getUserId(), request.getSerialNo(), request.getOrderNo(), request.getPayOrderNo(), request.getTypeId(), request.getStatus());
        return FinancialTransactionConvertor.MAPPER.listToQueryUserAssetsRecordResponse(uu898UserAssetsRecordDTOList);
    }

    /**
     * 提现
     */
    @Override
    public FinancialResponse withdrawal(WithdrawalFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOByWithdrawal(request, FinancialTypeEnum.Withdrawal);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    @Override
    public QueryAssetInfoMoneyResponse queryAssetInfoMoney(QueryAssetInfoMoneyRequest request) {
        List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOS = uu898UserAssetsRecordGateway.queryUserAssetsRecordDTOList(null, request.getOrderNo(), request.getPayOrderNo());
        if (CollectionUtils.isEmpty(uu898UserAssetsRecordDTOS)) {
            return QueryAssetInfoMoneyResponse.builder()
                    .hasData(false)
                    .build();
        }
        // 获取需要使用的 typeId
        List<Integer> queryTypeIdMoneyList = paymentClearParamsConfig.getQueryTypeIdMoneyList();
        //List 根据字段 转换  List AssetInfoMoney
        List<AssetInfoMoney> assetInfoMoneyList = new ArrayList<>();
        //根据 getTypeId 汇总金额
        uu898UserAssetsRecordDTOS.stream().filter(uu898UserAssetsRecordDTO -> queryTypeIdMoneyList.contains(uu898UserAssetsRecordDTO.getTypeId()))
                .filter(uu898UserAssetsRecordDTO -> uu898UserAssetsRecordDTO.getUserId().equals(request.getBuyerId()) || uu898UserAssetsRecordDTO.getUserId().equals(request.getSellerId()))
                .collect(Collectors.groupingBy(UU898UserAssetsRecordDTO::getTypeId)).forEach((typeId, list) -> {
                    BigDecimal reduce = list.stream().map(uu898UserAssetsRecordDTO -> uu898UserAssetsRecordDTO.getThisMoney().abs().add(uu898UserAssetsRecordDTO.getThisPurchaseMoney().abs())).reduce(BigDecimal.ZERO, BigDecimal::add);
                    assetInfoMoneyList.add(AssetInfoMoney.builder().money(reduce).typeId(typeId).build());
                });
        if (CollectionUtils.isEmpty(assetInfoMoneyList)) {
            return QueryAssetInfoMoneyResponse.builder()
                    .hasData(false)
                    .build();
        }
        return QueryAssetInfoMoneyResponse.builder().hasData(true).assetInfoMoneyList(assetInfoMoneyList).build();
    }

    private List<AssetInfoMoney> buildAssetList(FinancialProcessorDTO financialProcessorDTO) {
        AssetInfoMoney assetInfo247 = AssetInfoMoney.builder()
                .typeId(UserAssetsTypeEnum.TYPE_247.getTypeId())
                .money(Objects.requireNonNullElse(financialProcessorDTO.getSettleFrozeMoney(), BigDecimal.ZERO))
                .build();

        AssetInfoMoney assetInfo256 = AssetInfoMoney.builder()
                .typeId(UserAssetsTypeEnum.TYPE_256.getTypeId())
                .money(Objects.requireNonNullElse(financialProcessorDTO.getPrivateSettleFrozeMoney(), BigDecimal.ZERO))
                .build();
        return List.of(assetInfo247, assetInfo256);
    }


}
