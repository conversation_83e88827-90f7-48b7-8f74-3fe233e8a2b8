package com.youpin.clear.app.consumer;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.PolarToClearAssetsRecordService;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
import youpin.commons.rocketmq.core.RocketMQListener;

import java.util.List;

/**
 * 资金明细消费拆解
 */
@Slf4j
@Component
@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ, topic = MQConfig.USER_ACCOUNT_RECORD_TOPIC, expression = MQConfig.USER_ACCOUNT_RECORD_TAG, consumerGroup = MQConfig.USER_ACCOUNT_RECORD_CONSUMER_TWO_GROUP, filterExpressionType = FilterExpressionType.TAG)
public class UserAssetsRecordSplitConsumer implements RocketMQListener<String> {


    @Autowired
    PolarToClearAssetsRecordService polarToClearAssetsRecordService;

    @Autowired
    CompensationRecordGateway compensationRecordGateway;

    @Override
    public void onMessage(String msg) {

        if (StringUtils.isEmpty(msg)) {
            log.error("[资金明细消费拆解] 监听消息:接收到了空消息");
            return;
        }
        List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList = null;
        try {
            List<UserAssetsRecordMessage> userAssetsRecordMessageList = JSON.parseArray(msg, UserAssetsRecordMessage.class);
            userAssetsRecordDTOList = UserAssetsRecordConvertor.MAPPER.toClearUserAssetsRecordDTOList(userAssetsRecordMessageList);
            polarToClearAssetsRecordService.userAssetsRecordSplit(userAssetsRecordDTOList);
        } catch (Exception e) {
            log.info("[资金明细消费拆解] 资金类型ID缺失:{}", ExceptionUtils.getStackTrace(e));
            // 入补偿表
            if (userAssetsRecordDTOList != null) {
                saveSplitCompensationRecord(userAssetsRecordDTOList, BizCompensationSceneEnum.ASSET_SPLIT, "asset_split", true);
            }
        }
        log.info("[资金明细消费拆解] 完成");
        try {
            polarToClearAssetsRecordService.sendTransactionServiceFeeOperationRecord(userAssetsRecordDTOList);
        } catch (Exception e) {
            log.info("[交易服务费流水] 交易服务费流水:{}", ExceptionUtils.getStackTrace(e));
            // 入补偿表
            if (userAssetsRecordDTOList != null) {
                saveSplitCompensationRecord(userAssetsRecordDTOList, BizCompensationSceneEnum.TRANSACTION_SERVICE_FEE_STATEMENT_TOPIC, "TSFS_TOPIC", false);
            }
        }
    }

    /**
     * 保存异常记录
     */
    private void saveSplitCompensationRecord(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList, BizCompensationSceneEnum bizCompensationSceneEnum, String code, boolean isException) {
        String uniqueKey = String.format("%s_%s_%s", code, userAssetsRecordDTOList.get(0).getUserId(), userAssetsRecordDTOList.get(0).getOrderNo());
        try {
            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(userAssetsRecordDTOList), bizCompensationSceneEnum);
        } catch (Exception e) {
            log.error("[资金明细消费拆解] {} 错误 保存补偿表错误 Error:{}", uniqueKey, ExceptionUtils.getStackTrace(e));
            if (isException) {
                throw new PaymentClearBusinessException(ErrorCode.COMPENSATION_SAVE_FAIL);
            }
        }
        log.info("[资金明细消费拆解] 保存补偿表 完成:{}", uniqueKey);
    }

}