package com.youpin.clear.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.redis.utils.RedisUtils;
import com.youpin.clear.app.service.FinancialTransactionService;
import com.youpin.clear.app.service.TransactionServiceFeeStatementRecordService;
import com.youpin.clear.client.request.TransactionServiceFeeStatementRequest;
import com.youpin.clear.client.request.financial.ClearAssetInfoRequest;
import com.youpin.clear.client.request.financial.PayFinancialRequest;
import com.youpin.clear.client.response.TransactionServiceFeeStatementResponse;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.constant.RedisKey;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.domain.dto.CacheFinanceSummaryData;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.servcie.EmailService;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog;
import com.youpin.clear.infrastructure.feign.impl.SellOrderFeignService;
import com.youpin.clear.infrastructure.mapper.TransactionServiceFeeOperateRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.TransactionServiceFeeOperateRecordMapper;
import com.youpin.clear.infrastructure.mapper.TransactionServiceFeeStatementRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.TransactionServiceFeeStatementRecordMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898TbCaiwuDaylogExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898TbCaiwuDaylogMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordExtMapper;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import com.youpin.clear.infrastructure.utils.ClearRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.youpin.clear.common.enums.UserAssetsTypeEnum.TYPE_227;

@Service
@Slf4j
public class TransactionServiceFeeStatementRecordServiceImpl implements TransactionServiceFeeStatementRecordService {


    public static final String BIG_DECIMAL_ONE = "bigDecimalOne";
    public static final String BIG_DECIMAL_TWO = "bigDecimalTwo";
    public static final String BIG_DECIMAL_THREE = "bigDecimalThree";
    public static final String BIG_DECIMAL_FOUR = "bigDecimalFour";
    public static final String BIG_DECIMAL_FIVE = "bigDecimalFive";

    @Autowired
    SellOrderFeignService sellOrderFeignService;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    TransactionServiceFeeStatementRecordMapper transactionServiceFeeStatementRecordMapper;

    @Autowired
    TransactionServiceFeeStatementRecordExtMapper transactionServiceFeeStatementRecordExtMapper;

    @Autowired
    TransactionServiceFeeOperateRecordMapper transactionServiceFeeOperateRecordMapper;

    @Autowired
    TransactionServiceFeeOperateRecordExtMapper transactionServiceFeeOperateRecordExtMapper;

    @Autowired
    UU898UserAssetsRecordExtMapper userAssetsRecordExtMapper;

//    @Autowired
//    RedissonClient redissonClient;

    @Autowired
    IdWorkService idWorkService;

    @Autowired
    FinancialTransactionService financialTransactionService;

    @Autowired
    UU898TbCaiwuDaylogMapper uu898TbCaiwuDaylogMapper;

    @Autowired
    UU898TbCaiwuDaylogExtMapper uu898TbCaiwuDaylogExtMapper;

    @Autowired
    EmailService emailService;


    /**
     * yyyyMMdd
     */
    static final String DATE_FORMAT_YMD_CODE = "yyyyMMdd";


    @Override
    public TransactionServiceFeeStatementResponse selectMaxId() {
        Long maxId = transactionServiceFeeStatementRecordExtMapper.selectMaxId();
        if (null == maxId) {
            maxId = 0L;
        }
        TransactionServiceFeeStatementResponse response = new TransactionServiceFeeStatementResponse();
        response.setMaxId(maxId);
        return response;
    }


    @Transactional
    @Override
    public TransactionServiceFeeStatementResponse updateSerialNo(TransactionServiceFeeStatementRequest request) {

        LocalDateTime startDate = request.getNowDate().with(LocalTime.MIN).minusNanos(1);
        LocalDateTime endDate = request.getNowDate().with(LocalTime.MAX).plusNanos(1);
        log.info("[交易服务费流水] updateSerialNo maxId:{} pageSize:{} startDate:{} endDate:{}", request.getMaxId(), request.getPageSize(), startDate, endDate);
        String serialNo = idWorkService.getYmdNextIdByWholeKey();
        Integer countBySerialNo = transactionServiceFeeStatementRecordExtMapper.updateSerialNo(serialNo, request.getMaxId() == null ? 0L : request.getMaxId(), request.getPageSize(), startDate, endDate);
        if (null == countBySerialNo || countBySerialNo <= 0) {
            log.warn("[交易服务费流水] updateSerialNo 无数据 不做处理 request:{}", request);
            return createErrorResponse();
        }

        //获取服务费
        BigDecimal feeSumMoney = transactionServiceFeeStatementRecordExtMapper.selectSumMoney(serialNo);
        if (null == feeSumMoney || feeSumMoney.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("[交易服务费流水] updateSerialNo 汇总金额小于0 不做处理 request:{} feeSumMoney:{}", request, feeSumMoney);
            return createErrorResponse();
        }
        String operationDate = localDate2Str(request.getNowDate());
        //记录操作记录
        TransactionServiceFeeOperateRecord operateRecord = insertSelectiveOperateRecord(request.getNowDate(), serialNo, operationDate, feeSumMoney);
        //交易服务费比例
        BigDecimal serviceFeeRatio = paymentClearParamsConfig.getTransactionServiceFeeRatio();
        List<Long> platformUserIdListId = paymentClearParamsConfig.getTransactionServiceFeeStatementPlatformUserIdList();
        if (null == serviceFeeRatio || serviceFeeRatio.compareTo(BigDecimal.ZERO) <= 0 || null == platformUserIdListId || platformUserIdListId.isEmpty()) {
            log.error("[交易服务费流水] updateSerialNo apollo参数错误 serviceFeeRatio:{} platformUserIdListId:{}", serviceFeeRatio, platformUserIdListId);
            return createErrorResponse();
        }

        final String selectEndUserIdKey = String.format("%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_END_USER_CACHE, operationDate);
        //找到上次处理的 用户ID
        Long selectEndUserId = getEndUserIdRedis(selectEndUserIdKey);
        //服务费元转分
        Long feeSumMoneyCent = AmountUtils.convertToCent(feeSumMoney);
        //根据上次处理的用户排序 platformUserIdListId 顺序
        //在这个 selectEndUserId 用户之前的用户排在最后面
        List<Long> platformUserIdSortListId = rearrange(platformUserIdListId, selectEndUserId);
        log.info("[交易服务费流水] updateSerialNo 平台用户ID排序:{} 上次处理的用户:{}", platformUserIdSortListId, selectEndUserId);

        for (Long platformUserId : platformUserIdSortListId) {
            if (platformUserId == null) {
                log.warn("[交易服务费流水] 没有平台用户ID，跳过处理");
                continue;
            }
            final String sumKey = buildRedisKey(RedisKey.TRANSACTION_SERVICE_FEE_STATEMENT_SUM_CACHE, operationDate, platformUserId);
            final String ratioKey = buildRedisKey(RedisKey.TRANSACTION_SERVICE_FEE_STATEMENT_CACHE, operationDate, platformUserId);

            //增加总金额
            long sumMoney = Optional.of(getRedisFee(sumKey)).orElse(ClearConstants.CONSTANT_LONG_0);
            //获取当前比例费用
            long ratioMoney = Optional.of(getRedisFee(ratioKey)).orElse(ClearConstants.CONSTANT_LONG_0);

            boolean checkFlag = checkRatio(sumMoney + feeSumMoneyCent, ratioMoney, serviceFeeRatio);
            log.info("[交易服务费流水] updateSerialNo 分 平台用户:{} 汇总金额:{} 比例金额:{} 比例:{},当前批次金额:{} 判断结果:{}", platformUserId, sumMoney, ratioMoney, serviceFeeRatio, feeSumMoneyCent, checkFlag);
            if (checkFlag) {
                try {
                    //更新操作记录
                    operateRecord.setPlatformUserId(platformUserId);
                    transactionServiceFeeOperateRecordMapper.updateByPrimaryKeySelective(operateRecord);
                    //更新缓存
                    addAndGetRedisFee(sumKey, feeSumMoneyCent);
                    addAndGetRedisFee(ratioKey, feeSumMoneyCent);
                    setEndUserIdRedis(selectEndUserIdKey, platformUserId);
                } catch (Exception e) {
                    log.error("[交易服务费流水] updateSerialNo 缓冲设置失败 request:{} feeSumMoney:{}", request, feeSumMoney);
                    throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "交易服务费流水缓冲设置失败");
                }
                TransactionServiceFeeStatementResponse response = new TransactionServiceFeeStatementResponse();
                response.setCount(countBySerialNo);
                response.setSerialNo(serialNo);
                response.setPlatformUserId(platformUserId);
                log.info("[交易服务费流水] updateSerialNo 更新成功 response:{}", response);
                return response;
            }
        }
        //如果一个都没有匹配到
        log.info("[交易服务费流水] 用户没有匹配到 开始等分到缓存里面   feeSumMoneyCent:{} 分", feeSumMoneyCent);
        //放弃数据
        operateRecord.setStatus(ClearConstants.CONSTANT_INTEGER_3);
        transactionServiceFeeOperateRecordMapper.updateByPrimaryKeySelective(operateRecord);
        log.info("[交易服务费流水] 等分金额 更新状态为 3  操作ID:{}", operateRecord.getId());
        //获取等分金额
        long feeSumMoneyCentDivideEqually = feeSumMoneyCent / platformUserIdListId.size();
        for (int i = 0; i < platformUserIdListId.size(); i++) {
            Long platformUserId = platformUserIdListId.get(i);
            if (platformUserId == null) {
                log.warn("[交易服务费流水] 等分金额 没有平台用户ID，跳过处理");
                continue;
            }
            final String sumKey = buildRedisKey(RedisKey.TRANSACTION_SERVICE_FEE_STATEMENT_SUM_CACHE, operationDate, platformUserId);
//            final String ratioKey = buildRedisKey(RedisKey.TRANSACTION_SERVICE_FEE_STATEMENT_CACHE, operationDate, platformUserId);
            long feeSumMoneyToCentTemp = feeSumMoneyCentDivideEqually;
            if (i == 0) {
                feeSumMoneyToCentTemp = feeSumMoneyCent - (feeSumMoneyCentDivideEqually * (platformUserIdListId.size() - 1));
            }
            addAndGetRedisFee(sumKey, feeSumMoneyToCentTemp);
            log.info("[交易服务费流水] 等分金额 更新缓存 平台用户:{} 等分金额:{} 分", platformUserId, feeSumMoneyToCentTemp);
        }
        //默认
        return createErrorResponse();
    }

    private TransactionServiceFeeOperateRecord insertSelectiveOperateRecord(LocalDateTime createTime, String serialNo, String operationDate, BigDecimal feeSumMoney) {
        TransactionServiceFeeOperateRecord operateRecord = new TransactionServiceFeeOperateRecord();
        operateRecord.setSerialNo(serialNo);
        operateRecord.setOperationDate(Integer.valueOf(operationDate));
        operateRecord.setCreateTime(createTime);
        operateRecord.setTypeId(ClearConstants.CONSTANT_INTEGER_181);
        operateRecord.setOrderFeeType(ClearConstants.CONSTANT_INTEGER_0);
        operateRecord.setFeeMoney(feeSumMoney);
        operateRecord.setStatus(ClearConstants.CONSTANT_INTEGER_2);
        transactionServiceFeeOperateRecordMapper.insertSelective(operateRecord);
        return operateRecord;
    }


    private String buildRedisKey(String prefix, String date, Long userId) {
        return String.format("%s%s:%d", prefix, date, userId);
    }

    private TransactionServiceFeeStatementResponse createErrorResponse() {
        TransactionServiceFeeStatementResponse response = new TransactionServiceFeeStatementResponse();
        response.setCount(ClearConstants.CONSTANT_INTEGER_0);
        return response;
    }


    public static List<Long> rearrange(List<Long> list, Long target) {
        int index = list.indexOf(target);
        if (index == -1) {
            return list;
        }
        index = index + 1;
        List<Long> newList = new ArrayList<>();
        // 将目标元素及其之后的元素添加到新列表
        for (int i = index; i < list.size(); i++) {
            newList.add(list.get(i));
        }
        // 将目标元素之前的元素添加到新列表
        for (int i = 0; i < index; i++) {
            newList.add(list.get(i));
        }

        return newList;
    }

    @Override
    public void handleByJobId(TransactionServiceFeeStatementRequest request) {
        //获取当天的总资金 和 80%的资金
        if (null == request || StringUtils.isBlank(request.getSerialNo())) {
            log.error("[交易服务费流水] handleByJobId 入参为空 ");
            return;
        }
        //查询操作流水数据
        TransactionServiceFeeOperateRecord operateRecord = transactionServiceFeeOperateRecordExtMapper.selectBySerialNo(request.getSerialNo());
        if (null == operateRecord || null == operateRecord.getPlatformUserId() || null == operateRecord.getFeeMoney() || operateRecord.getFeeMoney().compareTo(BigDecimal.ZERO) <= 0) {
            log.error("[交易服务费流水] handleByJobId 操作流水不存在或数据异常,request:{} ,operateRecord:{}", request, operateRecord);
            return;
        }
        serviceFeeToPayAssets(operateRecord);
        log.info("[交易服务费流水] handleByJobId 操作流水成功,request:{} ,operateRecord:{}", request, operateRecord);
    }

    @Override
    public void handleByJobIdRetry() {
        List<TransactionServiceFeeOperateRecord> transactionServiceFeeOperateRecords = transactionServiceFeeOperateRecordExtMapper.selectPageByStatus(ClearConstants.CONSTANT_INTEGER_2);
        for (TransactionServiceFeeOperateRecord transactionServiceFeeOperateRecord : transactionServiceFeeOperateRecords) {
            serviceFeeToPayAssets(transactionServiceFeeOperateRecord);
        }
        log.info("[交易服务费流水] handleByJobIdRetry 操作流水成功");
    }


    @Override
    public void financeSummary(TransactionServiceFeeStatementRequest request) {

        //数据修改
        List<UU898TbCaiwuDaylog> uu898TbCaiwuDaylogList = uu898TbCaiwuDaylogExtMapper
                .selectByTtypeAndDdate(Arrays.asList(ClearConstants.CONSTANT_INTEGER_181, ClearConstants.CONSTANT_INTEGER_182, ClearConstants.CONSTANT_INTEGER_227), request.getNowDate().toLocalDate(), ClearConstants.CONSTANT_INT_0, ClearConstants.CONSTANT_INTEGER_0);
        if (null != uu898TbCaiwuDaylogList && !uu898TbCaiwuDaylogList.isEmpty()) {
            for (UU898TbCaiwuDaylog uu898TbCaiwuDaylog : uu898TbCaiwuDaylogList) {
                if (uu898TbCaiwuDaylog.getDisplaystatus().equals(ClearConstants.CONSTANT_INTEGER_0) && uu898TbCaiwuDaylog.getSource().equals(ClearConstants.CONSTANT_INTEGER_0)) {
                    uu898TbCaiwuDaylogExtMapper.updateDisplayStatusById(uu898TbCaiwuDaylog.getId(), ClearConstants.CONSTANT_INTEGER_1);
                }
            }
        }

        //数据汇总
        LocalDateTime startDate = request.getNowDate().with(LocalTime.MIN).minusNanos(ClearConstants.CONSTANT_INTEGER_1);
        LocalDateTime endDate = request.getNowDate().with(LocalTime.MAX).plusNanos(ClearConstants.CONSTANT_INTEGER_1);
        String operationDate = localDate2Str(request.getNowDate());
        //1.统计20%的非预售交易服务费总金额（181）
        CacheFinanceSummaryData bigDecimalOne = getCacheFinanceSummaryDayByCodeV3(BIG_DECIMAL_ONE, operationDate, ClearConstants.CONSTANT_INTEGER_3);
        //2.统计80%的非预售交易服务费总金额（181） -- 6.统计100%收取交易服务费（227）
        CacheFinanceSummaryData bigDecimalTwo = getCacheFinanceSummaryDayByCodeV3(BIG_DECIMAL_TWO, operationDate, ClearConstants.CONSTANT_INTEGER_1);
        //3.统计100%的预售交易服务费总金额（181）
        CacheFinanceSummaryData bigDecimalThree = getCacheFinanceSummaryDayByCodeV2(BIG_DECIMAL_THREE, operationDate, startDate, endDate, ClearConstants.CONSTANT_INTEGER_1, ClearConstants.CONSTANT_INTEGER_181);
        //4.统计100%非预售退还交易服务费（182）
        CacheFinanceSummaryData bigDecimalFour = getCacheFinanceSummaryDayByCodeV2(BIG_DECIMAL_FOUR, operationDate, startDate, endDate, ClearConstants.CONSTANT_INTEGER_0, ClearConstants.CONSTANT_INTEGER_182);
        //5.统计100%预售退还交易服务费（182）
        CacheFinanceSummaryData bigDecimalFive = getCacheFinanceSummaryDayByCodeV2(BIG_DECIMAL_FIVE, operationDate, startDate, endDate, ClearConstants.CONSTANT_INTEGER_1, ClearConstants.CONSTANT_INTEGER_182);
        //显示20%的非预售交易服务费总金额+100%的预售交易服务费
        List<UU898TbCaiwuDaylog> uu898TbCaiwuDaylogList181 = uu898TbCaiwuDaylogExtMapper.selectByTtypeAndDdate(List.of(ClearConstants.CONSTANT_INTEGER_181), request.getNowDate().toLocalDate(), ClearConstants.CONSTANT_INT_0, ClearConstants.CONSTANT_INTEGER_1);
        if (null == uu898TbCaiwuDaylogList181 || uu898TbCaiwuDaylogList181.isEmpty()) {
            //插入数据
            BigDecimal money = bigDecimalOne.getBalance().add(bigDecimalThree.getBalance());
            UU898TbCaiwuDaylog build181 = UU898TbCaiwuDaylog.builder()
                    .ttype(ClearConstants.CONSTANT_INTEGER_181).typename("交易服务费")
                    .ddate(request.getNowDate().toLocalDate()).alipay(BigDecimal.ZERO).wechat(BigDecimal.ZERO).douyinpay(BigDecimal.ZERO).jd(BigDecimal.ZERO).yibao(BigDecimal.ZERO).money(money.negate()).allmoney(money.negate()).source(ClearConstants.CONSTANT_INTEGER_1).build();
            uu898TbCaiwuDaylogMapper.insertSelective(build181);
        }
        //发送邮件
        //缓存判断是否已经发送过了
        if (!ClearRedisUtils.hasKey(RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_EMAIL_CACHE + operationDate)) {
            log.info("[交易服务费流水] financeSummary 发送邮件开始");
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("1.统计20%的非预售交易服务费总金额（181）：").append(bigDecimalOne.getBalance().setScale(2, RoundingMode.DOWN)).append("\n");
            stringBuilder.append("2.统计80%的非预售交易服务费总金额（181）：").append(bigDecimalTwo.getBalance().setScale(2, RoundingMode.DOWN)).append("\n");
            stringBuilder.append("3.统计100%的预售交易服务费总金额（181）：").append(bigDecimalThree.getBalance().setScale(2, RoundingMode.DOWN)).append("\n");
            stringBuilder.append("4.统计100%非预售退还交易服务费（182）：").append(bigDecimalFour.getBalance().setScale(2, RoundingMode.DOWN)).append("\n");
            stringBuilder.append("5.统计100%预售退还交易服务费（182）：").append(bigDecimalFive.getBalance().setScale(2, RoundingMode.DOWN)).append("\n");
            stringBuilder.append("6.统计100%收取交易服务费（227）：").append(bigDecimalTwo.getBalance().setScale(2, RoundingMode.DOWN)).append("\n");
            stringBuilder.append("---").append(request.getNowDate().toLocalDate()).append("----");
            //发送邮件
            try {
                emailService.sendSimpleMessage(paymentClearParamsConfig.getEmailFinanceToList(), "交易服务费流水汇总" + operationDate, stringBuilder.toString());
            } catch (Exception e) {
                log.error("[交易服务费流水] 邮件发送异常", e);
                throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "交易服务费流水汇总邮件发送异常");
            }
            //设置缓存
            ClearRedisUtils.setKey(RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_EMAIL_CACHE + operationDate, ClearConstants.CONSTANT_INTEGER_1, ClearConstants.CONSTANT_LONG_7, TimeUnit.DAYS);
            log.info("[交易服务费流水] financeSummary 发送邮件结束");
        }
    }

    @Override
    public void financeSummaryClear(TransactionServiceFeeStatementRequest request) {
        String operationDate = localDate2Str(request.getNowDate());
        ClearRedisUtils.delete(String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, BIG_DECIMAL_ONE));
        ClearRedisUtils.delete(String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, BIG_DECIMAL_TWO));
        ClearRedisUtils.delete(String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, BIG_DECIMAL_THREE));
        ClearRedisUtils.delete(String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, BIG_DECIMAL_FOUR));
        ClearRedisUtils.delete(String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, BIG_DECIMAL_FIVE));
        ClearRedisUtils.delete(String.format("%s%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_EMAIL_CACHE, operationDate));

    }


    private CacheFinanceSummaryData getCacheFinanceSummaryDayByCodeV2(String code, String operationDate, LocalDateTime startDate, LocalDateTime endDate, Integer orderFeeType, Integer typeId) {
        final String financeSummaryDayRedisKey = String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, code);
        BigDecimal bigDecimal = null;
        boolean repeatFlag = false;
        if (ClearRedisUtils.hasKey(financeSummaryDayRedisKey)) {
            Object bigDecimalObject = ClearRedisUtils.getValue(financeSummaryDayRedisKey);
            if (ObjectUtils.isNotEmpty(bigDecimalObject)) {
                bigDecimal = new BigDecimal(bigDecimalObject.toString());
                repeatFlag = true;
            }
        }
        if (ObjectUtils.isEmpty(bigDecimal) || bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
            bigDecimal = transactionServiceFeeStatementRecordExtMapper.reportFinanceSummary(startDate, endDate, orderFeeType, typeId);
            ClearRedisUtils.setKey(financeSummaryDayRedisKey, bigDecimal, ClearConstants.CONSTANT_LONG_7, TimeUnit.DAYS);
        }
        return new CacheFinanceSummaryData(bigDecimal, repeatFlag);
    }

    private CacheFinanceSummaryData getCacheFinanceSummaryDayByCodeV3(String code, String operationDate, Integer status) {
        final String financeSummaryDayRedisKey = String.format("%s%s:%s", RedisKey.TRANSACTION_SERVICE_FEE_SUMMARY_DAY_CACHE, operationDate, code);
        BigDecimal bigDecimal = null;
        boolean repeatFlag = false;
        if (ClearRedisUtils.hasKey(financeSummaryDayRedisKey)) {
            Object bigDecimalObject = ClearRedisUtils.getValue(financeSummaryDayRedisKey);
            if (ObjectUtils.isNotEmpty(bigDecimalObject)) {
                bigDecimal = new BigDecimal(bigDecimalObject.toString());
                repeatFlag = true;
            }
        }
        if (ObjectUtils.isEmpty(bigDecimal) || bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
            bigDecimal = transactionServiceFeeOperateRecordExtMapper.reportFinanceSummary(Integer.valueOf(operationDate), status);
            ClearRedisUtils.setKey(financeSummaryDayRedisKey, bigDecimal, ClearConstants.CONSTANT_LONG_7, TimeUnit.DAYS);
        }
        return new CacheFinanceSummaryData(bigDecimal, repeatFlag);
    }


    private void serviceFeeToPayAssets(TransactionServiceFeeOperateRecord operateRecord) {
        String orderNo = operateRecord.getSerialNo();
        String serialNo = operateRecord.getSerialNo();

        //查询资金明细判断是否已经处理
        Integer counted = userAssetsRecordExtMapper.countUserAssetsRecordDTOList(operateRecord.getPlatformUserId(), serialNo, orderNo, null, TYPE_227.getTypeId(), null);
        if (counted > 0) {
            log.info("[交易服务费流水] serviceFeeToPayAssets 资金明细已处理,operateRecord:{} ", operateRecord);
            return;
        }

        PayFinancialRequest payFinancialRequest = new PayFinancialRequest();
        payFinancialRequest.setMerchantId(10001);
        payFinancialRequest.setSubBusType(SubBusTypeFrontEnum.SPECIAL_LOGIC.getCode());
        payFinancialRequest.setSerialNo(serialNo);
        payFinancialRequest.setOrderNo(orderNo);
        payFinancialRequest.setStatus(NetStatusEnum.SUCCESS.getCode());
        payFinancialRequest.setCollectType(1);
        payFinancialRequest.setAssetInfoList(List.of(new ClearAssetInfoRequest(operateRecord.getPlatformUserId(), AmountUtils.convertToCent(operateRecord.getFeeMoney()), TYPE_227.getTypeId(), DoNetPayChannelEnum.Balance.getCode(), null)));
        FinancialResponse financialResponse = null;
        log.info("[交易服务费流水] serviceFeeToPayAssets payFinancialRequest:{}", JSON.toJSONString(payFinancialRequest));
        try {
            financialResponse = financialTransactionService.pay(payFinancialRequest);
        } catch (PaymentClearBusinessException e) {
            if (e.getCode().equals(FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode())) {
                log.warn("[交易服务费流水] serviceFeeToPayAssets 资金明细已处理,operateRecord:{} ", operateRecord);
                operateRecord.setStatus(ClearConstants.CONSTANT_INTEGER_1);
                transactionServiceFeeOperateRecordMapper.updateByPrimaryKeySelective(operateRecord);
            }
        } catch (Exception e) {
            log.error("[交易服务费流水] serviceFeeToPayAssets 交易服务费流水统计错误1 Exception operateRecord:{} {}", operateRecord, ExceptionUtils.getStackTrace(e));
            //处理失败
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "交易服务费流水统计错误1");
        }
        if (null == financialResponse || null == financialResponse.getCode()) {
            log.error("[交易服务费流水] serviceFeeToPayAssets financialResponse is null,operateRecord:{} ", operateRecord);
            //处理失败
            return;
        }
        if (financialResponse.getCode().equals(FinancialProcessorResultDTO.SUCCESS.getCode())) {
            //处理成功
            operateRecord.setStatus(ClearConstants.CONSTANT_INTEGER_1);
            transactionServiceFeeOperateRecordMapper.updateByPrimaryKeySelective(operateRecord);
        } else {
            log.error("[交易服务费流水] serviceFeeToPayAssets financialResponse is fail,operateRecord:{} ", operateRecord);
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "交易服务费流水统计错误2");
        }
    }


    /**
     * @param valueDecimal 分
     * @return 分
     */
    private long addAndGetRedisFee(String redisKey, Long valueDecimal) {
        return RedisUtils.getRedisTemplate().opsForValue().increment(redisKey, valueDecimal);
    }

    private Long getRedisFee(String redisKey) {
        if (!RedisUtils.getRedisTemplate().hasKey(redisKey)) {
            RedisUtils.getRedisTemplate().opsForValue().setIfAbsent(redisKey, 0L, Duration.ofDays(7));
        }
        Object o = RedisUtils.getRedisTemplate().opsForValue().get(redisKey);
        return o == null ? 0L : Long.parseLong(o.toString());
    }

    private Long getEndUserIdRedis(String redisKey) {
        Object o = RedisUtils.getRedisTemplate().opsForValue().get(redisKey);
        return o == null ? 0L : Long.parseLong(o.toString());
    }

    private void setEndUserIdRedis(String redisKey, Long userId) {
        if (!RedisUtils.getRedisTemplate().hasKey(redisKey)) {
            RedisUtils.getRedisTemplate().opsForValue().setIfAbsent(redisKey, 0L, Duration.ofDays(7));
        }
        RedisUtils.getRedisTemplate().opsForValue().set(redisKey, userId);
    }


    public static boolean checkRatio(long sumMoney, long ratioMoney, BigDecimal ratio) {
        BigDecimal checkRatio = new BigDecimal(ratioMoney).divide(new BigDecimal(sumMoney), 2, RoundingMode.DOWN);
        return checkRatio.compareTo(ratio) <= 0;
    }

    static String localDate2Str(LocalDateTime fromDate) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT_YMD_CODE);
        return fromDate.format(df);
    }

    public static void main(String[] args) {


//        List<Long> list = new ArrayList<>(Arrays.asList(1L, 2L, 3L, 4L, 5L));
//        System.out.println(rearrange(list, 2L));
//        System.out.println(rearrange(list, 3L));
//        System.out.println(rearrange(list, 4L));
//        System.out.println(rearrange(list, 5L));
//        System.out.println(rearrange(list, 6L));
//        System.out.println(rearrange(list, 1L));
//        System.out.println(rearrange(list, 0L));


        long sumMoney = 0;
        //获取当前比例费用
        long ratioMoney = 0;
        int count = 0;
        for (int i = 0; i < 100; i++) {
            boolean checked = checkRatio(sumMoney + 1, ratioMoney, new BigDecimal("0.8"));
            if (checked) {
                count = count + 1;
                ratioMoney = ratioMoney + 1;
            }
            sumMoney = sumMoney + 1;
            System.out.println(i + "--总金额:" + sumMoney + "----80%比例金额:" + ratioMoney + "----是否相加:" + checked);

        }
    }


}
