package com.youpin.clear.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.app.service.SeparateAccountService;
import com.youpin.clear.client.request.SeparatePrintLog;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillItemMemberExtension;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountAssetsTypeDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserBalanceAssetsCheckMessage;
import com.youpin.clear.domain.dto.UserOpenAccountMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.factory.SeparateFundingDirectionFactory;
import com.youpin.clear.domain.gateway.*;
import com.youpin.clear.domain.servcie.RocketMqService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.youpin.clear.common.enums.UserAssetsTypeEnum.*;

@Slf4j
@Service
public class SeparateAccountServiceImpl implements SeparateAccountService {

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    SeparateFundingDirectionFactory separateFundingDirectionFactory;

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Autowired
    UserAccountRecordV2Gateway userAccountRecordV2Gateway;

    @Autowired
    UserAccountGateway userAccountGateway;

    @Autowired
    RocketMqService rocketMqService;

    @Autowired
    UserAccountReconciliationRecordGateway userAccountReconciliationRecordGateway;

    @Autowired
    AccountAssetsTypeGateway accountAssetsTypeGateway;


    /**
     * 清理数据
     */
    @Override
    public void userClearData(Long userId) {
        if (null == userId) {
            log.warn("用户id为空，不进行清理");
            return;
        }
        userAccountGateway.deleteByUserId(userId);
        log.info("[删除用户账户信息] userId:{}", userId);
        userAccountRecordGateway.deleteByUserId(userId);
        log.info("[删除用户分账信息] userId:{}", userId);
        userAccountReconciliationRecordGateway.deleteByUserId(userId);
        log.info("[删除用户对账记录] userId:{}", userId);
    }

    /**
     * 数据幂等判断
     * true 重复 false 不重复
     */
    @Override
    public boolean checkIdempotentUserAssetsRecordIdempotent(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList) {
        for (UU898UserAssetsRecordDTO userAssetsRecordDTO : userAssetsRecordMessageDTOList) {
            List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(userAssetsRecordDTO.getUserId(), userAssetsRecordDTO.getId(), null);
            if (CollectionUtils.isEmpty(userAccountRecordMemberList)) {
                return false;
            }
            for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberList) {
                // 如果数据库状态和消息状态不一致
                if (!userAccountRecordMember.getStatus().equals(userAssetsRecordDTO.getStatus())) {
                    //如果数据库是 成功 /或者 是 失败 可以认为是重复处理
                    if (userAccountRecordMember.getStatus().equals(NetStatusEnum.SUCCESS.getCode()) || userAccountRecordMember.getStatus().equals(NetStatusEnum.FAIL.getCode())) {
                        continue;
                    }
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void userAssetsRecordMessageTest(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList, Boolean separateTransactionTest) {
        //数据抽象
        List<BillMember> billMemberListSum = fundingDirectionList(userAssetsRecordMessageDTOList);
        //需要循环的分账数据
        List<BillMember> billMemberList = billMemberListSum.stream().filter(item -> item.getIsReference().equals(Boolean.FALSE)).collect(Collectors.toList());
        //参考数据
        List<BillMember> billMemberListIsReference = billMemberListSum.stream().filter(item -> item.getIsReference().equals(Boolean.TRUE)).collect(Collectors.toList());
        //获取账户信息
        Map<Long, AccountAggregate> accountAggregateMap = getAccountAggregatesMap(billMemberList);
        //账户校验
        if (accountAggregateMap.isEmpty()) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_NOT_EXIST);
        }
        //分账处理
        separateAccountHandle(billMemberList, accountAggregateMap, billMemberListIsReference);

        //日志打印
        separatePrintTestLog(billMemberList);

        //事务保存 增加判断
        if (Boolean.TRUE.equals(separateTransactionTest)) {
            saveTransactional(billMemberList, accountAggregateMap);
        }
    }

    @Override
    public void handle(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList) {
        //数据抽象
        List<BillMember> billMemberListSum = fundingDirectionList(userAssetsRecordMessageDTOList);
        //需要循环的分账数据
        List<BillMember> billMemberList = billMemberListSum.stream().filter(item -> item.getIsReference().equals(Boolean.FALSE)).collect(Collectors.toList());
        //参考数据
        List<BillMember> billMemberListIsReference = billMemberListSum.stream().filter(item -> item.getIsReference().equals(Boolean.TRUE)).collect(Collectors.toList());

        //处理参考数据

        //获取账户信息
        Map<Long, AccountAggregate> accountAggregateMap = getAccountAggregatesMap(billMemberList);
        //账户校验
        if (accountAggregateMap.isEmpty()) {
            log.warn("[分账处理] 账户不存在,数据暂时不处理");
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_NOT_EXIST);
        }
        //分账处理
        separateAccountHandle(billMemberList, accountAggregateMap, billMemberListIsReference);

        //日志打印
        separatePrintLog(billMemberList);

        //事务保存
        saveTransactional(billMemberList, accountAggregateMap);

        //发送对账信息
        sendCheckInfo(billMemberList);
    }

    /**
     * 发送对账信息
     */
    private void sendCheckInfo(List<BillMember> billMemberList) {
        if (!paymentClearParamsConfig.getSendCheckInfo().equals(Boolean.TRUE)) {
//            log.info("[分账处理] 未开启发送对账信息");
            return;
        }
        // 剔除不需要校验的 平台账户
        List<Integer> platformTypeIdList;
        if (!paymentClearParamsConfig.getPlatformAccountCheck()) {
            platformTypeIdList = paymentClearParamsConfig.getPlatformTypeIdList();
        } else {
            platformTypeIdList = List.of();
        }
        List<UserBalanceAssetsCheckMessage> list = billMemberList.stream().filter(item -> !platformTypeIdList.contains(item.getTypeId())).map(item -> {
            UserBalanceAssetsCheckMessage userBalanceAssetsCheckMessage = new UserBalanceAssetsCheckMessage();
            userBalanceAssetsCheckMessage.setUserId(item.getUserId());
            userBalanceAssetsCheckMessage.setTypeId(item.getTypeId());
            userBalanceAssetsCheckMessage.setPayChannel(item.getBillItemMember().getPayChannelEnum().getCode());
            userBalanceAssetsCheckMessage.setUserAssetsRecordId(item.getBillItemMember().getUserAssetsRecordId());
            userBalanceAssetsCheckMessage.setOrderNo(item.getBillItemMember().getOrderNo());
            userBalanceAssetsCheckMessage.setStatus(item.getBillItemMember().getNetStatusEnum().getCode());
            return userBalanceAssetsCheckMessage;
        }).distinct().collect(Collectors.toList());
        //发送对账信息
        rocketMqService.sendDynamicsMQMsg(MQConfig.PAYMENT_CLEAR_MQ, MQConfig.PAYMENT_CLEAR_COMMON_TOPIC, MQConfig.USER_BALANCE_ASSETS_CHECK_TAG, JSON.toJSONString(list), Boolean.TRUE);
    }


    private void saveTransactional(List<BillMember> billMemberList, Map<Long, AccountAggregate> accountAggregateMap) {
        //按照用户,账户类型 分组
        Map<Long, Map<Integer, List<UserAccountRecordMember>>> collectMap = billMemberList.stream().map(BillMember::getUserAccountRecordMemberList).flatMap(Collection::stream)
                .collect(Collectors.groupingBy(UserAccountRecordMember::getUserId, Collectors.groupingBy(UserAccountRecordMember::getAccountType)));
        //分组排序
        collectMap.forEach((userId, map) -> map.forEach((accountType, list) -> list.sort(UserAccountRecordMember::compareTo)));
        //得到分组排序
        List<UserAccountRecordMember> userAccountRecordMemberList = collectMap.values().stream().map(Map::values).flatMap(Collection::stream).flatMap(Collection::stream).collect(Collectors.toList());
        //校验金额
        if (userAccountRecordMemberList.stream().anyMatch(item -> item.getBalanceAfter().compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0)) {
            log.error("[分账处理] 分账失败 金额出现负数, 请检查数据,billMember:{}", JSON.toJSONString(billMemberList));
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_DISTRIBUTION_FAIL);
        }
        userAccountRecordV2Gateway.userAccountRecordMemberTransactional(userAccountRecordMemberList, accountAggregateMap);

    }

    private List<BillMember> fundingDirectionList(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList) {
        //获取资金类型
        Map<Integer, AccountAssetsTypeDTO> allAccountAssetsType = gatAllAccountAssetsType();
        //获取资金关系
        Map<String, List<Integer>> allAccountAssetsTypeRelate = gatAllAccountAssetsTypeRelate();

        List<BillMember> billMemberList = new ArrayList<>();
        for (UU898UserAssetsRecordDTO userAssetsRecordDTO : userAssetsRecordMessageDTOList) {

            AccountAssetsTypeDTO accountAssetsTypeDTO = allAccountAssetsType.get(userAssetsRecordDTO.getTypeId());
            if (null == accountAssetsTypeDTO) {
                log.warn("用户资金记录类型不存在，typeId:{}", userAssetsRecordDTO.getTypeId());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_MAPPING_NOT_EXIST);
            }
            BillMember billMember = new BillMember();
            //资金记录
            BillItemMember.BillItemMemberBuilder billItemBuilder = BillItemMember.builder();

            billItemBuilder.userId(userAssetsRecordDTO.getUserId());
            billItemBuilder.userAssetsRecordId(userAssetsRecordDTO.getId());
            billItemBuilder.treadNo(userAssetsRecordDTO.getTreadNo());
            billItemBuilder.serialNo(userAssetsRecordDTO.getSerialNo());
            billItemBuilder.orderNo(userAssetsRecordDTO.getOrderNo());
            billItemBuilder.payOrderNo(userAssetsRecordDTO.getPayOrderNo());

            //资金状态
            billItemBuilder.netStatusEnum(NetStatusEnum.getNetStatusEnum(userAssetsRecordDTO.getStatus()));

            billItemBuilder.payChannelEnum(DoNetPayChannelEnum.getByPayChannel(userAssetsRecordDTO.getPayChannel()));
            billItemBuilder.typeId(userAssetsRecordDTO.getTypeId());
            billItemBuilder.directionEnum(DirectionEnum.getCodeEnum(accountAssetsTypeDTO.getDirection()));
            billItemBuilder.assetsName(accountAssetsTypeDTO.getAssetsName());
            billItemBuilder.assetsTypeEnum(AssetsTypeEnum.getCodeEnum(accountAssetsTypeDTO.getAssetsType()));


            //渠道类型由支付宝改成 余额
            if (userAssetsRecordDTO.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_21) || userAssetsRecordDTO.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_22)) {
                billItemBuilder.payChannelEnum(DoNetPayChannelEnum.Balance);
            }

            billItemBuilder.amountTypeEnum(AmountTypeEnum.AVAILABLE);

            //去掉 + -号
            if (userAssetsRecordDTO.isPurchaseMoney()) {
                //求购
                billItemBuilder.amount(userAssetsRecordDTO.getThisPurchaseMoney().abs());
                billItemBuilder.originalAmount(userAssetsRecordDTO.getThisPurchaseMoney());
            } else if (userAssetsRecordDTO.thisBlockMoney()) {
                //余额冻结
                billItemBuilder.amount(userAssetsRecordDTO.getThisBlockMoney().abs());
                billItemBuilder.originalAmount(userAssetsRecordDTO.getThisBlockMoney());
                billItemBuilder.amountTypeEnum(AmountTypeEnum.FREEZE);
            } else {
                //余额
                billItemBuilder.amount(userAssetsRecordDTO.getThisMoney().abs());
                billItemBuilder.originalAmount(userAssetsRecordDTO.getThisMoney());
            }

            //是否是租赁单
            if (null != userAssetsRecordDTO.getIsLeaseOrder()) {
                billItemBuilder.isLeaseOrder(userAssetsRecordDTO.getIsLeaseOrder());
            }
            //资金关系

            List<Integer> reverseTypeEnumList = allAccountAssetsTypeRelate.get(AccountAssetsTypeRelateEnum.INCOME_FROM_EXPENDITURE.getPrefix() + userAssetsRecordDTO.getTypeId());
            if (null != reverseTypeEnumList) {
                billItemBuilder.typeRelationList(reverseTypeEnumList);
            }
            List<Integer> associatedAdditionList = allAccountAssetsTypeRelate.get(AccountAssetsTypeRelateEnum.ASSOCIATED_ADDITION.getPrefix() + userAssetsRecordDTO.getTypeId());
            if (null != reverseTypeEnumList) {
                billItemBuilder.associatedAdditionList(associatedAdditionList);
            }
            List<Integer> dbAssociatedAdditionList = allAccountAssetsTypeRelate.get(AccountAssetsTypeRelateEnum.DB_ASSOCIATED_ADDITION.getPrefix() + userAssetsRecordDTO.getTypeId());
            if (null != dbAssociatedAdditionList) {
                billItemBuilder.dbAssociatedAdditionList(dbAssociatedAdditionList);
            }
            List<Integer> specialRelationList = allAccountAssetsTypeRelate.get(AccountAssetsTypeRelateEnum.SPECIAL.getPrefix() + userAssetsRecordDTO.getTypeId());
            if (null != specialRelationList) {
                billItemBuilder.specialRelationList(specialRelationList);
            }

            if (null != CollectTypeEnum.getCollectTypeEnum(userAssetsRecordDTO.getCollectType())) {
                billItemBuilder.collectTypeEnum(CollectTypeEnum.getCollectTypeEnum(userAssetsRecordDTO.getCollectType()));
            }

            billItemBuilder.addTime(userAssetsRecordDTO.getAddTime());
            billItemBuilder.finishTime(userAssetsRecordDTO.getCompleteTime());
            //扩展
            BillItemMemberExtension.BillItemMemberExtensionBuilder billItemMemberExtensionBuilder = BillItemMemberExtension.builder();
            billItemMemberExtensionBuilder.chargeMoney(userAssetsRecordDTO.getChargeMoney());
            billItemMemberExtensionBuilder.collectType(userAssetsRecordDTO.getCollectType());
            billItemMemberExtensionBuilder.isLeaseOrder(userAssetsRecordDTO.getIsLeaseOrder());
            billItemMemberExtensionBuilder.balance2TransferAmount(userAssetsRecordDTO.getBalance2TransferAmount());


            if (null != userAssetsRecordDTO.getAssetsRecordAccountBillExtInfo() && null != userAssetsRecordDTO.getAssetsRecordAccountBillExtInfo().getMarketingSubsidy()) {
                billItemMemberExtensionBuilder.marketingReduceAmount1(userAssetsRecordDTO.getAssetsRecordAccountBillExtInfo().getMarketingSubsidy());
            }

            billItemBuilder.billItemMemberExtension(billItemMemberExtensionBuilder.build());

            billMember.setUserId(userAssetsRecordDTO.getUserId());
            billMember.setTypeId(userAssetsRecordDTO.getTypeId());
            if (userAssetsRecordDTO.getId().equals(ClearConstants.CONSTANT_LONG_0) && StringUtils.isBlank(userAssetsRecordDTO.getTreadNo())) {
                billMember.setIsReference(Boolean.TRUE);
            }
            billMember.setDirectionEnum(DirectionEnum.getCodeEnum(accountAssetsTypeDTO.getDirection()));
            billMember.setBillItemMember(billItemBuilder.build());
            billMemberList.add(billMember);
        }
        return billMemberList;
    }

    /**
     * 账户缺失兜底创建逻辑
     * account does not exist in tagalog
     */
    @Override
    public void accountDoesNotExistInTagalog(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList) {
        Set<Long> userIdSet = userAssetsRecordMessageDTOList.stream().map(UU898UserAssetsRecordDTO::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
        for (Long userId : userIdSet) {
            try {
                AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(userId);
                if (accountAggregate == null) {
                    log.warn("[用户分账] 账户兜底创建逻辑 userId:{}", userId);
                    rocketMqService.sendDynamicsMQMsg(MQConfig.DEFAULT_PAYMENT_MQ, MQConfig.USER_REGISTER_SUCCESS_TOPIC, MQConfig.USER_REGISTER_TAG, JSON.toJSONString(new UserOpenAccountMessage(userId, "REGISTER")), Boolean.TRUE);
                }
            } catch (Exception e) {
                log.error("[用户分账] 账户兜底创建逻辑 userId: {}, 异常: {}", userId, e.getMessage(), e);
            }
        }
    }


    /**
     * 获取账户信息
     */
    private Map<Long, AccountAggregate> getAccountAggregatesMap(List<BillMember> billMemberList) {
        Set<Long> userIdSet = billMemberList.stream()
                .map(BillMember::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, AccountAggregate> accountAggregateMap = new HashMap<>(userIdSet.size());
        for (Long userId : userIdSet) {
            try {
                AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(userId);
                if (accountAggregate == null) {
                    log.warn("[用户分账] 账户数据为空 userId:{}", userId);
                    return Collections.emptyMap();
                }
                accountAggregateMap.put(userId, accountAggregate);
            } catch (Exception e) {
                log.error("[用户分账] 获取账户数据失败 userId: {}, 异常: {}", userId, e.getMessage(), e);
                return Collections.emptyMap();
            }
        }
        return accountAggregateMap;
    }



    /**
     * 分账处理
     */
    private void separateAccountHandle(List<BillMember> billMemberList, Map<Long, AccountAggregate> accountAggregateMap, List<BillMember> billMemberListIsReference) {
        //排序
        billMemberList.sort(Comparator.comparing(item -> item.getBillItemMember().getDirectionEnum().getSort()));
        //动态排序
        customSort(billMemberList, List.of(TYPE_5.getTypeId(),TYPE_181.getTypeId(), TYPE_83.getTypeId(), TYPE_222.getTypeId(), TYPE_247.getTypeId(), TYPE_95.getTypeId(), TYPE_94.getTypeId(),TYPE_256.getTypeId()));

        BillMember billMember_247 = billMemberList.stream().filter(item -> item.getTypeId().equals(TYPE_247.getTypeId())).findFirst().orElse(null);
        if (null != billMember_247) {
            billMemberList = billMemberList.stream().filter(item -> !item.getTypeId().equals(TYPE_247.getTypeId())).collect(Collectors.toList());
            billMemberList.removeIf(billMember -> billMember.getTypeId().equals(TYPE_247.getTypeId()));
        }

        try {
            for (BillMember billMember : billMemberList) {
                AccountAggregate accountAggregate = accountAggregateMap.getOrDefault(billMember.getBillItemMember().getUserId(), null);
                billMember.setAccountAggregate(accountAggregate);
                // 资金方向处理
                separateFundingDirectionFactory.getProcessor(billMember.getDirectionEnum()).process(billMember, billMemberList, billMemberListIsReference);
                System.out.println(billMember.getTypeId() + ":" + billMember.getIsSuccess());
            }

            //入参逻辑查找
            separateFundingDirectionFactory.getProcessor(DirectionEnum.PARAM_FIND).process(billMemberList);
            //特殊逻辑
            separateFundingDirectionFactory.getProcessor(DirectionEnum.TYPE_SPECIAL).process(billMemberList);

            if (null != billMember_247) {
                List<BillMember> billMemberList_247 = new ArrayList<>();
                billMemberList_247.add(billMember_247);
                for (BillMember billMember : billMemberList_247) {
                    AccountAggregate accountAggregate = accountAggregateMap.getOrDefault(billMember.getBillItemMember().getUserId(), null);
                    billMember.setAccountAggregate(accountAggregate);
                    // 资金方向处理
                    separateFundingDirectionFactory.getProcessor(billMember.getDirectionEnum()).process(billMember, billMemberList, billMemberListIsReference);
                }
                //入参逻辑查找
                separateFundingDirectionFactory.getProcessor(DirectionEnum.PARAM_FIND).process(billMemberList_247);
                //特殊逻辑
                separateFundingDirectionFactory.getProcessor(DirectionEnum.TYPE_SPECIAL).process(billMemberList_247);
                billMemberList.removeIf(billMember -> billMember.getTypeId().equals(TYPE_247.getTypeId()));
                billMemberList.addAll(billMemberList_247);
            }


        } catch (PaymentClearBusinessException e) {
            log.error("[分账处理] 业务异常 error:{}", ExceptionUtils.getStackTrace(e));
            throw e;
        } catch (Exception e) {
            log.error("[分账处理] 系统异常 error:{}", ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR);
        }
        //校验
        if (billMemberList.stream().anyMatch(billMember -> !billMember.getIsSuccess())) {
            log.error("[分账处理] 分账失败,请检查数据 billMember:{}", JSON.toJSONString(billMemberList));
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_DISTRIBUTION_FAIL);
        }
    }

    /**
     * 根据自定义顺序对对象列表进行排序
     */
    private void customSort(List<BillMember> billMemberList, List<Integer> customOrderList) {
        if (CollectionUtils.isEmpty(billMemberList) || CollectionUtils.isEmpty(customOrderList)) {
            return;
        }
        // 创建值到索引的映射
        int maxValue = customOrderList.stream().max(Integer::compare).orElse(0);
        int[] orderMap = new int[maxValue + 1];
        Arrays.fill(orderMap, Integer.MAX_VALUE);
        for (int i = 0; i < customOrderList.size(); i++) {
            orderMap[customOrderList.get(i)] = i;
        }
        // 使用反射获取字段值并排序
        billMemberList.sort((a, b) -> {
            int valueA = a.getTypeId();
            int valueB = b.getTypeId();
            // 根据自定义顺序排序
            // 检查值是否超出数组范围
            int indexA = valueA <= maxValue ? orderMap[valueA] : Integer.MAX_VALUE;
            int indexB = valueB <= maxValue ? orderMap[valueB] : Integer.MAX_VALUE;
            return Integer.compare(indexA, indexB);
        });
    }


    /**
     * 获取资金类型
     */
    private Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsType() {
        return accountAssetsTypeGateway.gatAllAccountAssetsType();
    }

    /**
     * 获取资金类型关系
     */
    private Map<String, List<Integer>> gatAllAccountAssetsTypeRelate() {
        return accountAssetsTypeGateway.gatAllAccountAssetsTypeRelate();

    }


    private void separatePrintLog(List<BillMember> billMemberList) {
        try {
            CompletableFuture.runAsync(() -> {
                if (Boolean.TRUE.equals(paymentClearParamsConfig.getSeparatePrintLog())) {
                    List<SeparatePrintLog> logList = new ArrayList<>();
                    for (BillMember billMember : billMemberList) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("<br>");
                        BillItemMember billItemMember = billMember.getBillItemMember();
                        sb.append("订单号: ").append(billItemMember.getOrderNo());
                        sb.append(StringUtils.rightPad(" 用户Id: " + billItemMember.getUserId(), 10));
                        sb.append(StringUtils.rightPad(" 收单类型: " + billItemMember.getCollectTypeEnum().getName(), 6));
                        sb.append(" 资金编号: ").append(billItemMember.getUserAssetsRecordId());
                        sb.append(StringUtils.rightPad(" 是否租赁单: " + billItemMember.getIsLeaseOrder(), 6));
                        sb.append(StringUtils.rightPad(" 资金方向: " + billItemMember.getDirectionEnum().getName(), 6));
                        sb.append(StringUtils.rightPad(" 状态: " + billItemMember.getNetStatusEnum().getName(), 6));
                        sb.append(StringUtils.rightPad(" 渠道: " + billItemMember.getPayChannelEnum().getText(), 8));
                        sb.append(StringUtils.rightPad(" 原始金额: " + billItemMember.getOriginalAmount(), 8));
                        sb.append(" 资金类型: ").append(billItemMember.getTypeId()).append(" ").append(billItemMember.getAssetsName());
                        sb.append("<br>");
                        for (UserAccountRecordMember userAccountRecordMember : billMember.getUserAccountRecordMemberList()) {
                            sb.append(StringUtils.rightPad("账户类型: " + AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()).getName(), 6));
                            sb.append(StringUtils.rightPad(" 变更之前: " + userAccountRecordMember.getBalanceBefore(), 10));
                            sb.append(StringUtils.rightPad(" 分账金额: " + userAccountRecordMember.getBalanceChange(), 10));
                            sb.append(StringUtils.rightPad(" 变更之后: " + userAccountRecordMember.getBalanceAfter(), 10));

                            sb.append(StringUtils.rightPad(" (冻结)变更之前: " + userAccountRecordMember.getFrozenBalanceBefore(), 10));
                            sb.append(StringUtils.rightPad(" (冻结)分账金额: " + userAccountRecordMember.getFrozenBalanceChange(), 10));
                            sb.append(StringUtils.rightPad(" (冻结)变更之后: " + userAccountRecordMember.getFrozenBalanceAfter(), 10));
                            sb.append("<br>");
                        }
                        logList.add(new SeparatePrintLog(sb.toString()));
                    }
                    log.info("分账日志:{}", JSON.toJSONString(logList));
                }
            });
        } catch (Exception e) {
            log.error("分账日志异常", e);
        }
    }

    private void separatePrintTestLog(List<BillMember> billMemberList) {
        try {
            CompletableFuture.runAsync(() -> {
                StringBuilder sb = new StringBuilder();
                for (BillMember billMember : billMemberList) {
                    sb.append("\n");
                    BillItemMember billItemMember = billMember.getBillItemMember();
                    sb.append("订单号: ").append(billItemMember.getOrderNo());
                    sb.append(StringUtils.rightPad(" 用户Id: " + billItemMember.getUserId(), 10));
                    sb.append(StringUtils.rightPad(" 收单类型: " + billItemMember.getCollectTypeEnum().getName(), 6));
                    sb.append(" 资金编号: ").append(billItemMember.getUserAssetsRecordId());
                    sb.append(StringUtils.rightPad(" 是否租赁单: " + billItemMember.getIsLeaseOrder(), 6));
                    sb.append(StringUtils.rightPad(" 资金方向: " + billMember.getDirectionEnum().getName(), 6));
                    sb.append(StringUtils.rightPad(" 状态: " + billItemMember.getNetStatusEnum().getName(), 6));
                    sb.append(StringUtils.rightPad(" 渠道: " + billItemMember.getPayChannelEnum().getText(), 8));
                    sb.append(StringUtils.rightPad(" 原始金额: " + billItemMember.getOriginalAmount(), 8));
                    sb.append(" 资金类型: ").append(billMember.getTypeId()).append(" ").append(billItemMember.getAssetsName());
                    sb.append("\n");
                    for (UserAccountRecordMember userAccountRecordMember : billMember.getUserAccountRecordMemberList()) {
                        sb.append(StringUtils.rightPad("账户类型: " + AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()).getName(), 6));
                        sb.append(StringUtils.rightPad(" 变更之前: " + userAccountRecordMember.getBalanceBefore(), 10));
                        sb.append(StringUtils.rightPad(" 分账金额: " + userAccountRecordMember.getBalanceChange(), 10));
                        sb.append(StringUtils.rightPad(" 变更之后: " + userAccountRecordMember.getBalanceAfter(), 10));

                        sb.append(StringUtils.rightPad(" (冻结)变更之前: " + userAccountRecordMember.getFrozenBalanceBefore(), 10));
                        sb.append(StringUtils.rightPad(" (冻结)分账金额: " + userAccountRecordMember.getFrozenBalanceChange(), 10));
                        sb.append(StringUtils.rightPad(" (冻结)变更之后: " + userAccountRecordMember.getFrozenBalanceAfter(), 10));
                        sb.append(StringUtils.rightPad(" 顺序: " + userAccountRecordMember.getCompareSort(), 10));
                        sb.append("\n");
                        sb.append("\n");
                    }
                }
                log.info("分账日志Test:{}", sb);
            });
        } catch (Exception e) {
            log.error("分账日志异常", e);
        }
    }
}