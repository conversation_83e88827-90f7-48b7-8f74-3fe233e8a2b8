package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.service.AccountAssetsTypeService;
import com.youpin.clear.domain.dto.AccountAssetsTypeDTO;
import com.youpin.clear.domain.gateway.AccountAssetsTypeGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class AccountAssetsTypeServiceImpl implements AccountAssetsTypeService {

    @Autowired
    AccountAssetsTypeGateway accountAssetsTypeGateway;

    @Override
    public Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsType() {
        return accountAssetsTypeGateway.gatAllAccountAssetsType();
    }

    @Override
    public Map<String, List<Integer>> gatAllAccountAssetsTypeRelate() {
        return accountAssetsTypeGateway.gatAllAccountAssetsTypeRelate();

    }

    @Override
    public void initAccountAssetsTypeCache() {
        accountAssetsTypeGateway.initAccountAssetsTypeCache();
    }

    @Override
    public Map<Integer, String> gatAccountAssetsTypeByCode(Set<Integer> assetsCodeList){
        return accountAssetsTypeGateway.gatAccountAssetsTypeByCode(assetsCodeList);
    }
}
