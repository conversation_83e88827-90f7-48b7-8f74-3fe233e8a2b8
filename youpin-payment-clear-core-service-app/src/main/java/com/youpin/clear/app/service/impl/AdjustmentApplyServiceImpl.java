package com.youpin.clear.app.service.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.uu898.youpin.commons.utils.JacksonUtils;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.AccountAssetsTypeService;
import com.youpin.clear.app.service.AdjustmentApplyService;
import com.youpin.clear.client.request.*;
import com.youpin.clear.client.response.*;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.gateway.*;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.domain.servcie.RocketMqService;
import com.youpin.clear.infrastructure.config.AdjustmentApplyConfig;
import com.youpin.clear.infrastructure.config.SendMessageConfig;
import com.youpin.clear.infrastructure.feign.impl.MessageFeignService;
import com.youpin.clear.infrastructure.feign.request.AuthorizationInfo;
import com.youpin.clear.infrastructure.feign.request.SendSmsRequest;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import com.youpin.clear.infrastructure.utils.ClearRedisUtils;
import com.youpin.clear.infrastructure.utils.DateTimeUtil;
import com.youpin.clear.infrastructure.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdjustmentApplyServiceImpl implements AdjustmentApplyService {

    @Resource
    private AdjustmentApplyGateway adjustmentApplyGateway;

    @Resource
    private AdjustmentApplyDetailGateway adjustmentApplyDetailGateway;

    @Resource
    private UU898UserAssetsInfoGateway userAssetsInfoGateway;

    @Resource
    private UU898UserAssetsRecordTypeGateway userAssetsRecordTypeGateway;

    @Resource
    private UU898UserAssetsRecordGateway userAssetsRecordGateway;

    @Resource
    private MessageFeignService messageFeignService;

    @Resource
    private SendMessageConfig sendMessageConfig;

    @Resource
    private AdjustmentApplyConfig adjustmentApplyConfig;

    @Resource
    private UserAccountRecordGateway userAccountRecordGateway;

    @Resource
    IdWorkService idWorkService;

    @Resource
    private UserAccountGateway userAccountGateway;

    @Resource
    private AccountAssetsTypeGateway accountAssetsTypeGateway;


    @Autowired
    AccountAssetsTypeService accountAssetsTypeService;

    @Autowired
    RocketMqService rocketMqService;


    @Override
    public AdjustApplyListResponse queryList(AdjustApplyListQueryRequest request) {
        Long count = adjustmentApplyGateway.queryCount(request);
        if (count == 0) {
            return AdjustApplyListResponse.builder()
                    .total(count)
                    .list(List.of())
                    .build();
        }
        List<AdjustmentApplyDTO> adjustmentApplyList = adjustmentApplyGateway.queryList(request);

        List<AdjustApplyResponse> applyResponseList = adjustmentApplyList.stream().map(dto -> {
            String incAmount = null;
            Long incUserId = null;
            String incAccountPayChannelStr = null;
            String decAmount = null;
            Long decUserId = null;
            String decAccountPayChannelStr = null;

            if (Objects.nonNull(dto.getIncomeUserId()) && dto.getIncomeUserId() != 0) {
                incAmount = AmountUtils.convertToString(dto.getIncomeAmount());
                incUserId = dto.getIncomeUserId();
                AccountPayChannelEnum accountPayChannelEnum = AccountPayChannelEnum.getByCode(dto.getIncomePayChannel());
                incAccountPayChannelStr = Objects.isNull(accountPayChannelEnum) ? null : accountPayChannelEnum.getDesc();
            }

            if (Objects.nonNull(dto.getExpenseUserId()) && dto.getExpenseUserId() != 0) {
                decAmount = AmountUtils.convertToString(dto.getExpenseAmount());
                decUserId = dto.getExpenseUserId();
                AccountPayChannelEnum accountPayChannelEnum = AccountPayChannelEnum.getByCode(dto.getExpensePayChannel());
                decAccountPayChannelStr = Objects.isNull(accountPayChannelEnum) ? null : accountPayChannelEnum.getDesc();
            }

            boolean repeatOrder = false;
            AdjustmentApplyExtendDTO adjustmentApplyExtendDTO = JacksonUtils.readValue(dto.getExtend(), AdjustmentApplyExtendDTO.class);
            if (Objects.nonNull(adjustmentApplyExtendDTO)) {
                repeatOrder = BooleanUtils.isTrue(adjustmentApplyExtendDTO.getRepeatFlag());
            }

            AdjustmentApplyStatusEnum applyStatusEnum = AdjustmentApplyStatusEnum.getByStatus(dto.getStatus());
            AdjustSourceEnum adjustSourceEnum = AdjustSourceEnum.getByCode(dto.getAdjustSource());
            return AdjustApplyResponse.builder()
                    .applyId(dto.getId())
                    .applyNo(dto.getApplyNo())
                    .incUserId(incUserId)
                    .incAmount(incAmount)
                    .incAccountPayChannelStr(incAccountPayChannelStr)
                    .decUserId(decUserId)
                    .decAmount(decAmount)
                    .decAccountPayChannelStr(decAccountPayChannelStr)
                    .relatedOrderNo(dto.getRelatedOrderNo())
                    .payOrderNo(dto.getPayOrderNo())
                    .applyStatus(dto.getStatus())
                    .applyStatusStr(Objects.isNull(applyStatusEnum) ? null : applyStatusEnum.getDesc())
                    .applyBy(dto.getApplyBy())
                    .applyRemark(dto.getApplyRemark())
                    .adjustSourceDesc(Objects.isNull(adjustSourceEnum) ? null : adjustSourceEnum.getDesc())
                    .adjustSource(dto.getAdjustSource())
                    .batchNo(dto.getBatchNo())
                    .auditBy(dto.getAuditBy())
                    .repeat(repeatOrder)
                    .applyTime(DateTimeUtil.timeFormat(dto.getCreateTime()))
                    .auditTime(DateTimeUtil.timeFormat(dto.getAuditTime()))
                    .build();
        }).collect(Collectors.toList());


        return AdjustApplyListResponse.builder()
                .total(count)
                .list(applyResponseList)
                .build();
    }

    @Override
    public Long submitApply(AdjustApplyRequest request) {
        // 参数校验
        checkParam(request);
        AdjustmentApplyDTO adjustmentApplyDTO = buildAdjustmentApplyDTO(request);
        List<AdjustmentApplyDetailDTO> adjustmentApplyDetailList = buildApplyDetailList(request, adjustmentApplyDTO.getApplyNo());

        BigDecimal checkAmountYuan = adjustmentApplyDTO.getIncomeAmount().compareTo(adjustmentApplyDTO.getExpenseAmount()) >= 0 ? adjustmentApplyDTO.getIncomeAmount() : adjustmentApplyDTO.getExpenseAmount();
        Long adjustAmount = AmountUtils.convertToCent(checkAmountYuan);
        if (adjustAmount.compareTo(adjustmentApplyConfig.getSingleAdjustmentAmountLimit()) > 0) {
            log.warn("调账金额超出单笔上限,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账金额超出单笔上限");
        }
        Long newStatisticAmount = getStaticAmount(getAmountStatisticKey(), adjustAmount);
        if (newStatisticAmount.compareTo(adjustmentApplyConfig.getSingleDayAmountLimit()) > 0) {
            log.warn("单日调账金额超限,newStatisticAmount={},request={}", newStatisticAmount, JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "单日调账金额超限");
        }
        Long innerAdjustAmount = 0L;
        Long outerAdjustAmount = 0L;
        // 增加余额统计
        checkAndSetAmount(adjustmentApplyDTO.getIncomeAmount(), adjustmentApplyDTO.getIncomeUserId(), innerAdjustAmount, outerAdjustAmount);
        // 减少余额统计
        checkAndSetAmount(adjustmentApplyDTO.getExpenseAmount(), adjustmentApplyDTO.getExpenseUserId(), innerAdjustAmount, outerAdjustAmount);

        boolean existRepeatAdjustmentApply = adjustmentApplyGateway.existRepeatAdjustmentApply(request.getRelatedOrderNo());
        AdjustmentApplyExtendDTO applyExtendDTO = AdjustmentApplyExtendDTO.builder().repeatFlag(existRepeatAdjustmentApply).build();
        adjustmentApplyDTO.setExtend(JacksonUtils.writeValueAsString(applyExtendDTO));

        // 累计单日金额
        accumulatedAmount(getAmountStatisticKey(), adjustAmount);
        // 累计单日内部账户金额
        accumulatedAmount(getInnerAccountAmountStatisticKey(), innerAdjustAmount);
        // 累计单日外部账户金额
        accumulatedAmount(getOuterAccountAmountStatisticKey(), outerAdjustAmount);
        AdjustmentApplyService adjustmentApplyService = SpringContextHolder.getBean(AdjustmentApplyService.class);
        return adjustmentApplyService.saveAdjustmentApplyInfo(adjustmentApplyDTO, adjustmentApplyDetailList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveAdjustmentApplyInfo(AdjustmentApplyDTO adjustmentApplyDTO, List<AdjustmentApplyDetailDTO> adjustmentApplyDetailDTOList) {
        if (StringUtils.isEmpty(adjustmentApplyDTO.getExtend())) {
            adjustmentApplyDTO.setExtend(JacksonUtils.writeValueAsString(new AdjustmentApplyExtendDTO()));
        }
        Long applyId = adjustmentApplyGateway.saveAdjustmentApply(adjustmentApplyDTO);
        adjustmentApplyDetailGateway.batchInsert(adjustmentApplyDetailDTOList);
        return applyId;
    }

    @Override
    public void sendVerifyCode(ApplySendVerifyCodeRequest request) {
        Object repeatCheck = ClearRedisUtils.getValue(getRepeatKey(request.getAuditBy()));
        if (Objects.nonNull(repeatCheck)) {
            log.warn("{}秒内只能发送一次短信验证码,request={}", sendMessageConfig.getAdjustmentRepeatTime(), JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), sendMessageConfig.getAdjustmentRepeatTime() + "秒内只能发送一次短信验证码,请稍后重试");
        }

        if (CollectionUtils.isEmpty(request.getApplyIdList())) {
            log.warn("申请单号为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "申请单号为空");
        }

        List<AdjustmentApplyDTO> adjustmentApplyDTOList = adjustmentApplyGateway.queryByIds(request.getApplyIdList());
        if (CollectionUtils.isEmpty(adjustmentApplyDTOList)) {
            log.warn("申请记录为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "申请记录为空");
        }

        for (AdjustmentApplyDTO adjustmentApplyDTO : adjustmentApplyDTOList) {
            if (!Objects.equals(adjustmentApplyDTO.getStatus(), AdjustmentApplyStatusEnum.APPLY_ING.getStatus())) {
                log.warn("记录状态非申请中，无法进行短信发送,request={},adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(request), JacksonUtils.writeValueAsString(adjustmentApplyDTO));
                throw new BusinessException(Status.SYSTEM.getCode(), "存在非申请中状态记录");
            }
        }

        if (request.getApplyIdList().size() == Constant.CONSTANT_INTEGER_1) {
            // 校验验证码是否有效
            String smsCode = (String) ClearRedisUtils.getValue(getVerifyCodeKey(request.getApplyIdList().get(0).toString()));
            if (StringUtils.isNotEmpty(smsCode)) {
                log.warn("短信验证码在有效期内,request={}", JacksonUtils.writeValueAsString(request));
                throw new BusinessException(Status.SYSTEM.getCode(), "短信验证码在有效期内，请勿重复发送");
            }
        }

        // 生成短信验证码
        String smsCode = generateSmsCode();
        SendSmsRequest sendSmsRequest = buildSendSmsRequest(Constant.CONSTANT_LONG_0,
                sendMessageConfig.getAdjustmentVerifyMobile(), smsCode,
                sendMessageConfig.getAdjustmentApplyVerifySmsTemplateCode(),
                sendMessageConfig.getAppKey(), sendMessageConfig.getAppSecret());
        // 发送短信
        messageFeignService.sendSms(sendSmsRequest);

        for (AdjustmentApplyDTO adjustmentApplyDTO : adjustmentApplyDTOList) {
            // 设置验证码与申请记录的关联关系
            ClearRedisUtils.setKey(getVerifyCodeKey(adjustmentApplyDTO.getId().toString()), smsCode, sendMessageConfig.getAdjustmentVerifyCodeEffectiveTime(), TimeUnit.MINUTES);
        }

        // 设置间隔时间，防止重复发送短信
        ClearRedisUtils.setKey(getRepeatKey(request.getAuditBy()), request.getAuditBy(), sendMessageConfig.getAdjustmentRepeatTime(), TimeUnit.SECONDS);
    }

    @Override
    public AdjustDetailResponse queryDetail(Long applyId) {
        if (Objects.isNull(applyId)) {
            return null;
        }
        // 查询申请记录
        AdjustmentApplyDTO applyDTO = adjustmentApplyGateway.queryById(applyId);
        if (Objects.isNull(applyDTO)) {
            return null;
        }
        // 查询申请明细
        List<AdjustmentApplyDetailDTO> detaiList = adjustmentApplyDetailGateway.queryByApplyNo(applyDTO.getApplyNo());
        if (CollectionUtils.isEmpty(detaiList)) {
            return null;
        }
        // 按照收支方向进行分组
        Map<Integer, List<AdjustmentApplyDetailDTO>> detailMap = detaiList.stream().collect(Collectors.groupingBy(AdjustmentApplyDetailDTO::getDirection));

        // 构建增加余额方账户信息
        AdjustAccountResponse incomeAccountResponse = buildAccountResponse(detailMap, AdjustmentDirectionEnum.INCOME,
                applyDTO.getIncomeUserId(), applyDTO.getIncomeAssetType(), applyDTO.getIncomePayChannel(), applyDTO.getIncomeServiceFee());

        // 构建减少余额方账户信息
        AdjustAccountResponse expenseAccountResponse = buildAccountResponse(detailMap, AdjustmentDirectionEnum.EXPENSE,
                applyDTO.getExpenseUserId(), applyDTO.getExpenseAssetType(), applyDTO.getExpensePayChannel(), applyDTO.getExpenseServiceFee());

        // 组装
        AdjustmentApplyStatusEnum applyStatusEnum = AdjustmentApplyStatusEnum.getByStatus(applyDTO.getStatus());
        AdjustSourceEnum adjustSourceEnum = AdjustSourceEnum.getByCode(applyDTO.getAdjustSource());
        return AdjustDetailResponse.builder()
                .relatedOrderNo(applyDTO.getRelatedOrderNo())
                .applyId(applyDTO.getId())
                .applyNo(applyDTO.getApplyNo())
                .applyStatus(applyDTO.getStatus())
                .applyStatusStr(Objects.isNull(applyStatusEnum) ? null : applyStatusEnum.getDesc())
                .payOrderNo(applyDTO.getPayOrderNo())
                .applyBy(applyDTO.getApplyBy())
                .auditBy(applyDTO.getAuditBy())
                .applyTime(DateTimeUtil.timeFormat(applyDTO.getCreateTime()))
                .auditTime(DateTimeUtil.timeFormat(applyDTO.getAuditTime()))
                .applyRemark(applyDTO.getApplyRemark())
                .auditRemark(applyDTO.getAuditRemark())
                .decAccountInfo(expenseAccountResponse)
                .incAccountInfo(incomeAccountResponse)
                .adjustSourceDesc(Objects.isNull(adjustSourceEnum) ? null : adjustSourceEnum.getDesc())
                .adjustSource(applyDTO.getAdjustSource())
                .batchNo(applyDTO.getBatchNo())
                .build();
    }

    @Override
    public void applyAudit(AdjustApplyAuditRequest request) {
        // 参数校验
        AdjustmentApplyDTO adjustmentApplyDTO = auditParamCheckAndGet(request);

        // 设置审核信息
        adjustmentApplyDTO.setAuditRemark(request.getAuditRemark());
        adjustmentApplyDTO.setAuditBy(request.getAuditBy());
        adjustmentApplyDTO.setAuditTime(LocalDateTime.now());

        // 审批拒绝
        if (Objects.equals(request.getAuditType(), AuditTypeEnum.REJECT.getCode())) {
            adjustmentApplyDTO.setStatus(AdjustmentApplyStatusEnum.APPLY_REJECT.getStatus());
            adjustmentApplyGateway.updateAdjustmentApply(adjustmentApplyDTO);
            return;
        }

        // 验证码校验
        String smsCode = (String) ClearRedisUtils.getValue(getVerifyCodeKey(adjustmentApplyDTO.getId().toString()));
        if (StringUtils.isEmpty(smsCode)) {
            log.warn("验证码已过期或未发送过验证码,request={}", JacksonUtils.writeValueAsString(request));
            verifyCodeCheckFailHandle(adjustmentApplyDTO);
            throw new BusinessException(Status.SYSTEM.getCode(), "验证码校验失败");
        }

        if (!Objects.equals(smsCode, request.getVerifyCode())) {
            log.warn("验证码不匹配,smsCode={},request={}", smsCode, JacksonUtils.writeValueAsString(request));
            verifyCodeCheckFailHandle(adjustmentApplyDTO);
            throw new BusinessException(Status.SYSTEM.getCode(), "验证码校验失败");
        }

        AdjustmentApplyExtendDTO applyExtendDTO = JacksonUtils.readValue(adjustmentApplyDTO.getExtend(), AdjustmentApplyExtendDTO.class);
        applyExtendDTO.setSmsCode(smsCode);
        applyExtendDTO.setRetryHandleCount(0);
        adjustmentApplyDTO.setStatus(AdjustmentApplyStatusEnum.APPLY_PROCESSING.getStatus());
        adjustmentApplyDTO.setExtend(JacksonUtils.writeValueAsString(applyExtendDTO));
        adjustmentApplyGateway.updateAdjustmentApply(adjustmentApplyDTO);
    }

    private void verifyCodeCheckFailHandle(AdjustmentApplyDTO adjustmentApplyDTO) {
        AdjustmentApplyExtendDTO adjustmentApplyExtendDTO = JacksonUtils.readValue(adjustmentApplyDTO.getExtend(), AdjustmentApplyExtendDTO.class);
        if (Objects.isNull(adjustmentApplyExtendDTO.getVerifyCodeCount())) {
            adjustmentApplyExtendDTO.setVerifyCodeCount(1);
        } else {
            adjustmentApplyExtendDTO.setVerifyCodeCount(adjustmentApplyExtendDTO.getVerifyCodeCount() + 1);
        }

        if (adjustmentApplyExtendDTO.getVerifyCodeCount() >= adjustmentApplyConfig.getVerifyCodeCheckCount()) {
            adjustmentApplyDTO.setStatus(AdjustmentApplyStatusEnum.APPLY_REJECT.getStatus());
            adjustmentApplyDTO.setAuditRemark("验证码错误达到上限，系统自动拒绝");
        }

        adjustmentApplyDTO.setExtend(JacksonUtils.writeValueAsString(adjustmentApplyExtendDTO));
        adjustmentApplyGateway.updateAdjustmentApply(adjustmentApplyDTO);
    }

    @Override
    public AdjustmentApplyConfigResponse queryApplyTypeConfig() {
        // 组装资金明细类型信息
        List<UU898UserAssetsRecordTypeDTO> incomeUserAssetsRecordTypeList = userAssetsRecordTypeGateway.queryByOperateTypeList(FrontAssetOperateTypeEnum.NON_EXPENSE.getInnerOperateTypeList());
        List<UU898UserAssetsRecordTypeDTO> expenseUserAssetsRecordTypeList = userAssetsRecordTypeGateway.queryByOperateTypeList(FrontAssetOperateTypeEnum.NON_INCOME.getInnerOperateTypeList());

        List<AssetTypeInfo> incomeAssetTypeList = incomeUserAssetsRecordTypeList.stream().map(dto -> AssetTypeInfo.builder()
                .type(dto.getId())
                .name(dto.getTypeName())
                .operateType(dto.getOperateType())
                .build()).collect(Collectors.toList());

        List<AssetTypeInfo> expenseAssetTypeList = expenseUserAssetsRecordTypeList.stream().map(dto -> AssetTypeInfo.builder()
                .type(dto.getId())
                .name(dto.getTypeName())
                .operateType(dto.getOperateType())
                .build()).collect(Collectors.toList());

        // 组装调账支付类型
        List<PayChannelTypeInfo> payChannelTypeInfoList = new ArrayList<>();
        for (AccountPayChannelEnum value : AccountPayChannelEnum.values()) {
            if (value == AccountPayChannelEnum.PURCHASE_BALANCE) {
                continue;
            }
            PayChannelTypeInfo payChannelTypeInfo = PayChannelTypeInfo.builder().name(value.getDesc()).type(value.getCode()).build();
            payChannelTypeInfoList.add(payChannelTypeInfo);
        }

        // 组装调账账户类型
        List<AccountTypeInfo> accountTypeInfoList = new ArrayList<>();
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (accountTypeEnum != AccountTypeEnum.BALANCE && accountTypeEnum != AccountTypeEnum.PURCHASE_BALANCE) {
                AccountTypeInfo accountTypeInfo = AccountTypeInfo.builder().name(accountTypeEnum.getName()).type(accountTypeEnum.getCode()).build();
                accountTypeInfoList.add(accountTypeInfo);
            }
        }

        List<ApplyStatusInfo> adjustmentApplyStatusInfoList = new ArrayList<>();
        for (AdjustmentApplyStatusEnum value : AdjustmentApplyStatusEnum.values()) {
            ApplyStatusInfo applyStatusInfo = ApplyStatusInfo.builder().status(value.getStatus()).desc(value.getDesc()).build();
            adjustmentApplyStatusInfoList.add(applyStatusInfo);
        }

        List<AdjustSourceInfo> adjustmentSourceInfoList = new ArrayList<>();
        for (AdjustSourceEnum value : AdjustSourceEnum.values()) {
            AdjustSourceInfo adjustSourceInfo = AdjustSourceInfo.builder().code(value.getCode()).desc(value.getDesc()).build();
            adjustmentSourceInfoList.add(adjustSourceInfo);
        }

        // 组装
        return AdjustmentApplyConfigResponse.builder()
                .incomeAssetTypeList(incomeAssetTypeList)
                .expenseAssetTypeList(expenseAssetTypeList)
                .payChannelTypeInfoList(payChannelTypeInfoList)
                .accountTypeInfoList(accountTypeInfoList)
                .applyStatusInfoList(adjustmentApplyStatusInfoList)
                .singleAdjustmentAmountLimit(adjustmentApplyConfig.getSingleAdjustmentAmountLimit())
                .singleDayAmountLimit(adjustmentApplyConfig.getSingleDayAmountLimit())
                .importTemplateUrl(adjustmentApplyConfig.getImportTemplateUrl())
                .adjustSourceInfoList(adjustmentSourceInfoList)
                .build();
    }


    @Override
    public void adjustmentHandle() {
        List<AdjustmentApplyDTO> adjustmentApplyDTOList = adjustmentApplyGateway.queryByStatus(List.of(AdjustmentApplyStatusEnum.APPLY_PROCESSING.getStatus()), adjustmentApplyConfig.getBatchQuerySize());
        if (CollectionUtils.isEmpty(adjustmentApplyDTOList)) {
            log.info("处理中数据为空");
            return;
        }
        for (AdjustmentApplyDTO adjustmentApplyDTO : adjustmentApplyDTOList) {
            try {
                log.info("调账申请开始处理,applyNo={}", adjustmentApplyDTO.getApplyNo());
                boolean result = applyHandle(adjustmentApplyDTO);
                log.info("调账申请处理结束,处理结果={}", result);
            } catch (Exception e) {
                log.error("调账申请处理异常,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO), e);
            }
        }
    }


    private boolean applyHandle(AdjustmentApplyDTO adjustmentApplyDTO) {

        AdjustmentApplyService adjustmentApplyService = SpringContextHolder.getBean(AdjustmentApplyService.class);
        try {
            adjustmentApplyService.handleUU898Account(adjustmentApplyDTO);
        } catch (Exception e) {
            log.error("uu898账户处理异常,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO), e);
            AdjustmentApplyExtendDTO adjustmentApplyExtendDTO = JacksonUtils.readValue(adjustmentApplyDTO.getExtend(), AdjustmentApplyExtendDTO.class);
            adjustmentApplyExtendDTO.setUuAccountMsg(e.getMessage());
            adjustmentApplyDTO.setExtend(JacksonUtils.writeValueAsString(adjustmentApplyExtendDTO));
            adjustmentApplyDTO.setStatus(AdjustmentApplyStatusEnum.APPLY_PROCESS_FAIL.getStatus());
            adjustmentApplyGateway.updateAdjustmentApply(adjustmentApplyDTO);
            return false;
        }
        boolean result = true;

        try {
            List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOList = adjustmentApplyDTO.getUu898UserAssetsRecordDTOList();
            if (null != uu898UserAssetsRecordDTOList && !uu898UserAssetsRecordDTOList.isEmpty()) {
                //增加 MQ 数据发送
                Set<Integer> collectTypeId = uu898UserAssetsRecordDTOList.stream().map(UU898UserAssetsRecordDTO::getTypeId).collect(Collectors.toSet());
                Map<Integer, String> typeIdNameList = accountAssetsTypeService.gatAccountAssetsTypeByCode(collectTypeId);
                uu898UserAssetsRecordDTOList.forEach(item -> {
                    ClearUserAssetsRecordDTO clearUserAssetsRecordDTO = UserAssetsRecordConvertor.MAPPER.toClearUserAssetsRecordDTOByUU898(item);
                    clearUserAssetsRecordDTO.setCollectType(CollectTypeEnum.DEFAULT.getCode());
                    clearUserAssetsRecordDTO.setTypeName(typeIdNameList.get(clearUserAssetsRecordDTO.getTypeId()));
                    rocketMqService.sendAssetsMessage(List.of(clearUserAssetsRecordDTO));
                });
            }

            adjustmentApplyService.handleMultiAccount(adjustmentApplyDTO);
            adjustmentApplyDTO.setStatus(AdjustmentApplyStatusEnum.APPLY_PROCESS_SUCCESS.getStatus());
        } catch (Exception e) {
            log.error("分账账户处理异常,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO), e);
            AdjustmentApplyExtendDTO adjustmentApplyExtendDTO = JacksonUtils.readValue(adjustmentApplyDTO.getExtend(), AdjustmentApplyExtendDTO.class);
            adjustmentApplyExtendDTO.setSeparateAccountMsg(e.getMessage());
            adjustmentApplyExtendDTO.setRetryHandleCount(adjustmentApplyExtendDTO.getRetryHandleCount() + 1);
            adjustmentApplyDTO.setExtend(JacksonUtils.writeValueAsString(adjustmentApplyExtendDTO));
            if (adjustmentApplyExtendDTO.getRetryHandleCount() > adjustmentApplyConfig.getSeparateAccountRetryHandleCount()) {
                log.error("分账账户多次调账后仍失败,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO));
                adjustmentApplyDTO.setStatus(AdjustmentApplyStatusEnum.APPLY_PROCESS_FAIL.getStatus());
            }
            result = false;
        }
        adjustmentApplyGateway.updateAdjustmentApply(adjustmentApplyDTO);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleUU898Account(AdjustmentApplyDTO adjustmentApplyDTO) {
        // 查询资金明细记录是否存在
        UU898UserAssetsRecordDTO userAssetsRecordDTO = userAssetsRecordGateway.selectByTradeNo(getOneTradeNo(adjustmentApplyDTO));
        if (Objects.nonNull(userAssetsRecordDTO)) {
            log.info("uu898账户已处理,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            return;
        }
        if (!Objects.equals(adjustmentApplyDTO.getIncomeUserId(), Constant.CONSTANT_LONG_0)) {
            // 增加余额账户&资金明细
            handleBalance(adjustmentApplyDTO, adjustmentApplyDTO.getApplyNo(),
                    AdjustmentDirectionEnum.INCOME,
                    adjustmentApplyDTO.getIncomeUserId(),
                    adjustmentApplyDTO.getIncomeAssetType(),
                    adjustmentApplyDTO.getIncomeAmount(),
                    adjustmentApplyDTO.getPayOrderNo(),
                    adjustmentApplyDTO.getRelatedOrderNo(),
                    adjustmentApplyDTO.getIncomePayChannel(),
                    adjustmentApplyDTO.getIncomeServiceFee());
        }

        if (!Objects.equals(adjustmentApplyDTO.getExpenseUserId(), Constant.CONSTANT_LONG_0)) {
            // 减少余额账户&资金明细
            handleBalance(adjustmentApplyDTO, adjustmentApplyDTO.getApplyNo(),
                    AdjustmentDirectionEnum.EXPENSE,
                    adjustmentApplyDTO.getExpenseUserId(),
                    adjustmentApplyDTO.getExpenseAssetType(),
                    adjustmentApplyDTO.getExpenseAmount(),
                    adjustmentApplyDTO.getPayOrderNo(),
                    adjustmentApplyDTO.getRelatedOrderNo(),
                    adjustmentApplyDTO.getExpensePayChannel(),
                    adjustmentApplyDTO.getExpenseServiceFee());
        }

    }


    @Override
    public void handleMultiAccount(AdjustmentApplyDTO adjustmentApplyDTO) {
        UU898UserAssetsRecordDTO incomeAssetRecord = getIncomeAssetRecord(adjustmentApplyDTO);
        UU898UserAssetsRecordDTO expenseAssetRecord = getExpenseAssetRecord(adjustmentApplyDTO);
        UU898UserAssetsRecordDTO userAssetsRecordDTO = userAssetsRecordGateway.selectByTradeNo(getOneTradeNo(adjustmentApplyDTO));
        if (Objects.isNull(userAssetsRecordDTO)) {
            log.error("uu898账户未处理,不存在资金明细记录,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "资金明细不存在");
        }

        List<AdjustmentApplyDetailDTO> adjustmentDetailList = adjustmentApplyDetailGateway.queryByApplyNo(adjustmentApplyDTO.getApplyNo());
        if (CollectionUtils.isEmpty(adjustmentDetailList)) {
            log.error("调账申请明细为空,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账申请明细记录为空");
        }

        AdjustmentApplyDetailDTO adjustmentApplyDetailDTO = adjustmentDetailList.stream().findAny().get();
        Boolean exist = userAccountRecordGateway.countUserAccountRecord(adjustmentApplyDTO.getOneUserId(), userAssetsRecordDTO.getId(), null, adjustmentApplyDetailDTO.getAdjustAccountType());
        if (BooleanUtils.isTrue(exist)) {
            log.info("分账账户已处理,adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            return;
        }

        List<Long> relatedUserIdList = adjustmentApplyDTO.getRelatedUserIdList();
        Map<Long, AccountAggregate> accountAggregatesMap = getAccountAggregatesMap(relatedUserIdList);
        Map<Integer, AccountAssetsTypeDTO> allAccountAssetsType = gatAllAccountAssetsType();

        List<UserAccountRecordMember> userAccountRecordMemberList = new ArrayList<>(adjustmentDetailList.size());
        for (AdjustmentApplyDetailDTO applyDetailDTO : adjustmentDetailList) {
            UserAccountRecordMember accountRecordMember = createAccountRecordMember(adjustmentApplyDTO, applyDetailDTO, incomeAssetRecord, expenseAssetRecord, accountAggregatesMap, allAccountAssetsType);
            userAccountRecordMemberList.add(accountRecordMember);
        }

        AdjustmentApplyService adjustmentApplyService = SpringContextHolder.getBean(AdjustmentApplyService.class);
        // 更新账户
        adjustmentApplyService.updateSeparateAccount(userAccountRecordMemberList, accountAggregatesMap);
    }

    private UU898UserAssetsRecordDTO getIncomeAssetRecord(AdjustmentApplyDTO adjustmentApplyDTO) {
        if (adjustmentApplyDTO.existIncome()) {
            return userAssetsRecordGateway.selectByTradeNo(adjustmentApplyDTO.getApplyNo() + adjustmentApplyDTO.getIncomeAssetType());
        }
        return null;
    }

    private UU898UserAssetsRecordDTO getExpenseAssetRecord(AdjustmentApplyDTO adjustmentApplyDTO) {
        if (adjustmentApplyDTO.existExpense()) {
            return userAssetsRecordGateway.selectByTradeNo(adjustmentApplyDTO.getApplyNo() + adjustmentApplyDTO.getExpenseAssetType());
        }
        return null;
    }


    private UserAccountRecordMember createAccountRecordMember(AdjustmentApplyDTO adjustmentApplyDTO, AdjustmentApplyDetailDTO applyDetailDTO, UU898UserAssetsRecordDTO incomeAssetRecord, UU898UserAssetsRecordDTO expenseAssetRecord, Map<Long, AccountAggregate> accountAggregatesMap, Map<Integer, AccountAssetsTypeDTO> allAccountAssetsType) {
        Long userId;
        BigDecimal balanceChangeAmount;
        UU898UserAssetsRecordDTO assetsRecordDTO;
        if (applyDetailDTO.getDirection().equals(AdjustmentDirectionEnum.INCOME.getCode())) {
            userId = adjustmentApplyDTO.getIncomeUserId();
            assetsRecordDTO = incomeAssetRecord;
            balanceChangeAmount = applyDetailDTO.getAdjustMoney();
        } else {
            userId = adjustmentApplyDTO.getExpenseUserId();
            assetsRecordDTO = expenseAssetRecord;
            balanceChangeAmount = applyDetailDTO.getAdjustMoney().negate();
        }

        if (Objects.isNull(assetsRecordDTO)) {
            log.error("资金明细不存在,direction={},adjustmentApplyDTO={}", applyDetailDTO.getDirection(), JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "资金明细不存在");
        }

        AccountAggregate accountAggregate = accountAggregatesMap.get(userId);
        if (Objects.isNull(accountAggregate)) {
            log.error("用户分账账户不存在,userId={},adjustmentApplyDTO={}", userId, JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "分账账户不存在");
        }

        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(applyDetailDTO.getAdjustAccountType());
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(accountTypeEnum);
        if (Objects.isNull(accountInfoMember)) {
            log.error("用户分账账户不存在,accountType={},applyDetailDTO={}", accountTypeEnum, JacksonUtils.writeValueAsString(applyDetailDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "分账子账户不存在");
        }

        BigDecimal balanceAfter;
        int balanceIsChange;
        if (adjustmentApplyDTO.balanceChange(applyDetailDTO.getDirection())) {
            // 余额发生变动
            balanceAfter = accountInfoMember.getBalance().add(balanceChangeAmount);
            balanceIsChange = Constant.CONSTANT_INTEGER_1;
        } else {
            // 余额不发生变动
            balanceAfter = accountInfoMember.getBalance();
            balanceIsChange = Constant.CONSTANT_INTEGER_0;
        }

        if (balanceAfter.compareTo(BigDecimal.ZERO) < 0) {
            log.error("余额不足,balance={},adjustmentApplyDTO={},adjustDetailDTO={}", JacksonUtils.writeValueAsString(accountInfoMember), JacksonUtils.writeValueAsString(adjustmentApplyDTO), JacksonUtils.writeValueAsString(applyDetailDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "账户余额不足");
        }

        AccountAssetsTypeDTO accountAssetsTypeDTO = allAccountAssetsType.get(assetsRecordDTO.getTypeId());
        return UserAccountRecordMember.builder()
                .accountRecordNo(idWorkService.getNextIdLeafKey())
                .userId(userId)
                .typeId(assetsRecordDTO.getTypeId())
                .userAssetsRecordId(assetsRecordDTO.getId())
                .treadNo(assetsRecordDTO.getTreadNo())
                .serialNo(assetsRecordDTO.getSerialNo())
                .orderNo(assetsRecordDTO.getOrderNo())
                .payOrderNo(assetsRecordDTO.getPayOrderNo())
                .userAccountNo(accountInfoMember.getUserAccountNo())
                .accountType(accountInfoMember.getAccountType())
                .balanceBefore(accountInfoMember.getBalance())
                .balanceChange(balanceChangeAmount)
                .balanceAfter(balanceAfter)
                .balanceIsChange(balanceIsChange)
                .status(assetsRecordDTO.getStatus())
                .payChannel(assetsRecordDTO.getPayChannel())
                .finishTime(assetsRecordDTO.getCompleteTime())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .directionEnum(DirectionEnum.getCodeEnum(accountAssetsTypeDTO.getDirection()))
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSeparateAccount(List<UserAccountRecordMember> userAccountRecordMemberList, Map<Long, AccountAggregate> accountAggregatesMap) {
        if (CollectionUtils.isEmpty(userAccountRecordMemberList)) {
            return;
        }

        for (UserAccountRecordMember accountRecordMember : userAccountRecordMemberList) {
            AccountAggregate accountAggregate = accountAggregatesMap.get(accountRecordMember.getUserId());
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(accountRecordMember.getAccountType());
            AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(accountTypeEnum);
            Long userAccountRecordId = userAccountRecordGateway.userAccountRecordSave(accountRecordMember);
            if (accountRecordMember.getBalanceIsChange().equals(Constant.CONSTANT_INTEGER_1)) {
                // 更新子账户
                userAccountGateway.updateBalanceAccountInfoMember(accountInfoMember.getId(), accountRecordMember.getUserId(), accountTypeEnum,
                        accountInfoMember.getUserAccountNo(), accountRecordMember.getBalanceBefore(),
                        accountRecordMember.getBalanceChange(), null, userAccountRecordId);
                // 汇总账户类型
                AccountTypeEnum summaryAccountTypeEnum;
                if (accountTypeEnum == AccountTypeEnum.BALANCE_1 || accountTypeEnum == AccountTypeEnum.BALANCE_2) {
                    summaryAccountTypeEnum = AccountTypeEnum.BALANCE;
                } else {
                    summaryAccountTypeEnum = AccountTypeEnum.PURCHASE_BALANCE;
                }
                AccountInfoMember summaryAccountInfoMember = accountAggregate.getAccountByType(summaryAccountTypeEnum);
                // 更新汇总账户
                userAccountGateway.updateBalanceAccountInfoMember(summaryAccountInfoMember.getId(), accountRecordMember.getUserId(), summaryAccountTypeEnum,
                        summaryAccountInfoMember.getUserAccountNo(), summaryAccountInfoMember.getBalance(),
                        accountRecordMember.getBalanceChange(), null, 0L);
                // 对余额进行重新赋值
                summaryAccountInfoMember.setBalance(summaryAccountInfoMember.getBalance().add(accountRecordMember.getBalanceChange()));
            }

        }
    }


    @Override
    public void adjustApplyImport(AdjustBatchImportRequest request) {
        for (AdjustApplyImportRequest importRequest : request.getImportList()) {
            AdjustApplyRequest adjustApplyRequest = convert(request, importRequest);
            try {
                log.info("调账批量导入处理,adjustApplyRequest={}", JacksonUtils.writeValueAsString(adjustApplyRequest));
                submitApply(adjustApplyRequest);
            } catch (Exception e) {
                log.warn("调账批量导入处理异常,adjustApplyRequest={},msg={}", JacksonUtils.writeValueAsString(adjustApplyRequest), e.getMessage(), e);
            }
        }
    }

    @Override
    public void applyRevoke(ApplyRevokeRequest request) {
        if (request.getApplyIds().size() > adjustmentApplyConfig.getRevokeBatchMaxSize()) {
            log.info("批量撤销调账申请数量超过限制,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "批量撤销调账申请数量超过限制");
        }
        List<AdjustmentApplyDTO> adjustmentApplyList = adjustmentApplyGateway.queryByIds(request.getApplyIds());
        if (CollectionUtils.isEmpty(adjustmentApplyList)) {
            log.info("申请列表查询为空,request={}", JacksonUtils.writeValueAsString(request));
            return;
        }
        List<Long> ids = adjustmentApplyList.stream().filter(dto -> Objects.equals(dto.getStatus(), AdjustmentApplyStatusEnum.APPLY_ING.getStatus())).map(AdjustmentApplyDTO::getId).collect(Collectors.toList());
        adjustmentApplyGateway.updateApplyStatus(ids, AdjustmentApplyStatusEnum.APPLY_REVOKE.getStatus(), AdjustmentApplyStatusEnum.APPLY_ING.getStatus());
    }


    @Override
    public BatchAuditDetailResponse batchAuditDetail(ApplyBatchQueryRequest request) {
        if (request.getApplyIds().size() > adjustmentApplyConfig.getAuditBatchMaxSize()) {
            log.info("批量审核查询-数量超过限制,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "批量审核数量超过限制");
        }

        List<AdjustmentApplyDTO> adjustmentApplyList = adjustmentApplyGateway.queryByIds(request.getApplyIds());
        AdjustmentApplyDTO adjustmentApplyDTO = adjustmentApplyList.stream().filter(dto -> !Objects.equals(dto.getAdjustSource(), request.getAdjustSource())).findFirst().orElse(null);
        if (Objects.nonNull(adjustmentApplyDTO)) {
            log.warn("批量审核查询-调账类型与请求不一致,adjustmentApplyDTO={},request={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO), JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "批量审核调账类型与请求不一致");
        }

        BigDecimal addAmount = new BigDecimal(0);
        BigDecimal subAmount = new BigDecimal(0);
        for (AdjustmentApplyDTO dto : adjustmentApplyList) {
            addAmount = addAmount.add(dto.getIncomeAmount());
            subAmount = subAmount.add(dto.getExpenseAmount());
        }

        AdjustSourceEnum adjustSourceEnum = AdjustSourceEnum.getByCode(request.getAdjustSource());
        return BatchAuditDetailResponse.builder()
                .addTotalAmount(AmountUtils.convertTenThousandths(addAmount))
                .subTotalAmount(AmountUtils.convertTenThousandths(subAmount))
                .totalCount(adjustmentApplyList.size())
                .adjustSourceDesc(Objects.isNull(adjustSourceEnum) ? null : adjustSourceEnum.getDesc())
                .adjustSource(request.getAdjustSource())
                .build();
    }


    @Override
    public void batchApplyAudit(ApplyBatchAuditRequest request) {
        if (request.getApplyIds().size() > adjustmentApplyConfig.getAuditBatchMaxSize()) {
            log.info("批量审核-调账申请数量超过限制,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "批量审核调账申请数量超过限制");
        }
        List<AdjustmentApplyDTO> adjustmentApplyList = adjustmentApplyGateway.queryByIds(request.getApplyIds());
        AdjustmentApplyDTO adjustmentApplyDTO = adjustmentApplyList.stream().filter(dto -> !Objects.equals(dto.getAdjustSource(), request.getAdjustSource())).findFirst().orElse(null);
        if (Objects.nonNull(adjustmentApplyDTO)) {
            log.warn("批量审核-调账类型与请求不一致,adjustmentApplyDTO={},request={}", JacksonUtils.writeValueAsString(adjustmentApplyDTO), JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "批量审核调账类型与请求不一致");
        }
        for (AdjustmentApplyDTO applyDTO : adjustmentApplyList) {
            AdjustApplyAuditRequest auditRequest = new AdjustApplyAuditRequest();
            auditRequest.setApplyId(applyDTO.getId());
            auditRequest.setAuditType(request.getAuditType());
            auditRequest.setAuditRemark(request.getAuditRemark());
            auditRequest.setAuditBy(request.getOperator());
            auditRequest.setVerifyCode(request.getVerifyCode());
            try {
                log.info("批量审核处理,auditRequest={}", JacksonUtils.writeValueAsString(auditRequest));
                applyAudit(auditRequest);
            } catch (Exception e) {
                log.warn("批量审核-调账申请审核异常,applyDTO={},msg={}", JacksonUtils.writeValueAsString(applyDTO), e.getMessage(), e);
            }
        }
    }

    @Override
    public void updateStatus(AdjustUpdateStatusRequest request) {
        if (CollectionUtils.isEmpty(request.getApplyIds())) {
            return;
        }
        adjustmentApplyGateway.updateApplyStatus(request.getApplyIds(), request.getExecuteStatus(), request.getCurrentStatus());
    }


    @Override
    public Boolean retry(AdjustRetryRequest request) {
        AdjustmentApplyDTO adjustmentApplyDTO = adjustmentApplyGateway.queryById(request.getApplyId());
        if (Objects.isNull(adjustmentApplyDTO)) {
            log.warn("调账单据不存在,request={}", JacksonUtils.writeValueAsString(request));
            return Boolean.FALSE;
        }
        if (!Objects.equals(adjustmentApplyDTO.getStatus(),AdjustmentApplyStatusEnum.APPLY_PROCESS_FAIL.getStatus())) {
            log.warn("调账单状态非处理失败，无法进行重试,request={}",JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(),"调账单状态非处理失败，无法进行重试");
        }
        int affect = adjustmentApplyGateway.updateApplyStatus(List.of(request.getApplyId()), AdjustmentApplyStatusEnum.APPLY_PROCESSING.getStatus(), AdjustmentApplyStatusEnum.APPLY_PROCESS_FAIL.getStatus());
        return affect > 0;
    }

    private AdjustApplyRequest convert(AdjustBatchImportRequest request, AdjustApplyImportRequest importRequest) {
        AdjustApplyRequest adjustApplyRequest = new AdjustApplyRequest();
        adjustApplyRequest.setRelatedOrderNo(importRequest.getRelatedOrderNo());
        adjustApplyRequest.setPayOrderNo(importRequest.getPayOrderNo());
        adjustApplyRequest.setAdjustSource(importRequest.getAdjustSource());
        adjustApplyRequest.setApplyRemark(importRequest.getRemark());
        adjustApplyRequest.setApplyBy(request.getOperator());
        adjustApplyRequest.setBatchNo(request.getBatchNo());

        if (BooleanUtils.isTrue(importRequest.getAddBalance())) {
            AdjustAccountRequest incAccountInfo = buildAccountInfo(importRequest.getAddUserId(), importRequest.getAddAssetType(), importRequest.getAddPayChannel(), importRequest.getAddServiceFee(), importRequest.getAddAmount(), importRequest.getAddAccountType());
            adjustApplyRequest.setIncAccountInfo(incAccountInfo);
        }

        if (BooleanUtils.isTrue(importRequest.getSubBalance())) {
            AdjustAccountRequest decAccountInfo = buildAccountInfo(importRequest.getSubUserId(), importRequest.getSubAssetType(), importRequest.getSubPayChannel(), importRequest.getSubServiceFee(), importRequest.getSubAmount(), importRequest.getSubAccountType());
            adjustApplyRequest.setDecAccountInfo(decAccountInfo);
        }
        return adjustApplyRequest;
    }

    private AdjustAccountRequest buildAccountInfo(Long userId, Long assetType, Integer payChannel, BigDecimal serviceFee, BigDecimal amount, Integer accountType) {
        AdjustAccountRequest accountInfo = new AdjustAccountRequest();
        accountInfo.setUserId(userId);
        accountInfo.setAssetType(assetType);
        accountInfo.setAccountPayChannelType(payChannel);
        accountInfo.setServiceFee(serviceFee);
        AdjustApplyItemRequest itemRequest = new AdjustApplyItemRequest();
        itemRequest.setAdjustAmount(amount);
        itemRequest.setAdjustAccountType(accountType);
        accountInfo.setItemList(List.of(itemRequest));
        return accountInfo;
    }

    /**
     * 获取资金类型
     */
    private Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsType() {
        return accountAssetsTypeGateway.gatAllAccountAssetsType();
    }

    private Map<Long, AccountAggregate> getAccountAggregatesMap(List<Long> userIdList) {
        Map<Long, AccountAggregate> accountAggregateMap = new HashMap<>(userIdList.size());
        for (Long userId : userIdList) {
            try {
                AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(userId);
                if (accountAggregate == null) {
                    log.warn("[用户分账] 账户数据为空 userId:{}", userId);
                    return Collections.emptyMap();
                }
                accountAggregateMap.put(userId, accountAggregate);
            } catch (Exception e) {
                log.error("[用户分账] 获取账户数据失败 userId: {}, 异常: {}", userId, e.getMessage(), e);
                return Collections.emptyMap();
            }
        }
        return accountAggregateMap;
    }

    private void handleBalance(AdjustmentApplyDTO adjustmentApplyDTO, String applyNo, AdjustmentDirectionEnum direction, Long userId, Long assetType, BigDecimal adjustAmount,
                               String payOrderNo, String relatedOrderNo, Integer accountPayChannel, BigDecimal serviceFee) {
        List<AdjustmentApplyDetailDTO> adjustmentApplyDetailDTOList = adjustmentApplyDetailGateway.queryByApplyNoAndDirection(applyNo, direction.getCode());
        AdjustmentApplyDetailDTO adjustmentApplyDetailDTO = adjustmentApplyDetailDTOList.stream().findFirst().orElse(null);
        if (Objects.isNull(adjustmentApplyDetailDTO)) {
            log.error("调账申请明细数据缺失");
            throw new BusinessException(Status.SYSTEM.getCode(), "调账明细数据缺失");
        }
        // 收入方账户信息
        UserAssetsInfoDTO userAssetsInfo = userAssetsInfoGateway.getUserAssetsInfo(userId);
        // 组装资金明细记录
        UU898UserAssetsRecordDTO userAssetsRecordDTO = buildAssetRecord(
                userId, assetType, applyNo, adjustAmount,
                payOrderNo, relatedOrderNo, userAssetsInfo, accountPayChannel,
                adjustmentApplyDetailDTO.getAdjustAccountType(), direction, serviceFee);

        Long assetId = userAssetsRecordGateway.insert(userAssetsRecordDTO);
        adjustmentApplyDTO.addUU898UserAssetsRecordDTO(userAssetsRecordDTO);
        AccountTypeEnum accountTypeEnum = judgeAccountType(adjustmentApplyDetailDTO.getAdjustAccountType());
        // 需要进行余额变动
        if (!accountPayChannel.equals(AccountPayChannelEnum.BALANCE.getCode())) {
            log.info("非余额调账,不进行资金变动");
            return;
        }

        if (direction == AdjustmentDirectionEnum.INCOME) {
            // 增加余额
            addBalance(applyNo, assetType, adjustAmount, accountTypeEnum, userAssetsInfo, assetId);
        } else {
            // 扣减余额
            subtractBalance(applyNo, assetType, adjustAmount, accountTypeEnum, userAssetsInfo, assetId);
        }
    }

    private void subtractBalance(String applyNo, Long assetType, BigDecimal adjustAmount, AccountTypeEnum accountTypeEnum, UserAssetsInfoDTO userAssetsInfo, Long assetId) {
        if (accountTypeEnum == AccountTypeEnum.BALANCE) {
            // 校验余额是否足够扣减
            if (userAssetsInfo.getMoney().compareTo(adjustAmount) < 0) {
                log.error("账户余额不足,userAssetsInfo={},adjustAmount={}", JacksonUtils.writeValueAsString(userAssetsInfo), adjustAmount);
                throw new BusinessException(Status.SYSTEM.getCode(), "账户余额不足");
            }
            UserAssetsInfoDTO toUpdate = UserAssetsInfoDTO.builder()
                    .money(userAssetsInfo.getMoney().subtract(adjustAmount))
                    .lastUserRecordId(assetId)
                    .updateTime(LocalDateTime.now())
                    .build();
            int affect = userAssetsInfoGateway.updateUserBalance(userAssetsInfo, toUpdate);
            if (affect <= 0) {
                log.error("减少-余额调账更新失败,applyNo={}", applyNo);
                throw new BusinessException(Status.SYSTEM.getCode(), "余额账户更新失败");
            }
        } else if (accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE) {
            // 校验求购余额是否足够扣减
            if (userAssetsInfo.getPurchaseMoney().compareTo(adjustAmount) < 0) {
                log.error("求购账户余额不足,userAssetsInfo={},adjustAmount={}", JacksonUtils.writeValueAsString(userAssetsInfo), adjustAmount);
                throw new BusinessException(Status.SYSTEM.getCode(), "账户余额不足");
            }
            UserAssetsInfoDTO toUpdate = UserAssetsInfoDTO.builder()
                    .purchaseMoney(userAssetsInfo.getPurchaseMoney().subtract(adjustAmount))
                    .lastUserRecordId(assetId)
                    .purchaseMoneyFromMoney(userAssetsInfo.getPurchaseMoneyFromMoney())
                    .updateTime(LocalDateTime.now())
                    .build();
            if (Objects.equals(assetType.intValue(), ClearConstants.CONSTANT_INTEGER_44)) {
                // 求购提现
                // 可提现余额 = 求购余额 - 钱包转入求购金额
                BigDecimal canWithdrawAmount = userAssetsInfo.getPurchaseMoney().subtract(userAssetsInfo.getPurchaseMoneyFromMoney());
                if (canWithdrawAmount.compareTo(adjustAmount) < 0) {
                    log.error("求购可提现余额不足,userAssetsInfo={},adjustAmount={}", JacksonUtils.writeValueAsString(userAssetsInfo), adjustAmount);
                    throw new BusinessException(Status.SYSTEM.getCode(), "求购可提现余额不足");
                }
            } else if (Objects.equals(assetType.intValue(), ClearConstants.CONSTANT_INTEGER_100)) {
                // 转出到余额
                if (userAssetsInfo.getPurchaseMoneyFromMoney().compareTo(adjustAmount) < 0) {
                    log.error("求购余额转入金额不足,无法转出到余额,userAssetsInfo={},adjustAmount={}", JacksonUtils.writeValueAsString(userAssetsInfo), adjustAmount);
                    throw new BusinessException(Status.SYSTEM.getCode(), "求购余额转入金额不足");
                }
                toUpdate.setPurchaseMoneyFromMoney(userAssetsInfo.getPurchaseMoneyFromMoney().subtract(adjustAmount));
            }

            // 求购余额小于钱包转入求购金额
            if (toUpdate.getPurchaseMoney().compareTo(userAssetsInfo.getPurchaseMoneyFromMoney()) < 0) {
                // 设置钱包转入求购金额为求购余额
                toUpdate.setPurchaseMoneyFromMoney(toUpdate.getPurchaseMoney());
            }

            int affect = userAssetsInfoGateway.updateUserPurchaseBalance(userAssetsInfo, toUpdate);
            if (affect <= 0) {
                log.error("更新求购余额失败,applyNo={}", applyNo);
                throw new BusinessException(Status.SYSTEM.getCode(), "求购余额更新失败");
            }
        }
    }

    private void addBalance(String applyNo, Long assetType, BigDecimal adjustAmount, AccountTypeEnum accountTypeEnum, UserAssetsInfoDTO userAssetsInfo, Long assetId) {
        if (accountTypeEnum == AccountTypeEnum.BALANCE) {
            // 余额变动
            UserAssetsInfoDTO toUpdate = UserAssetsInfoDTO.builder()
                    .money(userAssetsInfo.getMoney().add(adjustAmount))
                    .lastUserRecordId(assetId)
                    .updateTime(LocalDateTime.now())
                    .build();
            int affect = userAssetsInfoGateway.updateUserBalance(userAssetsInfo, toUpdate);
            if (affect <= 0) {
                log.error("增加-余额调账更新失败,applyNo={}", applyNo);
                throw new BusinessException(Status.SYSTEM.getCode(), "余额账户更新失败");
            }
        } else if (accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE) {
            UserAssetsInfoDTO toUpdate = UserAssetsInfoDTO.builder()
                    .purchaseMoney(userAssetsInfo.getPurchaseMoney().add(adjustAmount))
                    .lastUserRecordId(assetId)
                    .purchaseMoneyFromMoney(userAssetsInfo.getPurchaseMoneyFromMoney())
                    .updateTime(LocalDateTime.now())
                    .build();
            // 求购余额变动
            if (Objects.equals(assetType.intValue(), ClearConstants.CONSTANT_INTEGER_98)) {
                toUpdate.setPurchaseMoneyFromMoney(userAssetsInfo.getPurchaseMoneyFromMoney().add(adjustAmount));
            }
            int affect = userAssetsInfoGateway.updateUserPurchaseBalance(userAssetsInfo, toUpdate);
            if (affect <= 0) {
                log.error("求购余额调账更新失败,applyNo={}", applyNo);
                throw new BusinessException(Status.SYSTEM.getCode(), "更新失败");
            }
        }
    }

    private AccountTypeEnum judgeAccountType(Integer accountType) {
        if (accountType.equals(AccountTypeEnum.BALANCE_1.getCode()) || accountType.equals(AccountTypeEnum.BALANCE_2.getCode())) {
            return AccountTypeEnum.BALANCE;
        }
        return AccountTypeEnum.PURCHASE_BALANCE;
    }

    private String getOneTradeNo(AdjustmentApplyDTO adjustmentApplyDTO) {
        Long typeId = null;
        if (!Objects.equals(adjustmentApplyDTO.getIncomeAssetType(), Constant.CONSTANT_LONG_0)) {
            typeId = adjustmentApplyDTO.getIncomeAssetType();
        } else {
            typeId = adjustmentApplyDTO.getExpenseAssetType();
        }
        return adjustmentApplyDTO.getApplyNo() + typeId;
    }

    private UU898UserAssetsRecordDTO buildAssetRecord(Long userId, Long typeId, String outTradeNo, BigDecimal changeMoney,
                                                      String serialNo, String tradeOrderNo,
                                                      UserAssetsInfoDTO userAssetsInfoDTO, Integer accountPayChannel,
                                                      Integer accountType, AdjustmentDirectionEnum direction, BigDecimal serviceFee) {

        if (direction == AdjustmentDirectionEnum.EXPENSE) {
            changeMoney = changeMoney.negate();
        }
        UU898UserAssetsRecordDTO assetRecord = new UU898UserAssetsRecordDTO();
        assetRecord.setUserId(userId);
        assetRecord.setTypeId(typeId.intValue());
        assetRecord.setTreadNo(outTradeNo + assetRecord.getTypeId());
        assetRecord.setMoney(userAssetsInfoDTO.getMoney());

        AccountTypeEnum accountTypeEnum = judgeAccountType(accountType);
        if (accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE) {
            assetRecord.setThisMoney(BigDecimal.ZERO);
            assetRecord.setAfterMoney(userAssetsInfoDTO.getMoney());
            assetRecord.setThisPurchaseMoney(changeMoney);
            if (accountPayChannel.equals(AccountPayChannelEnum.BALANCE.getCode())) {
                assetRecord.setAfterPurchaseMoney(userAssetsInfoDTO.getPurchaseMoney().add(changeMoney));
            } else {
                assetRecord.setAfterPurchaseMoney(userAssetsInfoDTO.getPurchaseMoney());
            }
        } else {
            assetRecord.setThisMoney(changeMoney);
            if (accountPayChannel.equals(AccountPayChannelEnum.BALANCE.getCode())) {
                assetRecord.setAfterMoney(userAssetsInfoDTO.getMoney().add(changeMoney));
            } else {
                assetRecord.setAfterMoney(userAssetsInfoDTO.getMoney());
            }
            assetRecord.setThisPurchaseMoney(BigDecimal.ZERO);
            assetRecord.setAfterPurchaseMoney(userAssetsInfoDTO.getPurchaseMoney());
        }
        if (Objects.nonNull(serviceFee)) {
            assetRecord.setChargeMoney(serviceFee);
        } else {
            assetRecord.setChargeMoney(BigDecimal.ZERO);
        }
        assetRecord.setBlockMoney(userAssetsInfoDTO.getBlockMoney());
        assetRecord.setThisBlockMoney(BigDecimal.ZERO);
        assetRecord.setAfterBlockMoney(userAssetsInfoDTO.getBlockMoney());
        assetRecord.setPurchaseMoney(userAssetsInfoDTO.getPurchaseMoney());
        assetRecord.setSerialNo(serialNo);
        assetRecord.setOrderNo(tradeOrderNo);
        if (StringUtils.isNotEmpty(serialNo)) {
            assetRecord.setPayOrderNo(serialNo);
        } else {
            assetRecord.setPayOrderNo(outTradeNo);
        }
        assetRecord.setRemark("");
        assetRecord.setAddTime(LocalDateTime.now());
        assetRecord.setCompleteTime(LocalDateTime.now());
        if (accountPayChannel != AccountPayChannelEnum.BALANCE.getCode()) {
            // 默认，不变动余额
            assetRecord.setAttr(Constant.CONSTANT_INTEGER_0);
            assetRecord.setAssetType(Constant.CONSTANT_INTEGER_1);
        } else {
            if (accountTypeEnum == AccountTypeEnum.BALANCE) {
                // 余额变动
                assetRecord.setAttr(Constant.CONSTANT_INTEGER_1);
                assetRecord.setAssetType(Constant.CONSTANT_INTEGER_1);
            } else {
                // 求购账户变动
                assetRecord.setAttr(Constant.CONSTANT_INTEGER_2);
                // 30表示求购账户变动
                assetRecord.setAssetType(30);
                accountPayChannel = AccountPayChannelEnum.PURCHASE_BALANCE.getCode();
            }
        }
        assetRecord.setStatus(NetStatusEnum.SUCCESS.getCode());
        assetRecord.setPayChannel(accountPayChannel);
        return assetRecord;
    }

    private void checkParam(AdjustApplyRequest request) {
        if (StringUtils.isEmpty(request.getRelatedOrderNo())) {
            log.warn("关联订单号为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "关联订单号为空");
        }

        if (Objects.isNull(request.getIncAccountInfo()) && Objects.isNull(request.getDecAccountInfo())) {
            // 增减信息均为空
            log.warn("收款用户或支出用户未选择,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "收款用户或支出用户未选择");
        }

        if (StringUtils.isEmpty(request.getApplyRemark())) {
            log.warn("申请备注为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "申请备注为空");
        }

        if (request.getApplyRemark().length() > adjustmentApplyConfig.getApplyRemarkLength()) {
            log.warn("申请备注长度超限,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "申请备注长度超限");
        }

        if (Objects.isNull(request.getAdjustSource())) {
            log.warn("调账来源为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账类型为空");
        }
        // 校验增加余额用户调账信息
        checkAccountInfo(request.getIncAccountInfo(), AdjustmentDirectionEnum.INCOME.getCode());
        // 校验减少余额用户调账信息
        checkAccountInfo(request.getDecAccountInfo(), AdjustmentDirectionEnum.EXPENSE.getCode());

        if (Objects.nonNull(request.getIncAccountInfo()) && Objects.nonNull(request.getDecAccountInfo())) {
            if (request.getDecAccountInfo().getUserId().equals(request.getIncAccountInfo().getUserId())) {
                // 同一用户,校验账户是否相同
                Set<Integer> accountTypeSet = request.getIncAccountInfo().getItemList().stream().map(AdjustApplyItemRequest::getAdjustAccountType).collect(Collectors.toSet());
                for (AdjustApplyItemRequest adjustApplyItemRequest : request.getDecAccountInfo().getItemList()) {
                    if (accountTypeSet.contains(adjustApplyItemRequest.getAdjustAccountType())) {
                        log.warn("同一用户增加和减少余额存在相同账户类型,request={}", JacksonUtils.writeValueAsString(request));
                        throw new BusinessException(Status.SYSTEM.getCode(), "调账类型错误");
                    }
                }
            }

            if (request.getDecAccountInfo().getAssetType().equals(request.getIncAccountInfo().getAssetType())) {
                log.warn("增加双方资金明细类型相同,request={}", JacksonUtils.writeValueAsString(request));
                throw new BusinessException(Status.SYSTEM.getCode(), "资金明细类型相同");
            }
        }
    }

    private void checkAccountInfo(AdjustAccountRequest accountInfoRequest, Integer direction) {
        if (Objects.isNull(accountInfoRequest)) {
            return;
        }

        if (Objects.isNull(accountInfoRequest.getUserId())) {
            log.warn("用户id为空,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "用户id为空");
        }

        if (Objects.isNull(accountInfoRequest.getAssetType())) {
            log.warn("资金明细类型为空,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "资金明细类型为空");
        }

        if (Objects.isNull(accountInfoRequest.getAccountPayChannelType())) {
            log.warn("调账支付类型为空,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账支付类型为空");
        }

        if (CollectionUtils.isEmpty(accountInfoRequest.getItemList())) {
            log.warn("调账金额明细为空,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账金额明细为空");
        }

        if (accountInfoRequest.getItemList().size() > 4) {
            log.warn("调账金额明细最多4行,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账金额明细最多可选4行");
        }

        if (Objects.nonNull(accountInfoRequest.getServiceFee()) && accountInfoRequest.getServiceFee().compareTo(BigDecimal.ZERO) < 0) {
            log.warn("手续费不能为负数,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "手续费不能为负数");
        }

        UserAssetsInfoDTO userAssetsInfo = userAssetsInfoGateway.getUserAssetsInfo(accountInfoRequest.getUserId());
        if (Objects.isNull(userAssetsInfo)) {
            log.warn("账户不存在,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "账户不存在");
        }


        AccountPayChannelEnum accountPayChannelEnum = AccountPayChannelEnum.getByCode(accountInfoRequest.getAccountPayChannelType());
        if (Objects.isNull(accountPayChannelEnum)) {
            log.warn("调账支付类型不存在,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "调账支付类型不存在");
        }

        UU898UserAssetsRecordTypeDTO userAssetsRecordTypeDTO = userAssetsRecordTypeGateway.queryById(accountInfoRequest.getAssetType());
        if (Objects.isNull(userAssetsRecordTypeDTO)) {
            log.warn("资金明细类型不存在,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
            throw new BusinessException(Status.SYSTEM.getCode(), "资金明细类型不存在");
        }

        if (!userAssetsRecordTypeDTO.getOperateType().equals(Constant.CONSTANT_INTEGER_2)) {
            if (!Objects.equals(userAssetsRecordTypeDTO.getOperateType(), direction)) {
                log.warn("资金类型不匹配,accountInfoRequest={}", JacksonUtils.writeValueAsString(accountInfoRequest));
                throw new BusinessException(Status.SYSTEM.getCode(), "资金类型不匹配");
            }
        }

        AdjustApplyItemRequest itemRequest = accountInfoRequest.getItemList().stream().findFirst().get();
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(itemRequest.getAdjustAccountType());

        Integer accountType = AccountTypeEnum.PURCHASE_BALANCE.getCode();
        if (accountTypeEnum == AccountTypeEnum.BALANCE_1 || accountTypeEnum == AccountTypeEnum.BALANCE_2) {
            accountType = AccountTypeEnum.BALANCE.getCode();
        }

        Set<Integer> existAccountTypeSet = new HashSet<>();
        for (AdjustApplyItemRequest adjustApplyItemRequest : accountInfoRequest.getItemList()) {
            accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(adjustApplyItemRequest.getAdjustAccountType());
            if (Objects.isNull(accountTypeEnum)) {
                log.warn("调账账户类型不存在,adjustApplyItemRequest={}", JacksonUtils.writeValueAsString(adjustApplyItemRequest));
                throw new BusinessException(Status.SYSTEM.getCode(), "调账账户类型不存在");
            }

            if (existAccountTypeSet.contains(accountTypeEnum.getCode())) {
                log.warn("存在相同的账户,accountInfoRequest={}", JacksonUtils.writeValueAsString(adjustApplyItemRequest));
                throw new BusinessException(Status.SYSTEM.getCode(), "存在相同账户");
            } else {
                existAccountTypeSet.add(accountTypeEnum.getCode());
            }

            if (Objects.isNull(adjustApplyItemRequest.getAdjustAmount())) {
                log.warn("调账金额为空,adjustApplyItemRequest={}", adjustApplyItemRequest);
                throw new BusinessException(Status.SYSTEM.getCode(), "调账金额为空");
            }

            if (!AmountUtils.checkAmountScale(adjustApplyItemRequest.getAdjustAmount(), Constant.CONSTANT_INTEGER_2)) {
                log.warn("金额小数最多2位,adjustApplyItemRequest={}", JacksonUtils.writeValueAsString(adjustApplyItemRequest));
                throw new BusinessException(Status.SYSTEM.getCode(), "金额最多两位2小数");
            }

            if (adjustApplyItemRequest.getAdjustAmount().compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("余额调账-调账金额异常,adjustApplyItemRequest={}", JacksonUtils.writeValueAsString(adjustApplyItemRequest));
                throw new BusinessException(Status.SYSTEM.getCode(), "调账金额最低0.01");
            }

            if (accountType.equals(AccountTypeEnum.PURCHASE_BALANCE.getCode())) {
                // 求购账户类型
                if (accountTypeEnum == AccountTypeEnum.BALANCE_1 || accountTypeEnum == AccountTypeEnum.BALANCE_2) {
                    log.warn("只可选择单一账户类型,request={}", JacksonUtils.writeValueAsString(adjustApplyItemRequest));
                    throw new BusinessException(Status.SYSTEM.getCode(), "只可选择单一账户类型");
                }
            } else if (accountType.equals(AccountTypeEnum.BALANCE.getCode())) {
                if (accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1 || accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2
                        || accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1 || accountTypeEnum == AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2) {
                    log.warn("只可选择单一账户类型,request={}", JacksonUtils.writeValueAsString(adjustApplyItemRequest));
                    throw new BusinessException(Status.SYSTEM.getCode(), "只可选择单一账户类型");
                }
            }
        }
    }

    private AdjustmentApplyDTO buildAdjustmentApplyDTO(AdjustApplyRequest request) {
        String applyNo = idWorkService.getNextIdLeafKey();
        Long incomeUserId = 0L;
        BigDecimal incomeAmount = BigDecimal.ZERO;
        Long incomeAssetType = 0L;
        Integer incomePayChannel = -1;
        BigDecimal incomeServiceFee = null;
        Long expenseUserId = 0L;
        BigDecimal expenseAmount = BigDecimal.ZERO;
        Long expenseAssetType = 0L;
        Integer expensePayChannel = -1;
        BigDecimal expenseServiceFee = null;

        if (Objects.nonNull(request.getIncAccountInfo())) {
            AdjustAccountRequest incAccountInfo = request.getIncAccountInfo();
            incomeUserId = incAccountInfo.getUserId();
            incomeAmount = incAccountInfo.getItemList().stream().map(AdjustApplyItemRequest::getAdjustAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            incomeAssetType = incAccountInfo.getAssetType();
            incomePayChannel = incAccountInfo.getAccountPayChannelType();
            if (Objects.nonNull(incAccountInfo.getServiceFee())) {
                incomeServiceFee = incAccountInfo.getServiceFee();
            }
        }
        if (Objects.nonNull(request.getDecAccountInfo())) {
            AdjustAccountRequest decAccountInfo = request.getDecAccountInfo();
            expenseUserId = decAccountInfo.getUserId();
            expenseAmount = decAccountInfo.getItemList().stream().map(AdjustApplyItemRequest::getAdjustAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            expenseAssetType = decAccountInfo.getAssetType();
            expensePayChannel = decAccountInfo.getAccountPayChannelType();
            if (Objects.nonNull(decAccountInfo.getServiceFee())) {
                expenseServiceFee = decAccountInfo.getServiceFee();
            }
        }

        return AdjustmentApplyDTO.builder()
                .applyNo(applyNo)
                .relatedOrderNo(request.getRelatedOrderNo())
                .payOrderNo(request.getPayOrderNo())
                .incomeUserId(incomeUserId)
                .incomeAmount(incomeAmount)
                .incomeAssetType(incomeAssetType)
                .incomePayChannel(incomePayChannel)
                .incomeServiceFee(incomeServiceFee)
                .expenseUserId(expenseUserId)
                .expenseAmount(expenseAmount)
                .expenseAssetType(expenseAssetType)
                .expensePayChannel(expensePayChannel)
                .expenseServiceFee(expenseServiceFee)
                .adjustSource(request.getAdjustSource())
                .status(AdjustmentApplyStatusEnum.APPLY_ING.getStatus())
                .applyBy(request.getApplyBy())
                .applyRemark(request.getApplyRemark())
                .batchNo(request.getBatchNo())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    private List<AdjustmentApplyDetailDTO> buildApplyDetailList(AdjustApplyRequest request, String applyNo) {
        List<AdjustmentApplyDetailDTO> applyDetailList = new ArrayList<>();
        if (Objects.nonNull(request.getIncAccountInfo())) {
            AdjustAccountRequest incAccountInfo = request.getIncAccountInfo();
            for (AdjustApplyItemRequest adjustApplyItemRequest : incAccountInfo.getItemList()) {
                AdjustmentApplyDetailDTO applyDetailDTO = buildAdjustApplyDetail(adjustApplyItemRequest, applyNo, AdjustmentDirectionEnum.INCOME.getCode());
                applyDetailList.add(applyDetailDTO);
            }
        }

        if (Objects.nonNull(request.getDecAccountInfo())) {
            AdjustAccountRequest decAccountInfo = request.getDecAccountInfo();
            for (AdjustApplyItemRequest adjustApplyItemRequest : decAccountInfo.getItemList()) {
                AdjustmentApplyDetailDTO applyDetailDTO = buildAdjustApplyDetail(adjustApplyItemRequest, applyNo, AdjustmentDirectionEnum.EXPENSE.getCode());
                applyDetailList.add(applyDetailDTO);
            }
        }
        return applyDetailList;
    }

    private AdjustmentApplyDetailDTO buildAdjustApplyDetail(AdjustApplyItemRequest adjustApplyItemRequest, String applyNo, Integer direction) {
        return AdjustmentApplyDetailDTO.builder()
                .applyNo(applyNo)
                .direction(direction)
                .adjustMoney(adjustApplyItemRequest.getAdjustAmount())
                .adjustAccountType(adjustApplyItemRequest.getAdjustAccountType())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    private AdjustAccountResponse buildAccountResponse(Map<Integer, List<AdjustmentApplyDetailDTO>> detailMap, AdjustmentDirectionEnum direction, Long userId, Long assetType, Integer accountPayChannel, BigDecimal serviceFee) {
        if (!detailMap.containsKey(direction.getCode())) {
            return null;
        }
        List<AdjustmentApplyDetailDTO> applyDetailList = detailMap.get(direction.getCode());
        List<AdjustApplyItemResponse> itemList = applyDetailList.stream().map(detailDto -> {
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(detailDto.getAdjustAccountType());
            return AdjustApplyItemResponse.builder()
                    .adjustAccountType(detailDto.getAdjustAccountType())
                    .adjustAccountTypeStr(Objects.isNull(accountTypeEnum) ? null : accountTypeEnum.getName())
                    .adjustAmount(AmountUtils.convertToString(detailDto.getAdjustMoney()))
                    .build();
        }).collect(Collectors.toList());
        UU898UserAssetsRecordTypeDTO userAssetsRecordTypeDTO = userAssetsRecordTypeGateway.queryById(assetType);
        AccountPayChannelEnum accountPayChannelEnum = AccountPayChannelEnum.getByCode(accountPayChannel);
        return AdjustAccountResponse.builder()
                .userId(userId)
                .assetType(assetType)
                .assetTypeStr(Objects.isNull(userAssetsRecordTypeDTO) ? null : userAssetsRecordTypeDTO.getTypeName())
                .accountPayChannelType(accountPayChannel)
                .accountPayChannelStr(Objects.isNull(accountPayChannelEnum) ? null : accountPayChannelEnum.getDesc())
                .serviceFee(AmountUtils.convertToString(serviceFee))
                .itemList(itemList)
                .build();
    }

    private SendSmsRequest buildSendSmsRequest(Long userId, String mobile, String smsCode, String templateCode, String sendSmsAppKey, String sendSmsAppSecret) {
        // 模版参数
        Map<String, String> params = Map.of("code", smsCode);
        // 鉴权参数
        AuthorizationInfo authorizationInfo = AuthorizationInfo.builder().time(String.valueOf(System.currentTimeMillis())).appKey(sendSmsAppKey).build();
        // md5加密
        authorizationInfo.setToken(getMD5(sendSmsAppSecret + authorizationInfo.getTime()));
        return SendSmsRequest.builder().userId(userId).requestId(UUID.randomUUID().toString()).mobiles(new String[]{mobile}).templateCode(templateCode).params(params).authorization(authorizationInfo).build();
    }

    public static String getMD5(String input) {
        try {
            // 获取MD5消息摘要实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算输入字符串的MD5值
            byte[] md5Bytes = md.digest(input.getBytes());
            // 将MD5字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("md5加密失败,input={}", input);
            // 如果MD5算法不可用，则抛出异常
            throw new BusinessException(Status.SYSTEM.getCode(), "请稍后再试，若有其他问题，请联系客服处理");
        }
    }

    private String generateSmsCode() {
        int smsCode;
        try {
            smsCode = ThreadLocalRandom.current().nextInt(100000, 1000000);
        } catch (Exception e) {
            log.error("[短信验证码] 生成失败:{}", ExceptionUtils.getMessage(e));
            throw new BusinessException(Status.SYSTEM.getCode(), "验证码发送失败，请点击重新发送");
        }
        return String.valueOf(smsCode);
    }

    private String getVerifyCodeKey(String key) {
        return "adjustment:verify:" + key;
    }

    private String getRepeatKey(String key) {
        return "adjustment:send:" + key;
    }

    private String getAmountStatisticKey() {
        return "adjustment:amount:" + DateTimeUtil.dayTimeFormat(LocalDateTime.now());
    }

    private String getInnerAccountAmountStatisticKey() {
        return "adjustment:inner:account:amount:" + DateTimeUtil.dayTimeFormat(LocalDateTime.now());
    }

    private String getOuterAccountAmountStatisticKey() {
        return "adjustment:outer:account:amount:" + DateTimeUtil.dayTimeFormat(LocalDateTime.now());
    }

    private boolean isInnerAccount(Long userId) {
        return adjustmentApplyConfig.getInnerAccountIds().contains(userId);
    }

    private Long getStaticAmount(String key, Long adjustmentAmount) {
        Object amount = ClearRedisUtils.getValue(key);
        if (Objects.isNull(amount)) {
            return adjustmentAmount;
        }
        return Long.parseLong(amount.toString()) + adjustmentAmount;
    }

    private void checkAndSetAmount(BigDecimal amount, Long userId, Long innerAmount, Long outerAmount) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        Long adjustAmount = AmountUtils.convertToCent(amount);
        boolean isInnerAccount = isInnerAccount(userId);
        if (isInnerAccount) {
            Long newInnerAccountStatisticAmount = getStaticAmount(getInnerAccountAmountStatisticKey(), adjustAmount);
            if (newInnerAccountStatisticAmount.compareTo(adjustmentApplyConfig.getSingleInnerAccountAmountLimit()) > 0) {
                log.warn("内部户单日金额超出限制,newInnerAccountStatisticAmount={},request={}", newInnerAccountStatisticAmount);
                throw new BusinessException(Status.SYSTEM.getCode(), "内部户单日金额超出限制");
            }
        } else {
            Long newOuterAccountStatisticAmount = getStaticAmount(getOuterAccountAmountStatisticKey(), adjustAmount);
            if (newOuterAccountStatisticAmount.compareTo(adjustmentApplyConfig.getSingleOuterAccountAmountLimit()) > 0) {
                log.warn("外部户单日金额超出限制,newOuterAccountStatisticAmount={},request={}", newOuterAccountStatisticAmount);
                throw new BusinessException(Status.SYSTEM.getCode(), "外部户单日金额超出限制");
            }
        }
        if (isInnerAccount) {
            innerAmount = innerAmount + adjustAmount;
        } else {
            outerAmount = outerAmount + adjustAmount;
        }
    }

    private void accumulatedAmount(String key, Long amount) {
        if (amount <= 0) {
            return;
        }
        ClearRedisUtils.increment(key, amount, adjustmentApplyConfig.getStatisticAmountCacheTime(), TimeUnit.HOURS);
    }

    private AdjustmentApplyDTO auditParamCheckAndGet(AdjustApplyAuditRequest request) {
        if (Objects.isNull(request.getApplyId())) {
            log.warn("申请单id为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "申请单id为空");
        }

        if (Objects.isNull(request.getAuditType())) {
            log.warn("审核类型为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "审核类型为空");
        }

        AuditTypeEnum auditTypeEnum = AuditTypeEnum.getEnum(request.getAuditType());
        if (Objects.isNull(auditTypeEnum)) {
            log.warn("审核类型传参错误,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "审核类型传参错误");
        }

        if (StringUtils.isEmpty(request.getAuditRemark())) {
            log.warn("审核备注为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "审核备注为空");
        }

        if (request.getAuditRemark().length() > adjustmentApplyConfig.getAuditRemarkLength()) {
            log.warn("审核备注长度超出限制,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "审核备注长度超出限制");
        }

        if (auditTypeEnum == AuditTypeEnum.PASS && StringUtils.isEmpty(request.getVerifyCode())) {
            log.warn("验证码为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "请输入验证码");
        }

        if (StringUtils.isEmpty(request.getAuditBy())) {
            log.warn("审核人为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "审核人为空");
        }

        AdjustmentApplyDTO adjustmentApplyDTO = adjustmentApplyGateway.queryById(request.getApplyId());
        if (Objects.isNull(adjustmentApplyDTO)) {
            log.warn("申请单查询为空,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "申请记录不存在");
        }

        if (adjustmentApplyDTO.getApplyBy().equals(request.getAuditBy()) && auditTypeEnum == AuditTypeEnum.PASS) {
            log.warn("审核人和申请人不能是同一个,request={},adjustmentApplyDTO={}", JacksonUtils.writeValueAsString(request), JacksonUtils.writeValueAsString(adjustmentApplyDTO));
            throw new BusinessException(Status.SYSTEM.getCode(), "系统不支持同一个人申请、同一个人审核");
        }

        if (!Objects.equals(adjustmentApplyDTO.getStatus(), AdjustmentApplyStatusEnum.APPLY_ING.getStatus())) {
            log.warn("申请记录状态不是申请中，无法进行审核操作,request={}", JacksonUtils.writeValueAsString(request));
            throw new BusinessException(Status.SYSTEM.getCode(), "当前状态无法进行审核");
        }

        return adjustmentApplyDTO;
    }

    private String smsCodeCheckKey(String applyNo) {
        return "adjustment:audit:applyNo:" + applyNo;
    }

}
