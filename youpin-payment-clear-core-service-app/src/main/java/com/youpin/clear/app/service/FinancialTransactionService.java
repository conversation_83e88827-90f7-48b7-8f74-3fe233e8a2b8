package com.youpin.clear.app.service;

import com.youpin.clear.client.request.QueryAssetInfoMoneyRequest;
import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.QueryAssetInfoMoneyResponse;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.client.response.financial.UserAssetsRecordResponse;

import java.util.List;

public interface FinancialTransactionService {
    FinancialResponse pay(PayFinancialRequest request);

    FinancialResponse refund(RefundFinancialRequest request);

    FinancialResponse settlement(SettlementFinancialRequest request);

    List<UserAssetsRecordResponse> query(QueryFinancialRequest request);

    FinancialResponse specialSettlement(SettlementFinancialRequest request);

    FinancialResponse withdrawal(WithdrawalFinancialRequest request);

    QueryAssetInfoMoneyResponse queryAssetInfoMoney(QueryAssetInfoMoneyRequest request);
}
