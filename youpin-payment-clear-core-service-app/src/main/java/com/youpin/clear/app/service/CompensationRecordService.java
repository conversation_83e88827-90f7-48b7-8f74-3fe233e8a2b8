package com.youpin.clear.app.service;


import com.youpin.clear.client.request.CompensationRecordDeleteRequest;
import com.youpin.clear.client.request.CompensationRecordRetryJobRequest;
import com.youpin.clear.client.response.CompensationRecordRetryJobResponse;

public interface CompensationRecordService {


    CompensationRecordRetryJobResponse countExceptionRetryJob(CompensationRecordRetryJobRequest request);

    CompensationRecordRetryJobResponse selectExceptionRetryJob(CompensationRecordRetryJobRequest request);

    void handle(CompensationRecordRetryJobRequest request);

    void deleteCompensationRecord(CompensationRecordDeleteRequest request);

    void deleteMqCompensationRecord(CompensationRecordDeleteRequest request);
}
