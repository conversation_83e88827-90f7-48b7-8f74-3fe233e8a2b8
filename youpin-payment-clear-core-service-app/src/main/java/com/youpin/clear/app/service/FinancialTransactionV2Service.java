package com.youpin.clear.app.service;

import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.financial.FinancialResponse;


public interface FinancialTransactionV2Service {
    FinancialResponse pay(PayFinancialRequest request);

    FinancialResponse refund(RefundFinancialRequest request);

    FinancialResponse settlement(SettlementFinancialRequest request);

    FinancialResponse specialSettlement(SettlementFinancialRequest request);
}
