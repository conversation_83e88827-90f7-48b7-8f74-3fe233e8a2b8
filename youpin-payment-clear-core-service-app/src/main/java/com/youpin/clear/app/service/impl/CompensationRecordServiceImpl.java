package com.youpin.clear.app.service.impl;


import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.app.service.CompensationRecordService;
import com.youpin.clear.app.service.NetPlatformUserRecordService;
import com.youpin.clear.app.service.PolarToClearAssetsRecordService;
import com.youpin.clear.app.service.SeparateAccountService;
import com.youpin.clear.client.request.CompensationRecordDeleteRequest;
import com.youpin.clear.client.request.CompensationRecordRetryJobRequest;
import com.youpin.clear.client.response.CompensationRecordRetryJobResponse;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class CompensationRecordServiceImpl implements CompensationRecordService {

    /**
     * 策略外的默认间隔时间
     */
    private final static Integer DEFAULT_INTERVAL = 1;

    @Autowired
    CompensationRecordGateway compensationRecordGateway;

    @Autowired
    SeparateAccountService separateAccountService;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;


    @Autowired
    PolarToClearAssetsRecordService polarToClearAssetsRecordService;

    @Autowired
    NetPlatformUserRecordService netPlatformUserRecordService;


    @Override
    public CompensationRecordRetryJobResponse countExceptionRetryJob(CompensationRecordRetryJobRequest request) {
        return CompensationRecordRetryJobResponse.builder().count(compensationRecordGateway.countExceptionRetryJob(BeanUtilsWrapper.convert(request, CompensationRecordRetryJobDTO::new))).build();
    }

    @Override
    public CompensationRecordRetryJobResponse selectExceptionRetryJob(CompensationRecordRetryJobRequest request) {
        return CompensationRecordRetryJobResponse.builder().ids(compensationRecordGateway.selectExceptionRetryJob(BeanUtilsWrapper.convert(request, CompensationRecordRetryJobDTO::new))).build();
    }

    @Override
    public void handle(CompensationRecordRetryJobRequest request) {
        if (null == request || null == request.getId()) {
            return;
        }
        CompensationRecordDTO compensationRecordDTO = compensationRecordGateway.queryBizExceptionRecordByPrimaryKey(request.getId());
        if (null == compensationRecordDTO || compensationRecordDTO.getRetryFlag() == Constant.CONSTANT_INTEGER_0 || StringUtils.isBlank(compensationRecordDTO.getRetryMsg())) {
            return;
        }
        BizCompensationSceneEnum bizCompensationSceneEnum = BizCompensationSceneEnum.MAP.getOrDefault(compensationRecordDTO.getBizScene(), null);
        if (null == bizCompensationSceneEnum) {
            return;
        }
        Boolean flag = Boolean.FALSE;
        switch (bizCompensationSceneEnum) {
            case ASSET:
                List<UU898UserAssetsRecordDTO> userAssetsRecordDTOList = JSON.parseArray(compensationRecordDTO.getRetryMsg(), UU898UserAssetsRecordDTO.class);
                if (paymentClearParamsConfig.getDeduplicationFlag()) {
                    //判断数据是否重复
                    boolean checkIdempotentData = separateAccountService.checkIdempotentUserAssetsRecordIdempotent(userAssetsRecordDTOList);
                    if (checkIdempotentData) {
                        log.error("[异常记录] 重复记录:{}", compensationRecordDTO.getUniqueKey());
                        flag = Boolean.TRUE;
                        break;
                    }
                }
                try {
                    separateAccountService.handle(userAssetsRecordDTOList);
                    flag = Boolean.TRUE;
                } catch (Exception e) {
                    log.error("[资金分账记录] 错误:{}", ExceptionUtils.getStackTrace(e));
                }
                break;
            case ASSET_SPLIT:
                List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOList = JSON.parseArray(compensationRecordDTO.getRetryMsg(), ClearUserAssetsRecordDTO.class);
                try {
                    polarToClearAssetsRecordService.userAssetsRecordSplit(clearUserAssetsRecordDTOList);
                    flag = Boolean.TRUE;
                } catch (Exception e) {
                    log.error("[资金明细消费拆解] 错误:{}", ExceptionUtils.getStackTrace(e));
                }
                break;
            case SYNC:
                try {
                    polarToClearAssetsRecordService.batchClearUserAssetsRecordDTO(List.of(JSON.parseObject(compensationRecordDTO.getRetryMsg(), ClearUserAssetsRecordDTO.class)));
                    flag = Boolean.TRUE;
                } catch (Exception e) {
                    log.error("[资金明细消费同步数据] 错误:{}", ExceptionUtils.getStackTrace(e));
                }
                break;
            case NET_PLATFORM_USER_RECORD:
                try {
                    netPlatformUserRecordService.bufferAccountingProcess(JSON.parseObject(compensationRecordDTO.getRetryMsg(), NetPlatformUserRecordMessage.class));
                    flag = Boolean.TRUE;
                } catch (Exception e) {
                    log.error("[net平台户资金异步缓冲顺序处理] 错误:{}", ExceptionUtils.getStackTrace(e));
                }
                break;

            case TRANSACTION_SERVICE_FEE_STATEMENT_TOPIC:
                try {
                    polarToClearAssetsRecordService.sendTransactionServiceFeeOperationRecord(List.of(JSON.parseObject(compensationRecordDTO.getRetryMsg(), ClearUserAssetsRecordDTO.class)));
                    flag = Boolean.TRUE;
                } catch (Exception e) {
                    log.error("[交易服务费流水] 错误:{}", ExceptionUtils.getStackTrace(e));
                }
                break;

            default:
                log.error("[异常记录] 补单逻辑未找到重试方法:{}", bizCompensationSceneEnum);
                break;
        }
        if (flag) {
            compensationRecordDTO.setHandleStatus(Constant.CONSTANT_INTEGER_3);
        } else {
            List<Integer> retryIntervalPolicy = request.getRetryIntervalPolicy();
            Integer interval;
            if (retryIntervalPolicy.size() <= compensationRecordDTO.getCount()) {
                // 次数超过已知策略，给默认值
                interval = DEFAULT_INTERVAL;
            } else {
                // 从策略中获取
                interval = retryIntervalPolicy.get(compensationRecordDTO.getCount());
            }
            if (compensationRecordDTO.getCount() < request.getMaxRetryCount()) {
                compensationRecordDTO.setNextRetryTime(LocalDateTime.now().plusSeconds(interval));
            } else {
                compensationRecordDTO.setHandleStatus(Constant.CONSTANT_INTEGER_4);
            }
        }
        //retryMsg
        compensationRecordDTO.setRetryMsg(null);//这个参数不再更新
        compensationRecordDTO.setCount(compensationRecordDTO.getCount() + Constant.CONSTANT_INTEGER_1);
        compensationRecordGateway.updateRecord(compensationRecordDTO);
    }


    @Override
    public void deleteCompensationRecord(CompensationRecordDeleteRequest request) {
        for (int i = 0; i < request.getPageNum(); i++) {
            if (null != request.getMaxRetryCount() && request.getMaxRetryCount() > 0) {
                compensationRecordGateway.deleteByCountSum(request.getMaxRetryCount(), request.getPageSize());
            }
            if (StringUtils.isNotBlank(request.getCreateTime())) {
                compensationRecordGateway.deleteByCreateTime(LocalDateTime.parse(request.getCreateTime()), request.getPageSize());
            }
            if (null != request.getHandleStatus() && request.getHandleStatus() > 0) {
                compensationRecordGateway.deleteByHandleStatus(request.getHandleStatus(), request.getPageSize());
            }
        }
        log.info("[异常记录] 删除异常记录完成");
    }

    @Override
    public void deleteMqCompensationRecord(CompensationRecordDeleteRequest request) {
        for (int i = 0; i < request.getPageNum(); i++) {
            List<CompensationRecordDTO> compensationRecordDTOList = compensationRecordGateway.selectMaxRetryCount(request.getMaxRetryCount(), i * request.getPageSize(), request.getPageSize());
            for (CompensationRecordDTO compensationRecordDTO : compensationRecordDTOList) {
                List<UU898UserAssetsRecordDTO> userAssetsRecordDTOList = JSON.parseArray(compensationRecordDTO.getRetryMsg(), UU898UserAssetsRecordDTO.class);
                boolean flag = separateAccountService.checkIdempotentUserAssetsRecordIdempotent(userAssetsRecordDTOList);
                if (flag) {
                    log.info("[异常记录] 是否删除:{} 删除MQ重复记录:{}", request.getDeleteMq(), compensationRecordDTO.getUniqueKey());
                    if (Boolean.TRUE.equals(request.getDeleteMq())) {
                        compensationRecordGateway.deleteByPrimaryKey(compensationRecordDTO.getId());
                    }
                }
            }
        }
    }

}
