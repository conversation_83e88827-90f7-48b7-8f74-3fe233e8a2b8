package com.youpin.clear.app.consumer;


import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.NetPlatformUserRecordService;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.NetPlatformUserRecordMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import com.youpin.clear.infrastructure.config.CompensationUniqueKeyWorkspaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
import youpin.commons.rocketmq.core.RocketMQListener;

/**
 * 暂不使用
 * net平台户资金异步缓冲顺序处理-过度-用户分组
 * 开发-d1-通用  rmq-cn-wwo388zkk07
 * 测试-t1-通用2 rmq-cn-omn3llwdb4q
 * 预发-pre-通用2 rmq-cn-vo93muk7b09
 */
@Slf4j
@Component
@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ, topic = MQConfig.NET_PLATFORM_USER_RECORDS_TOPIC, expression = MQConfig.NET_PLATFORM_USER_RECORDS_TAG, consumerGroup = MQConfig.NET_PLATFORM_USER_RECORDS_GROUP, filterExpressionType = FilterExpressionType.TAG)
public class NetPlatformUserRecordConsumer implements RocketMQListener<String> {

    @Autowired
    NetPlatformUserRecordService netPlatformUserRecordService;

    @Autowired
    CompensationRecordGateway compensationRecordGateway;

    @Autowired
    CompensationUniqueKeyWorkspaces compensationUniqueKeyWorkspaces;


    /**
     * 不做补单是因为 数据已经存了备份
     * 使用MQ 异常处理
     */
    @Override
    public void onMessage(String msg) {
        if (StringUtils.isEmpty(msg)) {
            log.error("[net平台户资金异步缓冲顺序处理] 平台户消费处理 监听消息:接收到了空消息");
            return;
        }
        NetPlatformUserRecordMessage netPlatformUserRecordMessage = JSON.parseObject(msg, NetPlatformUserRecordMessage.class);
        log.info("[net平台户资金异步缓冲顺序处理]  接收到的消息:{}", netPlatformUserRecordMessage);

        try {
            netPlatformUserRecordService.bufferAccountingProcess(netPlatformUserRecordMessage);
        } catch (Exception e) {
            log.warn("[net平台户资金异步缓冲顺序处理] 业务异常 保存补偿表 error,{}", ExceptionUtils.getStackTrace(e));
            saveCompensationRecord(netPlatformUserRecordMessage);
        }
    }

    /**
     * 保存异常记录
     */
    private void saveCompensationRecord(NetPlatformUserRecordMessage dto) {
        String uniqueKey = compensationUniqueKeyWorkspaces.getNetPlatformUserRecordConsumerUniqueKey(dto.getUserId(), dto.getTreadNo());
        try {
            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(dto), BizCompensationSceneEnum.NET_PLATFORM_USER_RECORD);
        } catch (Exception e) {
            log.error("[net平台户资金异步缓冲顺序处理] {} 错误 保存补偿表错误 Error:{}", uniqueKey, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_CONSUMER_FAILED);
        }
        log.info("[net平台户资金异步缓冲顺序处理] 保存补偿表 完成:{}", uniqueKey);
    }


}
