package com.youpin.clear.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.AccountAssetsTypeService;
import com.youpin.clear.app.service.PolarToClearAssetsRecordService;
import com.youpin.clear.client.enums.ClearUserAssetsTagEnum;
import com.youpin.clear.client.request.ClearAssetsTagRequest;
import com.youpin.clear.client.request.PolarToClearAssetsRecordRequest;
import com.youpin.clear.client.response.ClearAssetsTagResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.member.BillItemMemberExtension;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsTagDTO;
import com.youpin.clear.domain.gateway.ClearUserAssetsRecordGateway;
import com.youpin.clear.domain.gateway.UserAssetsRecordPolarGateway;
import com.youpin.clear.domain.gateway.UserAssetsTagGateway;
import com.youpin.clear.domain.servcie.RocketMqService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord;
import com.youpin.clear.infrastructure.feign.impl.SellOrderFeignService;
import com.youpin.clear.infrastructure.mapper.TransactionServiceFeeStatementRecordMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PolarToClearAssetsRecordServiceImpl implements PolarToClearAssetsRecordService {

    @Autowired
    UserAssetsRecordPolarGateway userAssetsRecordPolarGateway;

    @Autowired
    ClearUserAssetsRecordGateway clearUserAssetsRecordGateway;

    @Autowired
    UserAssetsTagGateway userAssetsTagGateway;

    @Autowired
    RocketMqService rocketMqService;

    @Autowired
    AccountAssetsTypeService accountAssetsTypeService;


    @Override
    public void userAssetsRecordSplit(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList) {
        Set<Integer> typeIdList = userAssetsRecordDTOList.stream().map(ClearUserAssetsRecordDTO::getTypeId).collect(Collectors.toSet());
        Map<Integer, String> typeIdNameList = accountAssetsTypeService.gatAccountAssetsTypeByCode(typeIdList);
        for (ClearUserAssetsRecordDTO item : userAssetsRecordDTOList) {
            //剔除额外数据
            if (item.getId().equals(ClearConstants.CONSTANT_LONG_0) && StringUtils.isBlank(item.getTreadNo())) {
                continue;
            }
            BillItemMemberExtension.BillItemMemberExtensionBuilder billItemMemberExtensionBuilder = BillItemMemberExtension.builder();
            billItemMemberExtensionBuilder.collectType(item.getCollectType());
            billItemMemberExtensionBuilder.isLeaseOrder(item.getIsLeaseOrder());
            billItemMemberExtensionBuilder.balance2TransferAmount(item.getBalance2TransferAmount());
//            billItemMemberExtensionBuilder.merchantId(item.getMerchantId());
            billItemMemberExtensionBuilder.businessType(item.getBusinessType());
            billItemMemberExtensionBuilder.subBusType(item.getSubBusType());
            //扩展字段
            item.setExt(JSON.toJSONString(billItemMemberExtensionBuilder.build()));
            item.setTypeName(typeIdNameList.get(item.getTypeId()));
            rocketMqService.sendDynamicsMQFifoMsg(MQConfig.PAYMENT_CLEAR_MQ, MQConfig.USER_ACCOUNT_RECORD_SYNC_TOPIC, MQConfig.USER_ACCOUNT_RECORD_SYNC_TAG, String.valueOf(item.getUserId()), JSON.toJSONString(item), true);
        }
    }


    @Autowired
    TransactionServiceFeeStatementRecordMapper transactionServiceFeeStatementRecordMapper;

    @Autowired
    SellOrderFeignService sellOrderFeignService;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;
    /**
     * yyyyMMdd
     */
    static final String DATE_FORMAT_YMD_CODE = "yyyyMMdd";

    static String localDate2Str(LocalDateTime fromDate) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT_YMD_CODE);
        return fromDate.format(df);
    }

    @Override
    public void sendTransactionServiceFeeOperationRecord(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList) {
        log.info("[资金明细消费拆解] 交易服务费流水  开始");
        for (ClearUserAssetsRecordDTO item : userAssetsRecordDTOList) {
            if (!item.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_181) && !item.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_182)) {
                continue;
            }
            TransactionServiceFeeStatementRecord transactionServiceFeeStatementRecord = TransactionServiceFeeStatementRecord.builder()
                    .userId(item.getUserId())
                    .userAssetsRecordId(item.getUserAssetsRecordId())
                    .typeId(item.getTypeId())
                    .treadNo(item.getTreadNo())
                    .orderNo(item.getOrderNo())
                    .completeTime(item.getCompleteTime())
                    .status(ClearConstants.CONSTANT_INTEGER_2)
                    .feeMoney(item.getThisMoney().abs()).build();

            Integer orderFeeType = paymentClearParamsConfig.getTransactionServiceFeeOrderType();
            //查询订单是否预售单
            Map<String, Integer> queryIsPresaleResponseMap = sellOrderFeignService.sellOrderIsPresale(List.of(transactionServiceFeeStatementRecord.getOrderNo()));
            if (null != queryIsPresaleResponseMap) {
                orderFeeType = queryIsPresaleResponseMap.getOrDefault(transactionServiceFeeStatementRecord.getOrderNo(), orderFeeType);
            }
            transactionServiceFeeStatementRecord.setOrderFeeType(orderFeeType);

            try {
                transactionServiceFeeStatementRecordMapper.insertSelective(transactionServiceFeeStatementRecord);
            } catch (DataIntegrityViolationException e) {
                log.warn("重复插入数据，忽略:{}", JSON.toJSONString(transactionServiceFeeStatementRecord));
            }
//            rocketMqService.sendDynamicsMQFifoMsg(MQConfig.PAYMENT_CLEAR_MQ, MQConfig.TRANSACTION_SERVICE_FEE_STATEMENT_TOPIC, MQConfig.TRANSACTION_SERVICE_FEE_STATEMENT_TAG, String.valueOf(item.getUserId()), JSON.toJSONString(transactionServiceFeeStatementRecord), Boolean.FALSE);
        }
        log.info("[资金明细消费拆解] 交易服务费流水  完成");

    }

    @Override
    public Long maxUserAssetsRecordId(PolarToClearAssetsRecordRequest request) {
        return clearUserAssetsRecordGateway.maxUserAssetsRecordId(request.getUserId());
    }


    /**
     * polar资金同步分账资金库
     */
    @Override
    public Long polarToClearAssetsRecord(PolarToClearAssetsRecordRequest request) {
        //获取用户已经同步的最大ID
        Long maxUserAssetsRecordId = ClearConstants.CONSTANT_LONG_0;
        if (null != request.getMaxUserAssetsRecordId()) {
            maxUserAssetsRecordId = request.getMaxUserAssetsRecordId();
        }
        LocalDateTime startTime = StringUtils.isBlank(request.getStartTime()) ? null : LocalDateTime.parse(request.getStartTime());
        LocalDateTime endTime = StringUtils.isBlank(request.getEndTime()) ? null : LocalDateTime.parse(request.getEndTime());
        //原始数据
        List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOSList = userAssetsRecordPolarGateway.selectPageByUserId(request.getUserId(), request.getPageIndex(), request.getPageSize(), request.getTypeIdList(), maxUserAssetsRecordId, startTime, endTime);

        if (CollectionUtils.isEmpty(clearUserAssetsRecordDTOSList)) {
            return ClearConstants.CONSTANT_LONG_0;
        }

        batchClearUserAssetsRecordDTO(clearUserAssetsRecordDTOSList);

        if (clearUserAssetsRecordDTOSList.size() < request.getPageSize()) {
            return ClearConstants.CONSTANT_LONG_0;
        }
        //取出最大的ID
        return clearUserAssetsRecordDTOSList.stream().map(ClearUserAssetsRecordDTO::getUserAssetsRecordId).max(Long::compareTo).orElse(ClearConstants.CONSTANT_LONG_0);
    }

    @Override
    public Integer batchClearUserAssetsRecordDTO(List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOSList) {
        //保存数据
        List<ClearUserAssetsRecordDTO> saveClearUserAssetsRecordDTOList = clearUserAssetsRecordGateway.batchClearUserAssetsRecordDTO(clearUserAssetsRecordDTOSList);
        //拆分统计
        if (saveClearUserAssetsRecordDTOList == null || saveClearUserAssetsRecordDTOList.isEmpty()) {
            return ClearConstants.CONSTANT_INT_0;
        }
        saveUserAssetsTag(saveClearUserAssetsRecordDTOList);
        return ClearConstants.CONSTANT_INTEGER_1;
    }


    @Override
    public Integer assetsTagSyncByUserId(ClearAssetsTagRequest request) {
        List<UserAssetsTagDTO> daoList = userAssetsTagGateway.getUserAssetsTagByUserId(request.getUserId());
        if (CollectionUtils.isEmpty(daoList)) {
            return 0;
        }
        userAssetsTagGateway.syncDateCache(daoList);
        return daoList.size();
    }

    @Override
    public void assetsTagDelete(ClearAssetsTagRequest request) {
        userAssetsTagGateway.assetsTagDelete(request.getRedisKeySuffix(), request.getUserId(), request.getTagCode());
    }

    @Override
    public List<ClearAssetsTagResponse> assetsTagCacheGetUserId(ClearAssetsTagRequest request) {
        List<UserAssetsTagDTO> dtoList = userAssetsTagGateway.assetsTagCacheGetUserId(request.getUserId(), request.getTagCode());
        return BeanUtilsWrapper.convertList(dtoList, ClearAssetsTagResponse::new);
    }


    @Override
    public Integer assetsTagSync(ClearAssetsTagRequest request) {
        List<UserAssetsTagDTO> daoList = userAssetsTagGateway.selectUserAssetsTagByTableName(request.getTableSuffix(), request.getPageIndex(), request.getPageSize(), request.getMinId());
        if (CollectionUtils.isEmpty(daoList)) {
            return 0;
        }
        userAssetsTagGateway.syncDateCache(daoList);
        return daoList.size();
    }


    private void saveUserAssetsTag(List<ClearUserAssetsRecordDTO> saveClearUserAssetsRecordDTOList) {
        Map<String, UserAssetsTagDTO> tagMap = new ConcurrentHashMap<>();
        for (ClearUserAssetsRecordDTO clearUserAssetsRecordDTO : saveClearUserAssetsRecordDTOList) {
            if (!clearUserAssetsRecordDTO.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
                continue;
            }
            BigDecimal addBigDecimal = clearUserAssetsRecordDTO.getThisMoney().abs().add(clearUserAssetsRecordDTO.getThisPurchaseMoney().abs());
            //充值
            if (ClearConstants.CONSTANT_INTEGER_1.equals(clearUserAssetsRecordDTO.getTypeId()) || ClearConstants.CONSTANT_INTEGER_43.equals(clearUserAssetsRecordDTO.getTypeId())) {
                UserAssetsTagDTO accumulateRechargeTag = tagMap.getOrDefault(ClearUserAssetsTagEnum.ACCUMULATE_RECHARGE.getTagCode() + ":" + clearUserAssetsRecordDTO.getUserId(), null);
                if (null == accumulateRechargeTag) {
                    accumulateRechargeTag = new UserAssetsTagDTO();
                    accumulateRechargeTag.setUserId(clearUserAssetsRecordDTO.getUserId());
                    accumulateRechargeTag.setTagCode(ClearUserAssetsTagEnum.ACCUMULATE_RECHARGE.getTagCode());
                    accumulateRechargeTag.setTagValueDecimal(addBigDecimal);
                    accumulateRechargeTag.setCreateTime(clearUserAssetsRecordDTO.getAddTime());
                } else {
                    accumulateRechargeTag.setTagValueDecimal(accumulateRechargeTag.getTagValueDecimal().add(addBigDecimal));
                }
                tagMap.put(ClearUserAssetsTagEnum.ACCUMULATE_RECHARGE.getTagCode() + ":" + clearUserAssetsRecordDTO.getUserId(), accumulateRechargeTag);
            }
            //提现
            if (ClearConstants.CONSTANT_INTEGER_2.equals(clearUserAssetsRecordDTO.getTypeId()) || ClearConstants.CONSTANT_INTEGER_44.equals(clearUserAssetsRecordDTO.getTypeId())) {
                UserAssetsTagDTO accumulateWithdrawalTag = tagMap.getOrDefault(ClearUserAssetsTagEnum.ACCUMULATE_WITHDRAWAL.getTagCode() + ":" + clearUserAssetsRecordDTO.getUserId(), null);
                if (null == accumulateWithdrawalTag) {
                    accumulateWithdrawalTag = new UserAssetsTagDTO();
                    accumulateWithdrawalTag.setUserId(clearUserAssetsRecordDTO.getUserId());
                    accumulateWithdrawalTag.setTagCode(ClearUserAssetsTagEnum.ACCUMULATE_WITHDRAWAL.getTagCode());
                    accumulateWithdrawalTag.setTagValueDecimal(addBigDecimal);
                    accumulateWithdrawalTag.setCreateTime(clearUserAssetsRecordDTO.getAddTime());
                } else {
                    accumulateWithdrawalTag.setTagValueDecimal(accumulateWithdrawalTag.getTagValueDecimal().add(addBigDecimal));
                }
                tagMap.put(ClearUserAssetsTagEnum.ACCUMULATE_WITHDRAWAL.getTagCode() + ":" + clearUserAssetsRecordDTO.getUserId(), accumulateWithdrawalTag);
            }
        }
        //保存或更新
        tagMap.values().forEach(userAssetsTagDTO -> {
            UserAssetsTagDTO userAssetsTagDb = userAssetsTagGateway.getUserAssetsTagByTag(userAssetsTagDTO.getUserId(), userAssetsTagDTO.getTagCode());
            if (null == userAssetsTagDb) {
                try {
                    userAssetsTagGateway.saveUserAssetsTag(userAssetsTagDTO);
                } catch (DuplicateKeyException e) {
                    log.warn("saveUserAssetsTag 保存失败 尝试修改一次 error:{}", e.getMessage());
                    //尝试修改
                    userAssetsTagDb = userAssetsTagGateway.getUserAssetsTagByTag(userAssetsTagDTO.getUserId(), userAssetsTagDTO.getTagCode());
                    if (null != userAssetsTagDb) {
                        userAssetsTagGateway.updateBigDecimalByUserId(userAssetsTagDb.getId(), userAssetsTagDb.getUserId(), userAssetsTagDb.getTagCode(), userAssetsTagDTO.getTagValueDecimal());
                    } else {
                        log.warn("saveUserAssetsTag 保存失败 尝试修改一次 仍然失败 userId:{},code:{},tagValueDecimal:{}", userAssetsTagDTO.getUserId(), userAssetsTagDTO.getTagCode(), userAssetsTagDTO.getTagValueDecimal());
                    }
                }
            } else {
                userAssetsTagGateway.updateBigDecimalByUserId(userAssetsTagDb.getId(), userAssetsTagDb.getUserId(), userAssetsTagDb.getTagCode(), userAssetsTagDTO.getTagValueDecimal());
            }
        });
    }
}
