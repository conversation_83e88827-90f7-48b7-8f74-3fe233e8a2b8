package com.youpin.clear.app.consumer;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.SeparateAccountService;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import com.youpin.clear.infrastructure.config.LockKeyWorkspaces;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
import youpin.commons.rocketmq.core.RocketMQListener;

import java.nio.ByteBuffer;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 资金明细消费V2
 */
@Slf4j
@Component
@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ, topic = MQConfig.USER_ACCOUNT_RECORD_TOPIC, expression = MQConfig.USER_ACCOUNT_RECORD_TAG, consumerGroup = MQConfig.USER_ACCOUNT_RECORD_CONSUMER_GROUP, filterExpressionType = FilterExpressionType.TAG)
public class UserAssetsRecordConsumerV2 implements RocketMQListener<MessageView> {
    @Autowired
    SeparateAccountService separateAccountService;

    @Autowired
    CompensationRecordGateway compensationRecordGateway;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    LockKeyWorkspaces lockKeyWorkspaces;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;


    @Override
    public void onMessage(MessageView messageView) {
        String messageId = messageView.getMessageId().toString();
        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOList = null;
        try {
            ByteBuffer buffer = messageView.getBody();
            byte[] bytes = new byte[buffer.limit()];
            buffer.get(bytes);
            String messageBody = new String(bytes, RemotingHelper.DEFAULT_CHARSET);
            if (StringUtils.isEmpty(messageBody)) {
                log.warn("[资金明细消费] 监听消息:接收到了空消息 messageId:{}", messageId);
                return;
            }
            log.info("[资金明细消费] messageId:{} 接收到的消息:{}", messageId, messageBody);
            // 解析消息
            List<UserAssetsRecordMessage> userAssetsRecordMessageList = JSON.parseArray(messageBody, UserAssetsRecordMessage.class);
            // 大小写转换
            userAssetsRecordDTOList = UserAssetsRecordConvertor.MAPPER.toUserAssetsRecordDTOList(userAssetsRecordMessageList);
            //资金主键ID排序
            userAssetsRecordDTOList.sort(Comparator.comparing(UU898UserAssetsRecordDTO::getId));

            log.info("[资金明细消费] 排序后: {}", JSON.toJSONString(userAssetsRecordDTOList));

            //判断数据是否重复
            boolean checkIdempotentData = separateAccountService.checkIdempotentUserAssetsRecordIdempotent(userAssetsRecordDTOList);
            if (checkIdempotentData) {
                log.warn("[资金明细消费] 重复记录");
                return;
            }

            if (paymentClearParamsConfig.getConsumeUseLock()) {
                consumeUseLock(userAssetsRecordDTOList);
            } else {
                //不使用锁
                separateAccountService.handle(userAssetsRecordDTOList);
            }
        } catch (PaymentClearBusinessException e) {
            //有些异常需要跳掉
//            if (e.getCode().equals(ErrorCode.ACCOUNT_RECORD_EXIST.getCode())) {
//                log.error("[资金明细消费] 业务异常 错误剔除 error,{}", ExceptionUtils.getStackTrace(e));
//                return;
//            }
            if (e.getCode().equals(ErrorCode.ACCOUNT_NOT_EXIST.getCode())) {
                separateAccountService.accountDoesNotExistInTagalog(userAssetsRecordDTOList);
            }
            log.warn("[资金明细消费] 业务异常 保存补偿表 error,{}", ExceptionUtils.getStackTrace(e));
            saveCompensationRecord(messageId, userAssetsRecordDTOList);
        } catch (Exception e) {
            log.warn("[资金明细消费] {} 系统错误 保存补偿表 Error:{}", messageId, ExceptionUtils.getRootCauseMessage(e));
            saveCompensationRecord(messageId, userAssetsRecordDTOList);
        }
    }


    void consumeUseLock(List<UU898UserAssetsRecordDTO> userAssetsRecordDTOList) {
        String orderNo = getOrderNo(userAssetsRecordDTOList);
        if (StringUtils.isNotBlank(orderNo)) {
            String key = lockKeyWorkspaces.assetsRecordConsumerKey(orderNo);
            RLock lock = redissonClient.getLock(key);
            try {
                if (null != lock && lock.tryLock(3, 5, TimeUnit.SECONDS)) {
                    separateAccountService.handle(userAssetsRecordDTOList);
                } else {
                    log.error("[资金明细消费] 未获取到锁 key={}", key);
                    throw new RuntimeException();
                }
            } catch (Exception e) {
                log.error("[资金明细消费] 系统异常. error,{}", ExceptionUtils.getStackTrace(e));
                throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR);
            } finally {
                // 是否是锁定状态
                if (lock != null && lock.isLocked()) {
                    // 是否是当前线程的锁
                    if (lock.isHeldByCurrentThread()) {
                        // 释放锁
                        lock.unlock();
                    }
                }
            }
        } else {
            //当没有订单号 直接处理
            separateAccountService.handle(userAssetsRecordDTOList);
        }

    }

    private static String getOrderNo(List<UU898UserAssetsRecordDTO> userAssetsRecordDTOList) {
        List<String> collect = userAssetsRecordDTOList.stream().map(UU898UserAssetsRecordDTO::getOrderNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        return collect.stream().findFirst().orElse("");
    }

    /**
     * 保存异常记录
     */
    private void saveCompensationRecord(String messageId, List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList) {
        try {
            String orderNo = getOrderNo(userAssetsRecordMessageDTOList);
            String uniqueKey = String.format("assets_%s_%s", messageId, orderNo);
            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(userAssetsRecordMessageDTOList), BizCompensationSceneEnum.ASSET);
        } catch (Exception e) {
            //补偿保存失败
            log.error("[资金明细消费] {} 保存补偿表失败 Error:{}", messageId, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.COMPENSATION_SAVE_FAIL);
        }
        log.info("[资金明细消费] 保存补偿表 完成:{}", messageId);
    }


}