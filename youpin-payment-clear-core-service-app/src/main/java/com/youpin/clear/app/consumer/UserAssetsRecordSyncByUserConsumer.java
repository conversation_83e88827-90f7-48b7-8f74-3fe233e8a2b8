package com.youpin.clear.app.consumer;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.NetPlatformUserRecordService;
import com.youpin.clear.app.service.PlatformAccountRecordService;
import com.youpin.clear.app.service.PolarToClearAssetsRecordService;
import com.youpin.clear.app.service.SeparateAccountV2Service;
import com.youpin.clear.client.request.PlatformAccountRecordJobRequest;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.NetPlatformUserRecordMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import com.youpin.clear.infrastructure.config.CompensationUniqueKeyWorkspaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
import youpin.commons.rocketmq.core.RocketMQListener;

import java.util.List;


/**
 * 资金明细消费同步-顺序消费-用户分组
 */
@Slf4j
@Component
@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ, topic = MQConfig.USER_ACCOUNT_RECORD_SYNC_TOPIC, expression = MQConfig.USER_ACCOUNT_RECORD_SYNC_TAG, consumerGroup = MQConfig.USER_ACCOUNT_RECORD_CONSUMER_SYNC_GROUP, filterExpressionType = FilterExpressionType.TAG)
public class UserAssetsRecordSyncByUserConsumer implements RocketMQListener<String> {

    @Autowired
    PolarToClearAssetsRecordService polarToClearAssetsRecordService;

    @Autowired
    CompensationRecordGateway compensationRecordGateway;


    @Autowired
    NetPlatformUserRecordService netPlatformUserRecordService;

    @Autowired
    CompensationUniqueKeyWorkspaces compensationUniqueKeyWorkspaces;

    @Autowired
    PlatformAccountRecordService platformAccountRecordService;

    @Autowired
    SeparateAccountV2Service separateAccountV2Service;


    @Override
    public void onMessage(String msg) {
        if (StringUtils.isEmpty(msg)) {
            log.error("[资金明细消费同步数据] 监听消息:接收到了空消息");
            return;
        }
        log.info("[资金明细消费同步数据] 收到消息:{}", msg);
        ClearUserAssetsRecordDTO dto = JSON.parseObject(msg, ClearUserAssetsRecordDTO.class);
        try {
            polarToClearAssetsRecordService.batchClearUserAssetsRecordDTO(List.of(dto));
        } catch (Exception e) {
            log.error("[资金明细消费同步数据] 错误 保存补偿表错误 Error:{}", ExceptionUtils.getStackTrace(e));
            saveCompensationRecord(dto);
        }
        //net 缓冲
        if (!dto.getAccountBufferBookkeeping() && (dto.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_221) || dto.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_222))) {
            NetPlatformUserRecordMessage netPlatformUserRecordMessage = new NetPlatformUserRecordMessage();
            netPlatformUserRecordMessage.setUserId(dto.getUserId());
            netPlatformUserRecordMessage.setTreadNo(dto.getTreadNo());
            netPlatformUserRecordMessage.setTypeId(dto.getTypeId());
            netPlatformUserRecordMessage.setId(dto.getUserAssetsRecordId());
            log.info("[net平台户资金异步缓冲顺序处理]  接收到的消息:{}", netPlatformUserRecordMessage);
            try {
                netPlatformUserRecordService.bufferAccountingProcess(netPlatformUserRecordMessage);
            } catch (Exception e) {
                log.warn("[net平台户资金异步缓冲顺序处理] 业务异常 保存补偿表 error,{}", ExceptionUtils.getStackTrace(e));
                saveCompensationRecord(netPlatformUserRecordMessage);
            }
        }

        //JAVA 缓冲
        if (dto.getAccountBufferBookkeeping() && null != dto.getPlatformAccountRecordId()) {
            try {
                platformAccountRecordService.handle(PlatformAccountRecordJobRequest.builder().id(dto.getPlatformAccountRecordId()).build());
            } catch (Exception e) {
                log.warn("[java平台户资金异步缓冲顺序处理] 业务异常  error,{}", ExceptionUtils.getStackTrace(e));
            }
        }

        //2025-08-01 仅可提现数据拆分
        List<ClearUserAssetsRecordDTO> dtoList = List.of(dto);
        log.info("[仅可提现数据拆分] 业务数据  dtoList:{}", JSON.toJSONString(dtoList));
        try {
            separateAccountV2Service.handleMoneyOnlyWithdraw(dtoList);
        } catch (Exception e) {
            log.error("[仅可提现数据拆分] 业务异常  error,{}", ExceptionUtils.getStackTrace(e));
            saveCompensationRecord(dtoList, BizCompensationSceneEnum.HANDLE_MONEY_ONLY_WITHDRAW_EXCEPTION, "howe", Boolean.FALSE);
        }
    }


    /**
     * 保存异常记录
     */
    private void saveCompensationRecord(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList, BizCompensationSceneEnum bizCompensationSceneEnum, String code, Boolean isException) {
        String uniqueKey = String.format("%s_%s_%s", code, userAssetsRecordDTOList.get(0).getUserId(), userAssetsRecordDTOList.get(0).getOrderNo());
        try {
            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(userAssetsRecordDTOList), bizCompensationSceneEnum);
        } catch (Exception e) {
            log.error("[仅可提现数据拆分] {} 错误 保存补偿表错误 Error:{}", uniqueKey, ExceptionUtils.getStackTrace(e));
            if (Boolean.TRUE.equals(isException)) {
                throw new PaymentClearBusinessException(ErrorCode.COMPENSATION_SAVE_FAIL);
            }
        }
        log.info("[仅可提现数据拆分] 保存补偿表 完成:{}", uniqueKey);
    }

    /**
     * 保存异常记录
     */
    private void saveCompensationRecord(ClearUserAssetsRecordDTO dto) {
        String uniqueKey = String.format("sync_%s_%s", dto.getUserId(), dto.getUserAssetsRecordId());
        try {
            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(dto), BizCompensationSceneEnum.SYNC);
        } catch (Exception e) {
            log.error("[资金明细消费同步数据] {} 错误 保存补偿表错误 Error:{}", uniqueKey, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_CONSUMER_FAILED);
        }
        log.info("[资金明细消费同步数据] 保存补偿表 完成:{}", uniqueKey);
    }

    /**
     * 保存异常记录
     */
    private void saveCompensationRecord(NetPlatformUserRecordMessage dto) {
        String uniqueKey = compensationUniqueKeyWorkspaces.getNetPlatformUserRecordConsumerUniqueKey(dto.getUserId(), dto.getTreadNo());
        try {
            compensationRecordGateway.saveCompensationRecord(uniqueKey, JSON.toJSONString(dto), BizCompensationSceneEnum.NET_PLATFORM_USER_RECORD);
        } catch (Exception e) {
            log.error("[net平台户资金异步缓冲顺序处理] {} 错误 保存补偿表错误 Error:{}", uniqueKey, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_CONSUMER_FAILED);
        }
        log.info("[net平台户资金异步缓冲顺序处理] 保存补偿表 完成:{}", uniqueKey);
    }


}