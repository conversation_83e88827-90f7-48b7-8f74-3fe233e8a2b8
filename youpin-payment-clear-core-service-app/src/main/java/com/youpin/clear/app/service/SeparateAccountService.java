package com.youpin.clear.app.service;

import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;

import java.util.List;

public interface SeparateAccountService {


    void userAssetsRecordMessageTest(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList,Boolean separateTransactionTest);

    void handle(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList);

    void userClearData(Long userId);

    /**
     * 数据幂等判断
     * true 重复 false 不重复
     */
    boolean checkIdempotentUserAssetsRecordIdempotent(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList);


    void  accountDoesNotExistInTagalog(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList);

    
}
