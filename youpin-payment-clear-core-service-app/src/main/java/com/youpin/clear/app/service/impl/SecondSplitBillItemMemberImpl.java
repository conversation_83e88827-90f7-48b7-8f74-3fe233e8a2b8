package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.service.SecondSplitBillItemMemberService;
import com.youpin.clear.client.request.SeparateAccountJobRequest;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.RealUserAccountInfoDTO;
import com.youpin.clear.domain.dto.TempBalanceMoneyChangeDTO;
import com.youpin.clear.domain.gateway.ClearUserAssetsRecordGateway;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.converter.UserAccountRecordOrderRelateConvertor;
import com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordOrderRelateExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordOrderRelateMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SecondSplitBillItemMemberImpl implements SecondSplitBillItemMemberService {


    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Autowired
    ClearUserAssetsRecordGateway clearUserAssetsRecordGateway;

    @Autowired
    UserAccountGateway userAccountGateway;

    @Autowired
    UserAccountRecordOrderRelateExtMapper userAccountRecordOrderRelateExtMapper;

    @Autowired
    UserAccountRecordOrderRelateMapper userAccountRecordOrderRelateMapper;


    @Resource
    IdWorkService idWorkService;
    // 定义比较类型常量
    final int COMPARE_BALANCE = 1;
    final int COMPARE_BLOCK = 2;
    final int COMPARE_PURCHASE = 3;

    @Override
    public Long minUserAssetsRecordId(SeparateAccountJobRequest request) {
        return userAccountRecordGateway.minUserAssetsRecordId(request.getUserId());
    }

    //一旦发现 缺失数据 需要停止操作 ,补充之后再进行操作
    //判断失败需要停止

    @Override
    public void secondSplitBillItemMember(SeparateAccountJobRequest request) {
        //获取原始资金明细
        List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOList = clearUserAssetsRecordGateway.selectUserAssetsRecordById(request.getUserId(), Math.max(request.getPageIndex() - 1L, 0) * request.getPageSize(), request.getPageSize(), request.getMinUserAssetsRecordId());
        if (CollectionUtils.isEmpty(clearUserAssetsRecordDTOList)) {
            log.warn("没有需要处理的数据");
            return;
        }
        //获取分账信息
        Map<Long, List<UserAccountRecordMember>> userAccountRecordMemberListMap = getUserAccountRecordMembers(request, clearUserAssetsRecordDTOList);

        //数据比对
        for (ClearUserAssetsRecordDTO dto : clearUserAssetsRecordDTOList) {
            boolean flag = dataComparison(dto, userAccountRecordMemberListMap.getOrDefault(dto.getUserAssetsRecordId(), null));
            if (!flag) {
                break;
            }
        }
    }

    private boolean dataComparison(ClearUserAssetsRecordDTO userAssetsRecordDTO, List<UserAccountRecordMember> userAccountRecordMemberGroupList) {
        //有数据没数据
        boolean userAccountRecordEmpty = CollectionUtils.isEmpty(userAccountRecordMemberGroupList);

        if (userAccountRecordEmpty) {
            //补充明细
            log.info("[数据遗漏]类型:{} 资金ID:{} 订单号:{} 渠道:{}", userAssetsRecordDTO.getTypeId(), userAssetsRecordDTO.getUserAssetsRecordId(), userAssetsRecordDTO.getOrderNo(), userAssetsRecordDTO.getPayChannel());

            UserAccountRecordMember userAccountRecordMemberNew = toUserAccountRecordMember(userAssetsRecordDTO);
            log.info("[补充数据]类型:{} 资金ID:{} 订单号:{} 渠道:{},账号类型:{}", userAssetsRecordDTO.getTypeId(), userAssetsRecordDTO.getUserAssetsRecordId(), userAssetsRecordDTO.getOrderNo(), userAssetsRecordDTO.getPayChannel(), userAssetsRecordDTO.getAssetType());

            if (null != userAccountRecordMemberNew) {
                userAccountRecordGateway.insertSelective(userAccountRecordMemberNew);
                log.info("userAccountRecordMemberNew:{}", userAccountRecordMemberNew.getId());
                UserAccountRecordOrderRelate userAccountRecordOrderRelate = userAccountRecordOrderRelateExtMapper.getByUserAssetsRecordId(userAccountRecordMemberNew.getUserAssetsRecordId());
                if (null == userAccountRecordOrderRelate) {
                    userAccountRecordOrderRelateMapper.insertSelective(UserAccountRecordOrderRelateConvertor.MAPPER.toUserAccountRecordOrderRelate(userAccountRecordMemberNew));
                }
                //插入关系
                userAccountRecordMemberGroupList = List.of(userAccountRecordMemberNew);
            } else {
                log.info("[补充数据] 数据缺失暂停处理 类型:{} 资金ID:{} 订单号:{} 渠道:{},账号类型:{}", userAssetsRecordDTO.getTypeId(), userAssetsRecordDTO.getUserAssetsRecordId(), userAssetsRecordDTO.getOrderNo(), userAssetsRecordDTO.getPayChannel(), userAssetsRecordDTO.getAssetType());
                return false;
            }
        }


        //渠道余额变动
        boolean channelBalanceChangeFlag = userAssetsRecordDTO.getAttr().equals(0);
        //余额变动
        boolean balanceChangeFlag = userAssetsRecordDTO.getAttr().equals(1);
        //求购变动
        boolean purchaseBalanceChangeFlag = userAssetsRecordDTO.getAttr().equals(2);

        //获取  分账金额
        TempBalanceMoneyChangeDTO userAccountMoneyChangeDTO = getUserAccountRecordMemberTOBalanceMoneyChangeDTO(userAccountRecordMemberGroupList);
        //获取  资金流水金额
        TempBalanceMoneyChangeDTO userAssetsMoneyChangeDTO = getUserAssetsRecordTOBalanceMoneyChangeDTO(userAssetsRecordDTO);

        //余额变动比对结果
        boolean balanceChangeResultFlag = dataMoneyChangeComparison(COMPARE_BALANCE, userAccountMoneyChangeDTO, userAssetsMoneyChangeDTO);
        //冻结余额变动比对结果
        boolean blockBalanceChangeResultFlag = dataMoneyChangeComparison(COMPARE_BLOCK, userAccountMoneyChangeDTO, userAssetsMoneyChangeDTO);
        //求购余额变动比对结果
        boolean purchaseBalanceChangeResultFlag = dataMoneyChangeComparison(COMPARE_PURCHASE, userAccountMoneyChangeDTO, userAssetsMoneyChangeDTO);

        //余额冻结变动
        if (balanceChangeFlag && !balanceChangeResultFlag && !blockBalanceChangeResultFlag) {
            //数据不需要修复
            return true;
        }
        //余额冻结
        if (balanceChangeFlag && !balanceChangeResultFlag) {
            //数据不需要修复
            return true;
        }

        if (purchaseBalanceChangeFlag && !purchaseBalanceChangeResultFlag) {
            //数据不需要修复
            return true;
        }

        if (channelBalanceChangeFlag && !balanceChangeResultFlag) {
            //数据不需要修复
            return true;
        }

        //分账数据条数
        int userAccountRecordSize = userAccountRecordMemberGroupList.size();

        if (balanceChangeFlag && blockBalanceChangeResultFlag && userAccountRecordSize == 1) {
            singleBlockBalanceChange(userAssetsRecordDTO, userAccountRecordMemberGroupList, userAccountMoneyChangeDTO);
            return true;
        }

        if (balanceChangeFlag && blockBalanceChangeResultFlag && userAccountRecordSize > 1) {
            logOperation(2, "数据修复:冻结金额-多条数据 ", userAssetsRecordDTO, userAccountMoneyChangeDTO, userAccountRecordMemberGroupList);
            return true;
        }

        //余额单条处理
        //余额变动 && 分账数据条数:1 && 余额变动比对结果:不等于
        if (balanceChangeFlag && userAccountRecordSize == 1) {
            singleBalanceChange(userAssetsRecordDTO, userAccountRecordMemberGroupList, userAccountMoneyChangeDTO);
            return true;
        }
        //余额多条处理
        if (balanceChangeFlag && userAccountRecordSize > 1) {
            log.info("余额变动,资金两条:{}", userAccountRecordMemberGroupList);
            return true;
        }
        //求购变动 && 分账数据条数:1 && 余额变动比对结果:不等于
        if (purchaseBalanceChangeFlag && userAccountRecordSize == 1) {
            //需要增加 数据全部账户判断
            singlePurchaseWith(userAssetsRecordDTO, userAccountRecordMemberGroupList, userAccountMoneyChangeDTO);
            return true;
        }
        //求购多条处理
        if (purchaseBalanceChangeFlag && userAccountRecordSize > 1) { //isPurchaseWithMulti
            logOperation(3, "求购多条判断", userAssetsRecordDTO, userAccountMoneyChangeDTO, userAccountRecordMemberGroupList);
            return true;
        }
        //渠道余额变动  && 余额变动比对结果:不等于 渠道只会有一条
        if (channelBalanceChangeFlag) {
//                logOperation(1, "渠道余额判断", userAssetsRecordDTO, userAccountMoneyChangeDTO, userAccountRecordMemberGroupList);
            return true;
        }
        logOperation(1, "其他判断", userAssetsRecordDTO, userAccountMoneyChangeDTO, userAccountRecordMemberGroupList);

        return true;
    }

    private UserAccountRecordMember toUserAccountRecordMember(ClearUserAssetsRecordDTO userAssetsRecordDTO) {


        //渠道余额变动
        boolean channelBalanceChangeFlag = userAssetsRecordDTO.getAttr().equals(0);
        //余额变动
        boolean balanceChangeFlag = userAssetsRecordDTO.getAttr().equals(1);
        //求购变动
        boolean purchaseBalanceChangeFlag = userAssetsRecordDTO.getAttr().equals(2);
        //如果支出  默认 余额 默认 1
        //如果收入  查询正向单

        BigDecimal balanceBefore = BigDecimal.ZERO;
        BigDecimal balanceChange = BigDecimal.ZERO;
        BigDecimal balanceAfter = BigDecimal.ZERO;


        RealUserAccountInfoDTO realUserAccountInfo = new RealUserAccountInfoDTO();
        //余额
        if (balanceChangeFlag) {
            balanceBefore = userAssetsRecordDTO.getMoney();
            balanceChange = userAssetsRecordDTO.getThisMoney();
            balanceAfter = userAssetsRecordDTO.getAfterMoney();
            //支出
            if (balanceBefore.compareTo(balanceAfter) > 0) {
                UserAccountRecordMember userAccountRecordMember2 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAssetsRecordDTO.getUserId(), userAssetsRecordDTO.getUserAssetsRecordId(), AccountTypeEnum.BALANCE_2.getCode());
                if (userAccountRecordMember2 != null && userAccountRecordMember2.getBalanceAfter().compareTo(balanceChange.abs()) >= 0) {

                    realUserAccountInfo = new RealUserAccountInfoDTO(userAccountRecordMember2.getUserAccountNo(), userAccountRecordMember2.getAccountType());
                }
                UserAccountRecordMember userAccountRecordMember1 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAssetsRecordDTO.getUserId(), userAssetsRecordDTO.getUserAssetsRecordId(), AccountTypeEnum.BALANCE_1.getCode());
                if (userAccountRecordMember1 != null && userAccountRecordMember1.getBalanceAfter().compareTo(balanceChange.abs()) >= 0) {

                    realUserAccountInfo = new RealUserAccountInfoDTO(userAccountRecordMember1.getUserAccountNo(), userAccountRecordMember1.getAccountType());

                }
            } else {
                realUserAccountInfo = getRealUserAccountInfo(userAssetsRecordDTO, balanceChange);
            }
        }
        //求购
        if (purchaseBalanceChangeFlag) {
            balanceBefore = userAssetsRecordDTO.getBlockMoney();
            balanceChange = userAssetsRecordDTO.getThisBlockMoney();
            balanceAfter = userAssetsRecordDTO.getAfterBlockMoney();

            //除了充值:43 提现:44
            if (userAssetsRecordDTO.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_43) || userAssetsRecordDTO.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_44)) {


            } else {
                realUserAccountInfo = getRealUserAccountInfo(userAssetsRecordDTO, balanceChange);
            }
        }
        //渠道
        if (channelBalanceChangeFlag) {
            balanceBefore = userAssetsRecordDTO.getMoney();
            balanceChange = userAssetsRecordDTO.getThisMoney();
            balanceAfter = userAssetsRecordDTO.getAfterMoney();
            //支出 默认余额1
            if (balanceChange.compareTo(BigDecimal.ZERO) < 0) {
                AccountInfoMember accountInfoMember = getAccountInfoMember(userAssetsRecordDTO.getUserId(), AccountTypeEnum.BALANCE_1);
                if (accountInfoMember != null) {
                    realUserAccountInfo = new RealUserAccountInfoDTO(accountInfoMember.getUserAccountNo(), accountInfoMember.getAccountType());

                }
            } else {
                realUserAccountInfo = getRealUserAccountInfo(userAssetsRecordDTO, balanceChange);
            }
        }
        if (null == realUserAccountInfo.getUserAccountNo() || null == realUserAccountInfo.getAccountType()) {
            log.error("userAccountNo or accountType is null :{}", userAssetsRecordDTO);
            return null;
        }

        return UserAccountRecordMember.builder()
                .accountRecordNo(idWorkService.getNextIdLeafKey())
                .userId(userAssetsRecordDTO.getUserId()).typeId(userAssetsRecordDTO.getTypeId())
                .userAssetsRecordId(userAssetsRecordDTO.getUserAssetsRecordId())
                .treadNo(userAssetsRecordDTO.getTreadNo()).serialNo(userAssetsRecordDTO.getSerialNo()).orderNo(userAssetsRecordDTO.getOrderNo()).payOrderNo(userAssetsRecordDTO.getPayOrderNo())
                .userAccountNo(realUserAccountInfo.getUserAccountNo()).accountType(realUserAccountInfo.getAccountType())
                .balanceBefore(BigDecimal.ZERO).balanceAfter(BigDecimal.ZERO)
                .balanceChange(balanceChange)
                .balanceIsChange(userAssetsRecordDTO.getAttr().equals(1) ? 1 : 0)
                .status(userAssetsRecordDTO.getStatus()).payChannel(userAssetsRecordDTO.getPayChannel()).finishTime(userAssetsRecordDTO.getCompleteTime()).createTime(userAssetsRecordDTO.getAddTime())
                .updateTime(userAssetsRecordDTO.getCompleteTime()).frozenBalanceBefore(userAssetsRecordDTO.getBlockMoney()).frozenBalanceChange(userAssetsRecordDTO.getThisBlockMoney())
                .frozenBalanceAfter(userAssetsRecordDTO.getAfterBlockMoney()).build();

    }

    private RealUserAccountInfoDTO getRealUserAccountInfo(ClearUserAssetsRecordDTO userAssetsRecordDTO, BigDecimal
            balanceChange) {
        String userAccountNo = null;
        Integer accountType = null;
        List<UserAccountRecordMember> userAccountRecordByOrderNoOrPayOrderNo = userAccountRecordGateway.getUserAccountRecordByOrderNoOrPayOrderNo(userAssetsRecordDTO.getOrderNo(), userAssetsRecordDTO.getPayOrderNo());

        if (userAccountRecordByOrderNoOrPayOrderNo.isEmpty()) {
            AccountInfoMember accountInfoMember = getAccountInfoMember(userAssetsRecordDTO.getUserId(), AccountTypeEnum.BALANCE_1);
            if (accountInfoMember != null) {
                userAccountNo = accountInfoMember.getUserAccountNo();
                accountType = accountInfoMember.getAccountType();
            }
        } else {
            userAccountRecordByOrderNoOrPayOrderNo = userAccountRecordByOrderNoOrPayOrderNo.stream()
                    .filter(item -> item.getBalanceChange().compareTo(BigDecimal.ZERO) < 0)
                    .collect(Collectors.toList());

            UserAccountRecordMember userAccountRecordMember = null;
            for (UserAccountRecordMember item : userAccountRecordByOrderNoOrPayOrderNo) {
                if (item.getPayOrderNo().equals(userAssetsRecordDTO.getPayOrderNo())) {
                    userAccountRecordMember = item;
                    break;
                }
            }

            if (userAccountRecordMember != null) {
                for (UserAccountRecordMember item : userAccountRecordByOrderNoOrPayOrderNo) {
                    if (item.getBalanceChange().compareTo(balanceChange) >= 0) {
                        userAccountRecordMember = item;
                        break;
                    }
                }
            }
            if (userAccountRecordMember != null) {
                userAccountNo = userAccountRecordMember.getUserAccountNo();
                accountType = userAccountRecordMember.getAccountType();
            } else {
                AccountInfoMember accountInfoMember = getAccountInfoMember(userAssetsRecordDTO.getUserId(), AccountTypeEnum.BALANCE_2);
                if (accountInfoMember != null) {
                    userAccountNo = accountInfoMember.getUserAccountNo();
                    accountType = accountInfoMember.getAccountType();
                }
            }
        }
        return new RealUserAccountInfoDTO(userAccountNo, accountType);
    }


    private AccountInfoMember getAccountInfoMember(Long userId, AccountTypeEnum accountTypeEnum) {
        AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(userId);
        return accountAggregate.getAccountByType(accountTypeEnum);
    }

    private void singlePurchaseWith(ClearUserAssetsRecordDTO
                                            userAssetsRecordDTO, List<UserAccountRecordMember> userAccountRecordMemberGroupList, TempBalanceMoneyChangeDTO
                                            userAccountMoneyChangeDTO) {
        //分账数据有一条
        UserAccountRecordMember userAccountRecordMember = userAccountRecordMemberGroupList.stream().findFirst().get();
        //账户类型
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType());

        //查找最近一次 求购充值余额1的变动
        UserAccountRecordMember recharge1 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode());
        //查找最近一次 求购充值余额2的变动
        UserAccountRecordMember recharge2 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode());
        //查找最近一次 求购转入余额1的变动
        UserAccountRecordMember transfer1 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode());
        //查找最近一次 求购转入余额2的变动
        UserAccountRecordMember transfer2 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode());


        BigDecimal recharge1BalanceAfter = null == recharge1 ? BigDecimal.ZERO : recharge1.getBalanceAfter();
        BigDecimal recharge2BalanceAfter = null == recharge2 ? BigDecimal.ZERO : recharge2.getBalanceAfter();
        BigDecimal transfer1BalanceAfter = null == transfer1 ? BigDecimal.ZERO : transfer1.getBalanceAfter();
        BigDecimal transfer2BalanceAfter = null == transfer2 ? BigDecimal.ZERO : transfer2.getBalanceAfter();

        BigDecimal balanceBefore = BigDecimal.ZERO;
        BigDecimal balanceAfter = BigDecimal.ZERO;

        switch (accountTypeEnum) {
            case PURCHASE_BALANCE_RECHARGE_1:
                balanceBefore = userAssetsRecordDTO.getPurchaseMoney().subtract(recharge2BalanceAfter).subtract(transfer1BalanceAfter).subtract(transfer2BalanceAfter);
                balanceAfter = userAssetsRecordDTO.getAfterPurchaseMoney().subtract(recharge2BalanceAfter).subtract(transfer1BalanceAfter).subtract(transfer2BalanceAfter);
                break;
            case PURCHASE_BALANCE_RECHARGE_2:
                balanceBefore = userAssetsRecordDTO.getPurchaseMoney().subtract(recharge1BalanceAfter).subtract(transfer1BalanceAfter).subtract(transfer2BalanceAfter);
                balanceAfter = userAssetsRecordDTO.getAfterPurchaseMoney().subtract(recharge1BalanceAfter).subtract(transfer1BalanceAfter).subtract(transfer2BalanceAfter);
                break;
            case PURCHASE_BALANCE_TRANSFER_1:
                balanceBefore = userAssetsRecordDTO.getPurchaseMoney().subtract(recharge1BalanceAfter).subtract(recharge2BalanceAfter).subtract(transfer2BalanceAfter);
                balanceAfter = userAssetsRecordDTO.getAfterPurchaseMoney().subtract(recharge1BalanceAfter).subtract(recharge2BalanceAfter).subtract(transfer2BalanceAfter);
                break;
            case PURCHASE_BALANCE_TRANSFER_2:
                balanceBefore = userAssetsRecordDTO.getPurchaseMoney().subtract(recharge1BalanceAfter).subtract(recharge2BalanceAfter).subtract(transfer1BalanceAfter);
                balanceAfter = userAssetsRecordDTO.getAfterPurchaseMoney().subtract(recharge1BalanceAfter).subtract(recharge2BalanceAfter).subtract(transfer1BalanceAfter);
                break;
        }
        if (balanceBefore.compareTo(BigDecimal.ZERO) < 0) {
            balanceBefore = BigDecimal.ZERO;
        }
        if (balanceAfter.compareTo(BigDecimal.ZERO) < 0) {
            balanceAfter = BigDecimal.ZERO;
        }
        if (balanceBefore.compareTo(userAccountRecordMember.getBalanceBefore()) != 0 || balanceAfter.compareTo(userAccountRecordMember.getBalanceAfter()) != 0) {
            log.info("求购单条判断 balanceBefore:{},balanceAfter:{}", balanceBefore, balanceAfter);
            logOperation(3, "数据修复:求购单条数据对齐", userAssetsRecordDTO, userAccountMoneyChangeDTO, userAccountRecordMemberGroupList);
            userAccountRecordMember.setBalanceBefore(balanceBefore);
            userAccountRecordMember.setBalanceAfter(balanceAfter);
            userAccountRecordMember.setPayOrderNo(userAssetsRecordDTO.getPayOrderNo());
            userAccountRecordMember.setOrderNo(userAssetsRecordDTO.getOrderNo());
            userAccountRecordMember.setStatus(userAssetsRecordDTO.getStatus());
            userAccountRecordMember.setFinishTime(userAssetsRecordDTO.getCompleteTime());
            userAccountRecordGateway.updateUserAccountRecord(userAccountRecordMember);
        }
    }

    private void singleBlockBalanceChange(ClearUserAssetsRecordDTO
                                                  userAssetsRecordDTO, List<UserAccountRecordMember> userAccountRecordMemberGroupList, TempBalanceMoneyChangeDTO
                                                  userAccountMoneyChangeDTO) {
        assert userAccountRecordMemberGroupList.size() == 1;
        //分账数据有一条
        UserAccountRecordMember userAccountRecordMember = userAccountRecordMemberGroupList.stream().findFirst().get();
        //账户类型
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType());

        //查找最近一次 余额一的变动
        UserAccountRecordMember userAccountRecordMember1 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.BALANCE_1.getCode());

        //查找最近一次 余额二的变动
        UserAccountRecordMember userAccountRecordMember2 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.BALANCE_2.getCode());

        BigDecimal balanceBefore = BigDecimal.ZERO;
        BigDecimal balanceAfter = BigDecimal.ZERO;

        switch (accountTypeEnum) {
            case BALANCE_1:
                balanceBefore = userAssetsRecordDTO.getBlockMoney().subtract(userAccountRecordMember2.getFrozenBalanceAfter());
                balanceAfter = userAssetsRecordDTO.getAfterBlockMoney().subtract(userAccountRecordMember2.getFrozenBalanceAfter());
                break;
            case BALANCE_2:
                balanceBefore = userAssetsRecordDTO.getBlockMoney().subtract(userAccountRecordMember1.getFrozenBalanceAfter());
                balanceAfter = userAssetsRecordDTO.getAfterBlockMoney().subtract(userAccountRecordMember1.getFrozenBalanceAfter());
                break;
        }

        if (balanceBefore.compareTo(BigDecimal.ZERO) < 0) {
            balanceBefore = BigDecimal.ZERO;
        }
        if (balanceAfter.compareTo(BigDecimal.ZERO) < 0) {
            balanceAfter = BigDecimal.ZERO;
        }

        if (balanceBefore.compareTo(userAccountRecordMember.getFrozenBalanceBefore()) != 0 || balanceAfter.compareTo(userAccountRecordMember.getFrozenBalanceAfter()) != 0) {
            logOperation(2, "数据修复:冻结金额-单条数据对齐 ", userAssetsRecordDTO, userAccountMoneyChangeDTO, List.of(userAccountRecordMember));
            userAccountRecordMember.setFrozenBalanceBefore(balanceBefore);
            userAccountRecordMember.setFrozenBalanceAfter(balanceAfter);
            userAccountRecordMember.setPayOrderNo(userAssetsRecordDTO.getPayOrderNo());
            userAccountRecordMember.setOrderNo(userAssetsRecordDTO.getOrderNo());
            userAccountRecordMember.setStatus(userAssetsRecordDTO.getStatus());
            userAccountRecordMember.setFinishTime(userAssetsRecordDTO.getCompleteTime());
            userAccountRecordGateway.updateUserAccountRecord(userAccountRecordMember);
        }


    }

    /**
     * 余额单条处理
     */
    private void singleBalanceChange(ClearUserAssetsRecordDTO
                                             userAssetsRecordDTO, List<UserAccountRecordMember> userAccountRecordMemberGroupList, TempBalanceMoneyChangeDTO
                                             userAccountMoneyChangeDTO) {
        assert userAccountRecordMemberGroupList.size() == 1;
        //分账数据有一条
        UserAccountRecordMember userAccountRecordMember = userAccountRecordMemberGroupList.stream().findFirst().get();
        //账户类型
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType());

        //查找最近一次 余额一的变动
        UserAccountRecordMember userAccountRecordMember1 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.BALANCE_1.getCode());

        //查找最近一次 余额二的变动
        UserAccountRecordMember userAccountRecordMember2 = userAccountRecordGateway.selectLastUserAccountRecordByUserIdAndAccountType(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), AccountTypeEnum.BALANCE_2.getCode());

        BigDecimal balanceBefore = BigDecimal.ZERO;
        BigDecimal balanceAfter = BigDecimal.ZERO;

        switch (accountTypeEnum) {
            case BALANCE_1:
                balanceBefore = userAssetsRecordDTO.getMoney().subtract(userAccountRecordMember2.getBalanceAfter());
                balanceAfter = userAssetsRecordDTO.getAfterMoney().subtract(userAccountRecordMember2.getBalanceAfter());
                break;
            case BALANCE_2:
                balanceBefore = userAssetsRecordDTO.getMoney().subtract(userAccountRecordMember1.getBalanceAfter());
                balanceAfter = userAssetsRecordDTO.getAfterMoney().subtract(userAccountRecordMember1.getBalanceAfter());
                break;
        }

        if (balanceBefore.compareTo(userAccountRecordMember.getBalanceBefore()) != 0 || balanceAfter.compareTo(userAccountRecordMember.getBalanceAfter()) != 0) {
            logOperation(1, "数据修复:余额1-单条数据对齐 ", userAssetsRecordDTO, userAccountMoneyChangeDTO, List.of(userAccountRecordMember));
            userAccountRecordMember.setFrozenBalanceChange(BigDecimal.ZERO);
            userAccountRecordMember.setBalanceBefore(balanceBefore);
            userAccountRecordMember.setBalanceAfter(balanceAfter);
            userAccountRecordMember.setPayOrderNo(userAssetsRecordDTO.getPayOrderNo());
            userAccountRecordMember.setOrderNo(userAssetsRecordDTO.getOrderNo());
            userAccountRecordMember.setStatus(userAssetsRecordDTO.getStatus());
            userAccountRecordMember.setFinishTime(userAssetsRecordDTO.getCompleteTime());
            userAccountRecordGateway.updateUserAccountRecord(userAccountRecordMember);
        }
    }

    private Map<Long, List<UserAccountRecordMember>> getUserAccountRecordMembers(SeparateAccountJobRequest
                                                                                         request, List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOList) {
        Long minUserAssetsRecordId = clearUserAssetsRecordDTOList.get(0).getUserAssetsRecordId();
        Long maxUserAssetsRecordId = clearUserAssetsRecordDTOList.get(0).getUserAssetsRecordId();
        for (ClearUserAssetsRecordDTO dto : clearUserAssetsRecordDTOList) {
            long currentId = dto.getUserAssetsRecordId();
            if (currentId < minUserAssetsRecordId) {
                minUserAssetsRecordId = currentId;
            }
            if (currentId > maxUserAssetsRecordId) {
                maxUserAssetsRecordId = currentId;
            }
        }
        List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.selectUserAccountRecordById(request.getUserId(), minUserAssetsRecordId, maxUserAssetsRecordId);
        Map<Long, List<UserAccountRecordMember>> userAccountRecordMemberListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(clearUserAssetsRecordDTOList)) {
            userAccountRecordMemberListMap = userAccountRecordMemberList.stream().collect(Collectors.groupingBy(UserAccountRecordMember::getUserAssetsRecordId));
        }
        //获取分账明细
        return userAccountRecordMemberListMap;
    }

    /**
     * @return true:需要修复数据 false:不需要修复数据
     */
    private boolean dataMoneyChangeComparison(int type, TempBalanceMoneyChangeDTO
            userAccountMoneyChangeDTO, TempBalanceMoneyChangeDTO userAssetsMoneyChangeDTO) {
        if (type == 1) {
            return userAccountMoneyChangeDTO.getBalanceBefore().compareTo(userAssetsMoneyChangeDTO.getBalanceBefore()) != 0 || userAccountMoneyChangeDTO.getBalanceChange().compareTo(userAssetsMoneyChangeDTO.getBalanceChange()) != 0 || userAccountMoneyChangeDTO.getBalanceAfter().compareTo(userAssetsMoneyChangeDTO.getBalanceAfter()) != 0;

        } else if (type == 2) {
            return userAccountMoneyChangeDTO.getFrozenBalanceBefore().compareTo(userAssetsMoneyChangeDTO.getFrozenBalanceBefore()) != 0 || userAccountMoneyChangeDTO.getFrozenBalanceChange().compareTo(userAssetsMoneyChangeDTO.getFrozenBalanceChange()) != 0 || userAccountMoneyChangeDTO.getFrozenBalanceAfter().compareTo(userAssetsMoneyChangeDTO.getFrozenBalanceAfter()) != 0;

        } else if (type == 3) {
            return userAccountMoneyChangeDTO.getPurchaseMoney().compareTo(userAssetsMoneyChangeDTO.getPurchaseMoney()) != 0 || userAccountMoneyChangeDTO.getThisPurchaseMoney().compareTo(userAssetsMoneyChangeDTO.getThisPurchaseMoney()) != 0 || userAccountMoneyChangeDTO.getAfterPurchaseMoney().compareTo(userAssetsMoneyChangeDTO.getAfterPurchaseMoney()) != 0;
        } else {
            return false;
        }
    }

    private TempBalanceMoneyChangeDTO getUserAssetsRecordTOBalanceMoneyChangeDTO(ClearUserAssetsRecordDTO
                                                                                         userAssetsRecordDTO) {
        return new TempBalanceMoneyChangeDTO(userAssetsRecordDTO.getMoney(), userAssetsRecordDTO.getThisMoney(), userAssetsRecordDTO.getAfterMoney(), userAssetsRecordDTO.getBlockMoney(), userAssetsRecordDTO.getThisBlockMoney(), userAssetsRecordDTO.getAfterBlockMoney(), userAssetsRecordDTO.getPurchaseMoney(), userAssetsRecordDTO.getThisPurchaseMoney(), userAssetsRecordDTO.getAfterPurchaseMoney());
    }

    private TempBalanceMoneyChangeDTO getUserAccountRecordMemberTOBalanceMoneyChangeDTO
            (List<UserAccountRecordMember> userAccountRecordMemberGroupList) {
        BigDecimal balanceBefore = BigDecimal.ZERO, balanceChange = BigDecimal.ZERO, balanceAfter = BigDecimal.ZERO;
        BigDecimal frozenBalanceBefore = BigDecimal.ZERO, frozenBalanceChange = BigDecimal.ZERO, frozenBalanceAfter = BigDecimal.ZERO;
        BigDecimal purchaseMoney = BigDecimal.ZERO, thisPurchaseMoney = BigDecimal.ZERO, afterPurchaseMoney = BigDecimal.ZERO;

        for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberGroupList) {
            if (userAccountRecordMember.getAccountType().equals(AccountTypeEnum.BALANCE_1.getCode()) || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.BALANCE_2.getCode())) {
                balanceBefore = balanceBefore.add(userAccountRecordMember.getBalanceBefore());
                balanceChange = balanceChange.add(userAccountRecordMember.getBalanceChange());
                balanceAfter = balanceAfter.add(userAccountRecordMember.getBalanceAfter());
            }

            frozenBalanceBefore = frozenBalanceBefore.add(userAccountRecordMember.getFrozenBalanceBefore());
            frozenBalanceChange = frozenBalanceChange.add(userAccountRecordMember.getFrozenBalanceChange());
            frozenBalanceAfter = frozenBalanceAfter.add(userAccountRecordMember.getFrozenBalanceAfter());

            if (userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode()) || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode()) || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode()) || userAccountRecordMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode())) {
                purchaseMoney = purchaseMoney.add(userAccountRecordMember.getBalanceBefore());
                thisPurchaseMoney = thisPurchaseMoney.add(userAccountRecordMember.getBalanceChange());
                afterPurchaseMoney = afterPurchaseMoney.add(userAccountRecordMember.getBalanceAfter());
            }
        }

        return new TempBalanceMoneyChangeDTO(balanceBefore, balanceChange, balanceAfter, frozenBalanceBefore, frozenBalanceChange, frozenBalanceAfter, purchaseMoney, thisPurchaseMoney, afterPurchaseMoney);
    }

    private static void logOperation(int type, String logTag, ClearUserAssetsRecordDTO
            userAssetsRecordDTO, TempBalanceMoneyChangeDTO
                                             userAccountMoneyChangeDTO, List<UserAccountRecordMember> userAccountRecordMemberGroupList) {

        log.info("[{}] 类型:{} 用户:{} 资金ID:{} 订单号:{} 渠道:{} attr:{}", logTag, userAssetsRecordDTO.getTypeId(), userAssetsRecordDTO.getUserId(), userAssetsRecordDTO.getUserAssetsRecordId(), userAssetsRecordDTO.getOrderNo(), userAssetsRecordDTO.getPayChannel(), userAssetsRecordDTO.getAttr());

        if (type == 1) {
            log.info("[assetss]余额前:{},余额变动:{},余额后:{}", StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getMoney()), 10), StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getThisMoney()), 10), StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getAfterMoney()), 10));
            log.info("[account]余额前:{},余额变动:{},余额后:{}", StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getBalanceBefore()), 10), StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getBalanceChange()), 10), StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getBalanceAfter()), 10));
        } else if (type == 2) {
            log.info("[assetss]冻结前:{},冻结变动:{},冻结后:{}", StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getBlockMoney()), 10), StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getThisBlockMoney()), 10), StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getAfterBlockMoney()), 10));
            log.info("[account]冻结前:{},冻结变动:{},冻结后:{}", StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getFrozenBalanceBefore()), 10), StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getFrozenBalanceChange()), 10), StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getFrozenBalanceAfter()), 10));
        } else if (type == 3) {
            log.info("[assetss]求购前:{},求购变动:{},求购后:{}", StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getPurchaseMoney()), 10), StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getThisPurchaseMoney()), 10), StringUtils.rightPad(String.valueOf(userAssetsRecordDTO.getAfterPurchaseMoney()), 10));
            log.info("[account]求购前:{},求购变动:{},求购后:{}", StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getPurchaseMoney()), 10), StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getThisPurchaseMoney()), 10), StringUtils.rightPad(String.valueOf(userAccountMoneyChangeDTO.getAfterPurchaseMoney()), 10));
        }
        for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberGroupList) {
            log.info("[account子数据] 账户类型:{},余额前:{},余额变动:{},余额后:{}", userAccountRecordMember.getAccountType(), StringUtils.rightPad(String.valueOf(userAccountRecordMember.getBalanceBefore()), 10), StringUtils.rightPad(String.valueOf(userAccountRecordMember.getBalanceChange()), 10), StringUtils.rightPad(String.valueOf(userAccountRecordMember.getBalanceAfter()), 10));
        }
        log.info("----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------");

    }

}
