package com.youpin.clear.app.service;

import com.youpin.clear.client.request.PlatformAccountRecordJobRequest;
import com.youpin.clear.client.response.PlatformAccountRecordJobResponse;
import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;

public interface PlatformAccountRecordService {


    PlatformAccountRecordJobResponse selectPageSizeByStatusAndCreateTime(PlatformAccountRecordJobRequest request);

    void bufferAccountingProcessMq(PlatformAccountRecordDTO dtoMq);

    void bufferAccountingProcess(PlatformAccountRecordDTO dtoMq);

    void handle(PlatformAccountRecordJobRequest request);
}
