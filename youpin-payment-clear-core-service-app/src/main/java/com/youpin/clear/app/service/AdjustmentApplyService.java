package com.youpin.clear.app.service;

import com.youpin.clear.client.request.*;
import com.youpin.clear.client.response.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AdjustmentApplyDTO;
import com.youpin.clear.domain.dto.AdjustmentApplyDetailDTO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AdjustmentApplyService {

    /**
     * 查询调账申请列表
     */
    AdjustApplyListResponse queryList(AdjustApplyListQueryRequest request);

    /**
     * 提交申请
     */
    Long submitApply(AdjustApplyRequest request);

    /**
     * 发送短信验证码
     */
    void sendVerifyCode(ApplySendVerifyCodeRequest request);

     Long saveAdjustmentApplyInfo(AdjustmentApplyDTO adjustmentApplyDTO,List<AdjustmentApplyDetailDTO> adjustmentApplyDetailDTOList);
    /**
     * 调账详情查询
     */
    AdjustDetailResponse queryDetail(Long applyId);

    /**
     * 调账审核
     */
    void applyAudit(AdjustApplyAuditRequest request);


    AdjustmentApplyConfigResponse queryApplyTypeConfig();


    void adjustmentHandle();

    void handleUU898Account(AdjustmentApplyDTO adjustmentApplyDTO);

    void handleMultiAccount(AdjustmentApplyDTO adjustmentApplyDTO);

    void updateSeparateAccount(List<UserAccountRecordMember> userAccountRecordMemberList, Map<Long, AccountAggregate> accountAggregatesMap);

    void adjustApplyImport(AdjustBatchImportRequest request);

    void applyRevoke(ApplyRevokeRequest request);

    BatchAuditDetailResponse batchAuditDetail(ApplyBatchQueryRequest request);

    void batchApplyAudit(ApplyBatchAuditRequest request);

    void updateStatus(AdjustUpdateStatusRequest request);

    Boolean retry(AdjustRetryRequest request);
}