package com.youpin.clear.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.SeparateAccountV2Service;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.factory.MowMoneyHandleFactory;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserSubAccountRecordGateway;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.helper.SeparateAccountHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 易宝分账V2逻辑 实现
 */
@Service
@Slf4j
public class SeparateAccountV2ServiceImpl implements SeparateAccountV2Service {

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    UserAccountGateway userAccountGateway;

    @Autowired
    MowMoneyHandleFactory mowMoneyHandleFactory;

    @Autowired
    UserSubAccountRecordGateway userSubAccountRecordGateway;


    /**
     * 再次分账逻辑入口
     */
    @Override
    public void handle(List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList) {
        //先排序
        List<Integer> customOrderList = SubAssetsCustomOrderEnum.FRONT_NORMAL_GOOD.getCustomOrderList();
        SeparateAccountHelper.customSort(userAssetsRecordMessageDTOList, customOrderList);
        //账户资金查询
        //转换数据
        //UserAccountRecordAggregate userAccountRecordAggregate = new  UserAccountRecordAggregate();
    }


    /**
     * 仅提现分账逻辑入口
     */
    @Override
    public void handleMoneyOnlyWithdraw(List<ClearUserAssetsRecordDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        //状态是失败的剔除掉
        dtoList = dtoList.stream().filter(dto -> !Objects.equals(dto.getStatus(), NetStatusEnum.FAIL.getCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        //校验数据是否重复处理
        boolean checkIdempotentData = checkIdempotentUserAssetsRecordIdempotent(dtoList);
        if (checkIdempotentData) {
            log.warn("[仅可提现数据拆分] 重复操作");
            return;
        }
        log.info("[仅可提现数据拆分] 业务数据2  collect:{}", JSON.toJSONString(dtoList));
        //涉及的用户
        Set<Long> userIdsSet = dtoList.stream().map(ClearUserAssetsRecordDTO::getUserId).collect(Collectors.toSet());
        //涉及的账户类型   AccountTypeEnum 121: 余额1可提现  122: 余额2可提现(暂时不算)
        List<Integer> accountTypeCodeSet = List.of(AccountTypeEnum.BALANCE_WITHDRAW_121.getCode(), AccountTypeEnum.BALANCE_WITHDRAW_122.getCode());
        //获取账户消息
        Map<Long, AccountAggregate> accountAggregateMap = getAccountAggregatesMapByType(userIdsSet, accountTypeCodeSet);
        //账户校验
        if (accountAggregateMap.isEmpty()) {
            log.warn("[仅可提现数据拆分] 账户不存在 userIdsSet:{}", userIdsSet);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_NOT_EXIST);
        }
        for (ClearUserAssetsRecordDTO item : dtoList) {
            //判断是否租赁
            if (item.getIsLeaseOrder().equals(Boolean.TRUE)) {
                //租赁订单处理逻辑
                mowMoneyHandleFactory.getProcessor(MowMoneyEnum.lease_money.getCode()).process(item, accountAggregateMap.get(item.getUserId()));
            } else {
                List<Integer> assetsTypeMoneyList = paymentClearParamsConfig.getAssetsTypeMoneyList();
                if (CollectionUtils.isNotEmpty(assetsTypeMoneyList) && assetsTypeMoneyList.contains(item.getTypeId())) {
                    mowMoneyHandleFactory.getProcessor(MowMoneyEnum.This_Money.getCode()).process(item, accountAggregateMap.get(item.getUserId()));
                    continue;
                }
                List<Integer> assetsTypeSpecialMoneyList = paymentClearParamsConfig.getAssetsTypeSpecialMoneyList();
                if (CollectionUtils.isNotEmpty(assetsTypeSpecialMoneyList) && assetsTypeSpecialMoneyList.contains(item.getTypeId())) {
                    mowMoneyHandleFactory.getProcessor(MowMoneyEnum.Special_Mow_Money.getCode()).process(item, accountAggregateMap.get(item.getUserId()));
                    continue;
                }
                if (null != item.getSubAccountFlowRecordList()) {
                    mowMoneyHandleFactory.getProcessor(MowMoneyEnum.This_Only_Withdraw_Money.getCode()).process(item, accountAggregateMap.get(item.getUserId()));
                }
            }
        }
        //租赁默认是有 - + 的 余额1可提现 , 余额2可提现

        //判断资金使用了多少可提现金额 默认先扣减 余额2 再扣减余额1
        //资金类型判断

        //直接需要操作的类型  thisMoney

        //需要判断子流水的 thisMoney - thisOnlyTradeMoney = thisOnlyWithdrawMoney

        //需要特殊处理的 金额

        //账户加减

        //资金流水插入

    }

    /**
     * 数据幂等判断
     * true 重复 false 不重复
     */
    public boolean checkIdempotentUserAssetsRecordIdempotent(List<ClearUserAssetsRecordDTO> dtoList) {
        for (ClearUserAssetsRecordDTO clearUserAssetsRecordDTO : dtoList) {
            List<UserSubAccountRecordMember> userAccountRecordMemberList = userSubAccountRecordGateway.loadSubAccountRecordMember(clearUserAssetsRecordDTO.getOrderNo());
            if (CollectionUtils.isEmpty(userAccountRecordMemberList)) {
                return false;
            }
            for (UserSubAccountRecordMember userAccountRecordMember : userAccountRecordMemberList) {
                if (userAccountRecordMember.getUserAssetsRecordId().equals(clearUserAssetsRecordDTO.getUserAssetsRecordId()) && userAccountRecordMember.getStatus().equals(clearUserAssetsRecordDTO.getStatus())) {
                    return true;
                }
                // 如果数据库状态和消息状态不一致
//                if (!userAccountRecordMember.getStatus().equals(clearUserAssetsRecordDTO.getStatus())) {
//                    //如果数据库是 成功 /或者 是 失败 可以认为是重复处理
//                    if (userAccountRecordMember.getStatus().equals(NetStatusEnum.SUCCESS.getCode()) || userAccountRecordMember.getStatus().equals(NetStatusEnum.FAIL.getCode())) {
//                        continue;
//                    }
//                    return false;
//                }
            }
        }
        return true;
    }


    /**
     * 获取账户信息
     */
    private Map<Long, AccountAggregate> getAccountAggregatesMapByType(Set<Long> userIdsSet, List<Integer> accountTypeCodeSet) {

        Map<Long, AccountAggregate> accountAggregateMap = new HashMap<>(userIdsSet.size());
        for (Long userId : userIdsSet) {
            try {
                AccountAggregate accountAggregate = userAccountGateway.getAccountAggregatesMapByType(userId, accountTypeCodeSet);
                if (accountAggregate == null) {
                    log.warn("[仅可提现数据拆分] 账户数据为空 userId:{}", userId);
                    return Collections.emptyMap();
                }
                accountAggregateMap.put(userId, accountAggregate);
            } catch (Exception e) {
                log.error("[仅可提现数据拆分] 获取账户数据失败 userId: {}, 异常: {}", userId, e.getMessage(), e);
                return Collections.emptyMap();
            }
        }
        return accountAggregateMap;
    }


}
