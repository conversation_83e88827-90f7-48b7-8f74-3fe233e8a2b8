package com.youpin.clear.app.converter;

import com.youpin.clear.client.response.UserAccountInfoResponse;
import com.youpin.clear.domain.dto.UserAccountInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = "spring", uses = {TypeConversionWorker.class})
public interface UserAccountConvertor {

    UserAccountConvertor MAPPER = Mappers.getMapper(UserAccountConvertor.class);

    UserAccountInfoResponse toUserAccountInfoResponse(UserAccountInfoDTO userAccountInfoDTO);

}

