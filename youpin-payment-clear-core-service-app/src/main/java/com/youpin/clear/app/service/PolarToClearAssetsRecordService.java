package com.youpin.clear.app.service;

import com.youpin.clear.client.request.ClearAssetsTagRequest;
import com.youpin.clear.client.request.PolarToClearAssetsRecordRequest;
import com.youpin.clear.client.response.ClearAssetsTagResponse;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;

import java.util.List;

public interface PolarToClearAssetsRecordService {


    void userAssetsRecordSplit(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList);

    Long maxUserAssetsRecordId(PolarToClearAssetsRecordRequest request);

    Long polarToClearAssetsRecord(PolarToClearAssetsRecordRequest request);

    Integer assetsTagSync(ClearAssetsTagRequest request);

    Integer batchClearUserAssetsRecordDTO(List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOSList);

    Integer assetsTagSyncByUserId(ClearAssetsTagRequest request);

    void assetsTagDelete(ClearAssetsTagRequest request);

    List<ClearAssetsTagResponse> assetsTagCacheGetUserId(ClearAssetsTagRequest request);


    void sendTransactionServiceFeeOperationRecord(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList);
}
