package com.youpin.clear.app.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.NetPlatformUserRecordService;
import com.youpin.clear.client.request.UserAssetsRecordByTypeRequest;
import com.youpin.clear.client.response.UserAssetsRecordByTypeResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.NetPlatformUserRecordMessage;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.ClearUserAssetsRecordGateway;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsInfoGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.infrastructure.config.CompensationUniqueKeyWorkspaces;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class NetPlatformUserRecordServiceImpl implements NetPlatformUserRecordService {
    @Autowired
    UU898UserAssetsInfoGateway uu898UserAssetsInfoGateway;

    @Autowired
    UU898UserAssetsRecordGateway uu898UserAssetsRecordGateway;

    @Autowired
    ClearUserAssetsRecordGateway clearUserAssetsRecordGateway;

    @Autowired
    CompensationRecordGateway compensationRecordGateway;

    @Autowired
    CompensationUniqueKeyWorkspaces compensationUniqueKeyWorkspaces;


    @Override
    public UserAssetsRecordByTypeResponse selectMaxId(UserAssetsRecordByTypeRequest request) {
        Long maxId = uu898UserAssetsRecordGateway.selectMinIdByUserIdAndTypeIdTime(request.getUserId(), request.getTypeIdList(), request.getEndTime());
        if (null == maxId) {
            maxId = 0L;
        }
        UserAssetsRecordByTypeResponse response = new UserAssetsRecordByTypeResponse();
        response.setMaxId(maxId);
        return response;
    }

    /**
     * net缓冲记账-过度-核对流程
     */
    @Override
    public void uu898UserAssetsRecordNetBufferAccountingProcess(UserAssetsRecordByTypeRequest request) {
        List<Long> idList = uu898UserAssetsRecordGateway.selectIdListByUserIdAndTypeIdTimePage(request.getMaxId(), request.getUserId(), request.getTypeIdList(), request.getEndTime(), request.getPageSize());
        if (CollectionUtils.isEmpty(idList)) {
            log.info("[net缓冲记账-过度-核对流程]   资金明细不存在 {}", JSON.toJSONString(request.getTypeIdList()));
            return;
        }
        for (Long id : idList) {
            log.info("[net缓冲记账-过度-核对流程]   资金明细ID:{}", id);
            NetPlatformUserRecordMessage netPlatformUserRecordMessage = new NetPlatformUserRecordMessage();
            netPlatformUserRecordMessage.setId(id);
            UU898UserAssetsRecordDTO dto = uu898UserAssetsRecordGateway.selectById(netPlatformUserRecordMessage.getId());
            if (!dto.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
                continue;
            }
            netPlatformUserRecordMessage.setUserId(dto.getUserId());
            netPlatformUserRecordMessage.setTypeId(dto.getTypeId());
            netPlatformUserRecordMessage.setTreadNo(dto.getTreadNo());
            String uniqueKey = compensationUniqueKeyWorkspaces.getNetPlatformUserRecordConsumerUniqueKey(dto.getUserId(), dto.getTreadNo());
            if (compensationRecordGateway.checkUniqueKeyCount(uniqueKey, BizCompensationSceneEnum.NET_PLATFORM_USER_RECORD.getCode())) {
                log.info("[net缓冲记账-过度-核对流程] 已经存在补单数据 不在处理 {}", uniqueKey);
            }
            //处理开始
            bufferAccountingProcess(netPlatformUserRecordMessage);
        }
    }


    /**
     * net平台户资金异步缓冲顺序处理
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bufferAccountingProcess(NetPlatformUserRecordMessage netPlatformUserRecordMessage) {
        if (null == netPlatformUserRecordMessage.getId() || null == netPlatformUserRecordMessage.getUserId() || StringUtils.isBlank(netPlatformUserRecordMessage.getTreadNo())) {
            log.warn("[net平台户资金异步缓冲顺序处理] 用户资金记录ID为空 :{}", JSON.toJSONString(netPlatformUserRecordMessage));
            return;
        }
        //查询资金明细数据
        UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO = uu898UserAssetsRecordGateway.selectById(netPlatformUserRecordMessage.getId());

        if (null == uu898UserAssetsRecordDTO) {
            log.error("[net平台户资金异步缓冲顺序处理] 用户资金明细查询失败,数据为null :{}", JSON.toJSONString(netPlatformUserRecordMessage));
            return;
        }
        //进行中的数据 才处理
        if (uu898UserAssetsRecordDTO.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
            //查询用户资金信息
            UserAssetsInfoDTO originUserAssetsInfoDTO = uu898UserAssetsInfoGateway.getUserAssetsInfo(uu898UserAssetsRecordDTO.getUserId());
            if (originUserAssetsInfoDTO == null) {
                log.error("[net平台户资金异步缓冲顺序处理] 用户资金信息查询失败 :{}", JSON.toJSONString(netPlatformUserRecordMessage));
                return;
            }
            //计算出更新后的余额
            //221 支付补贴金额  222 退还补贴金额  thisMoney 有负 有正
            BigDecimal afterMoney = originUserAssetsInfoDTO.getMoney().add(uu898UserAssetsRecordDTO.getThisMoney());

            UserAssetsInfoDTO toUpdateUserAssetsInfoDTO = new UserAssetsInfoDTO();
            toUpdateUserAssetsInfoDTO.setMoney(afterMoney);
            toUpdateUserAssetsInfoDTO.setLastUserRecordId(uu898UserAssetsRecordDTO.getId());
            toUpdateUserAssetsInfoDTO.setUpdateTime(LocalDateTime.now());

            //校验
            if (afterMoney.compareTo(BigDecimal.ZERO) < 0) {
                log.error("[net平台户资金异步缓冲顺序处理] 余额不足 :{}  {}", afterMoney, JSON.toJSONString(uu898UserAssetsRecordDTO));
                throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
            }

            //更新用户余额
            boolean updateUserBalanceAndBlockMoneyFlag = uu898UserAssetsInfoGateway.updateUserBalance(originUserAssetsInfoDTO, toUpdateUserAssetsInfoDTO) > 0;
            if (!updateUserBalanceAndBlockMoneyFlag) {
                log.error("[net平台户资金异步缓冲顺序处理] 用户资金余额更新失败 :{}", JSON.toJSONString(originUserAssetsInfoDTO));
                log.info("[net平台户资金异步缓冲顺序处理] 用户资金余额更新失败 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "用户资金余额更新失败,待补偿");
            }

            //更新用户资金明细
            uu898UserAssetsRecordDTO.setMoney(originUserAssetsInfoDTO.getMoney());
            uu898UserAssetsRecordDTO.setAfterMoney(afterMoney);
            uu898UserAssetsRecordDTO.setStatus(NetStatusEnum.SUCCESS.getCode());
            uu898UserAssetsRecordDTO.setCompleteTime(LocalDateTime.now());
            boolean userAssetsRecordUpdateFlag = uu898UserAssetsRecordGateway.update(uu898UserAssetsRecordDTO) > 0;
            if (!userAssetsRecordUpdateFlag) {
                log.error("[net平台户资金异步缓冲顺序处理] 用户资金明细更新失败 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "用户资金明细更新失败,待补偿");
            }
        }
        //同步数据
        ClearUserAssetsRecordDTO clearUserAssetsRecordDTO = clearUserAssetsRecordGateway.selectUserAssetsRecordById(uu898UserAssetsRecordDTO.getUserId(), uu898UserAssetsRecordDTO.getId());
        if (null == clearUserAssetsRecordDTO) {
            //插入数据
            ClearUserAssetsRecordDTO clearUserAssetsRecordDTOInsert = UserAssetsRecordConvertor.MAPPER.toClearUserAssetsRecordDTOByUU898(uu898UserAssetsRecordDTO);
            clearUserAssetsRecordDTOInsert.setTypeName(getTypeName(uu898UserAssetsRecordDTO.getTypeId()));
            clearUserAssetsRecordDTOInsert.setCollectType(CollectTypeEnum.OWN.getCode());
            clearUserAssetsRecordDTOInsert.setMerchantId(ClearConstants.MERCHANT_ID);
            clearUserAssetsRecordDTOInsert.setStatus(uu898UserAssetsRecordDTO.getStatus());
            clearUserAssetsRecordDTOInsert.setMoney(uu898UserAssetsRecordDTO.getMoney());
            clearUserAssetsRecordDTOInsert.setAfterMoney(uu898UserAssetsRecordDTO.getAfterMoney());
            clearUserAssetsRecordDTOInsert.setCompleteTime(uu898UserAssetsRecordDTO.getCompleteTime());
            clearUserAssetsRecordGateway.insertSelective(clearUserAssetsRecordDTOInsert);
            log.info("[net平台户资金异步缓冲顺序处理] 用户资金明细插入成功 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());
        } else {
            //更新数据
            clearUserAssetsRecordDTO.setTypeName(getTypeName(uu898UserAssetsRecordDTO.getTypeId()));
            clearUserAssetsRecordDTO.setMoney(uu898UserAssetsRecordDTO.getMoney());
            clearUserAssetsRecordDTO.setAfterMoney(uu898UserAssetsRecordDTO.getAfterMoney());
            clearUserAssetsRecordDTO.setStatus(uu898UserAssetsRecordDTO.getStatus());
            clearUserAssetsRecordDTO.setCompleteTime(uu898UserAssetsRecordDTO.getCompleteTime());
            clearUserAssetsRecordGateway.updateByPrimaryKeySelectiveByUserId(clearUserAssetsRecordDTO);
            log.info("[net平台户资金异步缓冲顺序处理] 用户资金明细更新成功 资金ID:{},订单号:{},资金交易单号:{}", uu898UserAssetsRecordDTO.getId(), uu898UserAssetsRecordDTO.getOrderNo(), uu898UserAssetsRecordDTO.getTreadNo());
        }
    }


    String getTypeName(Integer typeId) {
        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_221)) {
            return "支付补贴金额";
        }
        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_222)) {
            return "退还补贴金额";
        }
        return "";
    }


}
