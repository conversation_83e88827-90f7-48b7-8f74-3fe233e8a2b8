package com.youpin.clear.app.consumer;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.app.service.UserAccountService;
import com.youpin.clear.client.request.OpenAccountRequest;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.UserOpenAccountMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
import youpin.commons.rocketmq.core.RocketMQListener;

@Slf4j
@Component
@RocketMQMessageListener(topic = MQConfig.USER_REGISTER_SUCCESS_TOPIC, consumerGroup = MQConfig.USER_REGISTER_ACCOUNT_GROUP)
public class UserOpenAccountConsumer implements RocketMQListener<String> {

    @Autowired
    UserAccountService userAccountService;

    @Override
    public void onMessage(String message) {
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("[用户开户] 监听消息:接收到了空消息");
            }
            UserOpenAccountMessage request = JSON.parseObject(message, UserOpenAccountMessage.class);
            userAccountService.createUserAccount(new OpenAccountRequest(request.getUserId()));
        } catch (Exception e) {
            log.error("[用户开户] 开户异常:{}", ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_CONSUMER_FAILED);
        }
    }
}
