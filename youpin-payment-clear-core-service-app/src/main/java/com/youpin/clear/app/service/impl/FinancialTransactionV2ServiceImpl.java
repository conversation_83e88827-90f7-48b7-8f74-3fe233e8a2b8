package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.converter.FinancialTransactionConvertor;
import com.youpin.clear.app.service.FinancialTransactionV2Service;
import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.AssetInfoMoney;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.client.response.financial.SettleResponse;
import com.youpin.clear.common.enums.FinancialTypeEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.factory.SubBusFinancialProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class FinancialTransactionV2ServiceImpl implements FinancialTransactionV2Service {

    @Autowired
    SubBusFinancialProcessorFactory subBusFinancialProcessorFactory;


    /**
     * 支付
     */
    @Override
    public FinancialResponse pay(PayFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTO(request, FinancialTypeEnum.Pay);
        financialProcessorDTO.setUseNewAssetBranch(Boolean.TRUE);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    /**
     * 退款
     */
    @Override
    public FinancialResponse refund(RefundFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOByRefund(request, FinancialTypeEnum.Refund);
        financialProcessorDTO.setUseNewAssetBranch(Boolean.TRUE);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        return FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
    }

    /**
     * 结算
     */
    @Override
    public FinancialResponse settlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Settlement);
        financialProcessorDTO.setUseNewAssetBranch(Boolean.TRUE);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        FinancialResponse financialResponse = FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
        SettleResponse settleResponse = SettleResponse.builder()
                .assetInfoMoneyList(buildAssetList(financialProcessorDTO))
                .build();
        financialResponse.setSettleResponse(settleResponse);
        return financialResponse;    }

    /**
     * 特殊结算
     */
    @Override
    public FinancialResponse specialSettlement(SettlementFinancialRequest request) {
        FinancialProcessorDTO financialProcessorDTO = FinancialTransactionConvertor.MAPPER.toFinancialProcessorResultDTOBySettlement(request, FinancialTypeEnum.Special_Settlement);
        financialProcessorDTO.setUseNewAssetBranch(Boolean.TRUE);
        FinancialProcessorResultDTO processorResultDTO = subBusFinancialProcessorFactory.getProcessor(financialProcessorDTO.getSubBusTypeFrontEnum()).process(financialProcessorDTO);
        FinancialResponse financialResponse = FinancialTransactionConvertor.MAPPER.toFinancialResponse(processorResultDTO);
        SettleResponse settleResponse = SettleResponse.builder()
                .assetInfoMoneyList(buildAssetList(financialProcessorDTO))
                .build();
        financialResponse.setSettleResponse(settleResponse);
        return financialResponse;
    }

    private List<AssetInfoMoney> buildAssetList(FinancialProcessorDTO financialProcessorDTO) {
        AssetInfoMoney assetInfo247 = AssetInfoMoney.builder()
                .typeId(UserAssetsTypeEnum.TYPE_247.getTypeId())
                .money(Objects.requireNonNullElse(financialProcessorDTO.getSettleFrozeMoney(), BigDecimal.ZERO))
                .build();

        AssetInfoMoney assetInfo256 = AssetInfoMoney.builder()
                .typeId(UserAssetsTypeEnum.TYPE_256.getTypeId())
                .money(Objects.requireNonNullElse(financialProcessorDTO.getPrivateSettleFrozeMoney(), BigDecimal.ZERO))
                .build();
        return List.of(assetInfo247, assetInfo256);
    }
}
