//package com.youpin.clear.app.consumer;
//
//
//import com.alibaba.fastjson.JSON;
//import com.youpin.clear.app.service.PlatformAccountRecordService;
//import com.youpin.clear.common.constant.MQConfig;
//import com.youpin.clear.common.enums.ErrorCode;
//import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;
//import com.youpin.clear.domain.exception.PaymentClearBusinessException;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import youpin.commons.rocketmq.annotation.RocketMQMessageListener;
//import youpin.commons.rocketmq.core.RocketMQListener;
//
///**
// * 缓冲记账-平台户消费处理-顺序消费-用户分组
// * 开发-d1-通用  rmq-cn-wwo388zkk07
// * 测试-t1-通用2 rmq-cn-omn3llwdb4q
// * 预发-pre-通用2 rmq-cn-vo93muk7b09
// */
//@Slf4j
//@Component
//@RocketMQMessageListener(name = MQConfig.PAYMENT_CLEAR_MQ, topic = MQConfig.PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TOPIC, expression = MQConfig.PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TAG, consumerGroup = MQConfig.PLATFORM_ACCOUNT_TRANSACTION_RECORDS_GROUP, filterExpressionType = FilterExpressionType.TAG)
//public class PlatformAccountRecordConsumer implements RocketMQListener<String> {
//
//    @Autowired
//    PlatformAccountRecordService platformAccountRecordService;
//
//    /**
//     * 不做补单是因为 数据已经存了备份
//     * 使用MQ 异常处理
//     */
//
//    @Override
//    public void onMessage(String msg) {
//        if (StringUtils.isEmpty(msg)) {
//            log.error("[缓冲记账] 平台户消费处理 监听消息:接收到了空消息");
//            return;
//        }
//        log.info("[缓冲记账]  接收到的消息:{}", msg);
//        PlatformAccountRecordDTO dto = JSON.parseObject(msg, PlatformAccountRecordDTO.class);
//        try {
//            platformAccountRecordService.bufferAccountingProcessMq(dto);
//        } catch (Exception e) {
//            log.error("[缓冲记账]数据: {}", msg);
//            throw new PaymentClearBusinessException(ErrorCode.MQ_CONSUMER_FAILED);
//        }
//    }
//
//}
