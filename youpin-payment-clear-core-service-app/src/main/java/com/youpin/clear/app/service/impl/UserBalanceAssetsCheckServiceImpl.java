package com.youpin.clear.app.service.impl;

import com.youpin.clear.app.service.UserBalanceAssetsCheckService;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.ReconciliationCheckFlagEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.UserAccountReconciliationRecordDTO;
import com.youpin.clear.domain.dto.UserBalanceAssetsCheckMessage;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserAccountReconciliationRecordGateway;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import com.youpin.clear.domain.servcie.DingTalkService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.utils.ClearRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 对账相关
 */
@Slf4j
@Service
public class UserBalanceAssetsCheckServiceImpl implements UserBalanceAssetsCheckService {
    @Autowired
    UserAccountGateway userAccountGateway;

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Autowired
    UserAccountReconciliationRecordGateway userAccountReconciliationRecordGateway;

    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    private static final String REDIS_KEY_USER_BALANCE_ASSETS_CHECK_ERROR = "pay:order:userBalanceAssetsCheck:dingtalk:";


    @Override
    public void userBalanceAssetsCheck(List<UserBalanceAssetsCheckMessage> userBalanceAssetsCheckMessageList) {
        //数据对账
        for (UserBalanceAssetsCheckMessage userBalanceAssetsCheckMessage : userBalanceAssetsCheckMessageList) {
            //用户id
            Long userId = userBalanceAssetsCheckMessage.getUserId();
            //查询用户账户信息
            AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(userId);
            if (null == accountAggregate || null == accountAggregate.getAccountInfoMemberMap() || accountAggregate.getAccountInfoMemberMap().isEmpty()) {
                log.error("[用户资金余额对账] 用户Id:{} 用户账户不存在", userId);
                return;
            }
            //账户金额对账
            boolean checkUserAccountFlag = checkUserAccount(accountAggregate);

            log.info("[用户资金余额对账] 用户Id:{} 用户账户对账结果:{}", userId, checkUserAccountFlag);
            //分账数据对账

            boolean checkFlag1 = checkUserAccountRecord(accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1), AccountTypeEnum.BALANCE_1);
            boolean checkFlag2 = checkUserAccountRecord(accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2), AccountTypeEnum.BALANCE_2);
            boolean checkPurchaseFlag1 = checkUserAccountRecord(accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1);
            boolean checkPurchaseFlag2 = checkUserAccountRecord(accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2);
            boolean checkPurchaseFlag3 = checkUserAccountRecord(accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1);
            boolean checkPurchaseFlag4 = checkUserAccountRecord(accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2);

            StringBuilder msg = new StringBuilder();
            if (!checkUserAccountFlag) {
                msg.append(userId).append(" 用户资金余额对账 ").append(" 账户余额对账结果:失败").append("\n");
            }
            if (!checkFlag1) {
                msg.append(userId).append(" 用户资金:").append(AccountTypeEnum.BALANCE_1.getName()).append("-").append(" 异常").append("\n");
            }
            if (!checkFlag2) {
                msg.append(userId).append(" 用户资金:").append(AccountTypeEnum.BALANCE_2.getName()).append("-").append(" 异常").append("\n");
            }
            if (!checkPurchaseFlag1) {
                msg.append(userId).append(" 用户资金:").append(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getName()).append("-").append(" 异常").append("\n");
            }
            if (!checkPurchaseFlag2) {
                msg.append(userId).append(" 用户资金:").append(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getName()).append("-").append(" 异常").append("\n");
            }
            if (!checkPurchaseFlag3) {
                msg.append(userId).append(" 用户资金:").append(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getName()).append("-").append(" 异常").append("\n");
            }
            if (!checkPurchaseFlag4) {
                msg.append(userId).append(" 用户资金:").append(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getName()).append("-").append(" 异常");
            }
            if (msg.length() > ClearConstants.CONSTANT_INT_0) {
                Long threshold = paymentClearParamsConfig.getDingtalkUserBalanceAssetsCheckThreshold();
                Long checkTimeout = paymentClearParamsConfig.getDingtalkUserBalanceAssetsCheckTimeout();
                String redisKey = REDIS_KEY_USER_BALANCE_ASSETS_CHECK_ERROR + userId;
                log.info("[用户资金余额对账] 用户Id:{} 对账结果:{}", userId, msg);
                boolean flag = ClearRedisUtils.incNumberFlag(redisKey, threshold, checkTimeout, TimeUnit.SECONDS);
                if (!flag) {
                    dingTalkService.sendDingTalkMessage("userBalanceAssetsCheck", msg.toString());
                } else {
                    log.info("[用户资金余额对账] 用户Id:{} redisKey:{} 超过设置阈值:{} ,{}", userId, redisKey, threshold, checkTimeout);
                }
            }
        }
    }


    boolean checkUserAccountRecord(AccountInfoMember accountInfoMember, AccountTypeEnum accountTypeEnum) {
        boolean checkFlag = Boolean.FALSE;

        Long userId = accountInfoMember.getUserId();
        String userAccountNo = accountInfoMember.getUserAccountNo();
        BigDecimal balance = accountInfoMember.getBalance();
        Long startId = ClearConstants.CONSTANT_LONG_0;
        //最后一次资金变动
        Long endId = accountInfoMember.getLastAccountRecordId();
        if (null == endId || endId.equals(ClearConstants.CONSTANT_LONG_0)) {
            return Boolean.TRUE;
        }

        UserAccountReconciliationRecordDTO accountReconciliationRecordDTO = userAccountReconciliationRecordGateway.getByUserIdAndAccountType(userId, accountTypeEnum.getCode());

        if (null != accountReconciliationRecordDTO) {
            if (null != accountReconciliationRecordDTO.getLastAccountRecordId() && !accountReconciliationRecordDTO.getLastAccountRecordId().equals(ClearConstants.CONSTANT_LONG_0)) {
                startId = accountReconciliationRecordDTO.getLastAccountRecordId();
            }
            if (null != accountReconciliationRecordDTO.getCheckFlag()) {
                checkFlag = accountReconciliationRecordDTO.isCheckFlag();
            }
        }
        //账户没有资金变动记录
        if (startId.compareTo(endId) == ClearConstants.CONSTANT_INT_0 && checkFlag) {
            return Boolean.TRUE;
        }

        if (startId.compareTo(endId) > ClearConstants.CONSTANT_INT_0) {
            log.warn("[用户资金余额对账] 用户:{} 账户:{} 类型:{} 变动记录ID 小于 对账记录ID startId:{} endId:{}", userId, userAccountNo, accountTypeEnum.getName(), startId, endId);
            //更新变动记录
            saveOrUpdateUserAccountReconciliationRecord(accountTypeEnum, accountReconciliationRecordDTO, userId, userAccountNo, startId, Boolean.TRUE, endId);
            return Boolean.TRUE;
        }

        //查询比对开始的金分账记录
        if (startId.compareTo(ClearConstants.CONSTANT_LONG_0) == ClearConstants.CONSTANT_INT_0) {
            startId = userAccountRecordGateway.minIdByUserId(userId, accountTypeEnum.getCode());
        }
        if (null == startId) {
            startId = ClearConstants.CONSTANT_LONG_0;
        }
        log.info("[用户资金余额对账] 用户:{} 账户:{} 类型:{} startId:{} endId:{}", userId, userAccountNo, accountTypeEnum.getName(), startId, endId);
        UserAccountRecordMember startUserAccountRecordMember = userAccountRecordGateway.getByUserIdAndId(userId, startId);
        if (null == startUserAccountRecordMember) {
            log.warn("[用户资金余额对账] 用户:{} 账户:{} 类型:{} 查询比对开始金分账记录 不存在", userId, userAccountNo, accountTypeEnum.getName());
            return Boolean.FALSE;
        }
        //变动之前的金额
        BigDecimal startBalanceBefore = startUserAccountRecordMember.getBalanceBefore();

        //余额变动汇总数据
        BigDecimal sumBalanceChange = userAccountRecordGateway.sumBalanceByIdAndAccountType(userId, startId, endId, accountTypeEnum.getCode());

        //比对结果
        checkFlag = balance.compareTo(startBalanceBefore.add(sumBalanceChange)) == ClearConstants.CONSTANT_INTEGER_0;

        saveOrUpdateUserAccountReconciliationRecord(accountTypeEnum, accountReconciliationRecordDTO, userId, userAccountNo, startId, checkFlag, endId);
        log.info("[用户资金余额对账] 用户:{} 账户:{} 类型:{} startId:{} endId:{} 对账结果:{}", userId, userAccountNo, accountTypeEnum.getName(), startId, endId, checkFlag ? "成功" : "失败");
        return checkFlag;
    }

    private void saveOrUpdateUserAccountReconciliationRecord(AccountTypeEnum accountTypeEnum, UserAccountReconciliationRecordDTO accountReconciliationRecordDTO, Long userId, String userAccountNo, Long startId, boolean checkFlag, Long endId) {
        if (null == accountReconciliationRecordDTO) {
            accountReconciliationRecordDTO = UserAccountReconciliationRecordDTO.builder().userId(userId).userAccountNo(userAccountNo)
                    .accountType(accountTypeEnum.getCode())
                    .lastAccountRecordId(startId)
                    .userWeight(ClearConstants.CONSTANT_LONG_1)
                    .createTime(LocalDateTime.now()).checkFlag(checkFlag ? ReconciliationCheckFlagEnum.SUCCESS.getCode() : ReconciliationCheckFlagEnum.ERROR.getCode()).build();
            userAccountReconciliationRecordGateway.save(accountReconciliationRecordDTO);
        } else {
            accountReconciliationRecordDTO.valCheckFlag(checkFlag);
            accountReconciliationRecordDTO.setLastAccountRecordId(checkFlag ? endId : startId);
            accountReconciliationRecordDTO.addUserWeight();
            accountReconciliationRecordDTO.setUpdateTime(null);
            userAccountReconciliationRecordGateway.updateByUserIdAndIdSelective(accountReconciliationRecordDTO);
        }
    }


    boolean checkUserAccount(AccountAggregate accountAggregate) {
        BigDecimal balance = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE).getBalance();
        BigDecimal balance1 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1).getBalance();
        BigDecimal balance2 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2).getBalance();
        BigDecimal purchaseBalance = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE).getBalance();
        BigDecimal purchaseBalanceRecharge1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1).getBalance();
        BigDecimal purchaseBalanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2).getBalance();
        BigDecimal purchaseBalanceTransfer1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1).getBalance();
        BigDecimal purchaseBalanceTransfer2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2).getBalance();
        Long userId = accountAggregate.getUserId();

        boolean checkBalance = balance.compareTo(balance1.add(balance2)) != ClearConstants.CONSTANT_INT_0;
        if (checkBalance) {
            log.warn("[获取用户账户信息] userId:{} 账户余额与余额1+余额2不一致 账户余额:{},余额1:{},余额2{}", userId, balance, balance1, balance2);
        }
        boolean checkBalance2 = purchaseBalance.compareTo(purchaseBalanceRecharge1.add(purchaseBalanceRecharge2).add(purchaseBalanceTransfer1).add(purchaseBalanceTransfer2)) != ClearConstants.CONSTANT_INT_0;
        if (checkBalance) {
            log.warn("[获取用户账户信息] userId:{} 账户求购余额与(求购充值1+求购充值2+求购转入1+求购转入2)不一致 求购余额:{},充值1:{},充值2:{},转入1:{},转入2{}", userId, purchaseBalance, purchaseBalanceRecharge1, purchaseBalanceRecharge2, purchaseBalanceTransfer1, purchaseBalanceTransfer2);
        }
        return !(checkBalance || checkBalance2);
    }

}
