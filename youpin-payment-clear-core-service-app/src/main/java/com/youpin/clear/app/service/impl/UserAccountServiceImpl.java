package com.youpin.clear.app.service.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.youpin.clear.app.converter.UserAccountConvertor;
import com.youpin.clear.app.service.UserAccountService;
import com.youpin.clear.client.request.CheckAccountRequest;
import com.youpin.clear.client.request.OpenAccountRequest;
import com.youpin.clear.client.request.UserAccountInfoRequest;
import com.youpin.clear.client.response.CheckAccountResponse;
import com.youpin.clear.client.response.UserAccountInfoResponse;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.constant.DingTalkConstants;
import com.youpin.clear.common.constant.RedisKey;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.UU898UserSubAccountType;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.aggregate.model.AccountTemplate;
import com.youpin.clear.domain.dto.UU898UserSubAccountDTO;
import com.youpin.clear.domain.dto.UpdateAccountBalanceDTO;
import com.youpin.clear.domain.dto.UserAccountInfoDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.*;
import com.youpin.clear.domain.servcie.DingTalkService;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.infrastructure.config.LockKeyWorkspaces;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.utils.ClearRedisUtils;
import com.youpin.clear.infrastructure.utils.DateTimeUtil;
import com.youpin.clear.infrastructure.utils.RLockUtilsComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserAccountServiceImpl implements UserAccountService {

    @Autowired
    UserAccountGateway userAccountGateway;

    @Autowired
    LockKeyWorkspaces lockKeyWorkspaces;

    @Autowired
    UU898UserAssetsInfoGateway userAssetsInfoGateway;

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Autowired
    UserAccountReconciliationRecordGateway userAccountReconciliationRecordGateway;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    @Qualifier("asyncTaskExecutor")
    ThreadPoolTaskExecutor asyncTaskExecutor;

    @Autowired
    RLockUtilsComponent rLockUtilsComponent;


    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    IdWorkService idWorkService;

    @Autowired
    UU898UserSubAccountGateway uu898UserSubAccountGateway;

    @Autowired
    UserSubAccountRecordGateway userSubAccountRecordGateway;


    @Override
    public void createUserAccount(OpenAccountRequest request) {
        rLockUtilsComponent.lockHandle(lockKeyWorkspaces.syncUserAccountKey(request.getUserId()), paymentClearParamsConfig.getLockWaitTime(), paymentClearParamsConfig.getLockLeaseTime(), tm -> createUserAccount(request.getUserId()));
    }

    private void createUserAccount(Long userId) {
        List<AccountInfoMember> accountInfoMember = userAccountGateway.getAccountInfoMember(userId);
        if (null == accountInfoMember || accountInfoMember.isEmpty()) {
            userAccountGateway.createUserAccount(userId);
        } else {
            throw new PaymentClearBusinessException(ErrorCode.OPEN_ACCOUNT_ERROR);
        }
    }

    /**
     * 同步账户数据
     */
    @Override
    public void syncUserAccount(OpenAccountRequest request) {
        log.info("[同步账户余额数据] userID:{} 开始 ", request.getUserId());
        try {
            syncUserAccount(request.getUserId());
        } catch (Exception e) {
            log.error("[同步账户余额数据] userID:{} 同步账户余额数据异常", request.getUserId(), e);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_SYNC_FAIL);
        }
        log.info("[同步账户余额数据] userID:{} 完成 ", request.getUserId());
    }


    @Override
    public UserAccountInfoResponse queryUserAccount(UserAccountInfoRequest request) {
        //入参不能为null
        if (request.getUserId() == null) {
            log.warn("[获取用户账户信息] 入参userId不能为null");
            return null;
        }
        //多线程查询
        CompletableFuture<UserAccountInfoDTO> userAccountInfoDTOFuture = CompletableFuture.supplyAsync(() -> userAccountGateway.queryUserAccountInfoDTO(request.getUserId()), asyncTaskExecutor);
        CompletableFuture<UserAssetsInfoDTO> userAssetsInfoDTOFuture = CompletableFuture.supplyAsync(() -> userAssetsInfoGateway.getUserAssetsInfo(request.getUserId()), asyncTaskExecutor);
        CompletableFuture<Map<Integer, UU898UserSubAccountDTO>> uu988SubAccountInfoDTOFuture = CompletableFuture.supplyAsync(() -> uu898UserSubAccountGateway.getUserSubAccountMap(request.getUserId()), asyncTaskExecutor);

        try {
            UserAccountInfoDTO userAccountInfoDTO = userAccountInfoDTOFuture.get();
            UserAssetsInfoDTO userAssetsInfoDTO = userAssetsInfoDTOFuture.get();
            Map<Integer, UU898UserSubAccountDTO> uu898SubAccountInfoDTOMap = uu988SubAccountInfoDTOFuture.get();
            //取出仅可交易账户
            UU898UserSubAccountDTO uu898OnlyTradeSubAccount = uu898SubAccountInfoDTOMap.getOrDefault(UU898UserSubAccountType.TRADE.getType(), null);
            if (null == uu898OnlyTradeSubAccount) {
                uu898OnlyTradeSubAccount = new UU898UserSubAccountDTO();
                uu898OnlyTradeSubAccount.setBalance(BigDecimal.ZERO);
            }

            UserAccountInfoResponse userAccountInfoResponse = UserAccountConvertor.MAPPER.toUserAccountInfoResponse(userAccountInfoDTO);
            userAccountInfoResponse.setUu898Balance(userAssetsInfoDTO.getMoney());
            userAccountInfoResponse.setUu898BalanceBlock(userAssetsInfoDTO.getBlockMoney());
            userAccountInfoResponse.setUu898PurchaseBalance(userAssetsInfoDTO.getPurchaseMoney());
            userAccountInfoResponse.setUu898PurchaseBalanceTransfer(userAssetsInfoDTO.getPurchaseMoneyFromMoney());
            userAccountInfoResponse.setUu898OnlyTradeBalance(uu898OnlyTradeSubAccount.getBalance());
            //变更一下值  uu898可用->uu898可用-余额1可提现
            BigDecimal balance2Withdraw = userAccountInfoResponse.getUu898Balance().subtract(userAccountInfoResponse.getUu898OnlyTradeBalance()).subtract(userAccountInfoResponse.getBalance1Withdraw());
            if (balance2Withdraw.compareTo(BigDecimal.ZERO) < ClearConstants.CONSTANT_INT_0) {
                balance2Withdraw = BigDecimal.ZERO;
            }
            userAccountInfoResponse.setBalance1Withdraw(userAccountInfoResponse.getBalance1Withdraw());
            userAccountInfoResponse.setBalance2Withdraw(balance2Withdraw);
            if (userAccountInfoResponse.getBalance1Withdraw().compareTo(BigDecimal.ZERO) >= 0 || userAccountInfoResponse.getBalance2Withdraw().compareTo(BigDecimal.ZERO) >= 0) {
                userAccountInfoResponse.setShowBalanceWithdraw(Boolean.TRUE);
            }
            if (Boolean.FALSE.equals(paymentClearParamsConfig.getAssetsTypeMoneyOnlyWithdrawalFlag())) {
                userAccountInfoResponse.setShowBalanceWithdraw(Boolean.FALSE);
            }
            return userAccountInfoResponse;
        } catch (Exception e) {
            log.error("[获取用户账户信息] userID:{} 获取用户账户信息异常", request.getUserId(), e);
            throw new BusinessException(ErrorCode.ACCOUNT_NOT_EXIST.getCode(), "获取用户账户信息失败");
        }
    }

    @Override
    public List<CheckAccountResponse> checkBatchUserAccount(CheckAccountRequest request) {
        List<CheckAccountResponse> list = new ArrayList<>();
        List<UserAssetsInfoDTO> userAssetsInfoList = userAssetsInfoGateway.getUserAssetsInfo(request.getUserIdMin(), request.getUserIdMax(), request.getEasy());
        if (null == userAssetsInfoList || userAssetsInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        //userAssetsInfoList多线程循环
        userAssetsInfoList.parallelStream().forEach(userAssetsInfoDTO -> {
            CheckAccountRequest checkAccountRequest = new CheckAccountRequest();
            checkAccountRequest.setUserId(userAssetsInfoDTO.getUserId());
            checkAccountRequest.setSyncAccountFlag(request.getSyncAccountFlag());
            Boolean checkBalance = checkUserAccount(checkAccountRequest, userAssetsInfoDTO);
            if (Boolean.FALSE.equals(checkBalance)) {
                list.add(new CheckAccountResponse(false, checkAccountRequest.getUserId()));
            }
        });
        return list;
    }

    @Override
    public Boolean checkUserAccount(CheckAccountRequest request) {
        UserAssetsInfoDTO userAssetsInfo = userAssetsInfoGateway.getUserAssetsInfo(request.getUserId());
        return checkUserAccount(request, userAssetsInfo);
    }

    @Override
    public Boolean checkUserAccount2(CheckAccountRequest request) {
        //查询uu898的账户消息
        UserAssetsInfoDTO userAssetsInfo = userAssetsInfoGateway.getUserAssetsInfo(request.getUserId());
        //查询clear 的账户消息
        AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(request.getUserId());

        BigDecimal balance, balance1, balance2;
        BigDecimal frozenBalance, frozenBalance1, frozenBalance2;
        BigDecimal purchaseBalance, purchaseBalanceRecharge, purchaseBalanceRecharge1, purchaseBalanceRecharge2;
        BigDecimal purchaseMoneyFromMoney, purchaseBalanceTransfer1, purchaseBalanceTransfer2;


        balance = userAssetsInfo.getMoney();
        balance2 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2).getBalance();
        balance1 = balance.subtract(balance2);
        balance1 = balance1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : balance1;
        balance2 = balance.subtract(balance1);

        frozenBalance = userAssetsInfo.getBlockMoney();
        frozenBalance2 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2).getFrozenBalance();
        frozenBalance1 = frozenBalance.subtract(frozenBalance2);
        frozenBalance1 = frozenBalance1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : frozenBalance1;
        frozenBalance2 = frozenBalance.subtract(frozenBalance1);

        purchaseBalance = userAssetsInfo.getPurchaseMoney();
        purchaseMoneyFromMoney = userAssetsInfo.getPurchaseMoneyFromMoney();
        purchaseBalanceRecharge = purchaseBalance.subtract(purchaseMoneyFromMoney);
        purchaseBalanceRecharge = purchaseBalanceRecharge.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : purchaseBalanceRecharge;

        purchaseBalanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2).getBalance();
        purchaseBalanceRecharge1 = purchaseBalanceRecharge.subtract(purchaseBalanceRecharge2);
        purchaseBalanceRecharge1 = purchaseBalanceRecharge1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : purchaseBalanceRecharge1;
        purchaseBalanceRecharge2 = purchaseBalanceRecharge.subtract(purchaseBalanceRecharge1);
        purchaseBalanceRecharge2 = purchaseBalanceRecharge2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : purchaseBalanceRecharge2;

        purchaseBalanceTransfer2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2).getBalance();
        purchaseBalanceTransfer1 = purchaseMoneyFromMoney.subtract(purchaseBalanceTransfer2);
        purchaseBalanceTransfer1 = purchaseBalanceTransfer1.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : purchaseBalanceTransfer1;
        purchaseBalanceTransfer2 = purchaseMoneyFromMoney.subtract(purchaseBalanceTransfer1);
        purchaseBalanceTransfer2 = purchaseBalanceTransfer2.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : purchaseBalanceTransfer2;

        log.info("[账户校验] userId:{} 数据结果,uu898余额:{} uu898冻结金额:{} 分账总金额:{} 冻结金额:{}  余额1:{}+余额2:{} = {} 余额1冻结:{}+余额2冻结:{} ={}", request.getUserId(),
                userAssetsInfo.getMoney(), userAssetsInfo.getBlockMoney(), balance, frozenBalance, balance1, balance2, balance1.add(balance2), frozenBalance1, frozenBalance2, frozenBalance1.add(frozenBalance2)
        );
        log.info("[账户校验] userId:{} 数据结果,uu898求购总金额:{}  分账求购总金额:{} 充值1:{} 充值2:{} 转入1:{} 转入2:{}", request.getUserId(),
                userAssetsInfo.getPurchaseMoney(), purchaseBalanceRecharge1.add(purchaseBalanceRecharge2).add(purchaseBalanceTransfer1).add(purchaseBalanceTransfer2),
                purchaseBalanceRecharge1, purchaseBalanceRecharge2, purchaseBalanceTransfer1, purchaseBalanceTransfer2
        );

        updateAccountByType(accountAggregate, AccountTypeEnum.BALANCE, balance, frozenBalance);
        updateAccountByType(accountAggregate, AccountTypeEnum.BALANCE_1, balance1, frozenBalance1);
        updateAccountByType(accountAggregate, AccountTypeEnum.BALANCE_2, balance2, frozenBalance2);
        updateAccountByType(accountAggregate, AccountTypeEnum.PURCHASE_BALANCE, purchaseBalance, BigDecimal.ZERO);
        updateAccountByType(accountAggregate, AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1, purchaseBalanceRecharge1, BigDecimal.ZERO);
        updateAccountByType(accountAggregate, AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2, purchaseBalanceRecharge2, BigDecimal.ZERO);
        updateAccountByType(accountAggregate, AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1, purchaseBalanceTransfer1, BigDecimal.ZERO);
        updateAccountByType(accountAggregate, AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2, purchaseBalanceTransfer2, BigDecimal.ZERO);
        return true;
    }

    private void updateAccountByType(AccountAggregate accountAggregate, AccountTypeEnum balance, BigDecimal balance1, BigDecimal frozenBalance) {
        AccountInfoMember originAccountMember = accountAggregate.getAccountByType(balance);
        //修复账户问题
        userAccountGateway.updateAccount(originAccountMember, toTargetAccountMember(originAccountMember, balance1, frozenBalance));
    }

    private AccountInfoMember toTargetAccountMember(AccountInfoMember originAccountMember, BigDecimal balance, BigDecimal frozenBalance) {
        return AccountInfoMember.builder()
                .id(originAccountMember.getId())
                .userId(originAccountMember.getUserId())
                .accountType(originAccountMember.getAccountType())
                .lastAccountRecordId(originAccountMember.getLastAccountRecordId())
                .balance(balance)
                .frozenBalance(frozenBalance)
                .build();
    }

    /**
     * true 校验通过 false 校验不通过
     */
    private Boolean checkUserAccount(CheckAccountRequest request, UserAssetsInfoDTO userAssetsInfo) {
        if (null == userAssetsInfo) {
            log.warn("[账户校验]  userAssetsInfo userId:{} 账户不存在", request.getUserId());
            return true;
        }
        UserAccountInfoDTO userAccountInfoDTO = userAccountGateway.queryUserAccountInfoDTO(request.getUserId());
        if (null == userAccountInfoDTO) {
            log.warn("[账户校验] userAccountInfo userId:{} 账户不存在", request.getUserId());
            // 同步账户数据
            userAccountGateway.syncUserAccount(request.getUserId());
            log.info("[账户校验] syncUserAccount userId:{}", request.getUserId());
            return true;
        }

        BigDecimal balance = userAccountInfoDTO.getBalance();
        BigDecimal balance1 = userAccountInfoDTO.getBalance1();
        BigDecimal balance2 = userAccountInfoDTO.getBalance2();
        BigDecimal balanceBlock1 = userAccountInfoDTO.getBalanceBlock1();
        BigDecimal balanceBlock2 = userAccountInfoDTO.getBalanceBlock2();
        BigDecimal purchaseBalance = userAccountInfoDTO.getPurchaseBalance();
        BigDecimal purchaseBalanceRecharge1 = userAccountInfoDTO.getPurchaseBalanceRecharge1();
        BigDecimal purchaseBalanceRecharge2 = userAccountInfoDTO.getPurchaseBalanceRecharge2();
        BigDecimal purchaseBalanceTransfer1 = userAccountInfoDTO.getPurchaseBalanceTransfer1();
        BigDecimal purchaseBalanceTransfer2 = userAccountInfoDTO.getPurchaseBalanceTransfer2();

        BigDecimal money = userAssetsInfo.getMoney();
        BigDecimal purchaseMoney = userAssetsInfo.getPurchaseMoney();
        BigDecimal blockMoney = userAssetsInfo.getBlockMoney();
//        BigDecimal purchaseMoneyFromMoney = userAssetsInfo.getPurchaseMoneyFromMoney();

        boolean checkBalance1 = money.compareTo(balance) != Constant.CONSTANT_INTEGER_0;
        if (checkBalance1) {
            log.warn("[账户校验] userId:{} 账户余额与用户账户余额不一致 uu898余额:{} 账户余额:{}", request.getUserId(), money, balance);
        }
        boolean checkBalance2 = balance.compareTo(balance1.add(balance2)) != Constant.CONSTANT_INTEGER_0;
        if (checkBalance2) {
            log.warn("[账户校验] userId:{} 账户余额与余额1+余额2不一致 账户余额:{},余额1:{},余额2:{}", request.getUserId(), balance, balance1, balance2);
        }
        boolean checkBalance3 = purchaseMoney.compareTo(purchaseBalance) != Constant.CONSTANT_INTEGER_0;
        if (checkBalance3) {
            log.warn("[账户校验] userId:{} 账户求购余额与用户账户求购余额不一致 uu898求购余额:{},账户求购余额:{}", request.getUserId(), purchaseMoney, purchaseBalance);
        }
        boolean checkBalance4 = purchaseBalance.compareTo(purchaseBalanceRecharge1.add(purchaseBalanceRecharge2).add(purchaseBalanceTransfer1).add(purchaseBalanceTransfer2)) != Constant.CONSTANT_INTEGER_0;
        if (checkBalance4) {
            log.warn("[账户校验] userId:{} 账户求购余额与(求购充值1+求购充值2+求购转入1+求购转入2)不一致 求购余额:{},充值1:{},充值2:{},转入1:{},转入2:{}", request.getUserId(), purchaseBalance, purchaseBalanceRecharge1, purchaseBalanceRecharge2, purchaseBalanceTransfer1, purchaseBalanceTransfer2);
        }
        boolean checkBalance5 = blockMoney.compareTo(balanceBlock1.add(balanceBlock2)) != Constant.CONSTANT_INTEGER_0;
        if (checkBalance5) {
            log.warn("[账户校验] userId:{} 账户冻结余额与用户账户冻结余额不一致 uu898冻结余额:{} 账户冻结余额:{}", request.getUserId(), blockMoney, balanceBlock1.add(balanceBlock2));
        }
//        boolean checkBalance5 = purchaseMoneyFromMoney.compareTo(purchaseBalanceTransfer1.add(purchaseBalanceTransfer2)) != 0;
//        if (checkBalance5) {
//            log.warn("[获取用户账户信息] userId:{} 钱包转入的求购金额与账户求购转入不一致 uu898求购余额:{},转入1:{},转入2:{}", request.getUserId(), purchaseMoneyFromMoney, purchaseBalanceTransfer1, purchaseBalanceTransfer2);
//        }
        boolean checkBalance = checkBalance1 || checkBalance2 || checkBalance3 || checkBalance4 || checkBalance5;

        log.info("[账户校验] userId:{} 校验结果:{}", request.getUserId(), !checkBalance);
        if (checkBalance && Boolean.TRUE.equals(request.getSyncAccountFlag())) {
            //删除错误数据
            userAccountGateway.syncUserAccountUpdate(request.getUserId());
            if (!Boolean.FALSE.equals(request.getDeleteUserAssetsRecordFlag())) {
                userAccountRecordGateway.deleteByUserId(request.getUserId());
            }
            userAccountReconciliationRecordGateway.deleteByUserId(request.getUserId());
        }
        return !checkBalance;
    }

    private void syncUserAccount(Long userId) {
        userAccountGateway.syncUserAccount(userId);
    }


    @Override
    public void transferAccountMoney1ToMoney2() {
        log.info("[金额划拨开始] 开始执行");
        //获取需要转账的账户
        List<Long> transferAccountBalanceUserIdList = paymentClearParamsConfig.getTransferAccountBalanceUserIdList();
        //判断缓存是否发送过
        String dateKey = localDate2Str(LocalDateTime.now().minusDays(ClearConstants.CONSTANT_INTEGER_1));
        String formatMD = DateTimeUtil.dayTimeFormat2(LocalDateTime.now().minusDays(ClearConstants.CONSTANT_INTEGER_1));
        for (Long userId : transferAccountBalanceUserIdList) {
            boolean hasKey = ClearRedisUtils.hasKey(RedisKey.MONEY1_TO_MONEY2_CACHE + dateKey + ":" + userId);
            if (hasKey && paymentClearParamsConfig.getTransferAccountBalanceUserIdListCheckFlag()) {
                //判断有么有发送信息
                boolean hasMsgKey = ClearRedisUtils.hasKey(RedisKey.MONEY1_TO_MONEY2_MSG_CACHE + dateKey + ":" + userId);
                if (!hasMsgKey) {
                    Object balance1Str = ClearRedisUtils.getValue(RedisKey.MONEY1_TO_MONEY2_CACHE + dateKey + ":" + userId);
                    if (ObjectUtils.isNotEmpty(balance1Str)) {
                        //发消息
                        dingTalkService.sendDingTalkMessage(DingTalkConstants.PLATFORM_FUND_IN_FORMATION_01, String.format("%s 账号:%d 划拨%s元到余额2中", formatMD, userId, balance1Str));
                        ClearRedisUtils.setKey(String.format("%s%s:%d", RedisKey.MONEY1_TO_MONEY2_MSG_CACHE, dateKey, userId), userId, ClearConstants.CONSTANT_LONG_12, TimeUnit.HOURS);
                        log.info("[金额划拨开始] 用户:{} 发消息 补发", userId);
                    }
                } else {
                    log.info("[金额划拨开始] 用户:{} 已经处理过了并发送过消息", userId);
                }
            } else {
                //转账 && 发消息
                AccountAggregate accountAggregate = userAccountGateway.getAccountAggregate(userId);
                if (accountAggregate == null) {
                    log.warn("[金额划拨开始] 账户数据为空 userId:{}", userId);
                    continue;
                }
                BigDecimal balance1 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1).getBalance().setScale(ClearConstants.CONSTANT_INTEGER_2, RoundingMode.DOWN);
                if (balance1.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("[金额划拨开始] 用户:{} 余额1为0:", userId);
                    continue;
                }
                List<UpdateAccountBalanceDTO> dataAccountChangeDTOList = new ArrayList<>();
                //余额1 -
                createTransferAccountBalance(Boolean.FALSE, balance1.negate(), accountAggregate, AccountTypeEnum.BALANCE_1, dataAccountChangeDTOList);
                //余额2 +
                createTransferAccountBalance(Boolean.FALSE, balance1, accountAggregate, AccountTypeEnum.BALANCE_2, dataAccountChangeDTOList);

                try {
                    userAccountGateway.updateAccountBalanceList(dataAccountChangeDTOList);
                } catch (Exception e) {
                    log.error("[金额划拨开始] 用户:{} 特殊账户转换余额失败:{}", userId, balance1);
                    throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR, "特殊账户转换余额失败");
                }
                ClearRedisUtils.setKey(RedisKey.MONEY1_TO_MONEY2_CACHE + dateKey + ":" + userId, String.valueOf(balance1), 12L, TimeUnit.HOURS);
                log.info("[金额划拨开始] 用户:{} 划拨成功:{}", userId, balance1);
                dingTalkService.sendDingTalkMessage("userBalanceAssetsCheck", formatMD + " 账号:" + userId + " 划拨" + balance1 + "元到余额2中");
                ClearRedisUtils.setKey(RedisKey.MONEY1_TO_MONEY2_MSG_CACHE + dateKey + ":" + userId, userId, 12L, TimeUnit.HOURS);
                log.info("[金额划拨开始] 用户:{} 发消息完成", userId);
            }
        }
    }

    /**
     * 补全用户账户
     *
     * @param request 请求
     */
    @Override
    public void completionUserAccount(OpenAccountRequest request) {
        // 获取已存在的账户类型
        Set<Integer> existsAccountType = userAccountGateway.getAccountInfoMember(request.getUserId())
                .stream()
                .map(AccountInfoMember::getAccountType)
                .collect(Collectors.toSet());
        // 全部的账户信息
        List<AccountInfoMember> allAccountInfoMembers = AccountTemplate.accountTemplate(request.getUserId());
        //
        List<AccountInfoMember> needAddAccountInfoMembers = new ArrayList<>(allAccountInfoMembers.size());
        //
        allAccountInfoMembers.forEach(accountInfoMember -> {
            // 判断当前类型是否不存在
            if (!existsAccountType.contains(accountInfoMember.getAccountType())) {
                // 设置账户编号
                accountInfoMember.setUserAccountNo(idWorkService.getNextIdLeafKey());
                // 添加到待添加列表
                needAddAccountInfoMembers.add(accountInfoMember);
            }
        });
        //
        if (CollectionUtils.isEmpty(needAddAccountInfoMembers)) {
            log.info("[补全用户账户] 用户:{} 已存在所有账户", request.getUserId());
            return;
        }
        // 补全用户
        userAccountGateway.completionUserAccount(needAddAccountInfoMembers);
        //
        log.info("[补全用户账户] 用户:{} 补全账户成功:{}", request.getUserId(), needAddAccountInfoMembers);
    }

    static AccountInfoMember originInitTargetAccountMember(AccountInfoMember originAccountMember, BigDecimal balance) {
        return AccountInfoMember.builder().id(originAccountMember.getId()).userId(originAccountMember.getUserId()).accountType(originAccountMember.getAccountType()).userAccountNo(originAccountMember.getUserAccountNo()).balance(balance).frozenBalance(originAccountMember.getFrozenBalance()).lastAccountRecordId(originAccountMember.getLastAccountRecordId()).ext(originAccountMember.getExt()).build();
    }

    @Override
    public void initUserWithdrawMoney(OpenAccountRequest request) {
        List<Integer> accountTypeCodeList = List.of(AccountTypeEnum.BALANCE_WITHDRAW_1.getCode(), AccountTypeEnum.BALANCE_WITHDRAW_121.getCode(), AccountTypeEnum.BALANCE_WITHDRAW_2.getCode());
        AccountAggregate accountAggregate = userAccountGateway.getAccountAggregatesMapByType(request.getUserId(), accountTypeCodeList);
        if (null == accountAggregate || null == accountAggregate.getAccountInfoMemberMap() || accountAggregate.getAccountInfoMemberMap().isEmpty()) {
            //补全账户
            this.completionUserAccount(request);
            accountAggregate = userAccountGateway.getAccountAggregatesMapByType(request.getUserId(), accountTypeCodeList);
        }
        //先查询用户uu898账户消息
        UserAssetsInfoDTO userAssetsInfoDTO = userAssetsInfoGateway.getUserAssetsInfo(request.getUserId());
        //子账户
        Map<Integer, UU898UserSubAccountDTO> uu898UserSubAccountDTOMap = uu898UserSubAccountGateway.getUserSubAccountMap(request.getUserId());
        //总金额
        BigDecimal totalAmount = null == userAssetsInfoDTO ? BigDecimal.ZERO : userAssetsInfoDTO.getMoney();

        //仅可交易金额
        BigDecimal tradeAmount = uu898UserSubAccountDTOMap.containsKey(UU898UserSubAccountType.TRADE.getType()) ? uu898UserSubAccountDTOMap.get(UU898UserSubAccountType.TRADE.getType()).getBalance() : BigDecimal.ZERO;

        //仅可提现金额
        BigDecimal withdrawAmount = totalAmount.subtract(tradeAmount).compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : totalAmount.subtract(tradeAmount);
        //仅可提现金额121
        BigDecimal withdrawAmount121 = accountAggregate.getAccountInfoMemberMap().containsKey(AccountTypeEnum.BALANCE_WITHDRAW_121) ? accountAggregate.getAccountInfoMemberMap().get(AccountTypeEnum.BALANCE_WITHDRAW_121).getBalance() : BigDecimal.ZERO;

        //获取一下最小的资金流水子明细资金Id
        Long lastAccountRecordId = userSubAccountRecordGateway.getMinAccountRecordId(request.getUserId());
        AccountInfoMember originAccountMember = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1);

        AccountInfoMember targetAccountMember;

        if (null == lastAccountRecordId || lastAccountRecordId <= 0) {
            //使用用户当前的余额1资金复写账户余额1可提现金额
            targetAccountMember = originInitTargetAccountMember(originAccountMember, originAccountMember.getBalance());
        } else {
            //查询一下 //分账的资金明细中数据变化
            List<UserAccountRecordMember> userAccountRecordByUserIdAndUserAssetsRecordId = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(request.getUserId(), lastAccountRecordId, null);
            if (null == userAccountRecordByUserIdAndUserAssetsRecordId) {
                targetAccountMember = originInitTargetAccountMember(originAccountMember, originAccountMember.getBalance());
            } else {
                UserAccountRecordMember userAccountRecordMember1 = userAccountRecordByUserIdAndUserAssetsRecordId.stream().filter(userAccountRecordMember -> userAccountRecordMember.getAccountType().equals(AccountTypeEnum.BALANCE_1.getCode())).findFirst().orElse(null);
                if (null == userAccountRecordMember1) {
                    targetAccountMember = originInitTargetAccountMember(originAccountMember, originAccountMember.getBalance());
                } else {
                    targetAccountMember = originInitTargetAccountMember(originAccountMember, userAccountRecordMember1.getBalanceBefore().add(withdrawAmount121));
                }
            }
            targetAccountMember = originInitTargetAccountMember(originAccountMember, withdrawAmount);
        }
        if (null == targetAccountMember) {
            targetAccountMember = originInitTargetAccountMember(originAccountMember, withdrawAmount);
        }
        if (targetAccountMember.getBalance().compareTo(withdrawAmount) > 0) {
            targetAccountMember.setBalance(withdrawAmount);
        }
        userAccountGateway.updateAccount(originAccountMember, targetAccountMember);

    }

    /**
     * yyyyMMdd
     */
    static final String DATE_FORMAT_YMD_CODE = "yyyyMMdd";

    static String localDate2Str(LocalDateTime fromDate) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT_YMD_CODE);
        return fromDate.format(df);
    }


    private static void createTransferAccountBalance(boolean isAccountFrozen, BigDecimal balanceChange, AccountAggregate accountAggregate, AccountTypeEnum accountTypeEnum, List<UpdateAccountBalanceDTO> dataAccountChangeDTOList) {
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(accountTypeEnum);
        UpdateAccountBalanceDTO dto = new UpdateAccountBalanceDTO();
        dto.setBalanceChange(BigDecimal.ZERO);
        dto.setFrozenBalanceChange(BigDecimal.ZERO);
        if (isAccountFrozen) {
            dto.setFrozenBalanceChange(balanceChange);
        } else {
            dto.setBalanceChange(balanceChange);
        }
        dto.setOriginalBalance(accountInfoMember.getBalance());
        dto.setOriginalFrozenBalance(accountInfoMember.getFrozenBalance());

        dto.calculateBalanceChangeAfter();
        dto.calculateFrozenBalanceChangeAfter();

        dto.setUserAccountNo(accountInfoMember.getUserAccountNo());
        dto.setUserId(accountInfoMember.getUserId());
        dto.setAccountType(accountTypeEnum.getCode());
        dto.setAccountId(accountInfoMember.getId());
        dataAccountChangeDTOList.add(dto);


    }

}
