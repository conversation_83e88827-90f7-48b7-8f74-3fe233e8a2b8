<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.youpin.clear</groupId>
        <artifactId>youpin-payment-clear-core-service</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>youpin-payment-clear-core-service-app</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>youpin-payment-clear-core-service-app</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.youpin.clear</groupId>
            <artifactId>youpin-payment-clear-core-service-infrastructure</artifactId>
        </dependency>
        <!-- JSR 303 Validation -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.glassfish</groupId>-->
        <!--            <artifactId>jakarta.el</artifactId>-->
        <!--        </dependency>-->

        <!-- JSR 303 Validation End-->
        <dependency>
            <groupId>youpin-commons</groupId>
            <artifactId>youpin-commons-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>
</project>
