package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.UU898UserAssetsRecordService;
import com.youpin.clear.client.request.FinancialStatementJobRequest;
import com.youpin.clear.client.response.FinancialStatementJobResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资金数据对账
 */
@RestController
@RequestMapping("/job/clear/financial-statement")
public class FinancialStatementController {

    @Autowired
    UU898UserAssetsRecordService uu898UserAssetsRecordService;

    @PostMapping(value = "/user/assets/record/date/query/count")
    public Result<FinancialStatementJobResponse> countUserAssetsRecordByAddTime(@RequestBody FinancialStatementJobRequest request) {
        return Result.ok(uu898UserAssetsRecordService.countByAddTime(request));
    }

    @PostMapping(value = "/user/assets/record/date/query/page")
    public Result<FinancialStatementJobResponse> selectUserAssetsRecordByAddTimePage(@RequestBody FinancialStatementJobRequest request) {
        return Result.ok(uu898UserAssetsRecordService.selectByAddTimePage(request));
    }

    @PostMapping(value = "/user/assets/record/id/interval/query/page")
    public Result<FinancialStatementJobResponse> selectUserAssetsRecordByIdInterval(@RequestBody FinancialStatementJobRequest request) {
        return Result.ok(uu898UserAssetsRecordService.selectByIdInterval(request));
    }

    @PostMapping(value = "/user/assets/record/max/id/query")
    public Result<FinancialStatementJobResponse> selectUserAssetsRecordMaxId() {
        return Result.ok(uu898UserAssetsRecordService.selectMaxId());
    }

    @PostMapping(value = "/user/assets/record/date/handle/id")
    public Result<Void> handleUserAssetsRecordId(@RequestBody FinancialStatementJobRequest request) {
        uu898UserAssetsRecordService.handleUserAssetsRecordId(request);
        return Result.ok();
    }


}
