package com.youpin.clear.adapter.service;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.AdjustmentApplyService;
import com.youpin.clear.client.request.*;
import com.youpin.clear.client.response.AdjustApplyListResponse;
import com.youpin.clear.client.response.AdjustDetailResponse;
import com.youpin.clear.client.response.AdjustmentApplyConfigResponse;
import com.youpin.clear.client.response.BatchAuditDetailResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 调账相关接口
 *
 * <AUTHOR>
 * @eo.api-type http
 * @eo.groupName 调账相关接口
 * @eo.path /adjust
 */
@RestController
@RequestMapping("/adjust")
public class AdjustController {

    @Resource
    private AdjustmentApplyService adjustmentApplyService;

    /**
     * 查询调账申请列表
     *
     * @param request
     * @return
     * @eo.name 查询调账申请列表
     * @eo.url /query/list
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/query/list")
    public Result<AdjustApplyListResponse> queryList(@Valid @RequestBody AdjustApplyListQueryRequest request) {
        AdjustApplyListResponse response = adjustmentApplyService.queryList(request);
        return Result.ok(response);
    }

    /**
     * 提交申请
     *
     * @param request
     * @return
     * @eo.name 提交申请
     * @eo.url /submitApply
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/submitApply")
    public Result<Long> submitApply(@RequestBody AdjustApplyRequest request) {
        Long applyId = adjustmentApplyService.submitApply(request);
        return Result.ok(applyId);
    }


    /**
     * 发送短信验证码
     *
     * @param request
     * @return
     * @eo.name 发送短信验证码
     * @eo.url /send/verifyCode
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/send/verifyCode")
    public Result<Void> sendVerifyCode(@RequestBody ApplySendVerifyCodeRequest request) {
        adjustmentApplyService.sendVerifyCode(request);
        return Result.ok();
    }


    /**
     * 调账详情查询
     *
     * @param applyId
     * @return
     * @eo.name 调账详情查询
     * @eo.url /query/detail
     * @eo.method get
     * @eo.request-type formdata
     */
    @GetMapping("/query/detail")
    public Result<AdjustDetailResponse> queryDetail(@RequestParam("applyId") Long applyId) {
        AdjustDetailResponse response = adjustmentApplyService.queryDetail(applyId);
        return Result.ok(response);
    }

    /**
     * 调账审核
     *
     * @param request
     * @return
     * @eo.name 调账审核
     * @eo.url /audit
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/audit")
    public Result<Void> applyAudit(@RequestBody AdjustApplyAuditRequest request) {
        adjustmentApplyService.applyAudit(request);
        return Result.ok();
    }

    /**
     * 查询类型配置信息
     *
     * @param request
     * @return
     * @eo.name 查询类型配置信息
     * @eo.url /query/typeConfig
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/query/typeConfig")
    public Result<AdjustmentApplyConfigResponse> queryApplyTypeConfig() {
        AdjustmentApplyConfigResponse response = adjustmentApplyService.queryApplyTypeConfig();
        return Result.ok(response);
    }


    @GetMapping("/handle")
    public Result<Void> handle() {
        adjustmentApplyService.adjustmentHandle();
        return Result.ok();
    }


    @PostMapping("/import")
    public Result<Void> adjustApplyImport(@RequestBody @Valid AdjustBatchImportRequest request) {
        adjustmentApplyService.adjustApplyImport(request);
        return Result.ok();
    }


    @PostMapping("/revoke")
    public Result<Void> applyRevoke(@RequestBody @Valid ApplyRevokeRequest request) {
        adjustmentApplyService.applyRevoke(request);
        return Result.ok();
    }

    @PostMapping("/batch/audit/detail")
    public Result<BatchAuditDetailResponse> batchAuditDetail(@RequestBody @Valid ApplyBatchQueryRequest request) {
        BatchAuditDetailResponse response = adjustmentApplyService.batchAuditDetail(request);
        return Result.ok(response);
    }

    @PostMapping("/batch/audit")
    public Result<Void> batchApplyAudit(@RequestBody @Valid ApplyBatchAuditRequest request) {
        adjustmentApplyService.batchApplyAudit(request);
        return Result.ok();
    }


    @PostMapping("/update/status")
    public Result<Void> updateStatus(@RequestBody AdjustUpdateStatusRequest request) {
        adjustmentApplyService.updateStatus(request);
        return Result.ok();
    }


    @PostMapping("/retry")
    public Result<Boolean> adjustRetry(@RequestBody @Valid AdjustRetryRequest request) {
        Boolean retry = adjustmentApplyService.retry(request);
        return Result.ok(retry);
    }

}
