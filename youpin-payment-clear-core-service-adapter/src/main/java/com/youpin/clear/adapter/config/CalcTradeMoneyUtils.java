package com.youpin.clear.adapter.config;

import com.alibaba.fastjson.JSONObject;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtils {

    public static void main(String[] args) {

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        RefundResult result = calcRefundTradeMoney(firstTransferRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("firstTransferRecords1：{}", JSONObject.toJSONString(result));

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        RefundResult result2 = calcRefundTradeMoney(firstTransferRecords2, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        log.info("firstTransferRecords2：{}", JSONObject.toJSONString(result2));


        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        RefundResult refundResult3 = calcRefundTradeMoney(firstTransferRecords3, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        ;
        ;
        log.info("firstTransferRecords3：{}", JSONObject.toJSONString(refundResult3));
    }

    /**
     * 计算退款仅可交易金额及退款明细
     * 核心逻辑：按LIFO（后进先出）原则分配退款，返回退款明细
     *
     * @param subAccountFlowRecordList 子账户流水记录列表
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @return 退款结果，包含总退款金额和明细
     */
    public static RefundResult calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList, BigDecimal currentTransferOutAmount, UserAssetsTypeEnum inType, UserAssetsTypeEnum outType) {
        if (currentTransferOutAmount == null || currentTransferOutAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return new RefundResult(BigDecimal.ZERO, new ArrayList<>());
        }

        if (subAccountFlowRecordList == null || subAccountFlowRecordList.isEmpty()) {
            return new RefundResult(currentTransferOutAmount, new ArrayList<>());
        }

        // 按时间排序流水记录
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());

        // 构建转入订单的剩余可退款金额映射（按时间倒序，LIFO原则）
        Map<String, BigDecimal> transferInRemaining = new LinkedHashMap<>();
        List<String> transferInOrderList = new ArrayList<>(); // 保存转入订单的顺序

        // 先处理所有转入记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (inType.getTypeId().equals(record.getJournalType()) && record.getBalanceChange().compareTo(BigDecimal.ZERO) > 0) {
                transferInRemaining.put(record.getOrderNo(), record.getBalanceChange());
                transferInOrderList.add(record.getOrderNo());
            }
        }

        // 再处理历史转出记录，按FIFO原则扣减转入记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (outType.getTypeId().equals(record.getJournalType()) && record.getBalanceChange().compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal transferOutAmount = record.getBalanceChange().abs();
                deductFromTransferIn(transferInRemaining, transferInOrderList, transferOutAmount);
            }
        }

        // 按LIFO原则分配本次退款（从最新的转入订单开始退款）
        List<RefundDetail> refundDetails = new ArrayList<>();
        BigDecimal remainingRefund = currentTransferOutAmount;

        // 倒序遍历转入订单（LIFO）
        for (int i = transferInOrderList.size() - 1; i >= 0 && remainingRefund.compareTo(BigDecimal.ZERO) > 0; i--) {
            // 获取订单号
            String orderNo = transferInOrderList.get(i);
            // 
            BigDecimal availableAmount = transferInRemaining.get(orderNo);

            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal refundFromThisOrder = remainingRefund.min(availableAmount);
                refundDetails.add(new RefundDetail(orderNo, refundFromThisOrder));
                remainingRefund = remainingRefund.subtract(refundFromThisOrder);
            }
        }

        // 计算总退款金额
        BigDecimal totalRefundAmount = currentTransferOutAmount.subtract(remainingRefund);

        // 
        return new RefundResult(totalRefundAmount, refundDetails);
    }

    /**
     * 从转入记录中按FIFO原则扣减金额
     */
    private static void deductFromTransferIn(Map<String, BigDecimal> transferInRemaining,
                                             List<String> transferInOrderList,
                                             BigDecimal deductAmount) {
        BigDecimal remainingDeduct = deductAmount;

        for (String orderNo : transferInOrderList) {
            if (remainingDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal availableAmount = transferInRemaining.get(orderNo);
            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal deductFromThis = remainingDeduct.min(availableAmount);
                transferInRemaining.put(orderNo, availableAmount.subtract(deductFromThis));
                remainingDeduct = remainingDeduct.subtract(deductFromThis);
            }
        }
    }

    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

    /**
     * 退款结果类
     */
    @Data
    public static class RefundResult {
        private final BigDecimal totalRefundAmount;  // 总退款金额
        private final List<RefundDetail> refundDetails; // 退款明细

        public RefundResult(BigDecimal totalRefundAmount, List<RefundDetail> refundDetails) {
            this.totalRefundAmount = totalRefundAmount;
            this.refundDetails = refundDetails;
        }
    }

    /**
     * 退款明细类
     */
    @Data
    public static class RefundDetail {
        private final String transferInOrderNo; // 转入订单号
        private final BigDecimal refundAmount;   // 从该订单退款的金额

        public RefundDetail(String transferInOrderNo, BigDecimal refundAmount) {
            this.transferInOrderNo = transferInOrderNo;
            this.refundAmount = refundAmount;
        }
    }

}
