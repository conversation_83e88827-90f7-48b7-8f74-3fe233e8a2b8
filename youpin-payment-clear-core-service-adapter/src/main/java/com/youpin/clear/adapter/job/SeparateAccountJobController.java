package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.SecondSplitBillItemMemberService;
import com.youpin.clear.client.request.SeparateAccountJobRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 分账job
 */
@RestController
@RequestMapping("/job/clear/separate-account")
public class SeparateAccountJobController {


    @Autowired
    SecondSplitBillItemMemberService secondSplitBillItemMemberService;

    /**
     * 获取用户的分账资金ID 最小值
     */
    @PostMapping(value = "/minUserAssetsRecordId")
    public Result<Long> minUserAssetsRecordId(@RequestBody SeparateAccountJobRequest request) {
        return Result.ok(secondSplitBillItemMemberService.minUserAssetsRecordId(request));
    }


    @PostMapping(value = "/secondSplit")
    public Result<Void> secondSplitBillItemMember(@RequestBody SeparateAccountJobRequest request) {
        secondSplitBillItemMemberService.secondSplitBillItemMember(request);
        return Result.ok();
    }


}