package com.youpin.clear.adapter.service;

import com.alibaba.fastjson.JSONObject;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class FundingUtilsV4 {

    public static void main(String[] args) {

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        Map<String, BigDecimal> result = fundHedging(firstTransferRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("firstTransferRecords1：{}", JSONObject.toJSONString(result));

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        Map<String, BigDecimal> result2 = fundHedging(firstTransferRecords2, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        log.info("firstTransferRecords2：{}", JSONObject.toJSONString(result2));
//

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        Map<String, BigDecimal> refundResult3 = fundHedging(firstTransferRecords3, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        ;
        ;
        log.info("firstTransferRecords3：{}", JSONObject.toJSONString(refundResult3));
    }

    /**
     * 资金对冲（优化版）
     *
     * <p><strong>算法逻辑：</strong></p>
     * <ul>
     *   <li>1. 按时间顺序处理所有流水记录</li>
     *   <li>2. 每次转出都按LIFO（后进先出）原则从最新的转入订单开始扣减</li>
     *   <li>3. 本次转出也按LIFO原则分配退款明细</li>
     *   <li>4. 返回详细的退款明细Map</li>
     * </ul>
     *
     * @param subAccountFlowRecordList 子账户流水记录列表
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @param inType                   转入类型
     * @param outType                  转出类型
     * @return 退款明细Map（key:订单号, value:退款金额）
     */
    public static Map<String, BigDecimal> fundHedging(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList,
                                                     BigDecimal currentTransferOutAmount,
                                                     UserAssetsTypeEnum inType,
                                                     UserAssetsTypeEnum outType) {
        // 1. 参数校验
        if (!isValidParameters(subAccountFlowRecordList, currentTransferOutAmount, inType, outType)) {
            return Collections.emptyMap();
        }

        // 2. 按时间正序排序（从早到晚）
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());

        // 3. 构建转入订单映射表（保持时间顺序）
        Map<String, BigDecimal> transferInRemaining = new LinkedHashMap<>();
        List<String> transferInOrderList = new ArrayList<>();

        // 4. 按时间顺序处理所有流水记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (inType.getTypeId().equals(record.getJournalType()) &&
                record.getBalanceChange().compareTo(BigDecimal.ZERO) > 0) {
                // 转入记录：添加到映射表
                transferInRemaining.put(record.getOrderNo(), record.getBalanceChange());
                transferInOrderList.add(record.getOrderNo());
                log.debug("转入记录：订单 {} 金额 {} 元", record.getOrderNo(), record.getBalanceChange());

            } else if (outType.getTypeId().equals(record.getJournalType()) &&
                       record.getBalanceChange().compareTo(BigDecimal.ZERO) < 0) {
                // 转出记录：按LIFO原则扣减转入记录
                BigDecimal transferOutAmount = record.getBalanceChange().abs();
                log.debug("处理历史转出：订单 {} 金额 {} 元", record.getOrderNo(), transferOutAmount);
                deductFromTransferInByLIFO(transferInRemaining, transferInOrderList, transferOutAmount);
            }
        }

        // 5. 按LIFO原则分配本次退款
        return allocateRefundByLIFO(transferInRemaining, transferInOrderList, currentTransferOutAmount);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 参数校验
     */
    private static boolean isValidParameters(List<UU898UserSubAccountFlowRecordDTO> records,
                                           BigDecimal amount,
                                           UserAssetsTypeEnum inType,
                                           UserAssetsTypeEnum outType) {
        if (records == null || records.isEmpty()) {
            log.warn("流水记录列表为空");
            return false;
        }

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("转出金额无效：{}", amount);
            return false;
        }

        if (inType == null || outType == null) {
            log.warn("转入或转出类型为空");
            return false;
        }

        return true;
    }

    /**
     * 从转入记录中按LIFO原则扣减金额
     * 每次转出都从最新的转入订单开始扣减，按时间倒序处理
     */
    private static void deductFromTransferInByLIFO(Map<String, BigDecimal> transferInRemaining,
                                                   List<String> transferInOrderList,
                                                   BigDecimal deductAmount) {
        BigDecimal remainingDeduct = deductAmount;

        log.debug("开始LIFO扣减，扣减金额：{}，转入订单顺序：{}", deductAmount, transferInOrderList);

        // 按时间倒序（LIFO）扣减转入记录，从最新的转入订单开始
        for (int i = transferInOrderList.size() - 1; i >= 0 && remainingDeduct.compareTo(BigDecimal.ZERO) > 0; i--) {
            String orderNo = transferInOrderList.get(i);
            BigDecimal availableAmount = transferInRemaining.get(orderNo);

            if (availableAmount != null && availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal deductFromThis = remainingDeduct.min(availableAmount);
                BigDecimal newAvailableAmount = availableAmount.subtract(deductFromThis);

                transferInRemaining.put(orderNo, newAvailableAmount);
                remainingDeduct = remainingDeduct.subtract(deductFromThis);

                log.debug("LIFO扣减：从订单 {} 扣减 {} 元，扣减前：{} 元，扣减后：{} 元，剩余待扣：{} 元",
                        orderNo, deductFromThis, availableAmount, newAvailableAmount, remainingDeduct);
            }
        }

        if (remainingDeduct.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("LIFO扣减完成，但仍有 {} 元未能扣减（转入金额不足）", remainingDeduct);
        }
    }

    /**
     * 按LIFO原则分配退款明细
     */
    private static Map<String, BigDecimal> allocateRefundByLIFO(Map<String, BigDecimal> transferInRemaining,
                                                               List<String> transferInOrderList,
                                                               BigDecimal currentTransferOutAmount) {
        Map<String, BigDecimal> refundMap = new LinkedHashMap<>();
        BigDecimal remainingRefund = currentTransferOutAmount;

        log.debug("开始分配本次退款 {} 元，按LIFO原则", currentTransferOutAmount);

        // 倒序遍历转入订单（LIFO原则）
        for (int i = transferInOrderList.size() - 1; i >= 0 && remainingRefund.compareTo(BigDecimal.ZERO) > 0; i--) {
            String orderNo = transferInOrderList.get(i);
            BigDecimal availableAmount = transferInRemaining.get(orderNo);

            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal refundFromThisOrder = remainingRefund.min(availableAmount);
                refundMap.put(orderNo, refundFromThisOrder);
                remainingRefund = remainingRefund.subtract(refundFromThisOrder);

                log.debug("从订单 {} 退款 {} 元，该订单剩余可退 {} 元，还需退款 {} 元",
                        orderNo, refundFromThisOrder, availableAmount, remainingRefund);
            }
        }

        BigDecimal totalRefundAmount = currentTransferOutAmount.subtract(remainingRefund);
        log.info("退款分配完成，总退款：{} 元，明细：{}", totalRefundAmount, refundMap);

        return refundMap;
    }


    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

    /**
     * 验证优化后的资金对冲逻辑
     */
    public static void validateOptimizedFundHedging() {
        log.info("=== 验证优化后的资金对冲逻辑 ===");

        // 第一次转出测试
        log.info("\n--- 第一次转出3元测试 ---");
        List<UU898UserSubAccountFlowRecordDTO> firstRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
        );

        Map<String, BigDecimal> firstResult = fundHedging(firstRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("第一次转出结果：{}", JSONObject.toJSONString(firstResult));
        log.info("预期：从订单10退1元，订单9退1元，订单8退1元");

        // 第二次转出测试
        log.info("\n--- 第二次转出5元测试 ---");
        List<UU898UserSubAccountFlowRecordDTO> secondRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
        );

        Map<String, BigDecimal> secondResult = fundHedging(secondRecords, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("第二次转出结果：{}", JSONObject.toJSONString(secondResult));
        log.info("预期：从订单11退1元，订单8退2元，订单7退1元，订单6退1元");

        // 第三次转出测试
        log.info("\n--- 第三次转出5元测试 ---");
        List<UU898UserSubAccountFlowRecordDTO> thirdRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
        );

        Map<String, BigDecimal> thirdResult = fundHedging(thirdRecords, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("第三次转出结果：{}", JSONObject.toJSONString(thirdResult));
        log.info("预期：从订单12退3元，剩余2元无法退款");

        log.info("=== 验证完成 ===");
    }

}
