package com.youpin.clear.adapter.service;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.PolarToClearAssetsRecordService;
import com.youpin.clear.app.service.SeparateAccountV2Service;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/service/test")
public class TestController {
    @Autowired
    PolarToClearAssetsRecordService polarToClearAssetsRecordService;

    @Autowired
    SeparateAccountV2Service separateAccountV2Service;


    /**
     * 关闭MQ消费
     */
    @PostMapping(value = "/sendTransactionServiceFeeOperationRecord")
    public Result<Void> sendTransactionServiceFeeOperationRecord(@Valid @RequestBody List<UserAssetsRecordMessage> requestList) {
        List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList = UserAssetsRecordConvertor.MAPPER.toClearUserAssetsRecordDTOList(requestList);
        polarToClearAssetsRecordService.sendTransactionServiceFeeOperationRecord(userAssetsRecordDTOList);
        return Result.ok();
    }


    /**
     * 仅提现分账逻辑入口
     */
    @PostMapping(value = "/handleMoneyOnlyWithdraw")
    public Result<Void> handleMoneyOnlyWithdraw(@Valid @RequestBody ClearUserAssetsRecordDTO request) {
        separateAccountV2Service.handleMoneyOnlyWithdraw(List.of(request));
        return Result.ok();
    }


    /**
     * 仅提现分账逻辑入口-批量
     */
    @PostMapping(value = "/handleMoneyOnlyWithdraw/list")
    public Result<Void> handleMoneyOnlyWithdraw(@Valid @RequestBody List<ClearUserAssetsRecordDTO> requestList) {
        separateAccountV2Service.handleMoneyOnlyWithdraw(requestList);
        return Result.ok();
    }


}
