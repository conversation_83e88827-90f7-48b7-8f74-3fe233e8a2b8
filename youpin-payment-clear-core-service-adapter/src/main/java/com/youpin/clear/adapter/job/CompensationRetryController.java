package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.CompensationRecordService;
import com.youpin.clear.client.request.CompensationRecordDeleteRequest;
import com.youpin.clear.client.request.CompensationRecordRetryJobRequest;
import com.youpin.clear.client.response.CompensationRecordRetryJobResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/job/clear/compensation-order")
public class CompensationRetryController {


    @Autowired
    CompensationRecordService compensationRecordService;

    /**
     * 统计总数
     */
    @PostMapping(value = "/count")
    public Result<CompensationRecordRetryJobResponse> countExceptionRetryJob(@RequestBody CompensationRecordRetryJobRequest request) {
        return Result.ok(compensationRecordService.countExceptionRetryJob(request));
    }

    /**
     * 分页查询
     */
    @PostMapping(value = "/select")
    public Result<CompensationRecordRetryJobResponse> selectExceptionRetryJob(@RequestBody CompensationRecordRetryJobRequest request) {
        return Result.ok(compensationRecordService.selectExceptionRetryJob(request));
    }


    /**
     * 处理
     */
    @PostMapping(value = "/handle")
    public Result<Void> handle(@RequestBody CompensationRecordRetryJobRequest request) {
        compensationRecordService.handle(request);
        return Result.ok();
    }

    /**
     * 清理数据
     */
    @PostMapping(value = "/delete")
    public Result<Void> delete(@RequestBody CompensationRecordDeleteRequest request) {
        compensationRecordService.deleteCompensationRecord(request);
        return Result.ok();
    }

    /**
     * 清理MQ重复数据
     */
    @PostMapping(value = "/delete-mq")
    public Result<Void> deleteMq(@RequestBody CompensationRecordDeleteRequest request) {
        compensationRecordService.deleteMqCompensationRecord(request);
        return Result.ok();
    }


}
