package com.youpin.clear.adapter.service;


import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.FinancialTransactionV2Service;
import com.youpin.clear.client.api.FinancialTransactionV2Feign;
import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.client.response.financial.SettleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 交易
 */
@Slf4j
@RestController
public class FinancialTransactionV2Controller implements FinancialTransactionV2Feign {


    @Autowired
    FinancialTransactionV2Service financialTransactionV2Service;

    /**
     * 支付
     */
    @PostMapping("/service/financial/transaction/v2/pay")
    @Override
    public Result<Void> pay(@Valid @RequestBody PayFinancialRequest request) {
        log.info("financial:pay:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionV2Service.pay(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg());
    }

    /**
     * 退款
     */
    @PostMapping("/service/financial/transaction/v2/refund")
    public Result<Void> refund(@Valid @RequestBody RefundFinancialRequest request) {
        log.info("financial:refund:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionV2Service.refund(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg());
    }

    /**
     * 结算
     */
    @PostMapping("/service/financial/transaction/v2/settlement")
    public Result<SettleResponse> settlement(@Valid @RequestBody SettlementFinancialRequest request) {
        log.info("financial:settlement:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionV2Service.settlement(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg(),financialResponse.getSettleResponse());
    }

    /**
     * 特殊结算
     */
    @PostMapping("/service/financial/transaction/v2/specialSettlement")
    public Result<SettleResponse> specialSettlement(@Valid @RequestBody SettlementFinancialRequest request) {
        log.info("financial:specialSettlement:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionV2Service.specialSettlement(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg(),financialResponse.getSettleResponse());
    }


}
