package com.youpin.clear.adapter.config;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class DiscardWithLoggingPolicy implements RejectedExecutionHandler {

	@Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        log.error("error_message 异步队列超过最大限制");
    }

}
