package com.youpin.clear.adapter.service;

import com.alibaba.fastjson.JSONObject;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtilsV2 {

    public static void main(String[] args) {

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        Map<String, BigDecimal> result = calcRefundTradeMoney(firstTransferRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("firstTransferRecords1：{}", JSONObject.toJSONString(result));

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        Map<String, BigDecimal> result2 = calcRefundTradeMoney(firstTransferRecords2, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        log.info("firstTransferRecords2：{}", JSONObject.toJSONString(result2));
//

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        Map<String, BigDecimal> refundResult3 = calcRefundTradeMoney(firstTransferRecords3, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        ;
        ;
        log.info("firstTransferRecords3：{}", JSONObject.toJSONString(refundResult3));
    }

    /**
     * 计算退款仅可交易金额及退款明细
     * 核心逻辑：按LIFO（后进先出）原则分配退款，返回退款明细
     *
     * @param subAccountFlowRecordList 子账户流水记录列表
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @return 退款结果，包含总退款金额和明细
     */
    public static Map<String, BigDecimal> calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList, BigDecimal currentTransferOutAmount, UserAssetsTypeEnum inType, UserAssetsTypeEnum outType) {

        // 定义转出金额map
        Map<String, BigDecimal> outRecordMap = new HashMap<>();
        // 定义转入金额map
        Map<String, BigDecimal> inRecordMap = new HashMap<>();
        // 遍历
        for (UU898UserSubAccountFlowRecordDTO record : subAccountFlowRecordList) {
            if (outType.getTypeId().equals(record.getJournalType())) {
                outRecordMap.put(record.getOrderNo(), record.getBalanceChange().abs());
            } else if (inType.getTypeId().equals(record.getJournalType())) {
                inRecordMap.put(record.getOrderNo(), record.getBalanceChange().abs());
            }
        }
        // 按时间排序流水记录
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime).reversed())
                .collect(Collectors.toList());

        Map<String, BigDecimal> refundMap = new HashMap<>();
        //
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            // 判断是否为0
            if (currentTransferOutAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            // 获取转入金额map
            BigDecimal inRecordAmount = inRecordMap.getOrDefault(record.getOrderNo(), BigDecimal.ZERO);
            //
            BigDecimal outRecordAmount = outRecordMap.getOrDefault(record.getOrderNo(), BigDecimal.ZERO);
            // 判断本次是否转入并且转入金额>0
            if (inType.getTypeId().equals(record.getJournalType()) && inRecordAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 获取扣减金额
                BigDecimal decAmount = currentTransferOutAmount.min(inRecordAmount);
                // 获取本次扣款金额
                currentTransferOutAmount = currentTransferOutAmount.subtract(decAmount);
                //
                BigDecimal currentInRecordAmount = inRecordAmount.subtract(decAmount);
                //
                inRecordMap.put(record.getOrderNo(), currentInRecordAmount);
                //
                refundMap.put(record.getOrderNo(), decAmount);
                // 处理转出
            } else if (outType.getTypeId().equals(record.getJournalType()) && outRecordAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 遍历所有的转入金额
                for (UU898UserSubAccountFlowRecordDTO sortedRecord : sortedRecords) {
                    if (outRecordAmount.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    //
                    BigDecimal stringBigDecimalEntry = inRecordMap.getOrDefault(sortedRecord.getOrderNo(), BigDecimal.ZERO);
                    //
                    if (stringBigDecimalEntry.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    //
                    // 获取扣减金额
                    BigDecimal decAmount = outRecordAmount.min(stringBigDecimalEntry);
                    //
                    outRecordAmount = outRecordAmount.subtract(decAmount);
                    //
                    outRecordMap.put(record.getOrderNo(), outRecordAmount);
                    //
                    BigDecimal ttt112 = stringBigDecimalEntry.subtract(decAmount);
                    //
                    inRecordMap.put(sortedRecord.getOrderNo(), ttt112);
                }

                //
            }
        }
        return refundMap;
    }


    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

//
//    /**
//     * 验证修正后的示例计算逻辑
//     */
//    public static void validateCorrectedExampleCalculation() {
//        log.info("=== 验证修正后的示例计算逻辑 ===");
//
//        // 第二次转出计算逻辑的测试数据（包含历史转出3元）
//        List<UU898UserSubAccountFlowRecordDTO> secondTransferRecords = Arrays.asList(
//                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
//                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
//                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//        );
//
//        log.info("\n=== 第二次转出计算（转出5元）===");
//        log.info("步骤1：初始转入金额：订单4(1)，订单5(1)，订单6(1)，订单7(1)，订单8(3)，订单9(1)，订单10(1)，订单11(1)");
//        log.info("步骤2：第一次转出3元按LIFO扣减：订单10(-1→0)，订单9(-1→0)，订单8(-1→2)");
//        log.info("步骤3：扣减后剩余可退：订单4(1)，订单5(1)，订单6(1)，订单7(1)，订单8(2)，订单9(0)，订单10(0)，订单11(1)");
//        log.info("步骤4：本次转出5元按LIFO分配：从订单11退1元，订单8退2元，订单7退1元，订单6退1元");
//
//        RefundResult secondResult = calcRefundTradeMoney(secondTransferRecords, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
//        printRefundResult("第二次转出", secondResult);
//
//        // 验证结果
//        boolean isCorrect = validateSecondTransferOutResult(secondResult);
//        log.info("第二次转出结果验证：{}", isCorrect ? "✅ 正确" : "❌ 错误");
//    }
//
//    /**
//     * 验证第二次转出结果是否正确
//     */
//    private static boolean validateSecondTransferOutResult(RefundResult result) {
//        if (result.getTotalRefundAmount().compareTo(new BigDecimal("5")) != 0) {
//            log.error("总退款金额错误，期望5元，实际{}元", result.getTotalRefundAmount());
//            return false;
//        }
//
//        // 检查退款明细
//        List<RefundDetail> details = result.getRefundDetails();
//        if (details.size() != 4) {
//            log.error("退款明细数量错误，期望4条，实际{}条", details.size());
//            return false;
//        }
//
//        // 验证具体明细（按LIFO顺序）
//        Map<String, BigDecimal> expectedRefunds = new HashMap<>();
//        expectedRefunds.put("转入订单11", new BigDecimal("1"));
//        expectedRefunds.put("转入订单8", new BigDecimal("2"));
//        expectedRefunds.put("转入订单7", new BigDecimal("1"));
//        expectedRefunds.put("转入订单6", new BigDecimal("1"));
//
//        for (RefundDetail detail : details) {
//            BigDecimal expectedAmount = expectedRefunds.get(detail.getTransferInOrderNo());
//            if (expectedAmount == null || expectedAmount.compareTo(detail.getRefundAmount()) != 0) {
//                log.error("订单 {} 退款金额错误，期望{}元，实际{}元",
//                        detail.getTransferInOrderNo(), expectedAmount, detail.getRefundAmount());
//                return false;
//            }
//        }
//
//        return true;
//    }
//
//    /**
//     * 打印退款结果
//     */
//    private static void printRefundResult(String title, RefundResult result) {
//        log.info("=== {} 结果 ===", title);
//        log.info("总退款金额：{} 元", result.getTotalRefundAmount());
//        log.info("退款明细：");
//        for (RefundDetail detail : result.getRefundDetails()) {
//            log.info("  从订单 {} 退款 {} 元", detail.getTransferInOrderNo(), detail.getRefundAmount());
//        }
//        log.info("==================");
//    }
//
//    /**
//     * 详细测试LIFO扣减逻辑（修正后）
//     */
//    public static void testLIFODeductionLogic() {
//        log.info("=== 测试LIFO扣减逻辑 ===");
//
//        // 创建转入订单映射
//        Map<String, BigDecimal> transferInRemaining = new LinkedHashMap<>();
//        List<String> transferInOrderList = new ArrayList<>();
//
//        // 初始化转入订单
//        transferInRemaining.put("转入订单4", new BigDecimal("1"));
//        transferInRemaining.put("转入订单5", new BigDecimal("1"));
//        transferInRemaining.put("转入订单6", new BigDecimal("1"));
//        transferInRemaining.put("转入订单7", new BigDecimal("1"));
//        transferInRemaining.put("转入订单8", new BigDecimal("3"));
//        transferInRemaining.put("转入订单9", new BigDecimal("1"));
//        transferInRemaining.put("转入订单10", new BigDecimal("1"));
//        transferInRemaining.put("转入订单11", new BigDecimal("1"));
//
//        transferInOrderList.addAll(Arrays.asList("转入订单4", "转入订单5", "转入订单6", "转入订单7",
//                "转入订单8", "转入订单9", "转入订单10", "转入订单11"));
//
//        log.info("初始状态：{}", transferInRemaining);
//
//        // 模拟历史转出3元
//        log.info("开始模拟历史转出3元的LIFO扣减...");
//        deductFromTransferInByLIFO(transferInRemaining, transferInOrderList, new BigDecimal("3"));
//
//        log.info("LIFO扣减后的状态：{}", transferInRemaining);
//
//        // 验证扣减结果（按LIFO：从订单11、10、9开始扣减）
//        Map<String, BigDecimal> expectedAfterDeduction = new HashMap<>();
//        expectedAfterDeduction.put("转入订单4", new BigDecimal("1"));
//        expectedAfterDeduction.put("转入订单5", new BigDecimal("1"));
//        expectedAfterDeduction.put("转入订单6", new BigDecimal("1"));
//        expectedAfterDeduction.put("转入订单7", new BigDecimal("1"));
//        expectedAfterDeduction.put("转入订单8", new BigDecimal("2")); // 3-1=2
//        expectedAfterDeduction.put("转入订单9", BigDecimal.ZERO);     // 1-1=0
//        expectedAfterDeduction.put("转入订单10", BigDecimal.ZERO);    // 1-1=0
//        expectedAfterDeduction.put("转入订单11", new BigDecimal("1"));
//
//        boolean isCorrect = true;
//        for (String orderNo : transferInOrderList) {
//            BigDecimal actual = transferInRemaining.get(orderNo);
//            BigDecimal expected = expectedAfterDeduction.get(orderNo);
//            if (actual.compareTo(expected) != 0) {
//                log.error("订单 {} 扣减结果错误，期望：{}，实际：{}", orderNo, expected, actual);
//                isCorrect = false;
//            }
//        }
//
//        log.info("LIFO扣减逻辑验证：{}", isCorrect ? "✅ 正确" : "❌ 错误");
//
//        // 现在模拟LIFO分配5元
//        log.info("开始模拟LIFO分配5元...");
//        List<RefundDetail> refundDetails = new ArrayList<>();
//        BigDecimal remainingRefund = new BigDecimal("5");
//
//        for (int i = transferInOrderList.size() - 1; i >= 0 && remainingRefund.compareTo(BigDecimal.ZERO) > 0; i--) {
//            String orderNo = transferInOrderList.get(i);
//            BigDecimal availableAmount = transferInRemaining.get(orderNo);
//
//            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
//                BigDecimal refundFromThisOrder = remainingRefund.min(availableAmount);
//                refundDetails.add(new RefundDetail(orderNo, refundFromThisOrder));
//                remainingRefund = remainingRefund.subtract(refundFromThisOrder);
//
//                log.info("LIFO分配：从订单 {} 退款 {} 元，该订单剩余 {} 元，还需退款 {} 元",
//                        orderNo, refundFromThisOrder, availableAmount, remainingRefund);
//            }
//        }
//
//        log.info("LIFO分配完成，退款明细：{}", refundDetails);
//
//        // 验证LIFO分配结果（修正后的预期）
//        List<String> expectedRefundOrder = Arrays.asList("转入订单11", "转入订单8", "转入订单7", "转入订单6");
//        List<BigDecimal> expectedRefundAmounts = Arrays.asList(new BigDecimal("1"), new BigDecimal("2"),
//                new BigDecimal("1"), new BigDecimal("1"));
//
//        boolean lifoCorrect = true;
//        if (refundDetails.size() != expectedRefundOrder.size()) {
//            log.error("退款明细数量错误，期望：{}，实际：{}", expectedRefundOrder.size(), refundDetails.size());
//            lifoCorrect = false;
//        } else {
//            for (int i = 0; i < refundDetails.size(); i++) {
//                RefundDetail detail = refundDetails.get(i);
//                String expectedOrder = expectedRefundOrder.get(i);
//                BigDecimal expectedAmount = expectedRefundAmounts.get(i);
//
//                if (!detail.getTransferInOrderNo().equals(expectedOrder) ||
//                        detail.getRefundAmount().compareTo(expectedAmount) != 0) {
//                    log.error("退款明细 {} 错误，期望：订单{}退款{}元，实际：订单{}退款{}元",
//                            i, expectedOrder, expectedAmount, detail.getTransferInOrderNo(), detail.getRefundAmount());
//                    lifoCorrect = false;
//                }
//            }
//        }
//
//        log.info("LIFO分配逻辑验证：{}", lifoCorrect ? "✅ 正确" : "❌ 错误");
//        log.info("=== 测试完成 ===");
//    }
//
//    /**
//     * 验证完整的三次转出场景
//     */
//    public static void validateThreeTransferOutScenario() {
//        log.info("=== 验证完整的三次转出场景 ===");
//
//        // 第一次转出：只有转入记录，转出3元
//        List<UU898UserSubAccountFlowRecordDTO> firstRecords = Arrays.asList(
//                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
//                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//        );
//
//        log.info("\n=== 第一次转出3元 ===");
//        log.info("预期：从订单10退1元，订单9退1元，订单8退1元");
//        RefundResult firstResult = calcRefundTradeMoney(firstRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
//        printRefundResult("第一次转出", firstResult);
//
//        // 第二次转出：包含第一次转出记录，再转出5元
//        List<UU898UserSubAccountFlowRecordDTO> secondRecords = new ArrayList<>(firstRecords);
//        secondRecords.add(createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")));
//        secondRecords.add(createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")));
//
//        log.info("\n=== 第二次转出5元 ===");
//        log.info("预期：从订单11退1元，订单8退2元，订单7退1元，订单6退1元");
//        RefundResult secondResult = calcRefundTradeMoney(secondRecords, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
//        printRefundResult("第二次转出", secondResult);
//
//        // 第三次转出：包含前两次转出记录，再转出5元
//        List<UU898UserSubAccountFlowRecordDTO> thirdRecords = new ArrayList<>(secondRecords);
//        thirdRecords.add(createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")));
//        thirdRecords.add(createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")));
//
//        log.info("\n=== 第三次转出5元 ===");
//        log.info("预期：从订单12退3元，剩余2元无法退款（因为其他订单已被前两次转出扣减完）");
//        RefundResult thirdResult = calcRefundTradeMoney(thirdRecords, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
//        printRefundResult("第三次转出", thirdResult);
//
//        // 验证第三次转出结果
//        if (thirdResult.getTotalRefundAmount().compareTo(new BigDecimal("3")) == 0 &&
//                thirdResult.getRefundDetails().size() == 1 &&
//                thirdResult.getRefundDetails().get(0).getTransferInOrderNo().equals("转入订单12") &&
//                thirdResult.getRefundDetails().get(0).getRefundAmount().compareTo(new BigDecimal("3")) == 0) {
//            log.info("✅ 第三次转出验证正确：只能从订单12退3元，剩余2元无法退款");
//        } else {
//            log.error("❌ 第三次转出验证失败");
//        }
//
//        log.info("=== 三次转出场景验证完成 ===");
//    }

}
