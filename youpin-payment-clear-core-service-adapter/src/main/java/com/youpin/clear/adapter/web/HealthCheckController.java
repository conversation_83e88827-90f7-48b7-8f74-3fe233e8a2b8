package com.youpin.clear.adapter.web;

import com.uu898.youpin.commons.annotation.SuppressTraceLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
public class HealthCheckController {

    @SuppressTraceLog
    @GetMapping("ping")
    public String ping() {
        return "ok";
    }

    @SuppressTraceLog
    @GetMapping("hc")
    public Map<String, Object> hc() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        data.put("db", "ok");
        data.put("redis", "ok");
        data.put("depn", "ok");
        result.put("code", 0);
        result.put("msg", "ok");
        result.put("data", data);
        return result;
    }
}
