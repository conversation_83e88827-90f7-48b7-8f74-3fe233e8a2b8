package com.youpin.clear.adapter.service;

import com.alibaba.fastjson.JSONObject;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtilsV3 {

    public static void main(String[] args) {

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        Map<String, BigDecimal> result = calcRefundTradeMoney(firstTransferRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("firstTransferRecords1：{}", JSONObject.toJSONString(result));

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        Map<String, BigDecimal> result2 = calcRefundTradeMoney(firstTransferRecords2, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        log.info("firstTransferRecords2：{}", JSONObject.toJSONString(result2));
//

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        Map<String, BigDecimal> refundResult3 = calcRefundTradeMoney(firstTransferRecords3, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        ;
        ;
        log.info("firstTransferRecords3：{}", JSONObject.toJSONString(refundResult3));
    }

    /**
     * 计算退款仅可交易金额及退款明细（LIFO原则）
     *
     * @param subAccountFlowRecordList 子账户流水记录列表（按时间倒序）
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @param inType                   转入类型
     * @param outType                  转出类型
     * @return 退款明细Map（key:订单号, value:退款金额）
     */
    public static Map<String, BigDecimal> calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList, BigDecimal currentTransferOutAmount, UserAssetsTypeEnum inType, UserAssetsTypeEnum outType) {
        // 1. 参数校验
        Objects.requireNonNull(subAccountFlowRecordList, "流水记录列表不能为空");
        Objects.requireNonNull(currentTransferOutAmount, "转出金额不能为空");
        Objects.requireNonNull(inType, "转入类型不能为空");
        Objects.requireNonNull(outType, "转出类型不能为空");

        if (currentTransferOutAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return Collections.emptyMap();
        }

        // 2. 按创建时间倒序排序
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime).reversed())
                .collect(Collectors.toList());
        // 3. 定义初始化金额映射
        Map<String, BigDecimal> inRecordMap = new HashMap<>();
        Map<String, BigDecimal> outRecordMap = new HashMap<>();
        // 3. 初始化金额映射
        sortedRecords.forEach(record -> {
            // 判断是否为转出
            if (outType.getTypeId().equals(record.getJournalType())) {
                outRecordMap.put(record.getOrderNo(), record.getBalanceChange().abs());
                // 判断是否为转入
            } else if (inType.getTypeId().equals(record.getJournalType())) {
                inRecordMap.put(record.getOrderNo(), record.getBalanceChange().abs());
            }
        });

        //
        Map<String, BigDecimal> refundMap = new LinkedHashMap<>();
        // 4. 处理退款逻辑
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (currentTransferOutAmount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            if (inType.getTypeId().equals(record.getJournalType())) {
                processInRecord(record, currentTransferOutAmount, inRecordMap, refundMap);
            } else if (outType.getTypeId().equals(record.getJournalType())) {
                processOutRecord(record, sortedRecords, inRecordMap, outRecordMap);
            }
        }
        return refundMap;
    }

    /**
     * 处理转入记录退款
     */
    private static void processInRecord(UU898UserSubAccountFlowRecordDTO record, BigDecimal remainingTransferAmount, Map<String, BigDecimal> inRecordMap, Map<String, BigDecimal> refundMap) {

        BigDecimal inAmount = inRecordMap.getOrDefault(record.getOrderNo(), BigDecimal.ZERO);
        if (inAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal refundAmount = remainingTransferAmount.min(inAmount);
            inRecordMap.put(record.getOrderNo(), inAmount.subtract(refundAmount));
            refundMap.put(record.getOrderNo(), refundAmount);
        }
    }

    /**
     * 处理转出记录对冲
     */
    private static void processOutRecord(UU898UserSubAccountFlowRecordDTO outRecord, List<UU898UserSubAccountFlowRecordDTO> sortedRecords, Map<String, BigDecimal> inRecordMap, Map<String, BigDecimal> outRecordMap) {

        BigDecimal outAmount = outRecordMap.getOrDefault(outRecord.getOrderNo(), BigDecimal.ZERO);
        if (outAmount.compareTo(BigDecimal.ZERO) > 0) {
            for (UU898UserSubAccountFlowRecordDTO inRecord : sortedRecords) {
                if (outAmount.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                BigDecimal inAmount = inRecordMap.getOrDefault(inRecord.getOrderNo(), BigDecimal.ZERO);
                if (inAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal offsetAmount = outAmount.min(inAmount);
                    outAmount = outAmount.subtract(offsetAmount);
                    outRecordMap.put(outRecord.getOrderNo(), outAmount);
                    inRecordMap.put(inRecord.getOrderNo(), inAmount.subtract(offsetAmount));
                }
            }
        }
    }


    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

}
