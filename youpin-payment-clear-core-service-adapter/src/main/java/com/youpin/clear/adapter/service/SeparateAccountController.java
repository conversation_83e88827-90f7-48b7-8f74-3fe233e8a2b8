package com.youpin.clear.adapter.service;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.converter.UserAssetsRecordConvertor;
import com.youpin.clear.app.service.SeparateAccountService;
import com.youpin.clear.app.service.UserBalanceAssetsCheckService;
import com.youpin.clear.app.service.UserBalanceChannelCheckService;
import com.youpin.clear.client.request.OpenAccountRequest;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import com.youpin.clear.domain.dto.UserAssetsRecordMessageTest;
import com.youpin.clear.domain.dto.UserBalanceAssetsCheckMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/service/account/separate")
public class SeparateAccountController {

    @Autowired
    SeparateAccountService separateAccountService;

    @Autowired
    UserBalanceAssetsCheckService userBalanceAssetsCheckService;

    @Autowired
    UserBalanceChannelCheckService userBalanceChannelCheckService;

    /**
     * 原生大写参数测试
     */
    @PostMapping(value = "/capital/test")
    public Result<Void> userAssetsRecordMessageCapitalTest(@Valid @RequestBody List<UserAssetsRecordMessage> requestList) {
        List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList = UserAssetsRecordConvertor.MAPPER.toUserAssetsRecordDTOList(requestList);
        try {
            separateAccountService.userAssetsRecordMessageTest(userAssetsRecordMessageDTOList, Boolean.FALSE);
        } catch (PaymentClearBusinessException e) {
            //有些异常需要跳掉
            if (e.getCode().equals(ErrorCode.ACCOUNT_RECORD_EXIST.getCode())) {
                log.error("[资金明细消费] 业务异常 剔除. error,{}", ExceptionUtils.getStackTrace(e));
                return Result.ok();
            }
            log.error("[资金明细消费] 业务异常 保存补偿表. error,{}", ExceptionUtils.getStackTrace(e));
        } catch (Exception e) {
            log.error("[资金明细消费] {} 系统错误 保存补偿表 Error:{}", null, ExceptionUtils.getStackTrace(e));
        }
        return Result.ok();
    }


    /**
     * 小写参数测试
     * b无事务 bc 有事务
     */
    @PostMapping(value = "/test/{pvm}")
    public Result<Void> userAssetsRecordMessageTest(@Valid @RequestBody List<UserAssetsRecordMessageTest> requestList, @PathVariable("pvm") String pvm) {
        log.info("pvm:{}", pvm);
        Boolean separateTransactionTest = Boolean.FALSE;
        if (pvm.equals("bc")) {
            separateTransactionTest = Boolean.TRUE;
        }
        List<UU898UserAssetsRecordDTO> userAssetsRecordMessageDTOList = UserAssetsRecordConvertor.MAPPER.toUserAssetsRecordDTOListTest(requestList);
        separateAccountService.userAssetsRecordMessageTest(userAssetsRecordMessageDTOList, separateTransactionTest);
        return Result.ok();
    }

    /**
     * 用户分账清理
     */
    @PostMapping(value = "/user/clear/data")
    public Result<Void> userClearData(@Valid @RequestBody OpenAccountRequest request) {
        separateAccountService.userClearData(request.getUserId());
        return Result.ok();
    }

    /**
     * 资产校验
     */
    @PostMapping(value = "/balance/assets/check")
    public Result<Void> userBalanceAssetsCheck(@Valid @RequestBody List<UserBalanceAssetsCheckMessage> userAssetsRecordMessageDTOList) {
        userBalanceAssetsCheckService.userBalanceAssetsCheck(userAssetsRecordMessageDTOList);
        return Result.ok();
    }

    /**
     * 渠道校验
     */
    @PostMapping(value = "/balance/channel/check")
    public Result<Void> userBalanceChannelCheck(@Valid @RequestBody List<UserBalanceAssetsCheckMessage> userAssetsRecordMessageDTOList) {
        userBalanceChannelCheckService.handle(userAssetsRecordMessageDTOList);
        return Result.ok();
    }


}
