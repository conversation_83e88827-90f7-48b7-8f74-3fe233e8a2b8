package com.youpin.clear.adapter.service;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.AccountAssetsTypeService;
import com.youpin.clear.domain.dto.AccountAssetsTypeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/service/account/assets/type")
public class AccountAssetsTypeController {

    @Autowired
    AccountAssetsTypeService accountAssetsTypeService;

    @PostMapping(value = "/gatAll")
    public Result<Map<Integer, AccountAssetsTypeDTO>> gatAllAccountAssetsType() {
        return Result.ok(accountAssetsTypeService.gatAllAccountAssetsType());
    }

    @PostMapping(value = "/gatAllRelate")
    public Result<Map<String, List<Integer>>> gatAllAccountAssetsTypeRelate() {
        return Result.ok(accountAssetsTypeService.gatAllAccountAssetsTypeRelate());
    }

    @PostMapping(value = "/initCache")
    public Result<Void> initAccountAssetsTypeCache() {
        accountAssetsTypeService.initAccountAssetsTypeCache();
        return Result.ok();
    }

}
