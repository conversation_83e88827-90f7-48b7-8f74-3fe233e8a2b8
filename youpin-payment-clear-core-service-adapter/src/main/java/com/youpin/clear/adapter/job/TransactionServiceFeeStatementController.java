package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.TransactionServiceFeeStatementRecordService;
import com.youpin.clear.app.service.UserAccountService;
import com.youpin.clear.client.request.TransactionServiceFeeStatementRequest;
import com.youpin.clear.client.response.TransactionServiceFeeStatementResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/job/clear/serviceFee-statement")
public class TransactionServiceFeeStatementController {


    @Autowired
    TransactionServiceFeeStatementRecordService transactionServiceFeeStatementRecordService;

    @Autowired
    UserAccountService userAccountService;


    /**
     * selectMaxId
     */
    @PostMapping(value = "/selectMaxId")
    public Result<TransactionServiceFeeStatementResponse> serviceFeeStatementSelectMaxId() {
        return Result.ok(transactionServiceFeeStatementRecordService.selectMaxId());
    }

    /**
     * 生成批次号
     */
    @PostMapping(value = "/updateSerialNo")
    public Result<TransactionServiceFeeStatementResponse> updateSerialNo(@RequestBody TransactionServiceFeeStatementRequest request) {
        return Result.ok(transactionServiceFeeStatementRecordService.updateSerialNo(request));
    }

    /**
     * 处理
     */
    @PostMapping(value = "/handle")
    public Result<Void> handleByJobId(@RequestBody TransactionServiceFeeStatementRequest request) {
        transactionServiceFeeStatementRecordService.handleByJobId(request);
        return Result.ok();
    }

    /**
     * 处理
     */
    @PostMapping(value = "/handleByJobIdRetry")
    public Result<Void> handleByJobIdRetry() {
        transactionServiceFeeStatementRecordService.handleByJobIdRetry();
        return Result.ok();
    }


    /**
     * 用户余额1转余额2处理
     */

    @PostMapping(value = "/money1/to/money2")
    public Result<Void> transferAccountMoney1ToMoney2() {
        userAccountService.transferAccountMoney1ToMoney2();
        return Result.ok();
    }


    /**
     * 服务费财务汇总
     * 1.统计20%的非预售交易服务费总金额（181）
     * 2.统计80%的非预售交易服务费总金额（181）
     * 3.统计100%的预售交易服务费总金额（181）
     * 4.统计100%非预售退还交易服务费（182）
     * 5.统计100%预售退还交易服务费（182）
     * 6.统计100%交易服务费（227）
     */
    @PostMapping(value = "/finance/summary")
    public Result<Void> financeSummary(@RequestBody TransactionServiceFeeStatementRequest request) {
        transactionServiceFeeStatementRecordService.financeSummary(request);
        return Result.ok();
    }

    /**
     * 统计清理
     */
    @PostMapping(value = "/finance/summary/clear")
    public Result<Void> financeSummaryClear(@RequestBody TransactionServiceFeeStatementRequest request) {
        transactionServiceFeeStatementRecordService.financeSummaryClear(request);
        return Result.ok();
    }



}
