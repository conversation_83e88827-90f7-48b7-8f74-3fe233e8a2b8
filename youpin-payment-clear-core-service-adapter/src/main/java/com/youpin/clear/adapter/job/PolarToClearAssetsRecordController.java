package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.NetPlatformUserRecordService;
import com.youpin.clear.app.service.PolarToClearAssetsRecordService;
import com.youpin.clear.client.request.ClearAssetsTagRequest;
import com.youpin.clear.client.request.PolarToClearAssetsRecordRequest;
import com.youpin.clear.client.request.UserAssetsRecordByTypeRequest;
import com.youpin.clear.client.response.ClearAssetsTagResponse;
import com.youpin.clear.client.response.UserAssetsRecordByTypeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 资金数据对账
 */
@RestController
@RequestMapping("/job/clear/user-assetsRecord")
public class PolarToClearAssetsRecordController {

    @Autowired
    PolarToClearAssetsRecordService polarToClearAssetsRecord;

    @Autowired
    @Qualifier("asyncTaskExecutor")
    ThreadPoolTaskExecutor asyncTaskExecutor;

    @Autowired
    NetPlatformUserRecordService netPlatformUserRecordService;

    /**
     * maxUserAssetsRecordId
     */
    @PostMapping(value = "/maxUserAssetsRecordId")
    public Result<Long> maxUserAssetsRecordId(@RequestBody PolarToClearAssetsRecordRequest request) {
        return Result.ok(polarToClearAssetsRecord.maxUserAssetsRecordId(request));
    }

    /**
     * 资金同步
     */
    @PostMapping(value = "/polar/to/handle")
    public Result<Long> polarToClearAssetsRecord(@RequestBody PolarToClearAssetsRecordRequest request) {
        return Result.ok(polarToClearAssetsRecord.polarToClearAssetsRecord(request));
    }

    /**
     * tag 同步
     */
    @PostMapping(value = "/tag/sync")
    public Result<Integer> assetsTagSync(@RequestBody ClearAssetsTagRequest request) {
        return Result.ok(polarToClearAssetsRecord.assetsTagSync(request));
    }

    /**
     * tag 同步 根据userId
     */
    @PostMapping(value = "/tag/sync/userId")
    public Result<Integer> assetsTagSyncByUserId(@RequestBody ClearAssetsTagRequest request) {
        return Result.ok(polarToClearAssetsRecord.assetsTagSyncByUserId(request));
    }

    /**
     * tag 缓存删除
     */
    @PostMapping(value = "/tag/cache/delete")
    public Result<Void> assetsTagDelete(@RequestBody ClearAssetsTagRequest request) {
        //异步
        CompletableFuture.runAsync(() -> polarToClearAssetsRecord.assetsTagDelete(request), asyncTaskExecutor);
        return Result.ok();
    }

    /**
     * tag get by userId
     */
    @PostMapping(value = "/tag/cache/get/userId")
    public Result<List<ClearAssetsTagResponse>> assetsTagCacheGetUserId(@RequestBody ClearAssetsTagRequest request) {
        return Result.ok(polarToClearAssetsRecord.assetsTagCacheGetUserId(request));
    }


    /**
     * selectMaxId
     */
    @PostMapping(value = "/net/bufferAccountingProcess/selectMaxId")
    public Result<UserAssetsRecordByTypeResponse> uu898UserAssetsRecordNetBufferAccountingMaxId(@Valid @RequestBody UserAssetsRecordByTypeRequest request) {
        return Result.ok(netPlatformUserRecordService.selectMaxId(request));
    }

    /**
     * net缓冲记账-过度-核对处理流程
     */
    @PostMapping(value = "/net/bufferAccountingProcess")
    public Result<Void> uu898UserAssetsRecordNetBufferAccountingProcess(@Valid @RequestBody UserAssetsRecordByTypeRequest request) {
        netPlatformUserRecordService.uu898UserAssetsRecordNetBufferAccountingProcess(request);
        return Result.ok();
    }


}
