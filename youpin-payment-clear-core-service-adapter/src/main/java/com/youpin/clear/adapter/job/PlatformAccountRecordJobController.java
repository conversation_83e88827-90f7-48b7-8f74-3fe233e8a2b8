package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.PlatformAccountRecordService;
import com.youpin.clear.client.request.CompensationRecordRetryJobRequest;
import com.youpin.clear.client.request.PlatformAccountRecordJobRequest;
import com.youpin.clear.client.response.PlatformAccountRecordJobResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/job/clear/platform-account-record")
public class PlatformAccountRecordJobController {

    @Autowired
    PlatformAccountRecordService platformAccountRecordService;
    
    /**
     * 缓冲记账-分页查询
     */
    @PostMapping(value = "/selectPage")
    public Result<PlatformAccountRecordJobResponse> selectPageSizeByStatusAndCreateTime(@RequestBody PlatformAccountRecordJobRequest request) {
        return Result.ok(platformAccountRecordService.selectPageSizeByStatusAndCreateTime(request));
    }

    /**
     * 缓冲记账-处理
     */
    @PostMapping(value = "/handle")
    public Result<Void> platformAccountRecordHandle(@RequestBody PlatformAccountRecordJobRequest request) {
        platformAccountRecordService.handle(request);
        return Result.ok();
    }

    /**
     * 缓冲记账-uu898steam反向对照
     */
    @PostMapping(value = "/reverseCompare")
    public Result<Void> reverseCompare(@RequestBody CompensationRecordRetryJobRequest request) {
        return Result.ok();
    }


}
