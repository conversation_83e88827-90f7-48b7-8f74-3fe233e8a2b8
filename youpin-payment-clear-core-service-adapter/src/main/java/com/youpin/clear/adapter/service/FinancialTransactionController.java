package com.youpin.clear.adapter.service;


import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.FinancialTransactionService;
import com.youpin.clear.client.request.QueryAssetInfoMoneyRequest;
import com.youpin.clear.client.request.financial.*;
import com.youpin.clear.client.response.QueryAssetInfoMoneyResponse;
import com.youpin.clear.client.response.financial.FinancialResponse;
import com.youpin.clear.client.response.financial.SettleResponse;
import com.youpin.clear.client.response.financial.UserAssetsRecordResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 交易
 */
@Slf4j
@RestController
@RequestMapping("/service/financial/transaction")
public class FinancialTransactionController {


    @Autowired
    FinancialTransactionService financialTransactionService;

    /**
     * 支付
     */
    @PostMapping("/pay")
    public Result<Void> pay(@Valid @RequestBody PayFinancialRequest request) {
        log.info("financial:pay:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionService.pay(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg());
    }

    /**
     * 退款
     */
    @PostMapping("/refund")
    public Result<Void> refund(@Valid @RequestBody RefundFinancialRequest request) {
        log.info("financial:refund:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionService.refund(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg());
    }

    /**
     * 结算
     */
    @PostMapping("/settlement")
    public Result<SettleResponse> settlement(@Valid @RequestBody SettlementFinancialRequest request) {
        log.info("financial:settlement:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionService.settlement(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg(), financialResponse.getSettleResponse());
    }

    /**
     * 特殊结算
     */
    @PostMapping("/specialSettlement")
    public Result<SettleResponse> specialSettlement(@Valid @RequestBody SettlementFinancialRequest request) {
        log.info("financial:specialSettlement:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionService.specialSettlement(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg(),financialResponse.getSettleResponse());
    }

    /**
     * 查询
     */
    @PostMapping("/query")
    public Result<List<UserAssetsRecordResponse>> query(@Valid @RequestBody QueryFinancialRequest request) {
        return Result.ok(financialTransactionService.query(request));
    }

    /**
     * 提现
     */
    @PostMapping("/withdrawal")
    public Result<Void> withdrawal(@Valid @RequestBody WithdrawalFinancialRequest request) {
        log.info("financial:withdrawal:mc{}:sub{}:{}", request.getMerchantId(), request.getSubBusType(), JSON.toJSONString(request));
        FinancialResponse financialResponse = financialTransactionService.withdrawal(request);
        return new Result<>(financialResponse.getCode(), financialResponse.getMsg());
    }

    /**
     * 提现
     */
    @PostMapping("/query/assetInfo/money")
    public Result<QueryAssetInfoMoneyResponse> queryAssetInfoMoney(@Valid @RequestBody QueryAssetInfoMoneyRequest request) {
        log.info("financial:queryAssetInf  request:{}", JSON.toJSONString(request));
        QueryAssetInfoMoneyResponse response = financialTransactionService.queryAssetInfoMoney(request);
        log.info("financial:queryAssetInf  response:{}", JSON.toJSONString(response));
        return Result.ok(response);
    }


}
