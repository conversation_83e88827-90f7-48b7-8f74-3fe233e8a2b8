package com.youpin.clear.adapter.service;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.NetPlatformUserRecordService;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.dto.NetPlatformUserRecordMessage;
import com.youpin.clear.domain.servcie.RocketMqService;
import com.youpin.clear.domain.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/service/tool")
public class ToolController {

    /**
     * 关闭MQ消费
     */
    @PostMapping(value = "/mq/shutdown")
    public Result<Void> shutdownMQ() {
        //PushRocketMQListenerContainer
        DefaultRocketMQListenerContainer defaultRocketMQListenerContainer = SpringBeanUtil.getBean(DefaultRocketMQListenerContainer.class);
        if (null == defaultRocketMQListenerContainer) {
            return Result.fail(ErrorCode.SYSTEM_ERROR.getCode(), "defaultRocketMQListenerContainer is null");
        }
        if (defaultRocketMQListenerContainer.isRunning()) {
            defaultRocketMQListenerContainer.stop();
        }
        log.info("关闭MQ消费");
        return Result.ok();
    }

    /**
     * 开启MQ消费
     */
    @PostMapping(value = "/mq/start")
    public Result<Void> startMQ() {
        DefaultRocketMQListenerContainer defaultRocketMQListenerContainer = SpringBeanUtil.getBean(DefaultRocketMQListenerContainer.class);
        if (null == defaultRocketMQListenerContainer) {
            return Result.fail(ErrorCode.SYSTEM_ERROR.getCode(), "defaultRocketMQListenerContainer is null");
        }
        if (!defaultRocketMQListenerContainer.isRunning()) {
            try {
                defaultRocketMQListenerContainer.afterPropertiesSet();
                defaultRocketMQListenerContainer.start();
                log.info("开启MQ消费");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            log.info("MQ消费已开启");
        }
        return Result.ok();
    }

    @Autowired
    RocketMqService rocketMqService;


    String cc = "[{\"Id\":0,\"UserId\":0,\"TypeId\":4,\"TreadNo\":\"**************\",\"AssetType\":1,\"Money\":998032.78,\"ThisMoney\":0.02,\"AfterMoney\":998032.80,\"ChargeMoney\":0.0,\"BlockMoney\":66.60,\"ThisBlockMoney\":0.0,\"AfterBlockMoney\":66.60,\"PurchaseMoney\":2179.50,\"ThisPurchaseMoney\":0.0,\"AfterPurchaseMoney\":2179.50,\"SerialNo\":\"202501231003330971\",\"OrderNo\":\"**************\",\"PayOrderNo\":\"202501231003330971\",\"Remark\":\"\",\"AddTime\":\"2025-01-23T18:02:52.6735151+08:00\",\"PayWaitExpireTime\":null,\"CompleteTime\":\"2025-01-23T18:02:52.6735151+08:00\",\"PayChannel\":0,\"AccountName\":\"\",\"Attr\":1,\"Status\":1,\"GenSource\":null,\"CollectType\":0,\"IsLeaseOrder\":false,\"Balance2TransferAmount\":0.0}]";

    @PostMapping(value = "/mq/send")
    public Result<Void> sendMq() {
        rocketMqService.sendDynamicsMQFifoMsg(MQConfig.PAYMENT_CLEAR_MQ, "USER_ACCOUNT_RECORD_TOPIC_CE", MQConfig.USER_ACCOUNT_RECORD_TAG, "USER_ACCOUNT_RECORD_CONSUMER_GROUP_CE", cc, Boolean.FALSE);
        return Result.ok();
    }

    @Autowired
    NetPlatformUserRecordService netPlatformUserRecordService;


    @PostMapping(value = "/bufferAccountingProcess")
    public Result<Void> netPlatformUserRecordServiceBufferAccountingProcess(@RequestBody NetPlatformUserRecordMessage netPlatformUserRecordMessage) {
        netPlatformUserRecordService.bufferAccountingProcess(netPlatformUserRecordMessage);
        return Result.ok();
    }


}
