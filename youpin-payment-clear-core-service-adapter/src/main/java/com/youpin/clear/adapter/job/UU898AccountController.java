package com.youpin.clear.adapter.job;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.UU898UserAccountService;
import com.youpin.clear.client.request.UU898SubAccountInitRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class UU898AccountController {

    @Autowired
    private UU898UserAccountService uu898UserAccountService;

    /**
     * uu898子账户初始化
     *
     * @param request 请求
     * @return 返回
     */
    @PostMapping(value = "/job/clear/uu898/subAccount/init")
    public Result<Void> uu898SubAccountInit(@RequestBody UU898SubAccountInitRequest request) {
        uu898UserAccountService.initUserSubAccount(request);
        return Result.ok();
    }
}
