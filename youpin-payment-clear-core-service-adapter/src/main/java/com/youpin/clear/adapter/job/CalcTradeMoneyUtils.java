package com.youpin.clear.adapter.job;

import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;

import java.util.ArrayList;
import java.util.List;

public class CalcTradeMoneyUtils {

    public static ChangeMoneyDTO calcRefundTradeMoney() {
        // 获取子账户所有的子流水
        List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordDTOList = new ArrayList<>();
        //
        Integer 转出订单journalType = 99;
        Integer 转入订单journalType = 97;
        //

    }
}
