package com.youpin.clear.adapter.job;

import com.alibaba.fastjson.JSONObject;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtils {

    public static void main(String[] args) {

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        RefundResult result = calcRefundTradeMoney(firstTransferRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("firstTransferRecords1：{}", JSONObject.toJSONString(result));

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        RefundResult result2 = calcRefundTradeMoney(firstTransferRecords2, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        log.info("firstTransferRecords2：{}", JSONObject.toJSONString(result2));


        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        RefundResult refundResult3 = calcRefundTradeMoney(firstTransferRecords3, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        ;
        ;
        log.info("firstTransferRecords3：{}", JSONObject.toJSONString(refundResult3));
    }

    /**
     * 计算退款仅可交易金额及退款明细
     * 核心逻辑：按LIFO（后进先出）原则分配退款，返回退款明细
     *
     * @param subAccountFlowRecordList 子账户流水记录列表
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @return 退款结果，包含总退款金额和明细
     */
    public static RefundResult calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList, BigDecimal currentTransferOutAmount, UserAssetsTypeEnum inType, UserAssetsTypeEnum outType) {
        if (currentTransferOutAmount == null || currentTransferOutAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return new RefundResult(BigDecimal.ZERO, new ArrayList<>());
        }

        if (subAccountFlowRecordList == null || subAccountFlowRecordList.isEmpty()) {
            return new RefundResult(currentTransferOutAmount, new ArrayList<>());
        }

        // 按时间排序流水记录
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());

        // 构建转入订单的剩余可退款金额映射（按时间倒序，LIFO原则）
        Map<String, BigDecimal> transferInRemaining = new LinkedHashMap<>();
        List<String> transferInOrderList = new ArrayList<>(); // 保存转入订单的顺序

        // 先处理所有转入记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (inType.getTypeId().equals(record.getJournalType()) && record.getBalanceChange().compareTo(BigDecimal.ZERO) > 0) {
                transferInRemaining.put(record.getOrderNo(), record.getBalanceChange());
                transferInOrderList.add(record.getOrderNo());
                log.info("转入记录：订单 {} 金额 {} 元", record.getOrderNo(), record.getBalanceChange());
            }
        }

        log.info("初始转入订单剩余金额：{}", transferInRemaining);

        // 再处理历史转出记录，按FIFO原则扣减转入记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (outType.getTypeId().equals(record.getJournalType()) && record.getBalanceChange().compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal transferOutAmount = record.getBalanceChange().abs();
                log.info("处理历史转出：订单 {} 金额 {} 元", record.getOrderNo(), transferOutAmount);
                deductFromTransferIn(transferInRemaining, transferInOrderList, transferOutAmount);
            }
        }

        // 按LIFO原则分配本次退款（从最新的转入订单开始退款）
        List<RefundDetail> refundDetails = new ArrayList<>();
        BigDecimal remainingRefund = currentTransferOutAmount;

        log.info("开始分配本次退款 {} 元，按LIFO原则", currentTransferOutAmount);
        log.info("历史转出处理后，各订单剩余可退金额：{}", transferInRemaining);

        // 倒序遍历转入订单（LIFO）
        for (int i = transferInOrderList.size() - 1; i >= 0 && remainingRefund.compareTo(BigDecimal.ZERO) > 0; i--) {
            String orderNo = transferInOrderList.get(i);
            BigDecimal availableAmount = transferInRemaining.get(orderNo);

            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal refundFromThisOrder = remainingRefund.min(availableAmount);
                refundDetails.add(new RefundDetail(orderNo, refundFromThisOrder));
                remainingRefund = remainingRefund.subtract(refundFromThisOrder);

                log.info("从订单 {} 退款 {} 元，该订单剩余可退 {} 元，还需退款 {} 元",
                        orderNo, refundFromThisOrder, availableAmount, remainingRefund);
            }
        }

        // 计算总退款金额
        BigDecimal totalRefundAmount = currentTransferOutAmount.subtract(remainingRefund);

        log.info("退款分配完成，总退款：{} 元，明细：{}", totalRefundAmount, refundDetails);

        return new RefundResult(totalRefundAmount, refundDetails);
    }

    /**
     * 从转入记录中按FIFO原则扣减金额
     * 修正逻辑：确保按时间顺序（先进先出）正确扣减每个转入订单的剩余金额
     */
    private static void deductFromTransferIn(Map<String, BigDecimal> transferInRemaining,
                                             List<String> transferInOrderList,
                                             BigDecimal deductAmount) {
        BigDecimal remainingDeduct = deductAmount;

        log.info("开始FIFO扣减，扣减金额：{}，转入订单顺序：{}", deductAmount, transferInOrderList);
        log.info("扣减前各订单剩余金额：{}", transferInRemaining);

        // 按时间顺序（FIFO）扣减转入记录
        for (String orderNo : transferInOrderList) {
            if (remainingDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal availableAmount = transferInRemaining.get(orderNo);
            if (availableAmount != null && availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal deductFromThis = remainingDeduct.min(availableAmount);
                BigDecimal newAvailableAmount = availableAmount.subtract(deductFromThis);
                transferInRemaining.put(orderNo, newAvailableAmount);
                remainingDeduct = remainingDeduct.subtract(deductFromThis);

                log.info("从订单 {} 扣减：{} 元，扣减前：{} 元，扣减后：{} 元，剩余待扣：{} 元",
                        orderNo, deductFromThis, availableAmount, newAvailableAmount, remainingDeduct);
            }
        }

        log.info("FIFO扣减完成，各订单最终剩余金额：{}", transferInRemaining);
        if (remainingDeduct.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("扣减完成，但仍有 {} 元未能扣减", remainingDeduct);
        }
    }

    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

    /**
     * 退款结果类
     */
    @Data
    public static class RefundResult {
        private final BigDecimal totalRefundAmount;  // 总退款金额
        private final List<RefundDetail> refundDetails; // 退款明细

        public RefundResult(BigDecimal totalRefundAmount, List<RefundDetail> refundDetails) {
            this.totalRefundAmount = totalRefundAmount;
            this.refundDetails = refundDetails;
        }
    }

    /**
     * 退款明细类
     */
    @Data
    public static class RefundDetail {
        private final String transferInOrderNo; // 转入订单号
        private final BigDecimal refundAmount;   // 从该订单退款的金额

        public RefundDetail(String transferInOrderNo, BigDecimal refundAmount) {
            this.transferInOrderNo = transferInOrderNo;
            this.refundAmount = refundAmount;
        }

        public String getTransferInOrderNo() {
            return transferInOrderNo;
        }

        public BigDecimal getRefundAmount() {
            return refundAmount;
        }

        @Override
        public String toString() {
            return "RefundDetail{" +
                    "transferInOrderNo='" + transferInOrderNo + '\'' +
                    ", refundAmount=" + refundAmount +
                    '}';
        }
    }

    /**
     * 验证修正后的示例计算逻辑
     */
    public static void validateCorrectedExampleCalculation() {
        log.info("=== 验证修正后的示例计算逻辑 ===");

        // 第二次转出计算逻辑的测试数据（包含历史转出3元）
        List<UU898UserSubAccountFlowRecordDTO> secondTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
        );

        log.info("\n=== 第二次转出计算（转出5元）===");
        log.info("历史转出3元应按FIFO扣减：订单4(-1)，订单5(-1)，订单6(-1)");
        log.info("预期剩余可退：订单7(1)，订单8(3)，订单9(1)，订单10(1)，订单11(1)");
        log.info("预期退款明细：从订单11退1元，订单10退1元，订单9退1元，订单8退2元");

        RefundResult secondResult = calcRefundTradeMoney(secondTransferRecords, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        printRefundResult("第二次转出", secondResult);

        // 验证结果
        boolean isCorrect = validateSecondTransferOutResult(secondResult);
        log.info("第二次转出结果验证：{}", isCorrect ? "✅ 正确" : "❌ 错误");
    }

    /**
     * 验证第二次转出结果是否正确
     */
    private static boolean validateSecondTransferOutResult(RefundResult result) {
        if (result.getTotalRefundAmount().compareTo(new BigDecimal("5")) != 0) {
            log.error("总退款金额错误，期望5元，实际{}元", result.getTotalRefundAmount());
            return false;
        }

        // 检查退款明细
        List<RefundDetail> details = result.getRefundDetails();
        if (details.size() != 4) {
            log.error("退款明细数量错误，期望4条，实际{}条", details.size());
            return false;
        }

        // 验证具体明细（按LIFO顺序）
        Map<String, BigDecimal> expectedRefunds = new HashMap<>();
        expectedRefunds.put("转入订单11", new BigDecimal("1"));
        expectedRefunds.put("转入订单10", new BigDecimal("1"));
        expectedRefunds.put("转入订单9", new BigDecimal("1"));
        expectedRefunds.put("转入订单8", new BigDecimal("2"));

        for (RefundDetail detail : details) {
            BigDecimal expectedAmount = expectedRefunds.get(detail.getTransferInOrderNo());
            if (expectedAmount == null || expectedAmount.compareTo(detail.getRefundAmount()) != 0) {
                log.error("订单 {} 退款金额错误，期望{}元，实际{}元",
                        detail.getTransferInOrderNo(), expectedAmount, detail.getRefundAmount());
                return false;
            }
        }

        return true;
    }

    /**
     * 打印退款结果
     */
    private static void printRefundResult(String title, RefundResult result) {
        log.info("=== {} 结果 ===", title);
        log.info("总退款金额：{} 元", result.getTotalRefundAmount());
        log.info("退款明细：");
        for (RefundDetail detail : result.getRefundDetails()) {
            log.info("  从订单 {} 退款 {} 元", detail.getTransferInOrderNo(), detail.getRefundAmount());
        }
        log.info("==================");
    }

}
