package com.youpin.clear.adapter.job;

import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtils {

    public static void main(String[] args) {
        log.info("=== 验证简化的示例计算逻辑 ===");

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        BigDecimal result = calcRefundTradeMoney(firstTransferRecords);
        log.info("firstTransferRecords计算结果：{}", result);

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        BigDecimal result2 = calcRefundTradeMoney(firstTransferRecords2);
        log.info("firstTransferRecords2：{}", result2);


        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        BigDecimal result3 = calcRefundTradeMoney(firstTransferRecords3);
        log.info("firstTransferRecords3：{}", result3);
    }

    /**
     * 计算退款仅可交易金额
     * 核心逻辑：按时间顺序处理流水，计算每次转出时的退款金额和剩余余额
     *
     * @param subAccountFlowRecordDTOList 子账户流水记录列表
     * @return 计算结果
     */
    public static BigDecimal calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordDTOList) {

        if (subAccountFlowRecordDTOList == null || subAccountFlowRecordDTOList.isEmpty()) {
            log.warn("子账户流水记录为空，返回零值");
            return BigDecimal.ZERO;
        }
        // 只保留7天内存

        // 按时间排序流水记录
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordDTOList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());

        // 计算最后一次转出的退款金额和剩余余额
        BigDecimal totalTransferIn = BigDecimal.ZERO;  // 总转入金额
        BigDecimal totalTransferOut = BigDecimal.ZERO; // 总转出金额
        BigDecimal lastTransferOutAmount = BigDecimal.ZERO; // 最后一次转出金额

        // 按时间顺序处理每条流水记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            BigDecimal amount = record.getBalanceChange();
            if (UserAssetsTypeEnum.TYPE_97.getTypeId().equals(record.getJournalType())) {
                // 转入记录：累加转入金额
                if (amount.compareTo(BigDecimal.ZERO) > 0) {
                    totalTransferIn = totalTransferIn.add(amount);
                }
            } else if (UserAssetsTypeEnum.TYPE_99.getTypeId().equals(record.getJournalType())) {
                // 转出记录：累加转出金额，记录最后一次转出
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    // 转出金额取绝对值
                    BigDecimal transferOutAmount = amount.abs();
                    // 添加转出金额
                    totalTransferOut = totalTransferOut.add(transferOutAmount);
                    // 记录最后一次转出金额
                    lastTransferOutAmount = transferOutAmount;
                }
            }
        }
        return lastTransferOutAmount;
    }

    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

}
