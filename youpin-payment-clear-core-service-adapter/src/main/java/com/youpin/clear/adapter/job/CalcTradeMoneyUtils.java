package com.youpin.clear.adapter.job;

import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtils {

    public static void main(String[] args) {
        CalcTradeMoneyUtils.validateExampleCalculation();
    }

    /**
     * 转出订单流水类型
     */
    private static final Integer TRANSFER_OUT_JOURNAL_TYPE = 99;

    /**
     * 转入订单流水类型
     */
    private static final Integer TRANSFER_IN_JOURNAL_TYPE = 97;

    /**
     * 计算退款仅可交易金额
     *
     * @param subAccountFlowRecordDTOList 子账户流水记录列表
     * @param currentTransferOutAmount    当前转出金额（正数）
     * @return 计算结果
     */
    public static ChangeMoneyDTO calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordDTOList,
                                                      BigDecimal currentTransferOutAmount) {

        if (subAccountFlowRecordDTOList == null || subAccountFlowRecordDTOList.isEmpty()) {
            log.warn("子账户流水记录为空，返回零值");
            return ChangeMoneyDTO.builder()
                    .changeMoney(BigDecimal.ZERO)
                    .tradeChangeMoney(BigDecimal.ZERO)
                    .build();
        }

        if (currentTransferOutAmount == null || currentTransferOutAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("当前转出金额无效：{}", currentTransferOutAmount);
            return ChangeMoneyDTO.builder()
                    .changeMoney(BigDecimal.ZERO)
                    .tradeChangeMoney(BigDecimal.ZERO)
                    .build();
        }

        // 按时间排序流水记录
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = subAccountFlowRecordDTOList.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());

        // 计算仅可交易余额
        TradeBalanceCalculationResult result = calculateTradeBalance(sortedRecords, currentTransferOutAmount);

        log.info("计算完成 - 退款仅可交易金额：{}，剩余仅可交易金额：{}",
                result.getRefundTradeAmount(), result.getRemainingTradeAmount());

        return ChangeMoneyDTO.builder()
                .changeMoney(result.getRefundTradeAmount())
                .tradeChangeMoney(result.getRemainingTradeAmount())
                .build();
    }

    /**
     * 计算仅可交易余额的核心逻辑
     */
    private static TradeBalanceCalculationResult calculateTradeBalance(List<UU898UserSubAccountFlowRecordDTO> sortedRecords,
                                                                       BigDecimal currentTransferOutAmount) {

        // 存储每个转入记录的剩余可交易金额
        Map<String, BigDecimal> transferInRemaining = new LinkedHashMap<>();

        // 当前总的仅可交易余额
        BigDecimal totalTradeBalance = BigDecimal.ZERO;

        // 处理所有历史记录
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (TRANSFER_IN_JOURNAL_TYPE.equals(record.getJournalType())) {
                // 转入记录：增加仅可交易余额
                BigDecimal transferInAmount = record.getBalanceChange();
                if (transferInAmount.compareTo(BigDecimal.ZERO) > 0) {
                    transferInRemaining.put(record.getOrderNo(), transferInAmount);
                    totalTradeBalance = totalTradeBalance.add(transferInAmount);
                    log.debug("转入记录 - 订单：{}，金额：{}，总余额：{}",
                            record.getOrderNo(), transferInAmount, totalTradeBalance);
                }
            } else if (TRANSFER_OUT_JOURNAL_TYPE.equals(record.getJournalType())) {
                // 转出记录：按FIFO原则扣减仅可交易余额
                BigDecimal transferOutAmount = record.getBalanceChange().abs(); // 转出金额取绝对值
                totalTradeBalance = processTransferOut(transferInRemaining, transferOutAmount);
                log.debug("转出记录 - 订单：{}，金额：{}，总余额：{}",
                        record.getOrderNo(), transferOutAmount, totalTradeBalance);
            }
        }

        // 处理当前转出
        BigDecimal finalTradeBalance = processTransferOut(transferInRemaining, currentTransferOutAmount);

        // 计算退款仅可交易金额（当前转出金额与实际可扣减金额的差值）
        BigDecimal actualDeductedAmount = totalTradeBalance.subtract(finalTradeBalance);
        BigDecimal refundTradeAmount = currentTransferOutAmount.subtract(actualDeductedAmount);
        refundTradeAmount = refundTradeAmount.max(BigDecimal.ZERO); // 不能为负数

        return new TradeBalanceCalculationResult(refundTradeAmount, finalTradeBalance);
    }

    /**
     * 处理转出操作，按FIFO原则扣减转入记录的剩余金额
     */
    private static BigDecimal processTransferOut(Map<String, BigDecimal> transferInRemaining,
                                                 BigDecimal transferOutAmount) {

        BigDecimal remainingToDeduct = transferOutAmount;

        // 按转入时间顺序（LinkedHashMap保证顺序）扣减
        Iterator<Map.Entry<String, BigDecimal>> iterator = transferInRemaining.entrySet().iterator();

        while (iterator.hasNext() && remainingToDeduct.compareTo(BigDecimal.ZERO) > 0) {
            Map.Entry<String, BigDecimal> entry = iterator.next();
            String transferInOrderNo = entry.getKey();
            BigDecimal availableAmount = entry.getValue();

            if (availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue; // 跳过已用完的转入记录
            }

            BigDecimal deductAmount = remainingToDeduct.min(availableAmount);
            BigDecimal newAvailableAmount = availableAmount.subtract(deductAmount);

            entry.setValue(newAvailableAmount);
            remainingToDeduct = remainingToDeduct.subtract(deductAmount);

            log.debug("扣减转入记录 - 转入订单：{}，扣减金额：{}，剩余：{}，待扣减：{}",
                    transferInOrderNo, deductAmount, newAvailableAmount, remainingToDeduct);
        }

        // 计算总剩余仅可交易金额
        BigDecimal totalRemaining = transferInRemaining.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return totalRemaining;
    }

    /**
     * 重载方法：不传入当前转出金额，用于查询当前仅可交易余额
     */
    public static ChangeMoneyDTO calcCurrentTradeBalance(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordDTOList) {
        return calcRefundTradeMoney(subAccountFlowRecordDTOList, BigDecimal.ZERO);
    }

    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

    /**
     * 打印计算过程的详细信息（用于调试）
     */
    public static void printCalculationDetails(List<UU898UserSubAccountFlowRecordDTO> records,
                                               BigDecimal transferOutAmount) {
        log.info("=== 仅可交易余额计算详情 ===");
        log.info("当前转出金额：{}", transferOutAmount);
        log.info("流水记录数量：{}", records.size());

        // 按时间排序
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = records.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());

        log.info("按时间排序的流水记录：");
        for (int i = 0; i < sortedRecords.size(); i++) {
            UU898UserSubAccountFlowRecordDTO record = sortedRecords.get(i);
            String type = TRANSFER_IN_JOURNAL_TYPE.equals(record.getJournalType()) ? "转入" : "转出";
            log.info("  {}. {} - 订单：{}，时间：{}，金额：{}",
                    i + 1, type, record.getOrderNo(), record.getCreateTime(), record.getBalanceChange());
        }

        // 执行计算
        ChangeMoneyDTO result = calcRefundTradeMoney(records, transferOutAmount);
        log.info("计算结果 - 退款仅可交易：{}，剩余仅可交易：{}",
                result.getChangeMoney(), result.getTradeChangeMoney());
        log.info("=== 计算完成 ===");
    }

    /**
     * 验证您提供的示例计算逻辑
     */
    public static void validateExampleCalculation() {
        log.info("=== 验证示例计算逻辑 ===");

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1"))
        );

        log.info("第一次转出计算（转出3元）：");
        printCalculationDetails(firstTransferRecords, new BigDecimal("3"));


        // 第二次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> secondTransferRecords = new ArrayList<>(firstTransferRecords);
        secondTransferRecords.add(createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, new BigDecimal("-3")));
        secondTransferRecords.add(createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("1")));

        log.info("第二次转出计算（转出5元）：");
        printCalculationDetails(secondTransferRecords, new BigDecimal("5"));

        // 第三次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> thirdTransferRecords = new ArrayList<>(secondTransferRecords);
        thirdTransferRecords.add(createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, new BigDecimal("-5")));
        thirdTransferRecords.add(createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), TRANSFER_IN_JOURNAL_TYPE, new BigDecimal("3")));

        log.info("第三次转出计算（转出5元）：");
        printCalculationDetails(thirdTransferRecords, new BigDecimal("5"));
    }

    /**
     * 计算结果内部类
     */
    private static class TradeBalanceCalculationResult {
        private final BigDecimal refundTradeAmount;    // 退款仅可交易金额
        private final BigDecimal remainingTradeAmount; // 剩余仅可交易金额

        public TradeBalanceCalculationResult(BigDecimal refundTradeAmount, BigDecimal remainingTradeAmount) {
            this.refundTradeAmount = refundTradeAmount;
            this.remainingTradeAmount = remainingTradeAmount;
        }

        public BigDecimal getRefundTradeAmount() {
            return refundTradeAmount;
        }

        public BigDecimal getRemainingTradeAmount() {
            return remainingTradeAmount;
        }
    }
}
