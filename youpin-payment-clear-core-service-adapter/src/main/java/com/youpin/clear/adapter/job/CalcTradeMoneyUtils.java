package com.youpin.clear.adapter.job;

import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtils {

    public static void main(String[] args) {
        log.info("=== 验证简化的示例计算逻辑 ===");

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        BigDecimal result = calcRefundTradeMoney(firstTransferRecords, new BigDecimal("3"));
        log.info("firstTransferRecords计算结果：{}", result);

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        BigDecimal result2 = calcRefundTradeMoney(firstTransferRecords2, new BigDecimal("5"));
        log.info("firstTransferRecords2：{}", result2);


        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        BigDecimal result3 = calcRefundTradeMoney(firstTransferRecords3, new BigDecimal("5"));
        log.info("firstTransferRecords3：{}", result3);
    }

    /**
     * 计算退款仅可交易金额
     * 核心逻辑：可退款金额 = min(剩余可转出金额, 本次转出金额)
     * 剩余可转出金额 = 历史转入总额 - 历史转出总额
     *
     * @param subAccountFlowRecordDTOList 子账户流水记录列表（不包含本次转出）
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @return 可退款金额
     */
    public static BigDecimal calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordDTOList,
                                                 BigDecimal currentTransferOutAmount) {
        if (currentTransferOutAmount == null || currentTransferOutAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (subAccountFlowRecordDTOList == null || subAccountFlowRecordDTOList.isEmpty()) {
            return currentTransferOutAmount; // 没有历史记录，全部可退款
        }

        BigDecimal historyTransferIn = BigDecimal.ZERO;   // 历史转入总额
        BigDecimal historyTransferOut = BigDecimal.ZERO;  // 历史转出总额

        // 统计历史转入和转出金额
        for (UU898UserSubAccountFlowRecordDTO record : subAccountFlowRecordDTOList) {
            BigDecimal amount = record.getBalanceChange();

            if (UserAssetsTypeEnum.TYPE_97.getTypeId().equals(record.getJournalType())) {
                // 转入记录：累加历史转入金额
                if (amount.compareTo(BigDecimal.ZERO) > 0) {
                    historyTransferIn = historyTransferIn.add(amount);
                }
            } else if (UserAssetsTypeEnum.TYPE_99.getTypeId().equals(record.getJournalType())) {
                // 转出记录：累加历史转出金额
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    historyTransferOut = historyTransferOut.add(amount.abs());
                }
            }
        }

        // 计算剩余可转出金额 = 历史转入总额 - 历史转出总额
        BigDecimal remainingTransferable = historyTransferIn.subtract(historyTransferOut);
        remainingTransferable = remainingTransferable.max(BigDecimal.ZERO); // 不能为负数

        // 计算可退款金额 = min(剩余可转出金额, 本次转出金额)
        BigDecimal refundAmount = remainingTransferable.min(currentTransferOutAmount);

        log.debug("计算结果 - 历史转入：{}，历史转出：{}，本次转出：{}，剩余可转出：{}，可退款：{}",
                historyTransferIn, historyTransferOut, currentTransferOutAmount, remainingTransferable, refundAmount);

        return refundAmount;
    }

    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

}
