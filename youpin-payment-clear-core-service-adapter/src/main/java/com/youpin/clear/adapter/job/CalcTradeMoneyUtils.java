package com.youpin.clear.adapter.job;

import com.alibaba.fastjson.JSONObject;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CalcTradeMoneyUtils {

    public static void main(String[] args) {

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords = Arrays.asList(
                createFlowRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3"))
        );

        // 执行计算
        RefundResult result = calcRefundTradeMoney(firstTransferRecords, new BigDecimal("3"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        log.info("firstTransferRecords1：{}", JSONObject.toJSONString(result));

        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords2 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1"))
//                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );

        // 执行计算
        RefundResult result2 = calcRefundTradeMoney(firstTransferRecords2, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        log.info("firstTransferRecords2：{}", JSONObject.toJSONString(result2));


        // 第一次转出计算逻辑的测试数据
        List<UU898UserSubAccountFlowRecordDTO> firstTransferRecords3 = Arrays.asList(
                createFlowRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3")),
                createFlowRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-3")),
                createFlowRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("1")),
                createFlowRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5")),
                createFlowRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), new BigDecimal("3"))
//                createFlowRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), new BigDecimal("-5"))
        );
        // 执行计算
        RefundResult refundResult3 = calcRefundTradeMoney(firstTransferRecords3, new BigDecimal("5"), UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        ;
        ;
        ;
        log.info("firstTransferRecords3：{}", JSONObject.toJSONString(refundResult3));
    }

    /**
     * 计算退款仅可交易金额及退款明细
     *
     * <p><strong>算法逻辑：</strong></p>
     * <ul>
     *   <li>1. 历史转出按FIFO（先进先出）原则扣减转入记录</li>
     *   <li>2. 本次退款按LIFO（后进先出）原则从剩余转入记录中分配</li>
     *   <li>3. 返回详细的退款明细，包含每笔转入订单的退款金额</li>
     * </ul>
     *
     * <p><strong>示例：</strong></p>
     * <pre>
     * 转入订单：[订单4:1元, 订单5:1元, 订单6:1元, 订单7:1元, 订单8:3元, 订单9:1元, 订单10:1元]
     * 历史转出：无
     * 本次转出：3元
     * 退款明细：订单10退1元, 订单9退1元, 订单8退1元
     * </pre>
     *
     * @param subAccountFlowRecordList 子账户流水记录列表（不包含本次转出）
     * @param currentTransferOutAmount 本次转出金额（正数）
     * @param inType                   转入流水类型枚举
     * @param outType                  转出流水类型枚举
     * @return 退款结果，包含总退款金额和明细
     */
    public static RefundResult calcRefundTradeMoney(List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList,
                                                    BigDecimal currentTransferOutAmount,
                                                    UserAssetsTypeEnum inType,
                                                    UserAssetsTypeEnum outType) {
        // 1. 参数校验
        if (!isValidTransferOutAmount(currentTransferOutAmount)) {
            return createEmptyRefundResult();
        }

        if (isEmptyFlowRecords(subAccountFlowRecordList)) {
            return createFullRefundResult(currentTransferOutAmount);
        }

        // 2. 数据预处理：按时间排序流水记录
        List<UU898UserSubAccountFlowRecordDTO> sortedRecords = sortFlowRecordsByTime(subAccountFlowRecordList);

        // 3. 构建转入订单映射表
        TransferInOrderMap transferInOrderMap = buildTransferInOrderMap(sortedRecords, inType);

        // 4. 处理历史转出：按FIFO原则扣减转入记录
        processHistoryTransferOut(sortedRecords, transferInOrderMap, outType);

        // 5. 分配本次退款：按LIFO原则分配退款明细
        List<RefundDetail> refundDetails = allocateRefundByLIFO(transferInOrderMap, currentTransferOutAmount);

        // 6. 计算总退款金额并返回结果
        BigDecimal totalRefundAmount = calculateTotalRefundAmount(refundDetails);

        return new RefundResult(totalRefundAmount, refundDetails);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 校验转出金额是否有效
     */
    private static boolean isValidTransferOutAmount(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查流水记录是否为空
     */
    private static boolean isEmptyFlowRecords(List<UU898UserSubAccountFlowRecordDTO> records) {
        return records == null || records.isEmpty();
    }

    /**
     * 创建空的退款结果
     */
    private static RefundResult createEmptyRefundResult() {
        return new RefundResult(BigDecimal.ZERO, new ArrayList<>());
    }

    /**
     * 创建全额退款结果（没有历史记录时）
     */
    private static RefundResult createFullRefundResult(BigDecimal amount) {
        return new RefundResult(amount, new ArrayList<>());
    }

    /**
     * 按时间排序流水记录
     */
    private static List<UU898UserSubAccountFlowRecordDTO> sortFlowRecordsByTime(List<UU898UserSubAccountFlowRecordDTO> records) {
        return records.stream()
                .sorted(Comparator.comparing(UU898UserSubAccountFlowRecordDTO::getCreateTime))
                .collect(Collectors.toList());
    }

    /**
     * 构建转入订单映射表
     */
    private static TransferInOrderMap buildTransferInOrderMap(List<UU898UserSubAccountFlowRecordDTO> sortedRecords, UserAssetsTypeEnum inType) {
        Map<String, BigDecimal> transferInRemaining = new LinkedHashMap<>();
        List<String> transferInOrderList = new ArrayList<>();

        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (inType.getTypeId().equals(record.getJournalType()) &&
                    record.getBalanceChange().compareTo(BigDecimal.ZERO) > 0) {
                transferInRemaining.put(record.getOrderNo(), record.getBalanceChange());
                transferInOrderList.add(record.getOrderNo());
            }
        }

        return new TransferInOrderMap(transferInRemaining, transferInOrderList);
    }

    /**
     * 处理历史转出记录，按FIFO原则扣减转入记录
     */
    private static void processHistoryTransferOut(List<UU898UserSubAccountFlowRecordDTO> sortedRecords,
                                                  TransferInOrderMap transferInOrderMap,
                                                  UserAssetsTypeEnum outType) {
        for (UU898UserSubAccountFlowRecordDTO record : sortedRecords) {
            if (outType.getTypeId().equals(record.getJournalType()) && record.getBalanceChange().compareTo(BigDecimal.ZERO) < 0) {
                BigDecimal transferOutAmount = record.getBalanceChange().abs();
                deductFromTransferInByFIFO(transferInOrderMap, transferOutAmount);
            }
        }
    }

    /**
     * 按LIFO原则分配退款明细
     */
    private static List<RefundDetail> allocateRefundByLIFO(TransferInOrderMap transferInOrderMap,
                                                           BigDecimal currentTransferOutAmount) {
        List<RefundDetail> refundDetails = new ArrayList<>();
        BigDecimal remainingRefund = currentTransferOutAmount;

        // 倒序遍历转入订单（LIFO原则）
        List<String> orderList = transferInOrderMap.getTransferInOrderList();
        Map<String, BigDecimal> remainingMap = transferInOrderMap.getTransferInRemaining();

        for (int i = orderList.size() - 1; i >= 0 && remainingRefund.compareTo(BigDecimal.ZERO) > 0; i--) {
            String orderNo = orderList.get(i);
            BigDecimal availableAmount = remainingMap.get(orderNo);

            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal refundFromThisOrder = remainingRefund.min(availableAmount);
                refundDetails.add(new RefundDetail(orderNo, refundFromThisOrder));
                remainingRefund = remainingRefund.subtract(refundFromThisOrder);
            }
        }

        return refundDetails;
    }

    /**
     * 计算总退款金额
     */
    private static BigDecimal calculateTotalRefundAmount(List<RefundDetail> refundDetails) {
        return refundDetails.stream()
                .map(RefundDetail::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 从转入记录中按FIFO原则扣减金额
     */
    private static void deductFromTransferInByFIFO(TransferInOrderMap transferInOrderMap, BigDecimal deductAmount) {
        BigDecimal remainingDeduct = deductAmount;
        Map<String, BigDecimal> remainingMap = transferInOrderMap.getTransferInRemaining();
        List<String> orderList = transferInOrderMap.getTransferInOrderList();

        for (String orderNo : orderList) {
            if (remainingDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal availableAmount = remainingMap.get(orderNo);
            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal deductFromThis = remainingDeduct.min(availableAmount);
                remainingMap.put(orderNo, availableAmount.subtract(deductFromThis));
                remainingDeduct = remainingDeduct.subtract(deductFromThis);
            }
        }
    }

    /**
     * 创建测试用的流水记录
     */
    public static UU898UserSubAccountFlowRecordDTO createFlowRecord(String orderNo,
                                                                    LocalDateTime createTime,
                                                                    Integer journalType,
                                                                    BigDecimal balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(balanceChange)
                .build();
    }

    // ==================== 内部数据结构 ====================

    /**
     * 转入订单映射表
     * 用于管理转入订单的剩余金额和顺序
     */
    private static class TransferInOrderMap {
        private final Map<String, BigDecimal> transferInRemaining;
        private final List<String> transferInOrderList;

        public TransferInOrderMap(Map<String, BigDecimal> transferInRemaining, List<String> transferInOrderList) {
            this.transferInRemaining = transferInRemaining;
            this.transferInOrderList = transferInOrderList;
        }

        public Map<String, BigDecimal> getTransferInRemaining() {
            return transferInRemaining;
        }

        public List<String> getTransferInOrderList() {
            return transferInOrderList;
        }
    }

    /**
     * 退款结果类
     */
    @Data
    public static class RefundResult {
        /**
         * 总退款金额
         */
        private final BigDecimal totalRefundAmount;
        /**
         * 退款明细
         */
        private final List<RefundDetail> refundDetails;

        public RefundResult(BigDecimal totalRefundAmount, List<RefundDetail> refundDetails) {
            this.totalRefundAmount = totalRefundAmount;
            this.refundDetails = refundDetails;
        }
    }

    /**
     * 退款明细类
     */
    @Data
    public static class RefundDetail {
        /**
         * 转入订单号
         */
        private final String transferInOrderNo;
        /**
         * 从该订单退款的金额
         */
        private final BigDecimal refundAmount;

        public RefundDetail(String transferInOrderNo, BigDecimal refundAmount) {
            this.transferInOrderNo = transferInOrderNo;
            this.refundAmount = refundAmount;
        }
    }

}
