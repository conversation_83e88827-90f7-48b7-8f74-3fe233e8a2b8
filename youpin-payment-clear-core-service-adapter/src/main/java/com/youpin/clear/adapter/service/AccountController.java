package com.youpin.clear.adapter.service;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.app.service.UserAccountService;
import com.youpin.clear.client.request.CheckAccountRequest;
import com.youpin.clear.client.request.OpenAccountRequest;
import com.youpin.clear.client.request.UserAccountInfoRequest;
import com.youpin.clear.client.response.CheckAccountResponse;
import com.youpin.clear.client.response.UserAccountInfoResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/service/account")
public class AccountController {


    @Autowired
    UserAccountService userAccountService;

    /**
     * 开户
     */
    @PostMapping(value = "/user/open")
    public Result<Void> openAccount(@Valid @RequestBody OpenAccountRequest request) {
        userAccountService.createUserAccount(request);
        return Result.ok();
    }

    /**
     * 账户同步初始化
     */
    @PostMapping(value = "/sync/net/init")
    public Result<Void> syncUserAccount(@Valid @RequestBody OpenAccountRequest request) {
        userAccountService.syncUserAccount(request);
        return Result.ok();
    }

    /**
     * 账户校验
     */
    @PostMapping(value = "/check")
    public Result<CheckAccountResponse> checkUserAccount(@Valid @RequestBody CheckAccountRequest request) {
        Boolean checkUserAccount = userAccountService.checkUserAccount(request);
        return Result.ok(new CheckAccountResponse(checkUserAccount, request.getUserId()));
    }

    /**
     * 账户校验并修正v2
     */
    @PostMapping(value = "/check/v2")
    public Result<CheckAccountResponse> checkUserAccountV2(@Valid @RequestBody CheckAccountRequest request) {
        Boolean checkUserAccount = userAccountService.checkUserAccount2(request);
        return Result.ok(new CheckAccountResponse(checkUserAccount, request.getUserId()));
    }

    /**
     * 批量账户校验
     */
    @PostMapping(value = "/batch/check")
    public Result<List<CheckAccountResponse>> checkBatchUserAccount(@Valid @RequestBody CheckAccountRequest request) {
        List<CheckAccountResponse> responses = userAccountService.checkBatchUserAccount(request);
        return Result.ok(responses);
    }


    /**
     * 查询账户信息
     */
    @PostMapping(value = "/query")
    public Result<UserAccountInfoResponse> queryUserAccount(@Valid @RequestBody UserAccountInfoRequest request) {
        return Result.ok(userAccountService.queryUserAccount(request));
    }

    /**
     * 账户补全
     *
     * @param request 请求
     * @return 返回
     */
    @PostMapping(value = "/user/completion")
    public Result<Void> completionUserAccount(@Valid @RequestBody OpenAccountRequest request) {
        userAccountService.completionUserAccount(request);
        return Result.ok();
    }


    /**
     * 初始化可用余额1账户金额
     */
    @PostMapping(value = "/user/withdrawMoneyTemp1/init")
    public Result<Void> initUserWithdrawMoney(@Valid @RequestBody OpenAccountRequest request) {
        userAccountService.initUserWithdrawMoney(request);
        return Result.ok();
    }
}
