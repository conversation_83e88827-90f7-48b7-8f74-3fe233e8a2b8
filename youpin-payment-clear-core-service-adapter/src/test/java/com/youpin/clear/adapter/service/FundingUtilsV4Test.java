package com.youpin.clear.adapter.service;

import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优化后的资金对冲工具类测试
 */
public class FundingUtilsV4Test {
    
    @Test
    @DisplayName("第一次转出3元测试")
    public void testFirstTransferOut() {
        // 构建测试数据：只有转入记录，没有转出记录
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1")
        );
        
        Map<String, BigDecimal> result = FundingUtilsV4.fundHedging(records, new BigDecimal("3"), 
                UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        
        // 验证结果：按LIFO原则，应该从订单10、9、8各退1元
        assertEquals(3, result.size());
        assertEquals(new BigDecimal("1"), result.get("转入订单10"));
        assertEquals(new BigDecimal("1"), result.get("转入订单9"));
        assertEquals(new BigDecimal("1"), result.get("转入订单8"));
        
        // 验证总退款金额
        BigDecimal totalRefund = result.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("3"), totalRefund);
    }
    
    @Test
    @DisplayName("第二次转出5元测试")
    public void testSecondTransferOut() {
        // 构建测试数据：包含第一次转出记录
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1")
        );
        
        Map<String, BigDecimal> result = FundingUtilsV4.fundHedging(records, new BigDecimal("5"), 
                UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        
        // 验证结果：历史转出3元已按LIFO扣减（订单10、9、8各扣1元）
        // 剩余可退：订单4(1)，订单5(1)，订单6(1)，订单7(1)，订单8(2)，订单11(1)
        // 本次转出5元按LIFO分配：从订单11退1元，订单8退2元，订单7退1元，订单6退1元
        assertEquals(4, result.size());
        assertEquals(new BigDecimal("1"), result.get("转入订单11"));
        assertEquals(new BigDecimal("2"), result.get("转入订单8"));
        assertEquals(new BigDecimal("1"), result.get("转入订单7"));
        assertEquals(new BigDecimal("1"), result.get("转入订单6"));
        
        // 验证总退款金额
        BigDecimal totalRefund = result.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("5"), totalRefund);
    }
    
    @Test
    @DisplayName("第三次转出5元测试")
    public void testThirdTransferOut() {
        // 构建测试数据：包含前两次转出记录
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), "-5"),
            createRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "3")
        );
        
        Map<String, BigDecimal> result = FundingUtilsV4.fundHedging(records, new BigDecimal("5"), 
                UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        
        // 验证结果：历史转出8元已按LIFO扣减完大部分转入记录
        // 只剩下订单12(3元)可以退款，所以只能退3元
        assertEquals(1, result.size());
        assertEquals(new BigDecimal("3"), result.get("转入订单12"));
        
        // 验证总退款金额（只能退3元，剩余2元无法退款）
        BigDecimal totalRefund = result.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("3"), totalRefund);
    }
    
    @Test
    @DisplayName("边界情况测试 - 空记录列表")
    public void testEmptyRecordList() {
        Map<String, BigDecimal> result = FundingUtilsV4.fundHedging(Arrays.asList(), new BigDecimal("100"), 
                UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        
        assertTrue(result.isEmpty());
    }
    
    @Test
    @DisplayName("边界情况测试 - 转出金额为零")
    public void testZeroTransferOutAmount() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单1", LocalDateTime.of(2025, 1, 1, 0, 0), UserAssetsTypeEnum.TYPE_97.getTypeId(), "100")
        );
        
        Map<String, BigDecimal> result = FundingUtilsV4.fundHedging(records, BigDecimal.ZERO, 
                UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        
        assertTrue(result.isEmpty());
    }
    
    @Test
    @DisplayName("边界情况测试 - 只有转出记录")
    public void testOnlyTransferOutRecords() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转出订单1", LocalDateTime.of(2025, 1, 1, 0, 0), UserAssetsTypeEnum.TYPE_99.getTypeId(), "-50")
        );
        
        Map<String, BigDecimal> result = FundingUtilsV4.fundHedging(records, new BigDecimal("30"), 
                UserAssetsTypeEnum.TYPE_97, UserAssetsTypeEnum.TYPE_99);
        
        // 没有转入记录，无法退款
        assertTrue(result.isEmpty());
    }
    
    /**
     * 创建测试记录
     */
    private UU898UserSubAccountFlowRecordDTO createRecord(String orderNo, LocalDateTime createTime, 
                                                         Integer journalType, String balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(new BigDecimal(balanceChange))
                .build();
    }
}
