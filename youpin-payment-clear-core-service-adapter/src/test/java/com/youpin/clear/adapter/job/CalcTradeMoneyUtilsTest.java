package com.youpin.clear.adapter.job;

import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 仅可交易余额计算工具类测试
 */
public class CalcTradeMoneyUtilsTest {
    
    private static final Integer TRANSFER_IN_JOURNAL_TYPE = 97;
    private static final Integer TRANSFER_OUT_JOURNAL_TYPE = 99;
    
    @Test
    @DisplayName("第一次转出计算逻辑测试")
    public void testFirstTransferOutCalculation() {
        // 构建测试数据：转入订单4-10
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1")
        );
        
        // 转出3元
        BigDecimal transferOutAmount = new BigDecimal("3");
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, transferOutAmount);
        
        // 验证结果
        // 总转入：8元，转出3元，剩余5元
        // 按FIFO：转入订单4(1) + 转入订单5(1) + 转入订单6(1) = 3元被转出
        // 剩余：转入订单7(1) + 转入订单8(3) + 转入订单9(1) + 转入订单10(1) = 6元
        assertEquals(new BigDecimal("0"), result.getChangeMoney()); // 退款仅可交易为0（全部可以从转入中扣除）
        assertEquals(new BigDecimal("5"), result.getTradeChangeMoney()); // 剩余仅可交易为5元
    }
    
    @Test
    @DisplayName("第二次转出计算逻辑测试")
    public void testSecondTransferOutCalculation() {
        // 构建测试数据：包含第一次转出后的所有记录
        List<UU898UserSubAccountFlowRecordDTO> records = new ArrayList<>(Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1")
        ));
        
        // 转出5元
        BigDecimal transferOutAmount = new BigDecimal("5");
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, transferOutAmount);
        
        // 验证结果
        // 历史转入总计：9元，历史转出：3元，历史剩余：6元
        // 当前转出5元，剩余1元，所以退款仅可交易为0，剩余仅可交易为1元
        assertEquals(new BigDecimal("0"), result.getChangeMoney()); // 退款仅可交易为0
        assertEquals(new BigDecimal("1"), result.getTradeChangeMoney()); // 剩余仅可交易为1元
    }
    
    @Test
    @DisplayName("第三次转出计算逻辑测试")
    public void testThirdTransferOutCalculation() {
        // 构建测试数据：包含前两次转出后的所有记录
        List<UU898UserSubAccountFlowRecordDTO> records = new ArrayList<>(Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-5"),
            createRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), TRANSFER_IN_JOURNAL_TYPE, "3")
        ));
        
        // 转出5元
        BigDecimal transferOutAmount = new BigDecimal("5");
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, transferOutAmount);
        
        // 验证结果
        // 历史转入总计：12元，历史转出：8元，历史剩余：4元
        // 当前转出5元，但只有4元可用，所以退款仅可交易为1元，剩余仅可交易为0元
        assertEquals(new BigDecimal("1"), result.getChangeMoney()); // 退款仅可交易为1元
        assertEquals(new BigDecimal("0"), result.getTradeChangeMoney()); // 剩余仅可交易为0元
    }
    
    @Test
    @DisplayName("边界情况测试 - 空记录列表")
    public void testEmptyRecordList() {
        List<UU898UserSubAccountFlowRecordDTO> emptyRecords = new ArrayList<>();
        BigDecimal transferOutAmount = new BigDecimal("100");
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(emptyRecords, transferOutAmount);
        
        assertEquals(BigDecimal.ZERO, result.getChangeMoney());
        assertEquals(BigDecimal.ZERO, result.getTradeChangeMoney());
    }
    
    @Test
    @DisplayName("边界情况测试 - 转出金额为零")
    public void testZeroTransferOutAmount() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单1", LocalDateTime.of(2025, 1, 1, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "100")
        );
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, BigDecimal.ZERO);
        
        assertEquals(BigDecimal.ZERO, result.getChangeMoney());
        assertEquals(BigDecimal.ZERO, result.getTradeChangeMoney());
    }
    
    @Test
    @DisplayName("边界情况测试 - 只有转出记录")
    public void testOnlyTransferOutRecords() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转出订单1", LocalDateTime.of(2025, 1, 1, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-50")
        );
        BigDecimal transferOutAmount = new BigDecimal("30");
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records, transferOutAmount);
        
        // 没有转入记录，所以全部转出金额都应该作为退款仅可交易
        assertEquals(new BigDecimal("30"), result.getChangeMoney());
        assertEquals(BigDecimal.ZERO, result.getTradeChangeMoney());
    }
    
    @Test
    @DisplayName("查询当前仅可交易余额测试")
    public void testCurrentTradeBalance() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单1", LocalDateTime.of(2025, 1, 1, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "100"),
            createRecord("转出订单1", LocalDateTime.of(2025, 1, 2, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-30")
        );
        
        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcCurrentTradeBalance(records);
        
        // 转入100，转出30，剩余70
        assertEquals(BigDecimal.ZERO, result.getChangeMoney());
        assertEquals(new BigDecimal("70"), result.getTradeChangeMoney());
    }
    
    /**
     * 创建测试用的流水记录
     */
    private UU898UserSubAccountFlowRecordDTO createRecord(String orderNo, LocalDateTime createTime, 
                                                         Integer journalType, String balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(new BigDecimal(balanceChange))
                .build();
    }
}
