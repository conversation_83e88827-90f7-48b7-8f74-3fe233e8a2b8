package com.youpin.clear.adapter.job;

import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的仅可交易余额计算工具类测试
 */
public class CalcTradeMoneyUtilsTest {

    private static final Integer TRANSFER_IN_JOURNAL_TYPE = 97;
    private static final Integer TRANSFER_OUT_JOURNAL_TYPE = 99;

    @Test
    @DisplayName("第一次转出计算逻辑测试 - 退款3元")
    public void testFirstTransferOutCalculation() {
        // 构建测试数据：包含最后一次转出-3元
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3")
        );

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);

        // 验证结果：最后一次转出3元，所以退款仅可交易为3元
        // 总转入8元，总转出3元，剩余5元
        assertEquals(new BigDecimal("3"), result.getChangeMoney()); // 退款仅可交易为3元
        assertEquals(new BigDecimal("5"), result.getTradeChangeMoney()); // 剩余仅可交易为5元
    }
    
    @Test
    @DisplayName("第二次转出计算逻辑测试 - 退款5元")
    public void testSecondTransferOutCalculation() {
        // 构建测试数据：包含最后一次转出-5元
        List<UU898UserSubAccountFlowRecordDTO> records = new ArrayList<>(Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-5")
        ));

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);

        // 验证结果：最后一次转出5元，所以退款仅可交易为5元
        // 总转入9元，总转出8元，剩余1元
        assertEquals(new BigDecimal("5"), result.getChangeMoney()); // 退款仅可交易为5元
        assertEquals(new BigDecimal("1"), result.getTradeChangeMoney()); // 剩余仅可交易为1元
    }
    
    @Test
    @DisplayName("第三次转出计算逻辑测试 - 退款3元")
    public void testThirdTransferOutCalculation() {
        // 构建测试数据：包含最后一次转出-3元
        List<UU898UserSubAccountFlowRecordDTO> records = new ArrayList<>(Arrays.asList(
            createRecord("转入订单4", LocalDateTime.of(2025, 1, 4, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单5", LocalDateTime.of(2025, 1, 5, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单6", LocalDateTime.of(2025, 1, 6, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单7", LocalDateTime.of(2025, 1, 7, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单8", LocalDateTime.of(2025, 1, 8, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转入订单9", LocalDateTime.of(2025, 1, 9, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转入订单10", LocalDateTime.of(2025, 1, 10, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单10", LocalDateTime.of(2025, 1, 10, 12, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3"),
            createRecord("转入订单11", LocalDateTime.of(2025, 1, 11, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "1"),
            createRecord("转出订单12", LocalDateTime.of(2025, 1, 12, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-5"),
            createRecord("转入订单12", LocalDateTime.of(2025, 1, 12, 12, 0), TRANSFER_IN_JOURNAL_TYPE, "3"),
            createRecord("转出订单12-2", LocalDateTime.of(2025, 1, 12, 18, 0), TRANSFER_OUT_JOURNAL_TYPE, "-3")
        ));

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);

        // 验证结果：最后一次转出3元，所以退款仅可交易为3元
        // 总转入12元，总转出11元，剩余1元
        assertEquals(new BigDecimal("3"), result.getChangeMoney()); // 退款仅可交易为3元
        assertEquals(new BigDecimal("1"), result.getTradeChangeMoney()); // 剩余仅可交易为1元
    }
    
    @Test
    @DisplayName("边界情况测试 - 空记录列表")
    public void testEmptyRecordList() {
        List<UU898UserSubAccountFlowRecordDTO> emptyRecords = new ArrayList<>();

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(emptyRecords);

        assertEquals(BigDecimal.ZERO, result.getChangeMoney());
        assertEquals(BigDecimal.ZERO, result.getTradeChangeMoney());
    }

    @Test
    @DisplayName("边界情况测试 - 只有转入记录")
    public void testOnlyTransferInRecords() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单1", LocalDateTime.of(2025, 1, 1, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "100")
        );

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);

        // 只有转入，没有转出，所以退款为0，剩余为100
        assertEquals(BigDecimal.ZERO, result.getChangeMoney());
        assertEquals(new BigDecimal("100"), result.getTradeChangeMoney());
    }

    @Test
    @DisplayName("边界情况测试 - 只有转出记录")
    public void testOnlyTransferOutRecords() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转出订单1", LocalDateTime.of(2025, 1, 1, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-50")
        );

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);

        // 只有转出50，没有转入，所以退款为50，剩余为0
        assertEquals(new BigDecimal("50"), result.getChangeMoney());
        assertEquals(BigDecimal.ZERO, result.getTradeChangeMoney());
    }

    @Test
    @DisplayName("正常情况测试 - 转入转出混合")
    public void testMixedTransferRecords() {
        List<UU898UserSubAccountFlowRecordDTO> records = Arrays.asList(
            createRecord("转入订单1", LocalDateTime.of(2025, 1, 1, 0, 0), TRANSFER_IN_JOURNAL_TYPE, "100"),
            createRecord("转出订单1", LocalDateTime.of(2025, 1, 2, 0, 0), TRANSFER_OUT_JOURNAL_TYPE, "-30")
        );

        ChangeMoneyDTO result = CalcTradeMoneyUtils.calcRefundTradeMoney(records);

        // 转入100，最后转出30，所以退款为30，剩余为70
        assertEquals(new BigDecimal("30"), result.getChangeMoney());
        assertEquals(new BigDecimal("70"), result.getTradeChangeMoney());
    }
    
    /**
     * 创建测试用的流水记录
     */
    private UU898UserSubAccountFlowRecordDTO createRecord(String orderNo, LocalDateTime createTime, 
                                                         Integer journalType, String balanceChange) {
        return UU898UserSubAccountFlowRecordDTO.builder()
                .orderNo(orderNo)
                .createTime(createTime)
                .journalType(journalType)
                .balanceChange(new BigDecimal(balanceChange))
                .build();
    }
}
