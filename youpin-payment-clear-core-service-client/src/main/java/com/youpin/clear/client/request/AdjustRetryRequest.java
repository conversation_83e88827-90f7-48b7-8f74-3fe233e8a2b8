package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustRetryRequest {

    /**
     * 申请单id
     */
    @NotNull(message = "申请单id为空")
    private Long applyId;

    /**
     * 操作人
     */
    private String operator;
}
