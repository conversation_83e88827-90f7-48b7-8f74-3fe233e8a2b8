package com.youpin.clear.client.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchAuditDetailResponse {

    /**
     * 调增余额统计信息，单位万
     */
    private String addTotalAmount;

    /**
     * 调减余额统计信息，单位万
     */
    private String subTotalAmount;

    /**
     * 调增余额统计数量
     */
    private Integer totalCount;

    /**
     * 调账来源描述
     */
    private String adjustSourceDesc;

    /**
     * 调账来源
     */
    private Integer adjustSource;
}
