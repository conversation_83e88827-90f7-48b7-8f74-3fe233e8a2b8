package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdjustApplyImportRequest {
    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 调账类型
     */
    private Integer adjustSource;

    /**
     * 增加余额调账
     * 【是】【否】
     */
    private Boolean addBalance;

    /**
     * 增加用户id
     */
    private Long addUserId;

    /**
     * 增加资金明细类型
     */
    private Long addAssetType;

    /**
     * 增加调账支付类型
     */
    private Integer addPayChannel;

    /**
     * 增加调账金额（元）
     */
    private BigDecimal addAmount;

    /**
     * 增加调账账户
     */
    private Integer addAccountType;

    /**
     * 调增手续费
     */
    private BigDecimal addServiceFee;

    /**
     * 减少余额调账
     * 【是】【否】
     */
    private Boolean subBalance;

    /**
     * 减少用户id
     */
    private Long subUserId;

    /**
     * 减少资金明细类型
     */
    private Long subAssetType;

    /**
     * 减少调
     */
    private Integer subPayChannel;

    /**
     * 减少调账金额（元）
     */
    private BigDecimal subAmount;

    /**
     * 减少调账账户类型
     */
    private Integer subAccountType;

    /**
     * 减少调手续费
     */
    private BigDecimal subServiceFee;

    /**
     * 备注
     */
    private String remark;
}
