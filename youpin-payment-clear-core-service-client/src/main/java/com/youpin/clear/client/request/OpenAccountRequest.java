package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenAccountRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;


}
