package com.youpin.clear.client.request.financial;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PayFinancialRequest extends BaseFinancialRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 流水号
     */
    @NotNull(message = "流水号不能为空")
    private String serialNo;

    /**
     * 资金明细
     */
    @NotEmpty(message = "资金明细不能为空")
    private List<ClearAssetInfoRequest> assetInfoList;



}
