package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustAccountRequest {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细类型
     */
    private Long assetType;


    /**
     * 调账支付类型
     */
    private Integer accountPayChannelType;

    /**
     * 手续费
     */
    private BigDecimal serviceFee;

    /**
     * 调账金额明细列表
     */
    private List<AdjustApplyItemRequest> itemList;
}
