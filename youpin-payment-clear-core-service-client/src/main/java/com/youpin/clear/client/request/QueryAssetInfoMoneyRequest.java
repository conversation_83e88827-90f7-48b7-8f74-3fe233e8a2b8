package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryAssetInfoMoneyRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 卖家ID
     */
    @NotNull(message = "卖家ID不能为空")
    private Long sellerId;


    /**
     * 买家Id
     */
    @NotNull(message = "买家Id不能为空")
    private Long buyerId;


    /**
     * 0, 默认
     * 1. 补贴
     */
    private Integer typeCode;


}
