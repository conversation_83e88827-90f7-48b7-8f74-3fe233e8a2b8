package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompensationRecordRetryJobRequest implements Serializable {
    private LocalDateTime currentTime;
    private Integer maxRetryCount;
    private Integer shardTotal;
    private Integer shardIndex;
    private Long pageIndex;
    private Long pageSize;
    private Integer bizScene;

    private Long id;

    /**
     * 重试策略  [10,60,300,...] 重试间隔，单位秒
     */
    private List<Integer> retryIntervalPolicy;

}
