package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckAccountRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否重新同步账户
     */
    private Boolean syncAccountFlag;

    /**
     * 是否删除资金明细 默认删除
     */
    private Boolean deleteUserAssetsRecordFlag = true;


    private Long userIdMin;

    private Long userIdMax;

    private Integer easy = 0;


}
