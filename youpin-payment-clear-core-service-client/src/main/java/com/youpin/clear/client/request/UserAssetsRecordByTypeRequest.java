package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserAssetsRecordByTypeRequest implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * typeId
     */
    @NotNull(message = "typeIdList不能为空")
    private List<Integer> typeIdList;

    private Integer pageSize;

    private LocalDateTime endTime;

    private Long maxId;


}
