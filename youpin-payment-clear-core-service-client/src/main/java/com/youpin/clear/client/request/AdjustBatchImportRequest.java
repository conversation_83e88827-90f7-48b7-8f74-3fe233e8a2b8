package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustBatchImportRequest {

    @NotNull
    private List<AdjustApplyImportRequest> importList;

    @NotNull
    private String batchNo;

    @NotNull
    private String operator;
}
