package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FinancialStatementJobRequest implements Serializable {

    private Long lastId;
    private Long startId;
    private Long endId;


    private LocalDateTime startTime;
    private LocalDateTime endTime;

    private Long pageIndex;
    private Long pageSize;


    private Long id;

}
