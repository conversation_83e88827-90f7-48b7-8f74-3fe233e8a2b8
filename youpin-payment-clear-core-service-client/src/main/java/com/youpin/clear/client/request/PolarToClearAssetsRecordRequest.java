package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolarToClearAssetsRecordRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    //页码
    private Long pageIndex;

    private Integer pageSize = 100;

    private List<Integer> typeIdList;

    private Long maxUserAssetsRecordId = 0L;

    /**
     * 是否判断最大值 0 判断 1 不判断 默认 0
     */
    private Integer maxIdFlagInt = 0;

    private String startTime;

    private String endTime;

}
