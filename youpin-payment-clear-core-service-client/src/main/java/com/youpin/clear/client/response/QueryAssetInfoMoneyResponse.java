package com.youpin.clear.client.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryAssetInfoMoneyResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否有数据 false 没有  true有
     */
    private Boolean hasData = false;

    private List<AssetInfoMoney> assetInfoMoneyList;
}