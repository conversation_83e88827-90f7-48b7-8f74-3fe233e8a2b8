package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompensationRecordDeleteRequest implements Serializable {

    private String createTime;
    private Integer maxRetryCount;
    private Integer pageSize = 100;
    private Integer pageNum = 1;
    /**
     * 默认不删除
     */
    private Boolean deleteMq = false;

    /**
     * 处理状态，1：初始化；2：推送中；3：推送成功；4：推送失败
     */
    private Integer handleStatus = -1;
}
