package com.youpin.clear.client.request;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class BalanceStrategy {

    /**
     * 策略编码
     * <p>
     * NONE=无
     * RATE=比例
     * SORT=优先级
     *
     * @see com.youpin.clear.client.enums.BalanceStrategyEnum
     */
    private String code;

    /**
     * 策略信息
     */
    private String content;

    /**
     * 比例策略
     */
    @Data
    public static class RateStrategyContent {
        /**
         * 金额map
         * key: 账户类型 (1可提现 2冻结 3可交易金额)
         * value: 比例
         */
        private Map<Integer, Long> amountMap;
    }

    /**
     * 优先级策略
     */
    @Data
    public static class SortStrategyContent {
        /**
         * 子账户类型list
         * <p>
         * 1=可交易
         * 2=可提现
         * </p>
         */
        private List<Integer> typeList;
    }
}
