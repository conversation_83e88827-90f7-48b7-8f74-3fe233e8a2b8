package com.youpin.clear.client.request.financial;

import com.youpin.clear.client.request.BalanceStrategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClearAssetInfoRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long userId;

    /**
     * 金额（单位分）
     */
    private Long money;


    private Integer typeId;

    /**
     * 渠道 DoNetPayChannelEnum
     */
    private Integer payChannel;

    /**
     * 余额变动策略
     */
    private BalanceStrategy balanceStrategy;
}
