package com.youpin.clear.client.api;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.client.request.*;
import com.youpin.clear.client.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;


/**
 *
 */
@FeignClient(name = "youpin-payment-clear-core-service", contextId = "youpin-payment-clear-core-service-job")
public interface ClearAccountJobFeign {

    /**
     * 统计总数
     */
    @PostMapping(value = "/job/clear/compensation-order/count")
    Result<CompensationRecordRetryJobResponse> countExceptionRetryJob(@RequestBody CompensationRecordRetryJobRequest request);

    /**
     * 分页查询
     */
    @PostMapping(value = "/job/clear/compensation-order/select")
    Result<CompensationRecordRetryJobResponse> selectExceptionRetryJob(@RequestBody CompensationRecordRetryJobRequest request);

    /**
     * 处理
     */
    @PostMapping(value = "/job/clear/compensation-order/handle")
    Result<Void> handle(@RequestBody CompensationRecordRetryJobRequest request);

    /**
     * 账户同步初始化
     */
    @PostMapping(value = "/service/account/sync/net/init")
    Result<Void> syncUserAccount(@Valid @RequestBody OpenAccountRequest request);

    /**
     * 校验账户信息
     */
    @PostMapping(value = "/service/account/check")
    Result<CheckAccountResponse> checkUserAccount(@Valid @RequestBody CheckAccountRequest request);

    /**
     * 批量账户校验
     */
    @PostMapping(value = "service/account/batch/check")
    Result<List<CheckAccountResponse>> checkBatchUserAccount(@Valid @RequestBody CheckAccountRequest request);

    /**
     * 查询账户信息
     */
    @PostMapping(value = "/service/account/query")
    Result<UserAccountInfoResponse> queryUserAccount(@Valid @RequestBody UserAccountInfoRequest request);

    /**
     * 根据 添加时间 统计资金明细总数
     */
    @PostMapping(value = "/job/clear/financial-statement/user/assets/record/date/query/count")
    Result<FinancialStatementJobResponse> countUserAssetsRecordByAddTime(@RequestBody FinancialStatementJobRequest request);

    /**
     * 根据 添加时间 分页资金明细ID
     */
    @PostMapping(value = "/job/clear/financial-statement/user/assets/record/date/query/page")
    Result<FinancialStatementJobResponse> selectUserAssetsRecordByAddTimePage(@RequestBody FinancialStatementJobRequest request);

    /**
     * 根据 添加时间 区间查询资金明细ID
     */
    @PostMapping(value = "/job/clear/financial-statement/user/assets/record/id/interval/query/page")
    Result<FinancialStatementJobResponse> selectUserAssetsRecordByIdInterval(@RequestBody FinancialStatementJobRequest request);

    /**
     * 查询资金明细最大ID
     */
    @PostMapping(value = "/job/clear/financial-statement/user/assets/record/max/id/query")
    Result<FinancialStatementJobResponse> selectUserAssetsRecordMaxId();

    /**
     * 处理资金明细ID
     */
    @PostMapping(value = "/job/clear/financial-statement/user/assets/record/date/handle/id")
    Result<Void> handleUserAssetsRecordId(@RequestBody FinancialStatementJobRequest request);


    /**
     * 资金同步
     */
    @PostMapping(value = "/job/clear/user-assetsRecord/polar/to/handle")
    Result<Long> polarToClearAssetsRecord(@RequestBody PolarToClearAssetsRecordRequest request);

    /**
     * tag 同步
     */
    @PostMapping(value = "/job/clear/user-assetsRecord/tag/sync")
    Result<Integer> assetsTagSync(@RequestBody ClearAssetsTagRequest request);

    /**
     * maxUserAssetsRecordId
     */
    @PostMapping(value = "/job/clear/user-assetsRecord/maxUserAssetsRecordId")
    Result<Long> maxUserAssetsRecordId(@RequestBody PolarToClearAssetsRecordRequest request);

    /**
     * serviceFeeStatementSelectMaxId
     */
    @PostMapping(value = "/job/clear/serviceFee-statement/selectMaxId")
    Result<TransactionServiceFeeStatementResponse> serviceFeeStatementSelectMaxId();

    /**
     * 生成批次号
     */
    @PostMapping(value = "/job/clear/serviceFee-statement/updateSerialNo")
    Result<TransactionServiceFeeStatementResponse> updateSerialNo(@RequestBody TransactionServiceFeeStatementRequest request);

    /**
     * 处理
     */
    @PostMapping(value = "/job/clear/serviceFee-statement/handle")
    Result<Void> handleByJobId(@RequestBody TransactionServiceFeeStatementRequest request);

    /**
     * 用户余额1转余额2处理
     */
    @PostMapping(value = "/job/clear/serviceFee-statement/money1/to/money2")
    Result<Void> money1ToMoney2();

    /**
     * 服务费财务汇总
     */
    @PostMapping(value = "/job/clear/serviceFee-statement/finance/summary")
    Result<Void> financeSummary(@RequestBody TransactionServiceFeeStatementRequest request);

    /**
     * selectMaxId
     */
    @PostMapping(value = "/job/clear/user-assetsRecord/net/bufferAccountingProcess/selectMaxId")
    Result<UserAssetsRecordByTypeResponse> uu898UserAssetsRecordNetBufferAccountingMaxId(@Valid @RequestBody UserAssetsRecordByTypeRequest request);

    /**
     * net缓冲记账-过度-核对处理流程
     */
    @PostMapping(value = "/job/clear/user-assetsRecord/net/bufferAccountingProcess")
    Result<Void> uu898UserAssetsRecordNetBufferAccountingProcess(@Valid @RequestBody UserAssetsRecordByTypeRequest request);


    /**
     * 缓冲记账-分页查询
     */
    @PostMapping(value = "/job/clear/platform-account-record/selectPage")
    Result<PlatformAccountRecordJobResponse> selectPageSizeByStatusAndCreateTime(@RequestBody PlatformAccountRecordJobRequest request);

    /**
     * 缓冲记账-处理
     */
    @PostMapping(value = "/job/clear/platform-account-record/handle")
    Result<Void> platformAccountRecordHandle(@RequestBody PlatformAccountRecordJobRequest request);


    /**
     * 账户补全
     *
     * @param request 请求
     * @return 返回
     */
    @PostMapping(value = "/service/account/user/completion")
    Result<Void> completionUserAccount(@Valid @RequestBody OpenAccountRequest request);

    /**
     * uu898子账户初始化
     *
     * @param request 请求
     * @return 返回
     */
    @PostMapping(value = "/job/clear/uu898/subAccount/init")
    Result<Void> uu898SubAccountInit(@RequestBody UU898SubAccountInitRequest request);

}
