package com.youpin.clear.client.response.financial;

import com.youpin.clear.client.response.AssetInfoMoney;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettleResponse {

    private List<AssetInfoMoney> assetInfoMoneyList;
}
