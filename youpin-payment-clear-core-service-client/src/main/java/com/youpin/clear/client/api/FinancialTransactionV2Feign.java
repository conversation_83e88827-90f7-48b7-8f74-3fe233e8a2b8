package com.youpin.clear.client.api;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.client.request.financial.PayFinancialRequest;
import com.youpin.clear.client.request.financial.RefundFinancialRequest;
import com.youpin.clear.client.request.financial.SettlementFinancialRequest;
import com.youpin.clear.client.response.financial.SettleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "youpin-payment-clear-core-service", contextId = "youpin-payment-clear-core-service-financial-v2")
public interface FinancialTransactionV2Feign {

    /**
     * 支付
     */
    @PostMapping("/service/financial/transaction/v2/pay")
    Result<Void> pay(@Valid @RequestBody PayFinancialRequest request);

    /**
     * 退款
     */
    @PostMapping("/service/financial/transaction/v2/refund")
    Result<Void> refund(@Valid @RequestBody RefundFinancialRequest request);

    /**
     * 结算
     */
    @PostMapping("/service/financial/transaction/v2/settlement")
    Result<SettleResponse> settlement(@Valid @RequestBody SettlementFinancialRequest request);

    /**
     * 特殊结算
     */
    @PostMapping("/service/financial/transaction/v2/specialSettlement")
    Result<SettleResponse> specialSettlement(@Valid @RequestBody SettlementFinancialRequest request);
}

