package com.youpin.clear.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


@Getter
@AllArgsConstructor
public enum ClearUserAssetsTagEnum {
    ACCUMULATE_RECHARGE("accumulateRecharge", "累计充值"),
    ACCUMULATE_WITHDRAWAL("accumulateWithdrawal", "累计提现"),
    ;

    private final String tagCode;
    private final String tagName;

    private static final Map<String, ClearUserAssetsTagEnum> MAP = new HashMap<>();

    static {
        for (ClearUserAssetsTagEnum item : ClearUserAssetsTagEnum.values()) {
            MAP.put(item.getTagCode(), item);
        }
    }

    public static ClearUserAssetsTagEnum getCodeEnum(String name) {
        if (name == null || !MAP.containsKey(name)) {
            return null;
        }
        return MAP.get(name);
    }
}
