package com.youpin.clear.client.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustApplyResponse {

    /**
     * 申请id
     */
    private Long applyId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 增加余额用户id
     */
    private Long incUserId;

    /**
     * 增加调账余额（元）
     */
    private String incAmount;

    /**
     * 增加余额支付类型
     */
    private String incAccountPayChannelStr;

    /**
     * 减少余额用户id
     */
    private Long decUserId;

    /**
     * 减少调账余额（元）
     */
    private String decAmount;

    /**
     * 减少余额支付类型
     */
    private String decAccountPayChannelStr;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 审核状态
     */
    private Integer applyStatus;

    /**
     * 调账状态
     */
    private String applyStatusStr;

    /**
     * 申请人
     */
    private String applyBy;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 调账来源描述
     */
    private String adjustSourceDesc;

    /**
     * 调账来源
     */
    private Integer adjustSource;

    /**
     * 调账批次号
     */
    private String batchNo;

    /**
     * 备注
     */
    private String applyRemark;

    /**
     * 订单重复标志
     */
    private Boolean repeat;
}
