package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformAccountRecordJobRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer shardIndex;
    private Integer status;
    private LocalDateTime createTime;
    private Integer pageSize;


    private Long id;


    private Long userId;


}