package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionServiceFeeStatementRequest implements Serializable {

    private Integer pageSize;
    private String serialNo;
    private Long maxId;

    private LocalDateTime nowDate;


    /**
     * 平台户ID
     */
    private Long platformUserId;


}
