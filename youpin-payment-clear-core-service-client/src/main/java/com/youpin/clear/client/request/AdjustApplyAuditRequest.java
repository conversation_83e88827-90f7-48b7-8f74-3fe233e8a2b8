package com.youpin.clear.client.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Data
public class AdjustApplyAuditRequest {

    /**
     * 申请单id
     */
    @NotNull
    private Long applyId;

    /**
     * 审核类型 1:通过 2:拒绝
     */
    @NotNull
    private Integer auditType;

    /**
     * 审核备注
     */
    @Size(min = 1, max = 500)
    private String auditRemark;

    /**
     * 验证码
     */
    @NotNull
    private String verifyCode;

    /**
     * 审核人
     */
    private String auditBy;
}
