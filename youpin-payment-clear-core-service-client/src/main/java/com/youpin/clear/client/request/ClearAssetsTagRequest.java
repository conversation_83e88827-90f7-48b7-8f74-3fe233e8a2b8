package com.youpin.clear.client.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClearAssetsTagRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private String tableSuffix;

    private Long userId;

    private Long pageIndex;

    private Integer pageSize = 100;

    private Long minId;

    private Long redisKeySuffix;

    private String tagCode;
}
