package com.youpin.clear.client.api;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.client.request.QueryAssetInfoMoneyRequest;
import com.youpin.clear.client.request.financial.PayFinancialRequest;
import com.youpin.clear.client.request.financial.QueryFinancialRequest;
import com.youpin.clear.client.request.financial.RefundFinancialRequest;
import com.youpin.clear.client.request.financial.SettlementFinancialRequest;
import com.youpin.clear.client.response.QueryAssetInfoMoneyResponse;
import com.youpin.clear.client.response.financial.SettleResponse;
import com.youpin.clear.client.response.financial.UserAssetsRecordResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "youpin-payment-clear-core-service", contextId = "youpin-payment-clear-core-service-financial", path = "/service/financial/transaction")
public interface FinancialTransactionFeign {

    /**
     * 支付
     */
    @PostMapping("/pay")
    Result<Void> pay(@Valid @RequestBody PayFinancialRequest request);

    /**
     * 退款
     */
    @PostMapping("/refund")
    Result<Void> refund(@Valid @RequestBody RefundFinancialRequest request);

    /**
     * 结算
     */
    @PostMapping("/settlement")
    Result<SettleResponse> settlement(@Valid @RequestBody SettlementFinancialRequest request);

    /**
     * 特殊结算
     */
    @PostMapping("/specialSettlement")
    Result<SettleResponse> specialSettlement(@Valid @RequestBody SettlementFinancialRequest request);


    /**
     * 查询
     */
    @PostMapping("/query")
    Result<List<UserAssetsRecordResponse>> query(@Valid @RequestBody QueryFinancialRequest request);

    /**
     * 查询订单用户资金金额x
     */
    @PostMapping("/query/assetInfo/money")
    Result<QueryAssetInfoMoneyResponse> queryAssetInfoMoney(@Valid @RequestBody QueryAssetInfoMoneyRequest request);
}

