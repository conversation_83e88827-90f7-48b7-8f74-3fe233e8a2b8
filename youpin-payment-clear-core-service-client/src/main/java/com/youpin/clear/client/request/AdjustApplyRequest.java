package com.youpin.clear.client.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Data
public class AdjustApplyRequest {

    /**
     * 关联订单号
     */
    @NotNull(message = "关联订单号为空")
    private String relatedOrderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 调账来源
     */
    private Integer adjustSource;

    /**
     * 增加余额用户调账信息
     */
    private AdjustAccountRequest incAccountInfo;

    /**
     * 减少余额用户调账信息
     */
    private AdjustAccountRequest decAccountInfo;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 申请人
     */
    private String applyBy;

    /**
     * 批次号
     */
    private String batchNo;

}
