package com.youpin.clear.client.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustmentApplyConfigResponse {
    /**
     * 收入类型-资金明细类型
     */
    private List<AssetTypeInfo> incomeAssetTypeList;

    /**
     * 支出类型-资金明细类型
     */
    private List<AssetTypeInfo> expenseAssetTypeList;

    /**
     * 支付类型
     */
    private List<PayChannelTypeInfo> payChannelTypeInfoList;

    /**
     * 账户类型信息
     */
    private List<AccountTypeInfo> accountTypeInfoList;

    /**
     * 状态类型信息
     */
    private List<ApplyStatusInfo> applyStatusInfoList;

    /**
     * 调账来源
     */
    private List<AdjustSourceInfo> adjustSourceInfoList;

    /**
     * 单笔金额上限，单位分
     */
    private Long singleAdjustmentAmountLimit;

    /**
     * 单日金额上限，单位分
     */
    private Long singleDayAmountLimit;

    /**
     * 批量导入模版地址
     */
    private String importTemplateUrl;
}
