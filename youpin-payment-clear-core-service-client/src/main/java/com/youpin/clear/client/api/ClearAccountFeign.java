package com.youpin.clear.client.api;


import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.client.request.UserAccountInfoRequest;
import com.youpin.clear.client.response.UserAccountInfoResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "youpin-payment-clear-core-service", contextId = "youpin-payment-clear-core-service-account")
public interface ClearAccountFeign {

    /**
     * 查询账户信息
     */
    @PostMapping(value = "/service/account/query")
    Result<UserAccountInfoResponse> queryUserAccount(@Valid @RequestBody UserAccountInfoRequest request);
}
