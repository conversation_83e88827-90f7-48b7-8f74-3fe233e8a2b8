package com.youpin.clear.client.response.financial;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsRecordResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资金明细主键ID
     */
    private Long id;

    /**
     * 所属用户的唯一标识
     */
    private Long userId;

    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;

    /**
     * 当前资金明细记录的唯一单号
     */
    private String treadNo;

    /**
     * 变化资产类型，1.账户余额，10.积分
     */
    private Integer assetType;

    /**
     * 变动之前的账户余额，含义随AssetType字段而变化
     */
    private BigDecimal money;

    /**
     * 本次变动数额，含义随AssetType字段而变化
     */
    private BigDecimal thisMoney;

    /**
     * 变动后的账户余额，含义随AssetType字段而变化
     */
    private BigDecimal afterMoney;

    /**
     * 服务费金额
     */
    private BigDecimal chargeMoney;

    /**
     * 变动前的冻结金额
     */
    private BigDecimal blockMoney;

    /**
     * 本次冻结金额
     */
    private BigDecimal thisBlockMoney;

    /**
     * 变动后冻结金额
     */
    private BigDecimal afterBlockMoney;

    /**
     * 变动前的求购金额
     */
    private BigDecimal purchaseMoney;

    /**
     * 本次变动求购金额
     */
    private BigDecimal thisPurchaseMoney;

    /**
     * 变动后的求购金额
     */
    private BigDecimal afterPurchaseMoney;

    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;


    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 特性，二进制保存，从低位至高位依次为：余额是否变动
     */
    private Integer attr;

    /**
     * 状态，0.失败，1.成功，2.进行中
     */
    private Integer status;

    /**
     * 支付渠道：0.余额，1.微信，2.支付宝，3.积分，4额度，5押金卡，6临时额度，7固定额度，8求购余额，9支付宝原路退还 15易宝
     */
    private Integer payChannel;

    /**
     * 提现支付宝账号
     */
    private String accountName;

    /**
     * 支付等待时间
     */
    private LocalDateTime payWaitExpireTime;
    
}
