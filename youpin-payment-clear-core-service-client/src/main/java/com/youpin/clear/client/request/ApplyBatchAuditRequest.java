package com.youpin.clear.client.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApplyBatchAuditRequest {

    /**
     * 申请单id列表
     */
    @NotNull
    private List<Long> applyIds;

    /**
     * 调账类型
     */
    @NotNull
    private Integer adjustSource;

    /**
     * 审核备注
     */
    @Size(min = 1, max = 500)
    private String auditRemark;

    /**
     * 审核类型 1:通过 2:拒绝
     */
    @NotNull
    private Integer auditType;

    /**
     * 操作人
     */
    @NotNull
    private String operator;

    /**
     * 验证码
     */
    private String verifyCode;
}
