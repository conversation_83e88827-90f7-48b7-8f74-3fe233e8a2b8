package com.youpin.clear.client.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AdjustApplyListQueryRequest {

    /**
     * 申请时间-起始
     */
    @NotNull(message = "起始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 申请时间-结束
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 增加余额用户id
     */
    private Long incUserId;

    /**
     * 减少余额用户id
     */
    private Long decUserId;

    /**
     * 增加余额支付类型
     */
    private Integer incAccountPayChannel;

    /**
     * 减少余额支付类型
     */
    private Integer decAccountPayChannel;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 调账状态
     * 1:申请中 2：审核拒绝 3:审核处理中 4:处理成功 5:处理失败
     */
    private Integer status;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 增加余额资金明细类型
     */
    private Long incAssetType;

    /**
     * 减少余额资金明细类型
     */
    private Long decAssetType;

    /**
     * 申请人
     */
    private String applyBy;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 调账来源
     */
    private Integer adjustSource;

    /**
     * 调账批次号
     */
    private String batchNo;

    /**
     * 页码
     */
    @NotNull
    @Min(value = 1, message = "页码最小为1")
    private Long page;

    /**
     * 分页大小
     */
    @NotNull
    @Max(value = 500, message = "最大分页大小为500")
    @Min(value = 1, message = "最小分页大小为1")
    private Long pageSize;

    private Long offset;
}
