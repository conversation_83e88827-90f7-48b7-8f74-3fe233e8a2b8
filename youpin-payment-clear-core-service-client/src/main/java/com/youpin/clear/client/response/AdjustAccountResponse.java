package com.youpin.clear.client.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustAccountResponse {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细类型
     */
    private Long assetType;

    /**
     * 资金明细类型文案
     */
    private String assetTypeStr;

    /**
     * 调账支付类型
     */
    private Integer accountPayChannelType;

    /**
     * 调账支付类型
     */
    private String accountPayChannelStr;

    /**
     * 手续费
     */
    private String serviceFee;

    /**
     * 调账金额明细列表
     */
    private List<AdjustApplyItemResponse> itemList;
}
