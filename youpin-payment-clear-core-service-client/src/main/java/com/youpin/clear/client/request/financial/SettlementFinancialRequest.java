package com.youpin.clear.client.request.financial;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SettlementFinancialRequest extends BaseFinancialRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 流水号
     */
    @NotNull(message = "流水号不能为空")
    private String serialNo;

    /**
     * 资金是否硬处理 默认不硬处理
     */
    private Boolean hardProcess = false;


    /**
     * 结算账户类型 0.1
     */
    private Integer settleAccountType;

    /**
     * 资金明细
     */
    private List<ClearAssetInfoRequest> assetInfoList;
}