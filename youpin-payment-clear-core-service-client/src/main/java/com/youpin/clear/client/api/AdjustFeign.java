package com.youpin.clear.client.api;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.client.request.*;
import com.youpin.clear.client.response.AdjustApplyListResponse;
import com.youpin.clear.client.response.AdjustDetailResponse;
import com.youpin.clear.client.response.AdjustmentApplyConfigResponse;
import com.youpin.clear.client.response.BatchAuditDetailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;


/**
 * 调账相关接口
 *
 * <AUTHOR>
 * @eo.api-type http
 * @eo.groupName 调账相关接口
 * @eo.path /adjust
 */
@FeignClient(name = "youpin-payment-clear-core-service", contextId = "youpin-payment-clear-core-service-adjust", path = "/adjust")
public interface AdjustFeign {


    @PostMapping("/query/list")
    Result<AdjustApplyListResponse> queryList(@Valid @RequestBody AdjustApplyListQueryRequest request);

    /**
     * 提交申请
     *
     * @param request
     * @return
     * @eo.name 提交申请
     * @eo.url /submitApply
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/submitApply")
    Result<Long> submitApply(@RequestBody AdjustApplyRequest request);


    /**
     * 发送短信验证码
     *
     * @param request
     * @return
     * @eo.name 发送短信验证码
     * @eo.url /send/verifyCode
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/send/verifyCode")
    Result<Void> sendVerifyCode(@RequestBody ApplySendVerifyCodeRequest request);


    /**
     * 调账详情查询
     *
     * @param applyId
     * @return
     * @eo.name 调账详情查询
     * @eo.url /query/detail
     * @eo.method get
     * @eo.request-type formdata
     */
    @GetMapping("/query/detail")
    Result<AdjustDetailResponse> queryDetail(@RequestParam("applyId") Long applyId);

    /**
     * 调账审核
     *
     * @param request
     * @return
     * @eo.name 调账审核
     * @eo.url /audit
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/audit")
    Result<Void> applyAudit(@RequestBody AdjustApplyAuditRequest request);

    /**
     * 查询类型配置信息
     *
     * @return
     * @eo.name 查询类型配置信息
     * @eo.url /query/typeConfig
     * @eo.method post
     * @eo.request-type json
     */
    @PostMapping("/query/typeConfig")
    Result<AdjustmentApplyConfigResponse> queryApplyTypeConfig();


    @GetMapping("/handle")
    Result<Void> handle();

    @PostMapping("/import")
    Result<Void> adjustApplyImport(@RequestBody @Valid AdjustBatchImportRequest request);

    @PostMapping("/revoke")
    Result<Void> applyRevoke(@RequestBody @Valid ApplyRevokeRequest request);

    @PostMapping("/batch/audit/detail")
    Result<BatchAuditDetailResponse> batchAuditDetail(@RequestBody @Valid ApplyBatchQueryRequest request);

    @PostMapping("/batch/audit")
    Result<Void> batchApplyAudit(@RequestBody @Valid ApplyBatchAuditRequest request);

    @PostMapping("/update/status")
    Result<Void> updateStatus(@RequestBody AdjustUpdateStatusRequest request);

    @PostMapping("/retry")
    Result<Boolean> adjustRetry(@RequestBody @Valid AdjustRetryRequest request);

}
