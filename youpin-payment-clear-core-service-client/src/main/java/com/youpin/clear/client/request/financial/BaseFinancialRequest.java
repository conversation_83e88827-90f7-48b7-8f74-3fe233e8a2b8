package com.youpin.clear.client.request.financial;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseFinancialRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /***
     * 商户id
     * @eo.required
     */
    @NotNull(message = "商户id不能为空")
    private Integer merchantId;

    /***
     * 币种
     */
    private String currency = "CNY";


    /**
     * 业务类型：出租，出售
     */
    private Integer businessType;

    /**
     * 子业务类型
     */
    @NotNull(message = "子业务类型不能为空")
    private Integer subBusType;

    /**
     * 收单类型 0,默认  1(自有) 2(监管)
     */
    private Integer collectType;

    /**
     * 状态 @NetStatusEnum
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
