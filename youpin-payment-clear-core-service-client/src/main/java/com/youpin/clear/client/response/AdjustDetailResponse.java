package com.youpin.clear.client.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustDetailResponse {

    private Long applyId;
    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 调账申请单号
     */
    private String applyNo;

    /**
     * 调账状态文案
     */
    private String applyStatusStr;

    /**
     * 调账状态
     */
    private Integer applyStatus;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 申请人
     */
    private String applyBy;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 增加余额用户调账信息
     */
    private AdjustAccountResponse incAccountInfo;

    /**
     * 减少余额用户调账信息
     */
    private AdjustAccountResponse decAccountInfo;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 调账来源描述
     */
    private String adjustSourceDesc;

    /**
     * 调账来源
     */
    private Integer adjustSource;

    /**
     * 批次号
     */
    private String batchNo;
}
