package com.youpin.clear.client.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum BalanceStrategyEnum {

    NONE("NONE", "默认"),
    RATE("RATE", "比例"),
    SORT("SORT", "优先级"),
    ;


    private final String code;

    private final String desc;

    BalanceStrategyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private static Map<String, BalanceStrategyEnum> map = new HashMap<>();

    static {
        for (BalanceStrategyEnum value : BalanceStrategyEnum.values()) {
            map.put(value.code, value);
        }
    }

    public static BalanceStrategyEnum getByCode(String code) {
        return map.get(code);
    }
}
