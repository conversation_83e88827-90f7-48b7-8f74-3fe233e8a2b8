<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <!-- 全局配置参数，需要时再设置 -->
    <settings>
        <!-- <setting name="logImpl" value="STDOUT_LOGGING" />-->
        <!-- <setting name="logImpl" value="slf4j" />-->
        <setting name="cacheEnabled" value="true"/>
        <!-- 当开启时，任何方法的调用都会加载该对象的所有属性。默认 false，可通过select标签的 fetchType来覆盖-->
        <setting name="aggressiveLazyLoading" value="false"/>
        <setting name="jdbcTypeForNull" value="NULL"/>
        <!-- 使用驼峰命名法转换字段。 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- mybatis.configuration.map-underscore-to-camel-case=true-->
    </settings>

    <mappers>
    </mappers>

</configuration>