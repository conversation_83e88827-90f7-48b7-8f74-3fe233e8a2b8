<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">

        <!-- 自动检查关键字，为关键字增加反引号 -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!--覆盖生成XML文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <!-- 带上序列化接口 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 整合lombok-->
        <plugin type="org.mybatis.generator.plugins.LombokPlugin">
            <property name="hasLombok" value="true"/>
        </plugin>

        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>

        <!-- 自定义的注释生成插件-->
        <plugin type="org.mybatis.generator.plugins.CommentPlugin">
        </plugin>

        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>


        <!-- 配置数据源，需要根据自己的项目修改 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="************************************************************************************************"
                        userId="uu898"
                        password="Zzfw8HP5">
        </jdbcConnection>


        <!-- domain类的位置 -->
        <javaModelGenerator targetProject="src/main/java"
                            targetPackage="com.youpin.clear.infrastructure.dataobject.uu898"/>

        <!-- mapper xml的位置 -->
        <sqlMapGenerator targetProject="src/main/resources"
                         targetPackage="mapper"/>

        <!-- mapper类的位置 -->
        <javaClientGenerator targetProject="src/main/java"
                             targetPackage="com.youpin.clear.infrastructure.mapper.uu898"
                             type="XMLMAPPER"/>


        <table tableName="UserAssetsRecord" domainObjectName="UU898UserAssetsRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="UserId" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="AddTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="CompleteTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="PayWaitExpireTime" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="UserAssetsInfo" domainObjectName="UU898UserAssetsInfo"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <columnOverride column="UpdateTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="UserId" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="LastUserRecordId" javaType="java.lang.Long" jdbcType="BIGINT"/>
        </table>

        <table tableName="UserAssetsRecordType" domainObjectName="UU898UserAssetsRecordType"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <columnOverride column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        </table>


        <table tableName="tb_caiwu_daylog" domainObjectName="UU898TbCaiwuDaylog"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <columnOverride column="ddate" javaType="java.time.LocalDate" />
            <columnOverride column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
        </table>

        <table tableName="NewOrderPayPurchaseExtend" domainObjectName="UU898NewOrderPayPurchaseExtend"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="UserId" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="AddTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="CompleteTime" javaType="java.time.LocalDateTime"/>
        </table>


        <table tableName="uu898_user_sub_account" domainObjectName="UserSubAccount"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
<!--            <property name="useActualColumnNames" value="true"/>-->
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="uu898_user_sub_account_flow_record" domainObjectName="UserSubAccountFlowRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
<!--            <property name="useActualColumnNames" value="true"/>-->
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>


        <table tableName="OrderPayMoneyInfo" domainObjectName="UU898OrderPayMoneyInfo"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <property name="useActualColumnNames" value="true"/>
            <generatedKey column="Id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="Id" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="UserId" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="AddTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="UpdateTime" javaType="java.time.LocalDateTime"/>
        </table>

    </context>
</generatorConfiguration>
