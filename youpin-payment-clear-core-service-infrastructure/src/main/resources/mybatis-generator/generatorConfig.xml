<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="MySql" targetRuntime="MyBatis3" defaultModelType="flat">

        <!-- 自动检查关键字，为关键字增加反引号 -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!--覆盖生成XML文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <!-- 带上序列化接口 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 整合lombok-->
        <plugin type="org.mybatis.generator.plugins.LombokPlugin">
            <property name="hasLombok" value="true"/>
        </plugin>

        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>

        <!-- 自定义的注释生成插件-->
        <plugin type="org.mybatis.generator.plugins.CommentPlugin">
        </plugin>

        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!-- 配置数据源，需要根据自己的项目修改 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="**********************************************************************************************************"
                        userId="uu898"
                        password="Zzfw8HP5">
        </jdbcConnection>

        <!-- domain类的位置 -->
        <javaModelGenerator targetProject="src/main/java"
                            targetPackage="com.youpin.clear.infrastructure.dataobject"/>

        <!-- mapper xml的位置 -->
        <sqlMapGenerator targetProject="src/main/resources"
                         targetPackage="mapper"/>

        <!-- mapper类的位置 -->
        <javaClientGenerator targetProject="src/main/java"
                             targetPackage="com.youpin.clear.infrastructure.mapper"
                             type="XMLMAPPER"/>


        <table tableName="user_account" domainObjectName="UserAccount"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">

            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="user_account_record" domainObjectName="UserAccountRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">

            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="finish_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="user_account_record_order_relate" domainObjectName="UserAccountRecordOrderRelate"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">

            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="compensation_record" domainObjectName="CompensationRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="next_retry_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="user_account_reconciliation_record" domainObjectName="UserAccountReconciliationRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="account_assets_type" domainObjectName="AccountAssetsType"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="delete_flag" javaType="java.lang.Integer"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="account_assets_type_relate" domainObjectName="AccountAssetsTypeRelate"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="adjustment_apply" domainObjectName="AdjustmentApply"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="audit_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="adjustment_apply_detail" domainObjectName="AdjustmentApplyDetail"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="user_assets_record" domainObjectName="UserAssetsRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">

            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="add_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="complete_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="pay_wait_expire_time" javaType="java.time.LocalDateTime"/>
        </table>


        <table tableName="user_assets_order_no_relate" domainObjectName="UserAssetsOrderNoRelate"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="user_assets_pay_order_no_relate" domainObjectName="UserAssetsPayOrderNoRelate"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="user_assets_tread_no_relate" domainObjectName="UserAssetsTreadNoRelate"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>


        <table tableName="user_assets_tag" domainObjectName="UserAssetsTag"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="transaction_service_fee_statement_record"
               domainObjectName="TransactionServiceFeeStatementRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="complete_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="transaction_service_fee_operate_record"
               domainObjectName="TransactionServiceFeeOperateRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="platform_account_record" domainObjectName="PlatformAccountRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>

        <table tableName="external_request_record" domainObjectName="ExternalRequestRecord"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="create_time" javaType="java.time.LocalDateTime"/>
            <columnOverride column="update_time" javaType="java.time.LocalDateTime"/>
        </table>


    </context>
</generatorConfiguration>

