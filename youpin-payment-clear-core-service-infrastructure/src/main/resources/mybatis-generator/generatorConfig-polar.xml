<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">

        <!-- 自动检查关键字，为关键字增加反引号 -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!--覆盖生成XML文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <!-- 带上序列化接口 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 整合lombok-->
        <plugin type="org.mybatis.generator.plugins.LombokPlugin">
            <property name="hasLombok" value="true"/>
        </plugin>

        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>

        <!-- 自定义的注释生成插件-->
        <plugin type="org.mybatis.generator.plugins.CommentPlugin">
        </plugin>

        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>


        <!-- 配置数据源，需要根据自己的项目修改 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*******************************************************************************************************************************************************************"
                        userId="u_test"
                        password="User@Assets">
        </jdbcConnection>

        <!-- domain类的位置 -->
        <javaModelGenerator targetProject="src/main/java"
                            targetPackage="com.youpin.clear.infrastructure.dataobject.polar"/>

        <!-- mapper xml的位置 -->
        <sqlMapGenerator targetProject="src/main/resources"
                         targetPackage="mapper"/>

        <!-- mapper类的位置 -->
        <javaClientGenerator targetProject="src/main/java"
                             targetPackage="com.youpin.clear.infrastructure.mapper.polar"
                             type="XMLMAPPER"/>

        <table tableName="UserAssetsRecord" domainObjectName="UserAssetsRecordPolar"
               enableSelectByPrimaryKey="true"
               enableDeleteByPrimaryKey="false"
               enableUpdateByPrimaryKey="false"
               enableInsert="false"
               enableSelectByExample="false"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" selectByExampleQueryId="false"
        >
            <property name="useActualColumnNames" value="true"/>
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
            <columnOverride column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="UserId" javaType="java.lang.Long" jdbcType="BIGINT"/>
            <columnOverride column="AddTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="CompleteTime" javaType="java.time.LocalDateTime"/>
            <columnOverride column="PayWaitExpireTime" javaType="java.time.LocalDateTime"/>
        </table>


    </context>
</generatorConfiguration>
