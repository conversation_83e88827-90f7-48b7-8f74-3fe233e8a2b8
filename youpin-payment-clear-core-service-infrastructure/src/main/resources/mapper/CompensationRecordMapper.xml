<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.CompensationRecordMapper">
    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.CompensationRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="unique_key" jdbcType="VARCHAR" property="uniqueKey"/>
        <result column="handle_status" jdbcType="INTEGER" property="handleStatus"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
        <result column="next_retry_time" jdbcType="TIMESTAMP" property="nextRetryTime"/>
        <result column="biz_scene" jdbcType="INTEGER" property="bizScene"/>
        <result column="retry_flag" jdbcType="INTEGER" property="retryFlag"/>
        <result column="shard" jdbcType="INTEGER" property="shard"/>
        <result column="valid" jdbcType="INTEGER" property="valid"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.youpin.clear.infrastructure.dataobject.CompensationRecord">
        <result column="retry_msg" jdbcType="LONGVARCHAR" property="retryMsg"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, unique_key, handle_status, `count`, next_retry_time, biz_scene, retry_flag, shard,
        `valid`, create_time, update_time
    </sql>
    <sql id="Blob_Column_List">
        retry_msg
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from compensation_record
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from compensation_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.CompensationRecord" useGeneratedKeys="true">
        insert into compensation_record (unique_key, handle_status, `count`,
        next_retry_time, biz_scene, retry_flag,
        shard, `valid`, create_time,
        update_time, retry_msg)
        values (#{uniqueKey,jdbcType=VARCHAR}, #{handleStatus,jdbcType=INTEGER}, #{count,jdbcType=INTEGER},
        #{nextRetryTime,jdbcType=TIMESTAMP}, #{bizScene,jdbcType=INTEGER}, #{retryFlag,jdbcType=INTEGER},
        #{shard,jdbcType=INTEGER}, #{valid,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{retryMsg,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.CompensationRecord" useGeneratedKeys="true">
        insert into compensation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniqueKey != null">
                unique_key,
            </if>
            <if test="handleStatus != null">
                handle_status,
            </if>
            <if test="count != null">
                `count`,
            </if>
            <if test="nextRetryTime != null">
                next_retry_time,
            </if>
            <if test="bizScene != null">
                biz_scene,
            </if>
            <if test="retryFlag != null">
                retry_flag,
            </if>
            <if test="shard != null">
                shard,
            </if>
            <if test="valid != null">
                `valid`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="retryMsg != null">
                retry_msg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniqueKey != null">
                #{uniqueKey,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null">
                #{handleStatus,jdbcType=INTEGER},
            </if>
            <if test="count != null">
                #{count,jdbcType=INTEGER},
            </if>
            <if test="nextRetryTime != null">
                #{nextRetryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bizScene != null">
                #{bizScene,jdbcType=INTEGER},
            </if>
            <if test="retryFlag != null">
                #{retryFlag,jdbcType=INTEGER},
            </if>
            <if test="shard != null">
                #{shard,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="retryMsg != null">
                #{retryMsg,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.youpin.clear.infrastructure.dataobject.CompensationRecord">
        update compensation_record
        <set>
            <if test="uniqueKey != null">
                unique_key = #{uniqueKey,jdbcType=VARCHAR},
            </if>
            <if test="handleStatus != null">
                handle_status = #{handleStatus,jdbcType=INTEGER},
            </if>
            <if test="count != null">
                `count` = #{count,jdbcType=INTEGER},
            </if>
            <if test="nextRetryTime != null">
                next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bizScene != null">
                biz_scene = #{bizScene,jdbcType=INTEGER},
            </if>
            <if test="retryFlag != null">
                retry_flag = #{retryFlag,jdbcType=INTEGER},
            </if>
            <if test="shard != null">
                shard = #{shard,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                `valid` = #{valid,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="retryMsg != null">
                retry_msg = #{retryMsg,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.youpin.clear.infrastructure.dataobject.CompensationRecord">
        update compensation_record
        set unique_key = #{uniqueKey,jdbcType=VARCHAR},
        handle_status = #{handleStatus,jdbcType=INTEGER},
        `count` = #{count,jdbcType=INTEGER},
        next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
        biz_scene = #{bizScene,jdbcType=INTEGER},
        retry_flag = #{retryFlag,jdbcType=INTEGER},
        shard = #{shard,jdbcType=INTEGER},
        `valid` = #{valid,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        retry_msg = #{retryMsg,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.CompensationRecord">
        update compensation_record
        set unique_key = #{uniqueKey,jdbcType=VARCHAR},
        handle_status = #{handleStatus,jdbcType=INTEGER},
        `count` = #{count,jdbcType=INTEGER},
        next_retry_time = #{nextRetryTime,jdbcType=TIMESTAMP},
        biz_scene = #{bizScene,jdbcType=INTEGER},
        retry_flag = #{retryFlag,jdbcType=INTEGER},
        shard = #{shard,jdbcType=INTEGER},
        `valid` = #{valid,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>


</mapper>