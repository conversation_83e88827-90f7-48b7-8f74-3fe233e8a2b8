<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AccountAssetsTypeMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.AccountAssetsType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="assets_code" jdbcType="INTEGER" property="assetsCode" />
    <result column="assets_name" jdbcType="VARCHAR" property="assetsName" />
    <result column="direction" jdbcType="VARCHAR" property="direction" />
    <result column="assets_type" jdbcType="VARCHAR" property="assetsType" />
    <result column="delete_flag" jdbcType="BIT" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, assets_code, assets_name, direction, assets_type, delete_flag, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from account_assets_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from account_assets_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsType" useGeneratedKeys="true">
    insert into account_assets_type (assets_code, assets_name, direction, 
      assets_type, delete_flag, create_time, 
      update_time)
    values (#{assetsCode,jdbcType=INTEGER}, #{assetsName,jdbcType=VARCHAR}, #{direction,jdbcType=VARCHAR}, 
      #{assetsType,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsType" useGeneratedKeys="true">
    insert into account_assets_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="assetsName != null">
        assets_name,
      </if>
      <if test="direction != null">
        direction,
      </if>
      <if test="assetsType != null">
        assets_type,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=INTEGER},
      </if>
      <if test="assetsName != null">
        #{assetsName,jdbcType=VARCHAR},
      </if>
      <if test="direction != null">
        #{direction,jdbcType=VARCHAR},
      </if>
      <if test="assetsType != null">
        #{assetsType,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsType">
    update account_assets_type
    <set>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=INTEGER},
      </if>
      <if test="assetsName != null">
        assets_name = #{assetsName,jdbcType=VARCHAR},
      </if>
      <if test="direction != null">
        direction = #{direction,jdbcType=VARCHAR},
      </if>
      <if test="assetsType != null">
        assets_type = #{assetsType,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsType">
    update account_assets_type
    set assets_code = #{assetsCode,jdbcType=INTEGER},
      assets_name = #{assetsName,jdbcType=VARCHAR},
      direction = #{direction,jdbcType=VARCHAR},
      assets_type = #{assetsType,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>