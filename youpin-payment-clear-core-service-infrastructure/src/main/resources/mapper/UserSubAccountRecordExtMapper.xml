<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserSubAccountRecordExtMapper">


    <insert id="batchInsertRecord">
        insert into user_sub_account_record
        ( id,account_record_no,user_id
        ,type_id,user_assets_record_id,serial_no
        ,order_no,pay_order_no,user_account_no
        ,account_type,balance_change,frozen_balance_change
        ,balance_is_change,status,pay_channel
        ,finish_time,ext,create_time
        ,update_time)
        values
        <foreach collection="subAccountRecords" item="record" separator=",">
            (#{record.id,jdbcType=BIGINT},#{record.accountRecordNo,jdbcType=VARCHAR},#{record.userId,jdbcType=BIGINT}
            ,#{record.typeId,jdbcType=INTEGER},#{record.userAssetsRecordId,jdbcType=BIGINT},#{record.serialNo,jdbcType=VARCHAR}
            ,#{record.orderNo,jdbcType=VARCHAR},#{record.payOrderNo,jdbcType=VARCHAR},#{record.userAccountNo,jdbcType=VARCHAR}
            ,#{record.accountType,jdbcType=INTEGER},#{record.balanceChange,jdbcType=DECIMAL},#{record.frozenBalanceChange,jdbcType=DECIMAL}
            ,#{record.balanceIsChange,jdbcType=INTEGER},#{record.status,jdbcType=INTEGER},#{record.payChannel,jdbcType=INTEGER}
            ,#{record.finishTime,jdbcType=TIMESTAMP},#{record.ext,jdbcType=VARCHAR},#{record.createTime,jdbcType=TIMESTAMP}
            ,#{record.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <insert id="batchInsertRecordOrderRelate">
        insert ignore into user_sub_account_record_order_relate
        ( id, user_id, order_no
        , create_time)
        values
        <foreach collection="recordOrderRelateList" item="record" separator=",">
            ( #{record.id}, #{record.userId}, #{record.orderNo}
            , #{record.createTime})
        </foreach>

    </insert>


    <update id="updateUserSubAccountRecord">>
        update user_sub_account_record
        <set>
            <if test="record.status != null">
                status = #{record.status},
            </if>

            <if test="finishTime != null">
                finish_time = #{record.finishTime},
            </if>
        </set>
        where id = #{record.id}
        and `status` = #{originStatus}
        and user_id = #{record.userId}
    </update>

    <select id="getOrderRelateUserIdByOrderNo" resultType="long" >
        select distinct user_id
        from user_sub_account_record_order_relate
        where order_no = #{orderNo}
    </select>

    <select id="getUserSubAccountRecordByOrderNoAndUserId"
            resultMap="com.youpin.clear.infrastructure.mapper.UserSubAccountRecordMapper.BaseResultMap">
        select *
        from user_sub_account_record
        where order_no = #{orderNo}
          and user_id = #{userId}
    </select>

    <select id="getMinAccountRecordId" resultType="java.lang.Long">
        select min(id)
        from user_sub_account_record
        where user_id = #{userId}
    </select>
</mapper>
