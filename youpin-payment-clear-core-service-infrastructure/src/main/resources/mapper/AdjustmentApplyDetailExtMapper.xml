<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AdjustmentApplyDetailExtMapper">
    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="apply_no" jdbcType="VARCHAR" property="applyNo"/>
        <result column="direction" jdbcType="INTEGER" property="direction"/>
        <result column="adjust_money" jdbcType="DECIMAL" property="adjustMoney"/>
        <result column="adjust_account_type" jdbcType="INTEGER" property="adjustAccountType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , apply_no, direction, adjust_money, adjust_account_type, create_time, update_time
    </sql>
    <select id="selectByApplyNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from adjustment_apply_detail
        where apply_no = #{applyNo}
    </select>

    <insert id="batchInsert">
        insert into adjustment_apply_detail (apply_no, direction, adjust_money,
        adjust_account_type, create_time, update_time
        )
        values
        <foreach collection="list" item="item" separator=",">

            (#{item.applyNo,jdbcType=VARCHAR}, #{item.direction,jdbcType=INTEGER}, #{item.adjustMoney,jdbcType=DECIMAL},
            #{item.adjustAccountType,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>


    <select id="selectByApplyNoAndDirection" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from adjustment_apply_detail
        where apply_no = #{applyNo} and direction = #{direction}
    </select>

</mapper>