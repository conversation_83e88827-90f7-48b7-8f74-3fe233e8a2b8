<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.CompensationRecordExtMapper">


    <select id="countExceptionRetryJob" resultType="long">
        select count(1) FROM compensation_record
        where valid = 1 and retry_flag = 1 and handle_status in (1,2)
        and next_retry_time &lt;= #{currentTime}
        and count &lt; #{maxRetryCount}
        and id%#{shardTotal} = #{shardIndex}
        <if test="bizScene != null">
            and biz_scene = #{bizScene,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectExceptionRetryJob" resultType="long">
        SELECT id FROM compensation_record
        where valid = 1 and retry_flag = 1 and handle_status in (1,2)
        and next_retry_time &lt;= #{currentTime}
        and count &lt; #{maxRetryCount} and id%#{shardTotal} = #{shardIndex}
        <if test="bizScene != null">
            and biz_scene = #{bizScene,jdbcType=INTEGER}
        </if>
        order by id limit #{pageIndex},#{pageSize}
    </select>

    <select id="selectUniqueKeyCount" resultType="int">
        select count(*) from compensation_record where valid = 1 and unique_key = #{uniqueKey} and biz_scene =
        #{bizScene}
    </select>

    <delete id="deleteByCountSum">
        delete from compensation_record where handle_status = 1 and count >= #{countSum} order by id limit #{pageSize}
    </delete>

    <delete id="deleteByCreateTime">
        delete from compensation_record where <![CDATA[create_time < #{createTime} ]]> order by id limit #{pageSize}
    </delete>

    <delete id="deleteByHandleStatus">
        delete from compensation_record where handle_status = #{handleStatus} order by id limit #{pageSize}
    </delete>

    <select id="selectMaxRetryCount" resultType="com.youpin.clear.infrastructure.dataobject.CompensationRecord">
        select id,
        unique_key,
        handle_status,
        count,
        next_retry_time,
        biz_scene,
        retry_flag,
        shard,
        valid,
        create_time,
        update_time,
        retry_msg
        from compensation_record
        where handle_status = 1 and count >= #{maxRetryCount} limit #{pageIndex},#{pageSize}
    </select>
</mapper>