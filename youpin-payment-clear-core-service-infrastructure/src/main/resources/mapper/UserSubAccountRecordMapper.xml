<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserSubAccountRecordMapper">

    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="accountRecordNo" column="account_record_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="typeId" column="type_id" jdbcType="INTEGER"/>
            <result property="userAssetsRecordId" column="user_assets_record_id" jdbcType="BIGINT"/>
            <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="payOrderNo" column="pay_order_no" jdbcType="VARCHAR"/>
            <result property="userAccountNo" column="user_account_no" jdbcType="VARCHAR"/>
            <result property="accountType" column="account_type" jdbcType="INTEGER"/>
            <result property="balanceChange" column="balance_change" jdbcType="DECIMAL"/>
            <result property="frozenBalanceChange" column="frozen_balance_change" jdbcType="DECIMAL"/>
            <result property="balanceIsChange" column="balance_is_change" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="payChannel" column="pay_channel" jdbcType="INTEGER"/>
            <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
            <result property="ext" column="ext" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_record_no,user_id,
        type_id,user_assets_record_id,serial_no,
        order_no,pay_order_no,user_account_no,
        account_type,balance_change,frozen_balance_change,
        balance_is_change,status,pay_channel,
        finish_time,ext,create_time,
        update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_sub_account_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from user_sub_account_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord" useGeneratedKeys="true">
        insert into user_sub_account_record
        ( id,account_record_no,user_id
        ,type_id,user_assets_record_id,serial_no
        ,order_no,pay_order_no,user_account_no
        ,account_type,balance_change,frozen_balance_change
        ,balance_is_change,status,pay_channel
        ,finish_time,ext,create_time
        ,update_time)
        values (#{id,jdbcType=BIGINT},#{accountRecordNo,jdbcType=VARCHAR},#{userId,jdbcType=BIGINT}
        ,#{typeId,jdbcType=INTEGER},#{userAssetsRecordId,jdbcType=BIGINT},#{serialNo,jdbcType=VARCHAR}
        ,#{orderNo,jdbcType=VARCHAR},#{payOrderNo,jdbcType=VARCHAR},#{userAccountNo,jdbcType=VARCHAR}
        ,#{accountType,jdbcType=INTEGER},#{balanceChange,jdbcType=DECIMAL},#{frozenBalanceChange,jdbcType=DECIMAL}
        ,#{balanceIsChange,jdbcType=INTEGER},#{status,jdbcType=INTEGER},#{payChannel,jdbcType=INTEGER}
        ,#{finishTime,jdbcType=TIMESTAMP},#{ext,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord" useGeneratedKeys="true">
        insert into user_sub_account_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="accountRecordNo != null">account_record_no,</if>
                <if test="userId != null">user_id,</if>
                <if test="typeId != null">type_id,</if>
                <if test="userAssetsRecordId != null">user_assets_record_id,</if>
                <if test="serialNo != null">serial_no,</if>
                <if test="orderNo != null">order_no,</if>
                <if test="payOrderNo != null">pay_order_no,</if>
                <if test="userAccountNo != null">user_account_no,</if>
                <if test="accountType != null">account_type,</if>
                <if test="balanceChange != null">balance_change,</if>
                <if test="frozenBalanceChange != null">frozen_balance_change,</if>
                <if test="balanceIsChange != null">balance_is_change,</if>
                <if test="status != null">status,</if>
                <if test="payChannel != null">pay_channel,</if>
                <if test="finishTime != null">finish_time,</if>
                <if test="ext != null">ext,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="accountRecordNo != null">#{accountRecordNo,jdbcType=VARCHAR},</if>
                <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
                <if test="typeId != null">#{typeId,jdbcType=INTEGER},</if>
                <if test="userAssetsRecordId != null">#{userAssetsRecordId,jdbcType=BIGINT},</if>
                <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
                <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
                <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
                <if test="userAccountNo != null">#{userAccountNo,jdbcType=VARCHAR},</if>
                <if test="accountType != null">#{accountType,jdbcType=INTEGER},</if>
                <if test="balanceChange != null">#{balanceChange,jdbcType=DECIMAL},</if>
                <if test="frozenBalanceChange != null">#{frozenBalanceChange,jdbcType=DECIMAL},</if>
                <if test="balanceIsChange != null">#{balanceIsChange,jdbcType=INTEGER},</if>
                <if test="status != null">#{status,jdbcType=INTEGER},</if>
                <if test="payChannel != null">#{payChannel,jdbcType=INTEGER},</if>
                <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
                <if test="ext != null">#{ext,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord">
        update user_sub_account_record
        <set>
                <if test="accountRecordNo != null">
                    account_record_no = #{accountRecordNo,jdbcType=VARCHAR},
                </if>
                <if test="userId != null">
                    user_id = #{userId,jdbcType=BIGINT},
                </if>
                <if test="typeId != null">
                    type_id = #{typeId,jdbcType=INTEGER},
                </if>
                <if test="userAssetsRecordId != null">
                    user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
                </if>
                <if test="serialNo != null">
                    serial_no = #{serialNo,jdbcType=VARCHAR},
                </if>
                <if test="orderNo != null">
                    order_no = #{orderNo,jdbcType=VARCHAR},
                </if>
                <if test="payOrderNo != null">
                    pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="userAccountNo != null">
                    user_account_no = #{userAccountNo,jdbcType=VARCHAR},
                </if>
                <if test="accountType != null">
                    account_type = #{accountType,jdbcType=INTEGER},
                </if>
                <if test="balanceChange != null">
                    balance_change = #{balanceChange,jdbcType=DECIMAL},
                </if>
                <if test="frozenBalanceChange != null">
                    frozen_balance_change = #{frozenBalanceChange,jdbcType=DECIMAL},
                </if>
                <if test="balanceIsChange != null">
                    balance_is_change = #{balanceIsChange,jdbcType=INTEGER},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=INTEGER},
                </if>
                <if test="payChannel != null">
                    pay_channel = #{payChannel,jdbcType=INTEGER},
                </if>
                <if test="finishTime != null">
                    finish_time = #{finishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="ext != null">
                    ext = #{ext,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord">
        update user_sub_account_record
        set 
            account_record_no =  #{accountRecordNo,jdbcType=VARCHAR},
            user_id =  #{userId,jdbcType=BIGINT},
            type_id =  #{typeId,jdbcType=INTEGER},
            user_assets_record_id =  #{userAssetsRecordId,jdbcType=BIGINT},
            serial_no =  #{serialNo,jdbcType=VARCHAR},
            order_no =  #{orderNo,jdbcType=VARCHAR},
            pay_order_no =  #{payOrderNo,jdbcType=VARCHAR},
            user_account_no =  #{userAccountNo,jdbcType=VARCHAR},
            account_type =  #{accountType,jdbcType=INTEGER},
            balance_change =  #{balanceChange,jdbcType=DECIMAL},
            frozen_balance_change =  #{frozenBalanceChange,jdbcType=DECIMAL},
            balance_is_change =  #{balanceIsChange,jdbcType=INTEGER},
            status =  #{status,jdbcType=INTEGER},
            pay_channel =  #{payChannel,jdbcType=INTEGER},
            finish_time =  #{finishTime,jdbcType=TIMESTAMP},
            ext =  #{ext,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
