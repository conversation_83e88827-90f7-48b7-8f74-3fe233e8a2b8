<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountMapper">

    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountNo" column="account_no" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="accountType" column="account_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="currency" column="currency" jdbcType="CHAR"/>
        <result property="balance" column="balance" jdbcType="DECIMAL"/>
        <result property="lastFlowId" column="last_flow_id" jdbcType="BIGINT"/>
        <result property="extInfo" column="ext_info" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_no,user_id,
        account_type,status,currency,
        balance,last_flow_id,ext_info,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uu898_user_sub_account
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from uu898_user_sub_account
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount"
            useGeneratedKeys="true">
        insert into uu898_user_sub_account
        ( id, account_no, user_id
        , account_type, status, currency
        , balance, last_flow_id, ext_info
        , create_time, update_time)
        values ( #{id,jdbcType=BIGINT}, #{accountNo,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}
               , #{accountType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{currency,jdbcType=CHAR}
               , #{balance,jdbcType=DECIMAL}, #{lastFlowId,jdbcType=BIGINT}, #{extInfo,jdbcType=VARCHAR}
               , #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount"
            useGeneratedKeys="true">
        insert into uu898_user_sub_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="accountNo != null">account_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="accountType != null">account_type,</if>
            <if test="status != null">status,</if>
            <if test="currency != null">currency,</if>
            <if test="balance != null">balance,</if>
            <if test="lastFlowId != null">last_flow_id,</if>
            <if test="extInfo != null">ext_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="accountNo != null">#{accountNo,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="accountType != null">#{accountType,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="currency != null">#{currency,jdbcType=CHAR},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="lastFlowId != null">#{lastFlowId,jdbcType=BIGINT},</if>
            <if test="extInfo != null">#{extInfo,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount">
        update uu898_user_sub_account
        <set>
            <if test="accountNo != null">
                account_no = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="currency != null">
                currency = #{currency,jdbcType=CHAR},
            </if>
            <if test="balance != null">
                balance = #{balance,jdbcType=DECIMAL},
            </if>
            <if test="lastFlowId != null">
                last_flow_id = #{lastFlowId,jdbcType=BIGINT},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount">
        update uu898_user_sub_account
        set account_no   = #{accountNo,jdbcType=VARCHAR},
            user_id      = #{userId,jdbcType=BIGINT},
            account_type = #{accountType,jdbcType=INTEGER},
            status       = #{status,jdbcType=INTEGER},
            currency     = #{currency,jdbcType=CHAR},
            balance      = #{balance,jdbcType=DECIMAL},
            last_flow_id = #{lastFlowId,jdbcType=BIGINT},
            ext_info     = #{extInfo,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
