<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAssetsRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="INTEGER" property="merchantId" />
    <result column="user_assets_record_id" jdbcType="BIGINT" property="userAssetsRecordId" />
    <result column="user_assets_record_no" jdbcType="VARCHAR" property="userAssetsRecordNo" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="tread_no" jdbcType="VARCHAR" property="treadNo" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="asset_type" jdbcType="INTEGER" property="assetType" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="this_money" jdbcType="DECIMAL" property="thisMoney" />
    <result column="after_money" jdbcType="DECIMAL" property="afterMoney" />
    <result column="charge_money" jdbcType="DECIMAL" property="chargeMoney" />
    <result column="block_money" jdbcType="DECIMAL" property="blockMoney" />
    <result column="this_block_money" jdbcType="DECIMAL" property="thisBlockMoney" />
    <result column="after_block_money" jdbcType="DECIMAL" property="afterBlockMoney" />
    <result column="purchase_money" jdbcType="DECIMAL" property="purchaseMoney" />
    <result column="this_purchase_money" jdbcType="DECIMAL" property="thisPurchaseMoney" />
    <result column="after_purchase_money" jdbcType="DECIMAL" property="afterPurchaseMoney" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="attr" jdbcType="INTEGER" property="attr" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="pay_channel" jdbcType="INTEGER" property="payChannel" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="pay_wait_expire_time" jdbcType="TIMESTAMP" property="payWaitExpireTime" />
    <result column="gen_source" jdbcType="VARCHAR" property="genSource" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_id, user_assets_record_id, user_assets_record_no, user_id, type_id, 
    type_name, tread_no, serial_no, order_no, pay_order_no, asset_type, money, this_money, 
    after_money, charge_money, block_money, this_block_money, after_block_money, purchase_money, 
    this_purchase_money, after_purchase_money, remark, ext, attr, `status`, pay_channel, 
    account_name, pay_wait_expire_time, gen_source, add_time, complete_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_assets_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_assets_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord" useGeneratedKeys="true">
    insert into user_assets_record (merchant_id, user_assets_record_id, user_assets_record_no, 
      user_id, type_id, type_name, 
      tread_no, serial_no, order_no, 
      pay_order_no, asset_type, money, 
      this_money, after_money, charge_money, 
      block_money, this_block_money, after_block_money, 
      purchase_money, this_purchase_money, after_purchase_money, 
      remark, ext, attr, 
      `status`, pay_channel, account_name, 
      pay_wait_expire_time, gen_source, add_time, 
      complete_time)
    values (#{merchantId,jdbcType=INTEGER}, #{userAssetsRecordId,jdbcType=BIGINT}, #{userAssetsRecordNo,jdbcType=VARCHAR}, 
      #{userId,jdbcType=BIGINT}, #{typeId,jdbcType=INTEGER}, #{typeName,jdbcType=VARCHAR}, 
      #{treadNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{payOrderNo,jdbcType=VARCHAR}, #{assetType,jdbcType=INTEGER}, #{money,jdbcType=DECIMAL}, 
      #{thisMoney,jdbcType=DECIMAL}, #{afterMoney,jdbcType=DECIMAL}, #{chargeMoney,jdbcType=DECIMAL}, 
      #{blockMoney,jdbcType=DECIMAL}, #{thisBlockMoney,jdbcType=DECIMAL}, #{afterBlockMoney,jdbcType=DECIMAL}, 
      #{purchaseMoney,jdbcType=DECIMAL}, #{thisPurchaseMoney,jdbcType=DECIMAL}, #{afterPurchaseMoney,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{ext,jdbcType=VARCHAR}, #{attr,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{payChannel,jdbcType=INTEGER}, #{accountName,jdbcType=VARCHAR}, 
      #{payWaitExpireTime,jdbcType=TIMESTAMP}, #{genSource,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{completeTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord" useGeneratedKeys="true">
    insert into user_assets_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="userAssetsRecordId != null">
        user_assets_record_id,
      </if>
      <if test="userAssetsRecordNo != null">
        user_assets_record_no,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="treadNo != null">
        tread_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="payOrderNo != null">
        pay_order_no,
      </if>
      <if test="assetType != null">
        asset_type,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="thisMoney != null">
        this_money,
      </if>
      <if test="afterMoney != null">
        after_money,
      </if>
      <if test="chargeMoney != null">
        charge_money,
      </if>
      <if test="blockMoney != null">
        block_money,
      </if>
      <if test="thisBlockMoney != null">
        this_block_money,
      </if>
      <if test="afterBlockMoney != null">
        after_block_money,
      </if>
      <if test="purchaseMoney != null">
        purchase_money,
      </if>
      <if test="thisPurchaseMoney != null">
        this_purchase_money,
      </if>
      <if test="afterPurchaseMoney != null">
        after_purchase_money,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="attr != null">
        attr,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="payWaitExpireTime != null">
        pay_wait_expire_time,
      </if>
      <if test="genSource != null">
        gen_source,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId,jdbcType=INTEGER},
      </if>
      <if test="userAssetsRecordId != null">
        #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="userAssetsRecordNo != null">
        #{userAssetsRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="treadNo != null">
        #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="assetType != null">
        #{assetType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="thisMoney != null">
        #{thisMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterMoney != null">
        #{afterMoney,jdbcType=DECIMAL},
      </if>
      <if test="chargeMoney != null">
        #{chargeMoney,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisBlockMoney != null">
        #{thisBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterBlockMoney != null">
        #{afterBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoney != null">
        #{purchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisPurchaseMoney != null">
        #{thisPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterPurchaseMoney != null">
        #{afterPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="attr != null">
        #{attr,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="payWaitExpireTime != null">
        #{payWaitExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="genSource != null">
        #{genSource,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord">
    update user_assets_record
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=INTEGER},
      </if>
      <if test="userAssetsRecordId != null">
        user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="userAssetsRecordNo != null">
        user_assets_record_no = #{userAssetsRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=INTEGER},
      </if>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="treadNo != null">
        tread_no = #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="assetType != null">
        asset_type = #{assetType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="thisMoney != null">
        this_money = #{thisMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterMoney != null">
        after_money = #{afterMoney,jdbcType=DECIMAL},
      </if>
      <if test="chargeMoney != null">
        charge_money = #{chargeMoney,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        block_money = #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisBlockMoney != null">
        this_block_money = #{thisBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterBlockMoney != null">
        after_block_money = #{afterBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoney != null">
        purchase_money = #{purchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisPurchaseMoney != null">
        this_purchase_money = #{thisPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterPurchaseMoney != null">
        after_purchase_money = #{afterPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="attr != null">
        attr = #{attr,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="payWaitExpireTime != null">
        pay_wait_expire_time = #{payWaitExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="genSource != null">
        gen_source = #{genSource,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord">
    update user_assets_record
    set merchant_id = #{merchantId,jdbcType=INTEGER},
      user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      user_assets_record_no = #{userAssetsRecordNo,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=INTEGER},
      type_name = #{typeName,jdbcType=VARCHAR},
      tread_no = #{treadNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      asset_type = #{assetType,jdbcType=INTEGER},
      money = #{money,jdbcType=DECIMAL},
      this_money = #{thisMoney,jdbcType=DECIMAL},
      after_money = #{afterMoney,jdbcType=DECIMAL},
      charge_money = #{chargeMoney,jdbcType=DECIMAL},
      block_money = #{blockMoney,jdbcType=DECIMAL},
      this_block_money = #{thisBlockMoney,jdbcType=DECIMAL},
      after_block_money = #{afterBlockMoney,jdbcType=DECIMAL},
      purchase_money = #{purchaseMoney,jdbcType=DECIMAL},
      this_purchase_money = #{thisPurchaseMoney,jdbcType=DECIMAL},
      after_purchase_money = #{afterPurchaseMoney,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      attr = #{attr,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      pay_channel = #{payChannel,jdbcType=INTEGER},
      account_name = #{accountName,jdbcType=VARCHAR},
      pay_wait_expire_time = #{payWaitExpireTime,jdbcType=TIMESTAMP},
      gen_source = #{genSource,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>