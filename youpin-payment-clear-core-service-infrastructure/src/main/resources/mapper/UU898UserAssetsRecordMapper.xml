<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord">
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="UserId" jdbcType="BIGINT" property="userId" />
    <result column="TypeId" jdbcType="INTEGER" property="typeId" />
    <result column="TreadNo" jdbcType="VARCHAR" property="treadNo" />
    <result column="AssetType" jdbcType="INTEGER" property="assetType" />
    <result column="Money" jdbcType="DECIMAL" property="money" />
    <result column="ThisMoney" jdbcType="DECIMAL" property="thisMoney" />
    <result column="AfterMoney" jdbcType="DECIMAL" property="afterMoney" />
    <result column="ChargeMoney" jdbcType="DECIMAL" property="chargeMoney" />
    <result column="BlockMoney" jdbcType="DECIMAL" property="blockMoney" />
    <result column="ThisBlockMoney" jdbcType="DECIMAL" property="thisBlockMoney" />
    <result column="AfterBlockMoney" jdbcType="DECIMAL" property="afterBlockMoney" />
    <result column="PurchaseMoney" jdbcType="DECIMAL" property="purchaseMoney" />
    <result column="ThisPurchaseMoney" jdbcType="DECIMAL" property="thisPurchaseMoney" />
    <result column="AfterPurchaseMoney" jdbcType="DECIMAL" property="afterPurchaseMoney" />
    <result column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <result column="OrderNo" jdbcType="VARCHAR" property="orderNo" />
    <result column="PayOrderNo" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
    <result column="AddTime" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="Attr" jdbcType="INTEGER" property="attr" />
    <result column="Status" jdbcType="INTEGER" property="status" />
    <result column="PayChannel" jdbcType="INTEGER" property="payChannel" />
    <result column="AccountName" jdbcType="VARCHAR" property="accountName" />
    <result column="PayWaitExpireTime" jdbcType="TIMESTAMP" property="payWaitExpireTime" />
    <result column="GenSource" jdbcType="VARCHAR" property="genSource" />
  </resultMap>
  <sql id="Base_Column_List">
    Id, UserId, TypeId, TreadNo, AssetType, Money, ThisMoney, AfterMoney, ChargeMoney,
    BlockMoney, ThisBlockMoney, AfterBlockMoney, PurchaseMoney, ThisPurchaseMoney, AfterPurchaseMoney,
    SerialNo, OrderNo, PayOrderNo, Remark, AddTime, CompleteTime, Attr, `Status`, PayChannel,
    AccountName, PayWaitExpireTime, GenSource
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UserAssetsRecordUU898
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from UserAssetsRecordUU898
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="Id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord" useGeneratedKeys="true">
    insert into UserAssetsRecordUU898 (UserId, TypeId, TreadNo,
      AssetType, Money, ThisMoney,
      AfterMoney, ChargeMoney, BlockMoney,
      ThisBlockMoney, AfterBlockMoney, PurchaseMoney,
      ThisPurchaseMoney, AfterPurchaseMoney,
      SerialNo, OrderNo, PayOrderNo,
      Remark, AddTime, CompleteTime,
      Attr, `Status`, PayChannel,
      AccountName, PayWaitExpireTime, GenSource
      )
    values (#{userId,jdbcType=BIGINT}, #{typeId,jdbcType=INTEGER}, #{treadNo,jdbcType=VARCHAR},
      #{assetType,jdbcType=INTEGER}, #{money,jdbcType=DECIMAL}, #{thisMoney,jdbcType=DECIMAL},
      #{afterMoney,jdbcType=DECIMAL}, #{chargeMoney,jdbcType=DECIMAL}, #{blockMoney,jdbcType=DECIMAL},
      #{thisBlockMoney,jdbcType=DECIMAL}, #{afterBlockMoney,jdbcType=DECIMAL}, #{purchaseMoney,jdbcType=DECIMAL},
      #{thisPurchaseMoney,jdbcType=DECIMAL}, #{afterPurchaseMoney,jdbcType=DECIMAL},
      #{serialNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{payOrderNo,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP},
      #{attr,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{payChannel,jdbcType=INTEGER},
      #{accountName,jdbcType=VARCHAR}, #{payWaitExpireTime,jdbcType=TIMESTAMP}, #{genSource,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="Id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord" useGeneratedKeys="true">
    insert into UserAssetsRecordUU898
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        UserId,
      </if>
      <if test="typeId != null">
        TypeId,
      </if>
      <if test="treadNo != null">
        TreadNo,
      </if>
      <if test="assetType != null">
        AssetType,
      </if>
      <if test="money != null">
        Money,
      </if>
      <if test="thisMoney != null">
        ThisMoney,
      </if>
      <if test="afterMoney != null">
        AfterMoney,
      </if>
      <if test="chargeMoney != null">
        ChargeMoney,
      </if>
      <if test="blockMoney != null">
        BlockMoney,
      </if>
      <if test="thisBlockMoney != null">
        ThisBlockMoney,
      </if>
      <if test="afterBlockMoney != null">
        AfterBlockMoney,
      </if>
      <if test="purchaseMoney != null">
        PurchaseMoney,
      </if>
      <if test="thisPurchaseMoney != null">
        ThisPurchaseMoney,
      </if>
      <if test="afterPurchaseMoney != null">
        AfterPurchaseMoney,
      </if>
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="orderNo != null">
        OrderNo,
      </if>
      <if test="payOrderNo != null">
        PayOrderNo,
      </if>
      <if test="remark != null">
        Remark,
      </if>
      <if test="addTime != null">
        AddTime,
      </if>
      <if test="true">
        CompleteTime,
      </if>
      <if test="attr != null">
        Attr,
      </if>
      <if test="status != null">
        `Status`,
      </if>
      <if test="payChannel != null">
        PayChannel,
      </if>
      <if test="accountName != null">
        AccountName,
      </if>
      <if test="payWaitExpireTime != null">
        PayWaitExpireTime,
      </if>
      <if test="genSource != null">
        GenSource,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="treadNo != null">
        #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="assetType != null">
        #{assetType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="thisMoney != null">
        #{thisMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterMoney != null">
        #{afterMoney,jdbcType=DECIMAL},
      </if>
      <if test="chargeMoney != null">
        #{chargeMoney,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisBlockMoney != null">
        #{thisBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterBlockMoney != null">
        #{afterBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoney != null">
        #{purchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisPurchaseMoney != null">
        #{thisPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterPurchaseMoney != null">
        #{afterPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="true">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attr != null">
        #{attr,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="payWaitExpireTime != null">
        #{payWaitExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="genSource != null">
        #{genSource,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord">
    update UserAssetsRecordUU898
    <set>
      <if test="userId != null">
        UserId = #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        TypeId = #{typeId,jdbcType=INTEGER},
      </if>
      <if test="treadNo != null">
        TreadNo = #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="assetType != null">
        AssetType = #{assetType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        Money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="thisMoney != null">
        ThisMoney = #{thisMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterMoney != null">
        AfterMoney = #{afterMoney,jdbcType=DECIMAL},
      </if>
      <if test="chargeMoney != null">
        ChargeMoney = #{chargeMoney,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        BlockMoney = #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisBlockMoney != null">
        ThisBlockMoney = #{thisBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterBlockMoney != null">
        AfterBlockMoney = #{afterBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoney != null">
        PurchaseMoney = #{purchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="thisPurchaseMoney != null">
        ThisPurchaseMoney = #{thisPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="afterPurchaseMoney != null">
        AfterPurchaseMoney = #{afterPurchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="serialNo != null">
        SerialNo = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        PayOrderNo = #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        AddTime = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attr != null">
        Attr = #{attr,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `Status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="payChannel != null">
        PayChannel = #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        AccountName = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="payWaitExpireTime != null">
        PayWaitExpireTime = #{payWaitExpireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="genSource != null">
        GenSource = #{genSource,jdbcType=VARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord">
    update UserAssetsRecordUU898
    set UserId = #{userId,jdbcType=BIGINT},
      TypeId = #{typeId,jdbcType=INTEGER},
      TreadNo = #{treadNo,jdbcType=VARCHAR},
      AssetType = #{assetType,jdbcType=INTEGER},
      Money = #{money,jdbcType=DECIMAL},
      ThisMoney = #{thisMoney,jdbcType=DECIMAL},
      AfterMoney = #{afterMoney,jdbcType=DECIMAL},
      ChargeMoney = #{chargeMoney,jdbcType=DECIMAL},
      BlockMoney = #{blockMoney,jdbcType=DECIMAL},
      ThisBlockMoney = #{thisBlockMoney,jdbcType=DECIMAL},
      AfterBlockMoney = #{afterBlockMoney,jdbcType=DECIMAL},
      PurchaseMoney = #{purchaseMoney,jdbcType=DECIMAL},
      ThisPurchaseMoney = #{thisPurchaseMoney,jdbcType=DECIMAL},
      AfterPurchaseMoney = #{afterPurchaseMoney,jdbcType=DECIMAL},
      SerialNo = #{serialNo,jdbcType=VARCHAR},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      PayOrderNo = #{payOrderNo,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      AddTime = #{addTime,jdbcType=TIMESTAMP},
      CompleteTime = #{completeTime,jdbcType=TIMESTAMP},
      Attr = #{attr,jdbcType=INTEGER},
      `Status` = #{status,jdbcType=INTEGER},
      PayChannel = #{payChannel,jdbcType=INTEGER},
      AccountName = #{accountName,jdbcType=VARCHAR},
      PayWaitExpireTime = #{payWaitExpireTime,jdbcType=TIMESTAMP},
      GenSource = #{genSource,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>