<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AdjustmentApplyDetailMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="direction" jdbcType="INTEGER" property="direction" />
    <result column="adjust_money" jdbcType="DECIMAL" property="adjustMoney" />
    <result column="adjust_account_type" jdbcType="INTEGER" property="adjustAccountType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, apply_no, direction, adjust_money, adjust_account_type, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from adjustment_apply_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from adjustment_apply_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail" useGeneratedKeys="true">
    insert into adjustment_apply_detail (apply_no, direction, adjust_money, 
      adjust_account_type, create_time, update_time
      )
    values (#{applyNo,jdbcType=VARCHAR}, #{direction,jdbcType=INTEGER}, #{adjustMoney,jdbcType=DECIMAL}, 
      #{adjustAccountType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail" useGeneratedKeys="true">
    insert into adjustment_apply_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="direction != null">
        direction,
      </if>
      <if test="adjustMoney != null">
        adjust_money,
      </if>
      <if test="adjustAccountType != null">
        adjust_account_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="direction != null">
        #{direction,jdbcType=INTEGER},
      </if>
      <if test="adjustMoney != null">
        #{adjustMoney,jdbcType=DECIMAL},
      </if>
      <if test="adjustAccountType != null">
        #{adjustAccountType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail">
    update adjustment_apply_detail
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="direction != null">
        direction = #{direction,jdbcType=INTEGER},
      </if>
      <if test="adjustMoney != null">
        adjust_money = #{adjustMoney,jdbcType=DECIMAL},
      </if>
      <if test="adjustAccountType != null">
        adjust_account_type = #{adjustAccountType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail">
    update adjustment_apply_detail
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      direction = #{direction,jdbcType=INTEGER},
      adjust_money = #{adjustMoney,jdbcType=DECIMAL},
      adjust_account_type = #{adjustAccountType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>