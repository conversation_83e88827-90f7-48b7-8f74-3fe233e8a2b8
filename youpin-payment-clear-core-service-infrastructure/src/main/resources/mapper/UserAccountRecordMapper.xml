<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_record_no" jdbcType="VARCHAR" property="accountRecordNo" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="user_assets_record_id" jdbcType="BIGINT" property="userAssetsRecordId" />
    <result column="tread_no" jdbcType="VARCHAR" property="treadNo" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="user_account_no" jdbcType="VARCHAR" property="userAccountNo" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="balance_before" jdbcType="DECIMAL" property="balanceBefore" />
    <result column="balance_change" jdbcType="DECIMAL" property="balanceChange" />
    <result column="balance_after" jdbcType="DECIMAL" property="balanceAfter" />
    <result column="balance_is_change" jdbcType="INTEGER" property="balanceIsChange" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="pay_channel" jdbcType="INTEGER" property="payChannel" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="frozen_balance_before" jdbcType="DECIMAL" property="frozenBalanceBefore" />
    <result column="frozen_balance_change" jdbcType="DECIMAL" property="frozenBalanceChange" />
    <result column="frozen_balance_after" jdbcType="DECIMAL" property="frozenBalanceAfter" />
  </resultMap>
  <sql id="Base_Column_List">
    id, account_record_no, user_id, type_id, user_assets_record_id, tread_no, serial_no, 
    order_no, pay_order_no, user_account_no, account_type, balance_before, balance_change, 
    balance_after, balance_is_change, `status`, pay_channel, finish_time, ext, create_time, 
    update_time, frozen_balance_before, frozen_balance_change, frozen_balance_after
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_account_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_account_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord" useGeneratedKeys="true">
    insert into user_account_record (account_record_no, user_id, type_id, 
      user_assets_record_id, tread_no, serial_no, 
      order_no, pay_order_no, user_account_no, 
      account_type, balance_before, balance_change, 
      balance_after, balance_is_change, `status`, 
      pay_channel, finish_time, ext, 
      create_time, update_time, frozen_balance_before, 
      frozen_balance_change, frozen_balance_after
      )
    values (#{accountRecordNo,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{typeId,jdbcType=INTEGER}, 
      #{userAssetsRecordId,jdbcType=BIGINT}, #{treadNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{payOrderNo,jdbcType=VARCHAR}, #{userAccountNo,jdbcType=VARCHAR}, 
      #{accountType,jdbcType=INTEGER}, #{balanceBefore,jdbcType=DECIMAL}, #{balanceChange,jdbcType=DECIMAL}, 
      #{balanceAfter,jdbcType=DECIMAL}, #{balanceIsChange,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{payChannel,jdbcType=INTEGER}, #{finishTime,jdbcType=TIMESTAMP}, #{ext,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{frozenBalanceBefore,jdbcType=DECIMAL}, 
      #{frozenBalanceChange,jdbcType=DECIMAL}, #{frozenBalanceAfter,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord" useGeneratedKeys="true">
    insert into user_account_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountRecordNo != null">
        account_record_no,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="userAssetsRecordId != null">
        user_assets_record_id,
      </if>
      <if test="treadNo != null">
        tread_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="payOrderNo != null">
        pay_order_no,
      </if>
      <if test="userAccountNo != null">
        user_account_no,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="balanceBefore != null">
        balance_before,
      </if>
      <if test="balanceChange != null">
        balance_change,
      </if>
      <if test="balanceAfter != null">
        balance_after,
      </if>
      <if test="balanceIsChange != null">
        balance_is_change,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="frozenBalanceBefore != null">
        frozen_balance_before,
      </if>
      <if test="frozenBalanceChange != null">
        frozen_balance_change,
      </if>
      <if test="frozenBalanceAfter != null">
        frozen_balance_after,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountRecordNo != null">
        #{accountRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="userAssetsRecordId != null">
        #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="treadNo != null">
        #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="userAccountNo != null">
        #{userAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="balanceBefore != null">
        #{balanceBefore,jdbcType=DECIMAL},
      </if>
      <if test="balanceChange != null">
        #{balanceChange,jdbcType=DECIMAL},
      </if>
      <if test="balanceAfter != null">
        #{balanceAfter,jdbcType=DECIMAL},
      </if>
      <if test="balanceIsChange != null">
        #{balanceIsChange,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="frozenBalanceBefore != null">
        #{frozenBalanceBefore,jdbcType=DECIMAL},
      </if>
      <if test="frozenBalanceChange != null">
        #{frozenBalanceChange,jdbcType=DECIMAL},
      </if>
      <if test="frozenBalanceAfter != null">
        #{frozenBalanceAfter,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
    update user_account_record
    <set>
      <if test="accountRecordNo != null">
        account_record_no = #{accountRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=INTEGER},
      </if>
      <if test="userAssetsRecordId != null">
        user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="treadNo != null">
        tread_no = #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="userAccountNo != null">
        user_account_no = #{userAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="balanceBefore != null">
        balance_before = #{balanceBefore,jdbcType=DECIMAL},
      </if>
      <if test="balanceChange != null">
        balance_change = #{balanceChange,jdbcType=DECIMAL},
      </if>
      <if test="balanceAfter != null">
        balance_after = #{balanceAfter,jdbcType=DECIMAL},
      </if>
      <if test="balanceIsChange != null">
        balance_is_change = #{balanceIsChange,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="frozenBalanceBefore != null">
        frozen_balance_before = #{frozenBalanceBefore,jdbcType=DECIMAL},
      </if>
      <if test="frozenBalanceChange != null">
        frozen_balance_change = #{frozenBalanceChange,jdbcType=DECIMAL},
      </if>
      <if test="frozenBalanceAfter != null">
        frozen_balance_after = #{frozenBalanceAfter,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
    update user_account_record
    set account_record_no = #{accountRecordNo,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=INTEGER},
      user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      tread_no = #{treadNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      user_account_no = #{userAccountNo,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=INTEGER},
      balance_before = #{balanceBefore,jdbcType=DECIMAL},
      balance_change = #{balanceChange,jdbcType=DECIMAL},
      balance_after = #{balanceAfter,jdbcType=DECIMAL},
      balance_is_change = #{balanceIsChange,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      pay_channel = #{payChannel,jdbcType=INTEGER},
      finish_time = #{finishTime,jdbcType=TIMESTAMP},
      ext = #{ext,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      frozen_balance_before = #{frozenBalanceBefore,jdbcType=DECIMAL},
      frozen_balance_change = #{frozenBalanceChange,jdbcType=DECIMAL},
      frozen_balance_after = #{frozenBalanceAfter,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>