<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AdjustmentApplyExtMapper">
    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="apply_no" jdbcType="VARCHAR" property="applyNo"/>
        <result column="related_order_no" jdbcType="VARCHAR" property="relatedOrderNo"/>
        <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo"/>
        <result column="income_user_id" jdbcType="BIGINT" property="incomeUserId"/>
        <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount"/>
        <result column="income_asset_type" jdbcType="BIGINT" property="incomeAssetType"/>
        <result column="income_pay_channel" jdbcType="INTEGER" property="incomePayChannel"/>
        <result column="income_service_fee" jdbcType="DECIMAL" property="incomeServiceFee"/>
        <result column="expense_user_id" jdbcType="BIGINT" property="expenseUserId"/>
        <result column="expense_amount" jdbcType="DECIMAL" property="expenseAmount"/>
        <result column="expense_asset_type" jdbcType="BIGINT" property="expenseAssetType"/>
        <result column="expense_pay_channel" jdbcType="INTEGER" property="expensePayChannel"/>
        <result column="expense_service_fee" jdbcType="DECIMAL" property="expenseServiceFee"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="apply_by" jdbcType="VARCHAR" property="applyBy"/>
        <result column="apply_remark" jdbcType="VARCHAR" property="applyRemark"/>
        <result column="audit_by" jdbcType="VARCHAR" property="auditBy"/>
        <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
        <result column="extend" jdbcType="LONGVARCHAR" property="extend"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, apply_no, related_order_no, pay_order_no, income_user_id, income_amount, income_asset_type,
    income_pay_channel, income_service_fee, expense_user_id, expense_amount, expense_asset_type,
    expense_pay_channel, expense_service_fee, `status`, apply_by, apply_remark, audit_by,
    audit_remark, audit_time, create_time, update_time, adjust_source, batch_no
    </sql>
    <sql id="Blob_Column_List">
        extend
    </sql>

    <select id="count" parameterType="com.youpin.clear.client.request.AdjustApplyListQueryRequest" resultType="long">
        select count(1) from adjustment_apply
        <include refid="queryCondition"/>
    </select>


    <select id="queryList" parameterType="com.youpin.clear.client.request.AdjustApplyListQueryRequest"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from adjustment_apply
        <include refid="queryCondition"/>
        order by id desc
        limit #{offset},#{pageSize}
    </select>

    <sql id="queryCondition">
        <where>
            <if test="startTime != null">
                create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
            <if test="incUserId != null">
                and income_user_id = #{incUserId}
            </if>
            <if test="decUserId != null">
                and expense_user_id = #{decUserId}
            </if>
            <if test="incAccountPayChannel != null">
                and income_pay_channel = #{incAccountPayChannel}
            </if>
            <if test="decAccountPayChannel != null">
                and expense_pay_channel = #{decAccountPayChannel}
            </if>
            <if test="relatedOrderNo != null and relatedOrderNo != '' ">
                and related_order_no = #{relatedOrderNo}
            </if>
            <if test="status != null">
                and `status` = #{status}
            </if>
            <if test="payOrderNo != null and payOrderNo != ''">
                and pay_order_no = #{payOrderNo}
            </if>
            <if test="incAssetType != null">
                and income_asset_type = #{incAssetType}
            </if>
            <if test="decAssetType != null">
                and expense_asset_type = #{decAssetType}
            </if>
            <if test="applyBy != null and applyBy != ''">
                and apply_by = #{applyBy}
            </if>
            <if test="adjustSource != null">
                and adjust_source = #{adjustSource}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="auditBy != null and auditBy != ''">
                and audit_by = #{auditBy}
            </if>
        </where>
    </sql>

    <select id="queryByIds" parameterType="com.youpin.clear.client.request.AdjustApplyListQueryRequest"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from adjustment_apply
        where id in
        <foreach collection="ids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryByApplyStatus" parameterType="com.youpin.clear.client.request.AdjustApplyListQueryRequest"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from adjustment_apply
        where `status` in
        <foreach collection="statusList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        order by id
        limit #{size}
    </select>

    <update id="updateApplyStatus">
        update adjustment_apply
        set `status` = #{expectStatus}
        where id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        and `status` = #{currentStatus}
    </update>

</mapper>