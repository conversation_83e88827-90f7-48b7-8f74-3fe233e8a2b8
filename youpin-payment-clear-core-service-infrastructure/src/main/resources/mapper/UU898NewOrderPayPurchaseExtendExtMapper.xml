<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898NewOrderPayPurchaseExtendExtMapper">

    <sql id="Base_Column_List_Ext">
        Id, UserId, OrderNo, PayOrderNo, PurchaseMoneyFromMoney, LeftPurchaseMoneyFromMoney,
        AddTime, UpdateTime
    </sql>

    <select id="selectList"
            resultType="com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend">
        select
        <include refid="Base_Column_List_Ext"/>
        from NewOrderPayPurchaseExtend
        where  UserId = #{userId} and OrderNo = #{orderNo}
        <if test="payOrderNo != null">
            and PayOrderNo = #{payOrderNo}
        </if>
        limit 10
    </select>

    <update id="updatePurchaseMoneyFromMoney">
        Update NewOrderPayPurchaseExtend
        set LeftPurchaseMoneyFromMoney = LeftPurchaseMoneyFromMoney - #{money},
        UpdateTime = #{updateTime}
        where Id = #{id}
        and UserId = #{userId}
        and OrderNo = #{orderNo}
        and PayOrderNo = #{payOrderNo}
    </update>

</mapper>