<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.ext.UU898UserSubAccountFlowRecordExtMapper">


    <select id="listSubAccountRecord"
            resultMap="com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountFlowRecordMapper.BaseResultMap">
        select *
        from uu898_user_sub_account_flow_record
        where user_id = #{userId}
        and account_no = #{accountNo}
        and order_no = #{orderNo}
        and pay_order_no = #{payOrderNo}
        and journal_type in
        <foreach collection="journalTypeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listSubAccountRecordByTime"
            resultMap="com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountFlowRecordMapper.BaseResultMap">
        select *
        from uu898_user_sub_account_flow_record
        where user_id = #{userId}
        and account_no = #{accountNo}
        and create_time between #{startCreateTime} and #{endCreateTime}
        and journal_type in
        <foreach collection="journalTypeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
