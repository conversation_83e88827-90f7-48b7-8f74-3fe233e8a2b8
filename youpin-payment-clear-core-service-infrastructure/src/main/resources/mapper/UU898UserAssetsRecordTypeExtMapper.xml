<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordTypeExtMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="TypeName" jdbcType="VARCHAR" property="typeName" />
    <result column="OperateType" jdbcType="INTEGER" property="operateType" />
    <result column="TypeCode" jdbcType="INTEGER" property="typeCode" />
    <result column="OperateTypeUser" jdbcType="INTEGER" property="operateTypeUser" />
    <result column="CheckType" jdbcType="INTEGER" property="checkType" />
  </resultMap>
  <sql id="Base_Column_List">
    Id, TypeName, OperateType, TypeCode, OperateTypeUser, CheckType
  </sql>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
        from UserAssetsRecordType
    where Id in
    <foreach collection="ids" separator="," item="item" close=")" open="(">
      #{item}
    </foreach>
  </select>

  <select id="selectByOperateTypeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from UserAssetsRecordType
    where OperateType in
    <foreach collection="list" separator="," item="item" close=")" open="(">
      #{item}
    </foreach>
  </select>
</mapper>