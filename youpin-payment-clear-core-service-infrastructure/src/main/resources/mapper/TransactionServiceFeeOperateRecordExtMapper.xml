<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.TransactionServiceFeeOperateRecordExtMapper">

    <sql id="Base_Column_List">
        id, operation_date, platform_user_id, type_id, serial_no, order_fee_type, fee_money,
        `status`, create_time, update_time
    </sql>


    <select id="selectBySerialNo"
            resultType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord">
        select
        <include refid="Base_Column_List"/>
        from transaction_service_fee_operate_record
        where serial_no = #{serialNo} and status = 2
    </select>

    <select id="selectPageByStatus"
            resultType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord">
        select
        <include refid="Base_Column_List"/>
        from transaction_service_fee_operate_record
        where status = #{status}
        order by id
        limit 100
    </select>

    <select id="reportFinanceSummary" resultType="java.math.BigDecimal">
        select IFNULL(sum(fee_money),0) from transaction_service_fee_operate_record where operation_date =
        #{operationDate} and status = #{status}
    </select>

</mapper>