<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.ext.UU898UserSubAccountExtMapper">

    <insert id="batchInsert">
        insert ignore into uu898_user_sub_account
        ( id, account_no, user_id
        , account_type, status, currency
        , balance, last_flow_id, ext_info
        , create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.id,jdbcType=BIGINT}, #{item.accountNo,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}
            , #{item.accountType,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, #{item.currency,jdbcType=CHAR}
            , #{item.balance,jdbcType=DECIMAL}, #{item.lastFlowId,jdbcType=BIGINT}, #{item.extInfo,jdbcType=VARCHAR}
            , #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateSubAccount">
        update uu898_user_sub_account
        set balance      = #{subAccount.balance},
            last_flow_id = #{subAccount.lastFlowId}
        where id = #{subAccount.id}
          and user_id = #{subAccount.userId}
          and account_no = #{subAccount.accountNo}
          and balance = #{beforeBalance}
    </update>

    <select id="selectByUserId" parameterType="java.lang.Long"
            resultMap="com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountMapper.BaseResultMap">
        select *
        from uu898_user_sub_account
        where user_id = #{userId}
    </select>
</mapper>