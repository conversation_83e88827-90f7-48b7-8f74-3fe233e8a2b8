<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AdjustmentApplyMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="related_order_no" jdbcType="VARCHAR" property="relatedOrderNo" />
    <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="income_user_id" jdbcType="BIGINT" property="incomeUserId" />
    <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount" />
    <result column="income_asset_type" jdbcType="BIGINT" property="incomeAssetType" />
    <result column="income_pay_channel" jdbcType="INTEGER" property="incomePayChannel" />
    <result column="income_service_fee" jdbcType="DECIMAL" property="incomeServiceFee" />
    <result column="expense_user_id" jdbcType="BIGINT" property="expenseUserId" />
    <result column="expense_amount" jdbcType="DECIMAL" property="expenseAmount" />
    <result column="expense_asset_type" jdbcType="BIGINT" property="expenseAssetType" />
    <result column="expense_pay_channel" jdbcType="INTEGER" property="expensePayChannel" />
    <result column="expense_service_fee" jdbcType="DECIMAL" property="expenseServiceFee" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="apply_by" jdbcType="VARCHAR" property="applyBy" />
    <result column="apply_remark" jdbcType="VARCHAR" property="applyRemark" />
    <result column="audit_by" jdbcType="VARCHAR" property="auditBy" />
    <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="adjust_source" jdbcType="INTEGER" property="adjustSource" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
    <result column="extend" jdbcType="LONGVARCHAR" property="extend" />
  </resultMap>
  <sql id="Base_Column_List">
    id, apply_no, related_order_no, pay_order_no, income_user_id, income_amount, income_asset_type, 
    income_pay_channel, income_service_fee, expense_user_id, expense_amount, expense_asset_type, 
    expense_pay_channel, expense_service_fee, `status`, apply_by, apply_remark, audit_by, 
    audit_remark, audit_time, create_time, update_time, adjust_source, batch_no
  </sql>
  <sql id="Blob_Column_List">
    extend
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from adjustment_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from adjustment_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApply" useGeneratedKeys="true">
    insert into adjustment_apply (apply_no, related_order_no, pay_order_no, 
      income_user_id, income_amount, income_asset_type, 
      income_pay_channel, income_service_fee, expense_user_id, 
      expense_amount, expense_asset_type, expense_pay_channel, 
      expense_service_fee, `status`, apply_by, 
      apply_remark, audit_by, audit_remark, 
      audit_time, create_time, update_time, 
      adjust_source, batch_no, extend
      )
    values (#{applyNo,jdbcType=VARCHAR}, #{relatedOrderNo,jdbcType=VARCHAR}, #{payOrderNo,jdbcType=VARCHAR}, 
      #{incomeUserId,jdbcType=BIGINT}, #{incomeAmount,jdbcType=DECIMAL}, #{incomeAssetType,jdbcType=BIGINT}, 
      #{incomePayChannel,jdbcType=INTEGER}, #{incomeServiceFee,jdbcType=DECIMAL}, #{expenseUserId,jdbcType=BIGINT}, 
      #{expenseAmount,jdbcType=DECIMAL}, #{expenseAssetType,jdbcType=BIGINT}, #{expensePayChannel,jdbcType=INTEGER}, 
      #{expenseServiceFee,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, #{applyBy,jdbcType=VARCHAR}, 
      #{applyRemark,jdbcType=VARCHAR}, #{auditBy,jdbcType=VARCHAR}, #{auditRemark,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{adjustSource,jdbcType=INTEGER}, #{batchNo,jdbcType=VARCHAR}, #{extend,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApply" useGeneratedKeys="true">
    insert into adjustment_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="relatedOrderNo != null">
        related_order_no,
      </if>
      <if test="payOrderNo != null">
        pay_order_no,
      </if>
      <if test="incomeUserId != null">
        income_user_id,
      </if>
      <if test="incomeAmount != null">
        income_amount,
      </if>
      <if test="incomeAssetType != null">
        income_asset_type,
      </if>
      <if test="incomePayChannel != null">
        income_pay_channel,
      </if>
      <if test="incomeServiceFee != null">
        income_service_fee,
      </if>
      <if test="expenseUserId != null">
        expense_user_id,
      </if>
      <if test="expenseAmount != null">
        expense_amount,
      </if>
      <if test="expenseAssetType != null">
        expense_asset_type,
      </if>
      <if test="expensePayChannel != null">
        expense_pay_channel,
      </if>
      <if test="expenseServiceFee != null">
        expense_service_fee,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="applyBy != null">
        apply_by,
      </if>
      <if test="applyRemark != null">
        apply_remark,
      </if>
      <if test="auditBy != null">
        audit_by,
      </if>
      <if test="auditRemark != null">
        audit_remark,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="adjustSource != null">
        adjust_source,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedOrderNo != null">
        #{relatedOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="incomeUserId != null">
        #{incomeUserId,jdbcType=BIGINT},
      </if>
      <if test="incomeAmount != null">
        #{incomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="incomeAssetType != null">
        #{incomeAssetType,jdbcType=BIGINT},
      </if>
      <if test="incomePayChannel != null">
        #{incomePayChannel,jdbcType=INTEGER},
      </if>
      <if test="incomeServiceFee != null">
        #{incomeServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="expenseUserId != null">
        #{expenseUserId,jdbcType=BIGINT},
      </if>
      <if test="expenseAmount != null">
        #{expenseAmount,jdbcType=DECIMAL},
      </if>
      <if test="expenseAssetType != null">
        #{expenseAssetType,jdbcType=BIGINT},
      </if>
      <if test="expensePayChannel != null">
        #{expensePayChannel,jdbcType=INTEGER},
      </if>
      <if test="expenseServiceFee != null">
        #{expenseServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="applyBy != null">
        #{applyBy,jdbcType=VARCHAR},
      </if>
      <if test="applyRemark != null">
        #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        #{auditBy,jdbcType=VARCHAR},
      </if>
      <if test="auditRemark != null">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustSource != null">
        #{adjustSource,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
    update adjustment_apply
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="relatedOrderNo != null">
        related_order_no = #{relatedOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="incomeUserId != null">
        income_user_id = #{incomeUserId,jdbcType=BIGINT},
      </if>
      <if test="incomeAmount != null">
        income_amount = #{incomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="incomeAssetType != null">
        income_asset_type = #{incomeAssetType,jdbcType=BIGINT},
      </if>
      <if test="incomePayChannel != null">
        income_pay_channel = #{incomePayChannel,jdbcType=INTEGER},
      </if>
      <if test="incomeServiceFee != null">
        income_service_fee = #{incomeServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="expenseUserId != null">
        expense_user_id = #{expenseUserId,jdbcType=BIGINT},
      </if>
      <if test="expenseAmount != null">
        expense_amount = #{expenseAmount,jdbcType=DECIMAL},
      </if>
      <if test="expenseAssetType != null">
        expense_asset_type = #{expenseAssetType,jdbcType=BIGINT},
      </if>
      <if test="expensePayChannel != null">
        expense_pay_channel = #{expensePayChannel,jdbcType=INTEGER},
      </if>
      <if test="expenseServiceFee != null">
        expense_service_fee = #{expenseServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="applyBy != null">
        apply_by = #{applyBy,jdbcType=VARCHAR},
      </if>
      <if test="applyRemark != null">
        apply_remark = #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        audit_by = #{auditBy,jdbcType=VARCHAR},
      </if>
      <if test="auditRemark != null">
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustSource != null">
        adjust_source = #{adjustSource,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
    update adjustment_apply
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      related_order_no = #{relatedOrderNo,jdbcType=VARCHAR},
      pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      income_user_id = #{incomeUserId,jdbcType=BIGINT},
      income_amount = #{incomeAmount,jdbcType=DECIMAL},
      income_asset_type = #{incomeAssetType,jdbcType=BIGINT},
      income_pay_channel = #{incomePayChannel,jdbcType=INTEGER},
      income_service_fee = #{incomeServiceFee,jdbcType=DECIMAL},
      expense_user_id = #{expenseUserId,jdbcType=BIGINT},
      expense_amount = #{expenseAmount,jdbcType=DECIMAL},
      expense_asset_type = #{expenseAssetType,jdbcType=BIGINT},
      expense_pay_channel = #{expensePayChannel,jdbcType=INTEGER},
      expense_service_fee = #{expenseServiceFee,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      apply_by = #{applyBy,jdbcType=VARCHAR},
      apply_remark = #{applyRemark,jdbcType=VARCHAR},
      audit_by = #{auditBy,jdbcType=VARCHAR},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      adjust_source = #{adjustSource,jdbcType=INTEGER},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.AdjustmentApply">
    update adjustment_apply
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      related_order_no = #{relatedOrderNo,jdbcType=VARCHAR},
      pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
      income_user_id = #{incomeUserId,jdbcType=BIGINT},
      income_amount = #{incomeAmount,jdbcType=DECIMAL},
      income_asset_type = #{incomeAssetType,jdbcType=BIGINT},
      income_pay_channel = #{incomePayChannel,jdbcType=INTEGER},
      income_service_fee = #{incomeServiceFee,jdbcType=DECIMAL},
      expense_user_id = #{expenseUserId,jdbcType=BIGINT},
      expense_amount = #{expenseAmount,jdbcType=DECIMAL},
      expense_asset_type = #{expenseAssetType,jdbcType=BIGINT},
      expense_pay_channel = #{expensePayChannel,jdbcType=INTEGER},
      expense_service_fee = #{expenseServiceFee,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      apply_by = #{applyBy,jdbcType=VARCHAR},
      apply_remark = #{applyRemark,jdbcType=VARCHAR},
      audit_by = #{auditBy,jdbcType=VARCHAR},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      adjust_source = #{adjustSource,jdbcType=INTEGER},
      batch_no = #{batchNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>