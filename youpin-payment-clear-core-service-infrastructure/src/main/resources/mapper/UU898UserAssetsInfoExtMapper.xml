<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoExtMapper">


    <update id="updateTimeById">
        update UserAssetsInfo
        set UpdateTime = now()
        where UserId = #{userId}
    </update>

    <select id="getUserAssetsInfoByUserIdMinMax"
            resultType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
        select * from UserAssetsInfo where UserId > #{userIdMin} and  <![CDATA[  UserId <= #{userIdMax} ]]>
        <if test="easy==1">
            and (Money >0 OR PurchaseMoney>0)
        </if>
    </select>

    <update id="updateUserBalance">
        update UserAssetsInfo
        set Money            = #{toUpdate.money},
            UpdateTime       = #{toUpdate.updateTime},
            LastUserRecordId = #{toUpdate.lastUserRecordId}
        where UserId = #{origin.userId}
          and Money = #{origin.money}
    </update>

    <update id="updateUserPurchaseBalance">
        update UserAssetsInfo
        set PurchaseMoney = #{toUpdate.purchaseMoney},
            PurchaseMoneyFromMoney = #{toUpdate.purchaseMoneyFromMoney},
            UpdateTime             = #{toUpdate.updateTime},
            LastUserRecordId       = #{toUpdate.lastUserRecordId}
        where UserId = #{origin.userId}
          and PurchaseMoney = #{origin.purchaseMoney}
          and PurchaseMoneyFromMoney = #{origin.purchaseMoneyFromMoney}
    </update>

    <select id="selectByUserIdList"
            resultType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
        select * from UserAssetsInfo where UserId in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateUserBalanceAndBlockMoney">
        update UserAssetsInfo
        set Money            = #{toUpdate.money},
            BlockMoney       = #{toUpdate.blockMoney},
        UpdateTime       = #{toUpdate.updateTime},
        LastUserRecordId = #{toUpdate.lastUserRecordId}
        where UserId = #{origin.userId}
        and Money = #{origin.money}
        and BlockMoney = #{origin.blockMoney}
    </update>

    <select id="selectByUserId"
            resultType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
        select * from UserAssetsInfo where UserId = #{userId}
    </select>


</mapper>