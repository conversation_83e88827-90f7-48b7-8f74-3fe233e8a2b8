<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountRecordOrderRelateMapper">
    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_assets_record_id" jdbcType="BIGINT" property="userAssetsRecordId"/>
        <result column="type_id" jdbcType="INTEGER" property="typeId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, user_assets_record_id, type_id, order_no, pay_order_no, create_time,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_account_record_order_relate
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from user_account_record_order_relate
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate"
            useGeneratedKeys="true">
        insert into user_account_record_order_relate (user_id, user_assets_record_id, type_id,
        order_no, pay_order_no, create_time,
        update_time)
        values (#{userId,jdbcType=BIGINT}, #{userAssetsRecordId,jdbcType=BIGINT}, #{typeId,jdbcType=INTEGER},
        #{orderNo,jdbcType=VARCHAR}, #{payOrderNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate"
            useGeneratedKeys="true">
        insert into user_account_record_order_relate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="userAssetsRecordId != null">
                user_assets_record_id,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="payOrderNo != null">
                pay_order_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userAssetsRecordId != null">
                #{userAssetsRecordId,jdbcType=BIGINT},
            </if>
            <if test="typeId != null">
                #{typeId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null">
                #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate">
        update user_account_record_order_relate
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userAssetsRecordId != null">
                user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
            </if>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null">
                pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate">
        update user_account_record_order_relate
        set user_id = #{userId,jdbcType=BIGINT},
        user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
        type_id = #{typeId,jdbcType=INTEGER},
        order_no = #{orderNo,jdbcType=VARCHAR},
        pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>