<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898TbCaiwuDaylogMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ddate" jdbcType="DATE" property="ddate" />
    <result column="ttype" jdbcType="INTEGER" property="ttype" />
    <result column="typename" jdbcType="VARCHAR" property="typename" />
    <result column="allmoney" jdbcType="DECIMAL" property="allmoney" />
    <result column="alipay" jdbcType="DECIMAL" property="alipay" />
    <result column="wechat" jdbcType="DECIMAL" property="wechat" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="jd" jdbcType="DECIMAL" property="jd" />
    <result column="douyinpay" jdbcType="DECIMAL" property="douyinpay" />
    <result column="yibao" jdbcType="DECIMAL" property="yibao" />
    <result column="displaystatus" jdbcType="INTEGER" property="displaystatus" />
    <result column="source" jdbcType="INTEGER" property="source" />
  </resultMap>
  <sql id="Base_Column_List">
    id, ddate, ttype, typename, allmoney, alipay, wechat, money, jd, douyinpay, yibao, 
    displaystatus, `source`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_caiwu_daylog
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_caiwu_daylog
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog">
    insert into tb_caiwu_daylog (id, ddate, ttype, 
      typename, allmoney, alipay, 
      wechat, money, jd, 
      douyinpay, yibao, displaystatus, 
      `source`)
    values (#{id,jdbcType=BIGINT}, #{ddate,jdbcType=DATE}, #{ttype,jdbcType=INTEGER}, 
      #{typename,jdbcType=VARCHAR}, #{allmoney,jdbcType=DECIMAL}, #{alipay,jdbcType=DECIMAL}, 
      #{wechat,jdbcType=DECIMAL}, #{money,jdbcType=DECIMAL}, #{jd,jdbcType=DECIMAL}, 
      #{douyinpay,jdbcType=DECIMAL}, #{yibao,jdbcType=DECIMAL}, #{displaystatus,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog">
    insert into tb_caiwu_daylog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ddate != null">
        ddate,
      </if>
      <if test="ttype != null">
        ttype,
      </if>
      <if test="typename != null">
        typename,
      </if>
      <if test="allmoney != null">
        allmoney,
      </if>
      <if test="alipay != null">
        alipay,
      </if>
      <if test="wechat != null">
        wechat,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="jd != null">
        jd,
      </if>
      <if test="douyinpay != null">
        douyinpay,
      </if>
      <if test="yibao != null">
        yibao,
      </if>
      <if test="displaystatus != null">
        displaystatus,
      </if>
      <if test="source != null">
        `source`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ddate != null">
        #{ddate,jdbcType=DATE},
      </if>
      <if test="ttype != null">
        #{ttype,jdbcType=INTEGER},
      </if>
      <if test="typename != null">
        #{typename,jdbcType=VARCHAR},
      </if>
      <if test="allmoney != null">
        #{allmoney,jdbcType=DECIMAL},
      </if>
      <if test="alipay != null">
        #{alipay,jdbcType=DECIMAL},
      </if>
      <if test="wechat != null">
        #{wechat,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="jd != null">
        #{jd,jdbcType=DECIMAL},
      </if>
      <if test="douyinpay != null">
        #{douyinpay,jdbcType=DECIMAL},
      </if>
      <if test="yibao != null">
        #{yibao,jdbcType=DECIMAL},
      </if>
      <if test="displaystatus != null">
        #{displaystatus,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog">
    update tb_caiwu_daylog
    <set>
      <if test="ddate != null">
        ddate = #{ddate,jdbcType=DATE},
      </if>
      <if test="ttype != null">
        ttype = #{ttype,jdbcType=INTEGER},
      </if>
      <if test="typename != null">
        typename = #{typename,jdbcType=VARCHAR},
      </if>
      <if test="allmoney != null">
        allmoney = #{allmoney,jdbcType=DECIMAL},
      </if>
      <if test="alipay != null">
        alipay = #{alipay,jdbcType=DECIMAL},
      </if>
      <if test="wechat != null">
        wechat = #{wechat,jdbcType=DECIMAL},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="jd != null">
        jd = #{jd,jdbcType=DECIMAL},
      </if>
      <if test="douyinpay != null">
        douyinpay = #{douyinpay,jdbcType=DECIMAL},
      </if>
      <if test="yibao != null">
        yibao = #{yibao,jdbcType=DECIMAL},
      </if>
      <if test="displaystatus != null">
        displaystatus = #{displaystatus,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog">
    update tb_caiwu_daylog
    set ddate = #{ddate,jdbcType=DATE},
      ttype = #{ttype,jdbcType=INTEGER},
      typename = #{typename,jdbcType=VARCHAR},
      allmoney = #{allmoney,jdbcType=DECIMAL},
      alipay = #{alipay,jdbcType=DECIMAL},
      wechat = #{wechat,jdbcType=DECIMAL},
      money = #{money,jdbcType=DECIMAL},
      jd = #{jd,jdbcType=DECIMAL},
      douyinpay = #{douyinpay,jdbcType=DECIMAL},
      yibao = #{yibao,jdbcType=DECIMAL},
      displaystatus = #{displaystatus,jdbcType=INTEGER},
      `source` = #{source,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>