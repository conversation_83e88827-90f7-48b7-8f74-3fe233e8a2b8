<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAssetsTagMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tag_code" jdbcType="VARCHAR" property="tagCode" />
    <result column="tag_value_decimal" jdbcType="DECIMAL" property="tagValueDecimal" />
    <result column="tag_value" jdbcType="VARCHAR" property="tagValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, tag_code, tag_value_decimal, tag_value, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_assets_tag
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_assets_tag
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag" useGeneratedKeys="true">
    insert into user_assets_tag (user_id, tag_code, tag_value_decimal, 
      tag_value, create_time, update_time
      )
    values (#{userId,jdbcType=BIGINT}, #{tagCode,jdbcType=VARCHAR}, #{tagValueDecimal,jdbcType=DECIMAL}, 
      #{tagValue,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag" useGeneratedKeys="true">
    insert into user_assets_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="tagCode != null">
        tag_code,
      </if>
      <if test="tagValueDecimal != null">
        tag_value_decimal,
      </if>
      <if test="tagValue != null">
        tag_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="tagCode != null">
        #{tagCode,jdbcType=VARCHAR},
      </if>
      <if test="tagValueDecimal != null">
        #{tagValueDecimal,jdbcType=DECIMAL},
      </if>
      <if test="tagValue != null">
        #{tagValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
    update user_assets_tag
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="tagCode != null">
        tag_code = #{tagCode,jdbcType=VARCHAR},
      </if>
      <if test="tagValueDecimal != null">
        tag_value_decimal = #{tagValueDecimal,jdbcType=DECIMAL},
      </if>
      <if test="tagValue != null">
        tag_value = #{tagValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
    update user_assets_tag
    set user_id = #{userId,jdbcType=BIGINT},
      tag_code = #{tagCode,jdbcType=VARCHAR},
      tag_value_decimal = #{tagValueDecimal,jdbcType=DECIMAL},
      tag_value = #{tagValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>