<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountRecordExtMapper">


    <sql id="Base_Column_List">
        id, account_record_no, user_id, type_id, user_assets_record_id, tread_no, serial_no,
        order_no, pay_order_no, user_account_no, account_type, balance_before, balance_change,
        balance_after, balance_is_change, `status`, pay_channel, finish_time, ext, create_time,
        update_time, frozen_balance_before, frozen_balance_change, frozen_balance_after
    </sql>

    <select id="getUserAccountRecordByOrderNo"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_record where order_no = #{orderNo} and user_id = #{userId}
    </select>

    <select id="getUserAccountRecordByUserIdAndUserAssetsRecordId"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_record where user_id = #{userId} and user_assets_record_id = #{userAssetsRecordId}
        <if test="accountType != null">
            and account_type = #{accountType,jdbcType=INTEGER}
        </if>
    </select>

    <select id="countUserAccountRecordByUserIdAndUserAssetsRecordId" resultType="java.lang.Integer">
        select count(1) from user_account_record where user_id = #{userId} and user_assets_record_id =
        #{userAssetsRecordId}
        <if test="balanceChange != null">
            and balance_change = #{balanceChange,jdbcType=DECIMAL}
        </if>
        <if test="accountType != null">
            and account_type = #{accountType,jdbcType=INTEGER}
        </if>

    </select>

    <select id="userAccountRecordUpdateStatus" resultType="java.lang.Integer">
        update user_account_record set `status` = #{status} where user_id = #{userId} and id = #{Id}
    </select>


    <delete id="deleteByUserId">
        delete from user_account_record where user_id = #{userId}
    </delete>

    <update id="updateByPrimaryKeySelectiveUserId"
            parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        update user_account_record
        <set>
            <if test="accountRecordNo != null">
                account_record_no = #{accountRecordNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=INTEGER},
            </if>
            <if test="userAssetsRecordId != null">
                user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
            </if>
            <if test="serialNo != null">
                serial_no = #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null">
                pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="userAccountNo != null">
                user_account_no = #{userAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=INTEGER},
            </if>
            <if test="balanceBefore != null">
                balance_before = #{balanceBefore,jdbcType=DECIMAL},
            </if>
            <if test="balanceChange != null">
                balance_change = #{balanceChange,jdbcType=DECIMAL},
            </if>
            <if test="balanceAfter != null">
                balance_after = #{balanceAfter,jdbcType=DECIMAL},
            </if>
            <if test="balanceIsChange != null">
                balance_is_change = #{balanceIsChange,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="frozenBalanceBefore != null">
                frozen_balance_before = #{frozenBalanceBefore,jdbcType=DECIMAL},
            </if>
            <if test="frozenBalanceChange != null">
                frozen_balance_change = #{frozenBalanceChange,jdbcType=DECIMAL},
            </if>
            <if test="frozenBalanceAfter != null">
                frozen_balance_after = #{frozenBalanceAfter,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId}
    </update>

    <select id="sumBalanceByIdAndAccountType" resultType="java.math.BigDecimal">
        select IFNULL(sum(balance_change),0) from user_account_record where user_id = #{userId} and balance_is_change =1
        and account_type = #{accountType}
        <if test="startId != null">
            <![CDATA[  and id >= #{startId} ]]>
        </if>
        <if test="entId != null">
            <![CDATA[  and id <= #{entId} ]]>
        </if>
    </select>

    <select id="getByUserIdAndId"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_record where user_id = #{userId} and id = #{id}
    </select>

    <select id="minIdByUserId" resultType="java.lang.Long">
        select min(id) from user_account_record where user_id = #{userId}
        <if test="accountType != null">
            and account_type = #{accountType}
        </if>
    </select>

    <select id="minUserAssetsRecordId" resultType="java.lang.Long">
        select min(user_assets_record_id) from user_account_record where user_id = #{userId}
    </select>


    <select id="selectUserAccountRecordById"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_record where user_id = #{userId,jdbcType=BIGINT}
        <if test="minUserAssetsRecordId != null and minUserAssetsRecordId>0 ">
            and user_assets_record_id >= #{minUserAssetsRecordId,jdbcType=BIGINT}
        </if>
        <if test="maxUserAssetsRecordId != null and maxUserAssetsRecordId>0 ">
            <![CDATA[ and user_assets_record_id <= #{maxUserAssetsRecordId,jdbcType=BIGINT}]]>
        </if>
    </select>

    <select id="selectLastUserAccountRecordByUserIdAndAccountType" resultType="java.lang.Long">
        select max(user_assets_record_id) from user_account_record where user_id = #{userId}
        <![CDATA[ and user_assets_record_id < #{userAssetsRecordId} ]]>
        and account_type= #{accountType};
    </select>


    <select id="selectByUserAssetsRecordId" resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_record where user_id = #{userId,jdbcType=BIGINT}
        and user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT}
        and account_type= #{accountType}
        limit 1
    </select>

    <select id="selectByUserIdAndOrderNo"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_record where user_id = #{userId,jdbcType=BIGINT}
        and order_no = #{orderNo,jdbcType=VARCHAR}
    </select>
</mapper>