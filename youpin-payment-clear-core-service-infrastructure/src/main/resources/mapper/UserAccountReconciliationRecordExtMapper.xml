<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountReconciliationRecordExtMapper">

    <sql id="Base_Column_List">
        id, user_id, user_account_no, account_type, last_account_record_id, check_flag, user_weight,
        ext, create_time, update_time
    </sql>

    <select id="getByUserIdAndAccountType"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord">
        select
        <include refid="Base_Column_List"/>
        from user_account_reconciliation_record
        where user_id = #{userId}
        and account_type = #{accountType}
        limit 1
    </select>

    <update id="updateByUserIdAndIdSelective">
        update user_account_reconciliation_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userAccountNo != null">
                user_account_no = #{userAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=INTEGER},
            </if>
            <if test="lastAccountRecordId != null">
                last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
            </if>
            <if test="checkFlag != null">
                check_flag = #{checkFlag,jdbcType=INTEGER},
            </if>
            <if test="userWeight != null">
                user_weight = #{userWeight,jdbcType=BIGINT},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <delete id="deleteByUserId">
        delete from user_account_reconciliation_record where user_id = #{userId}
    </delete>

</mapper>