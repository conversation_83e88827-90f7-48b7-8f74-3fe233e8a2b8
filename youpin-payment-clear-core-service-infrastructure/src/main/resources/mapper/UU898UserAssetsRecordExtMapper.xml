<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordExtMapper">

    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="UserId" jdbcType="BIGINT" property="userId"/>
        <result column="TypeId" jdbcType="INTEGER" property="typeId"/>
        <result column="TreadNo" jdbcType="VARCHAR" property="treadNo"/>
        <result column="AssetType" jdbcType="INTEGER" property="assetType"/>
        <result column="Money" jdbcType="DECIMAL" property="money"/>
        <result column="ThisMoney" jdbcType="DECIMAL" property="thisMoney"/>
        <result column="AfterMoney" jdbcType="DECIMAL" property="afterMoney"/>
        <result column="ChargeMoney" jdbcType="DECIMAL" property="chargeMoney"/>
        <result column="BlockMoney" jdbcType="DECIMAL" property="blockMoney"/>
        <result column="ThisBlockMoney" jdbcType="DECIMAL" property="thisBlockMoney"/>
        <result column="AfterBlockMoney" jdbcType="DECIMAL" property="afterBlockMoney"/>
        <result column="PurchaseMoney" jdbcType="DECIMAL" property="purchaseMoney"/>
        <result column="ThisPurchaseMoney" jdbcType="DECIMAL" property="thisPurchaseMoney"/>
        <result column="AfterPurchaseMoney" jdbcType="DECIMAL" property="afterPurchaseMoney"/>
        <result column="SerialNo" jdbcType="VARCHAR" property="serialNo"/>
        <result column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="PayOrderNo" jdbcType="VARCHAR" property="payOrderNo"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="AddTime" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="Attr" jdbcType="INTEGER" property="attr"/>
        <result column="Status" jdbcType="INTEGER" property="status"/>
        <result column="PayChannel" jdbcType="INTEGER" property="payChannel"/>
        <result column="AccountName" jdbcType="VARCHAR" property="accountName"/>
        <result column="PayWaitExpireTime" jdbcType="TIMESTAMP" property="payWaitExpireTime"/>
        <result column="GenSource" jdbcType="VARCHAR" property="genSource"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, UserId, TypeId, TreadNo, AssetType, Money, ThisMoney, AfterMoney, ChargeMoney,
        BlockMoney, ThisBlockMoney, AfterBlockMoney, PurchaseMoney, ThisPurchaseMoney, AfterPurchaseMoney,
        SerialNo, OrderNo, PayOrderNo, Remark, AddTime, CompleteTime, Attr, `Status`, PayChannel,
        AccountName, PayWaitExpireTime, GenSource
    </sql>

    <select id="queryUserAssetsRecordDTOList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UserAssetsRecordUU898
        <where>
            <if test="userId != null and userId != ''">
                and UserId = #{userId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and OrderNo = #{orderNo}
            </if>
            <if test="serialNo != null and serialNo != ''">
                and SerialNo = #{serialNo}
            </if>
            <if test="payOrderNo != null and payOrderNo != ''">
                and PayOrderNo = #{payOrderNo}
            </if>
            <if test="typeId != null">
                and TypeId = #{typeId}
            </if>
            <if test="status != null">
                and Status = #{status}
            </if>
        </where>
    </select>

    <select id="countByAddTime" resultType="java.lang.Long">
        select count(*) from UserAssetsRecordUU898
        <where>
            <if test="lastId != null">
                and Id > #{lastId}
            </if>
            <if test="startTime != null">
                and AddTime > #{startTime}
            </if>
            <if test="endTime != null">
                and AddTime &lt; #{endTime}
            </if>
        </where>
    </select>

    <select id="selectByAddTimePage" resultType="java.lang.Long">
        select Id from UserAssetsRecordUU898
        <where>
            <if test="lastId != null">
                and Id > #{lastId}
            </if>
            <if test="startTime != null">
                and AddTime > #{startTime}
            </if>
            <if test="endTime != null">
                and AddTime &lt; #{endTime}
            </if>
            order by AddTime limit #{pageIndex},#{pageSize}
        </where>
    </select>

    <select id="selectByIdInterval" resultType="java.lang.Long">
        select Id from UserAssetsRecordUU898
        <where>
            <if test="startId != null">
                <![CDATA[  and Id > #{startId} ]]>
            </if>
            <if test="endId != null">
                <![CDATA[  and Id <= #{endId} ]]>
            </if>
            <if test="startTime != null">
                and AddTime >= #{startTime}
            </if>
            <if test="endTime != null">
                <![CDATA[  and AddTime <= #{endTime} ]]>
            </if>
            order by Id limit #{pageIndex},#{pageSize}
        </where>
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max(Id) from UserAssetsRecordUU898
    </select>

    <select id="selectByTradeNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UserAssetsRecordUU898
        where TreadNo = #{tradeNo}
    </select>

    <select id="countUserAssetsRecordDTOList" resultType="java.lang.Integer">
        select count(id) from UserAssetsRecordUU898
        <where>
            <if test="userId != null ">
                and UserId = #{userId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and OrderNo = #{orderNo}
            </if>
            <if test="serialNo != null and serialNo != ''">
                and SerialNo = #{serialNo}
            </if>
            <if test="payOrderNo != null and payOrderNo != ''">
                and PayOrderNo = #{payOrderNo}
            </if>
            <if test="typeId != null">
                and TypeId = #{typeId}
            </if>
            <if test="status != null">
                and Status = #{status}
            </if>
        </where>
    </select>

    <select id="selectIdListByUserIdAndTypeIdTimePage" resultType="java.lang.Long">
        select Id from UserAssetsRecordUU898 where Id >= #{lastId} and UserId = #{userId} and Status = 2
        <if test="typeIdList != null and typeIdList.size() > 0">
            and TypeId in
            <foreach collection="typeIdList" item="typeId" open="(" close=")" separator=",">
                #{typeId}
            </foreach>
        </if>
        <if test="endTime !=null">
            <![CDATA[  and AddTime <= #{endTime} ]]>
        </if>
        order by Id limit #{pageSize}
    </select>

    <select id="selectMinIdByUserIdAndTypeIdTime" resultType="java.lang.Long">
        select min(Id) from UserAssetsRecordUU898 where UserId = #{userId} and Status = 2
        <if test="typeIdList != null and typeIdList.size() > 0">
            and TypeId in
            <foreach collection="typeIdList" item="typeId" open="(" close=")" separator=",">
                #{typeId}
            </foreach>
        </if>
        <if test="endTime !=null">
            <![CDATA[  and AddTime <= #{endTime} ]]>
        </if>
    </select>

    <delete id="deleteByTest">
        delete from UserAssetsRecordUU898
        <where>
            <if test="userId != null">
                and UserId = #{userId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and OrderNo = #{orderNo}
            </if>
            <if test="serialNo != null and serialNo != ''">
                and SerialNo = #{serialNo}
            </if>
            <if test="payOrderNo != null and payOrderNo != ''">
                and PayOrderNo = #{payOrderNo}
            </if>
            <if test="typeId != null">
                and TypeId = #{typeId}
            </if>
        </where>
    </delete>


</mapper>