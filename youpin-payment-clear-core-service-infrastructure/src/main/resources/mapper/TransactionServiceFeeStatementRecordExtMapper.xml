<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.TransactionServiceFeeStatementRecordExtMapper">


    <select id="selectMaxId" resultType="java.lang.Long">
        select min(id) from transaction_service_fee_statement_record where order_fee_type = 0 and type_id = 181 and
        serial_no is null
    </select>

    <update id="updateSerialNo">
        update transaction_service_fee_statement_record
        set serial_no = #{serialNo}
        where
        order_fee_type = 0
        <if test="maxId != null and maxId > 0">
            and id >= #{maxId}
        </if>
        and type_id = 181 and serial_no is null
        <![CDATA[ and complete_time > #{startDate}]]>  <![CDATA[ and complete_time < #{endDate}]]>
<!--        order by id-->
        limit #{pageSize}
    </update>

    <select id="selectSumMoney" resultType="java.math.BigDecimal">
        select sum(fee_money) from transaction_service_fee_statement_record where status = 2 and serial_no = #{serialNo}
    </select>

    <!--    <select id="selectSumMoneyNotSerialNo" resultType="java.math.BigDecimal">-->
    <!--        select sum(fee_money) from transaction_service_fee_statement_record where-->
    <!--        id >= #{maxId} and status = 2 and order_fee_type = 0 and type_id = 181 and serial_no is null-->
    <!--        order by id-->
    <!--        limit #{pageSize}-->
    <!--    </select>-->

    <!--    <update id="updateSerialNoStatus">-->
    <!--        update transaction_service_fee_statement_record-->
    <!--        set status = #{status}-->
    <!--        where serial_no = #{serialNo} and status = 2-->
    <!--    </update>-->

    <select id="reportFinanceSummary" resultType="java.math.BigDecimal">
        select IFNULL(SUM(fee_money),0) from transaction_service_fee_statement_record where
        <![CDATA[ complete_time > #{startDate}]]>  <![CDATA[ and complete_time < #{endDate}]]>
        and order_fee_type = #{orderFeeType} and type_id = #{typeId}

    </select>

</mapper>