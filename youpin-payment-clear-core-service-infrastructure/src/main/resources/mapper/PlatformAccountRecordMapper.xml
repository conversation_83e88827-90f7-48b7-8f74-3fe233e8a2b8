<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.PlatformAccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="shard" jdbcType="INTEGER" property="shard" />
    <result column="user_assets_record_id" jdbcType="BIGINT" property="userAssetsRecordId" />
    <result column="tread_no" jdbcType="VARCHAR" property="treadNo" />
    <result column="money_type" jdbcType="INTEGER" property="moneyType" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="block_money" jdbcType="DECIMAL" property="blockMoney" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, shard, user_assets_record_id, tread_no, money_type, money, block_money, 
    `status`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from platform_account_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from platform_account_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord" useGeneratedKeys="true">
    insert into platform_account_record (user_id, shard, user_assets_record_id, 
      tread_no, money_type, money, 
      block_money, `status`, create_time, 
      update_time)
    values (#{userId,jdbcType=BIGINT}, #{shard,jdbcType=INTEGER}, #{userAssetsRecordId,jdbcType=BIGINT}, 
      #{treadNo,jdbcType=VARCHAR}, #{moneyType,jdbcType=INTEGER}, #{money,jdbcType=DECIMAL}, 
      #{blockMoney,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord" useGeneratedKeys="true">
    insert into platform_account_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="shard != null">
        shard,
      </if>
      <if test="userAssetsRecordId != null">
        user_assets_record_id,
      </if>
      <if test="treadNo != null">
        tread_no,
      </if>
      <if test="moneyType != null">
        money_type,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="blockMoney != null">
        block_money,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="shard != null">
        #{shard,jdbcType=INTEGER},
      </if>
      <if test="userAssetsRecordId != null">
        #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="treadNo != null">
        #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="moneyType != null">
        #{moneyType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord">
    update platform_account_record
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="shard != null">
        shard = #{shard,jdbcType=INTEGER},
      </if>
      <if test="userAssetsRecordId != null">
        user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="treadNo != null">
        tread_no = #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="moneyType != null">
        money_type = #{moneyType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        block_money = #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord">
    update platform_account_record
    set user_id = #{userId,jdbcType=BIGINT},
      shard = #{shard,jdbcType=INTEGER},
      user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      tread_no = #{treadNo,jdbcType=VARCHAR},
      money_type = #{moneyType,jdbcType=INTEGER},
      money = #{money,jdbcType=DECIMAL},
      block_money = #{blockMoney,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>