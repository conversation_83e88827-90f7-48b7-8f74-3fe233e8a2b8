<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_account_no" jdbcType="VARCHAR" property="userAccountNo" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="frozen_balance" jdbcType="DECIMAL" property="frozenBalance" />
    <result column="last_account_record_id" jdbcType="BIGINT" property="lastAccountRecordId" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_account_no, user_id, account_type, balance, frozen_balance, last_account_record_id, 
    ext, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccount" useGeneratedKeys="true">
    insert into user_account (user_account_no, user_id, account_type, 
      balance, frozen_balance, last_account_record_id, 
      ext, create_time, update_time
      )
    values (#{userAccountNo,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{accountType,jdbcType=INTEGER}, 
      #{balance,jdbcType=DECIMAL}, #{frozenBalance,jdbcType=DECIMAL}, #{lastAccountRecordId,jdbcType=BIGINT}, 
      #{ext,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccount" useGeneratedKeys="true">
    insert into user_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userAccountNo != null">
        user_account_no,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="frozenBalance != null">
        frozen_balance,
      </if>
      <if test="lastAccountRecordId != null">
        last_account_record_id,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userAccountNo != null">
        #{userAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="frozenBalance != null">
        #{frozenBalance,jdbcType=DECIMAL},
      </if>
      <if test="lastAccountRecordId != null">
        #{lastAccountRecordId,jdbcType=BIGINT},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccount">
    update user_account
    <set>
      <if test="userAccountNo != null">
        user_account_no = #{userAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="frozenBalance != null">
        frozen_balance = #{frozenBalance,jdbcType=DECIMAL},
      </if>
      <if test="lastAccountRecordId != null">
        last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccount">
    update user_account
    set user_account_no = #{userAccountNo,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      account_type = #{accountType,jdbcType=INTEGER},
      balance = #{balance,jdbcType=DECIMAL},
      frozen_balance = #{frozenBalance,jdbcType=DECIMAL},
      last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
      ext = #{ext,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>