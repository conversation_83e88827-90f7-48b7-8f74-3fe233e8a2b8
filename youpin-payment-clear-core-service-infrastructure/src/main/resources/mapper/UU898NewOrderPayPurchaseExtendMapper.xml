<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898NewOrderPayPurchaseExtendMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend">
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="UserId" jdbcType="BIGINT" property="userId" />
    <result column="OrderNo" jdbcType="VARCHAR" property="orderNo" />
    <result column="PayOrderNo" jdbcType="VARCHAR" property="payOrderNo" />
    <result column="PurchaseMoneyFromMoney" jdbcType="DECIMAL" property="purchaseMoneyFromMoney" />
    <result column="LeftPurchaseMoneyFromMoney" jdbcType="DECIMAL" property="leftPurchaseMoneyFromMoney" />
    <result column="AddTime" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UpdateTime" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    Id, UserId, OrderNo, PayOrderNo, PurchaseMoneyFromMoney, LeftPurchaseMoneyFromMoney, 
    AddTime, UpdateTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from NewOrderPayPurchaseExtend
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from NewOrderPayPurchaseExtend
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="Id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend" useGeneratedKeys="true">
    insert into NewOrderPayPurchaseExtend (UserId, OrderNo, PayOrderNo, 
      PurchaseMoneyFromMoney, LeftPurchaseMoneyFromMoney, 
      AddTime, UpdateTime)
    values (#{userId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{payOrderNo,jdbcType=VARCHAR}, 
      #{purchaseMoneyFromMoney,jdbcType=DECIMAL}, #{leftPurchaseMoneyFromMoney,jdbcType=DECIMAL}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="Id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend" useGeneratedKeys="true">
    insert into NewOrderPayPurchaseExtend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        UserId,
      </if>
      <if test="orderNo != null">
        OrderNo,
      </if>
      <if test="payOrderNo != null">
        PayOrderNo,
      </if>
      <if test="purchaseMoneyFromMoney != null">
        PurchaseMoneyFromMoney,
      </if>
      <if test="leftPurchaseMoneyFromMoney != null">
        LeftPurchaseMoneyFromMoney,
      </if>
      <if test="addTime != null">
        AddTime,
      </if>
      <if test="updateTime != null">
        UpdateTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseMoneyFromMoney != null">
        #{purchaseMoneyFromMoney,jdbcType=DECIMAL},
      </if>
      <if test="leftPurchaseMoneyFromMoney != null">
        #{leftPurchaseMoneyFromMoney,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend">
    update NewOrderPayPurchaseExtend
    <set>
      <if test="userId != null">
        UserId = #{userId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payOrderNo != null">
        PayOrderNo = #{payOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseMoneyFromMoney != null">
        PurchaseMoneyFromMoney = #{purchaseMoneyFromMoney,jdbcType=DECIMAL},
      </if>
      <if test="leftPurchaseMoneyFromMoney != null">
        LeftPurchaseMoneyFromMoney = #{leftPurchaseMoneyFromMoney,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        AddTime = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UpdateTime = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend">
    update NewOrderPayPurchaseExtend
    set UserId = #{userId,jdbcType=BIGINT},
      OrderNo = #{orderNo,jdbcType=VARCHAR},
      PayOrderNo = #{payOrderNo,jdbcType=VARCHAR},
      PurchaseMoneyFromMoney = #{purchaseMoneyFromMoney,jdbcType=DECIMAL},
      LeftPurchaseMoneyFromMoney = #{leftPurchaseMoneyFromMoney,jdbcType=DECIMAL},
      AddTime = #{addTime,jdbcType=TIMESTAMP},
      UpdateTime = #{updateTime,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>