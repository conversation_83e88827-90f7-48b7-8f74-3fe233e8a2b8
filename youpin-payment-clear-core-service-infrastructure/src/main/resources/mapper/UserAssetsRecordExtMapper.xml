<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAssetsRecordExtMapper">


    <sql id="UserAssetsRecord_Base_Column_List">
        id, merchant_id, user_assets_record_id, user_assets_record_no, user_id, type_id, type_name, tread_no,
        serial_no, order_no, pay_order_no, asset_type, money, this_money, after_money, charge_money,
        block_money, this_block_money, after_block_money, purchase_money, this_purchase_money,
        after_purchase_money, remark, ext, attr, `status`, pay_channel, account_name, pay_wait_expire_time,
        gen_source, add_time, complete_time
    </sql>


    <select id="countPkUserAssetsRecordById" resultType="java.lang.Integer">
        select count(id) from user_assets_record where user_id = #{userId} and user_assets_record_id =
        #{userAssetsRecordId}
    </select>

    <select id="countPkUserAssetsPayOrderNoRelateByPayOrderNo" resultType="java.lang.Integer">
        select count(id) from user_assets_pay_order_no_relate where user_id = #{userId} and pay_order_no = #{payOrderNo}
    </select>

    <select id="countPkUserAssetsOrderNoRelateByOrderNo" resultType="java.lang.Integer">
        select count(id) from user_assets_order_no_relate where user_id = #{userId} and order_no = #{orderNo}
    </select>

    <select id="countPkUserAssetsTreadNoRelateByTreadNo" resultType="java.lang.Integer">
        select count(id) from user_assets_tread_no_relate where user_id = #{userId} and tread_no = #{treadNo}
    </select>

    <select id="maxUserAssetsRecordId" resultType="java.lang.Long">
        select max(user_assets_record_id) from user_assets_record where user_id = #{userId}
    </select>

    <select id="getUserAssetsRecordById"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord">
        select
        <include refid="UserAssetsRecord_Base_Column_List"/>
        from user_assets_record where user_id = #{userId} and user_assets_record_id = #{userAssetsRecordId}
    </select>

    <update id="updateByPrimaryKeySelectiveByUserId">
        update user_assets_record
        <set>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=INTEGER},
            </if>
            <if test="typeName != null">
                type_name = #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="treadNo != null">
                tread_no = #{treadNo,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null">
                serial_no = #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null">
                pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="assetType != null">
                asset_type = #{assetType,jdbcType=INTEGER},
            </if>
            <if test="money != null">
                money = #{money,jdbcType=DECIMAL},
            </if>
            <if test="thisMoney != null">
                this_money = #{thisMoney,jdbcType=DECIMAL},
            </if>
            <if test="afterMoney != null">
                after_money = #{afterMoney,jdbcType=DECIMAL},
            </if>
            <if test="chargeMoney != null">
                charge_money = #{chargeMoney,jdbcType=DECIMAL},
            </if>
            <if test="blockMoney != null">
                block_money = #{blockMoney,jdbcType=DECIMAL},
            </if>
            <if test="thisBlockMoney != null">
                this_block_money = #{thisBlockMoney,jdbcType=DECIMAL},
            </if>
            <if test="afterBlockMoney != null">
                after_block_money = #{afterBlockMoney,jdbcType=DECIMAL},
            </if>
            <if test="purchaseMoney != null">
                purchase_money = #{purchaseMoney,jdbcType=DECIMAL},
            </if>
            <if test="thisPurchaseMoney != null">
                this_purchase_money = #{thisPurchaseMoney,jdbcType=DECIMAL},
            </if>
            <if test="afterPurchaseMoney != null">
                after_purchase_money = #{afterPurchaseMoney,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
            <if test="attr != null">
                attr = #{attr,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="accountName != null">
                account_name = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="payWaitExpireTime != null">
                pay_wait_expire_time = #{payWaitExpireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="genSource != null">
                gen_source = #{genSource,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                complete_time = #{completeTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>

    <select id="selectUserAssetsRecordById"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord">
        select
        <include refid="UserAssetsRecord_Base_Column_List"/>
        from user_assets_record where user_id = #{userId,jdbcType=BIGINT}
        <if test="userAssetsRecordId != null and userAssetsRecordId>0 ">
            and user_assets_record_id >= #{userAssetsRecordId,jdbcType=BIGINT}
        </if>
        order by add_time limit #{pageIndex,jdbcType=BIGINT}, #{pageSize,jdbcType=INTEGER}
    </select>

    <select id="batchGetUserAssetsRecordById" resultType="com.youpin.clear.infrastructure.dataobject.UserAssetsRecord">
        select
        <include refid="UserAssetsRecord_Base_Column_List"/>
        from user_assets_record where user_id = #{userId}
        <if test="minUserAssetsRecordId != null and minUserAssetsRecordId>0 ">
            and user_assets_record_id >= #{minUserAssetsRecordId,jdbcType=BIGINT}
        </if>
        <if test="maxUserAssetsRecordId != null and maxUserAssetsRecordId>0 ">
            <![CDATA[ and user_assets_record_id <= #{maxUserAssetsRecordId,jdbcType=BIGINT} ]]>
        </if>
    </select>




</mapper>