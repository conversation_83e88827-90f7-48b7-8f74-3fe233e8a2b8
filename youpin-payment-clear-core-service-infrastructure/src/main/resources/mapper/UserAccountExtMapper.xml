<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountExtMapper">

    <sql id="Base_Column_List">
        id, user_account_no, user_id, account_type, balance, frozen_balance, last_account_record_id,
        ext, create_time, update_time
    </sql>

    <select id="getAccountInfoMember" resultType="com.youpin.clear.infrastructure.dataobject.UserAccount">
        select
        <include refid="Base_Column_List"/>
        from
        user_account
        where
        user_id = #{userId}
    </select>


    <update id="updateBalanceAccountInfoMember">
        update user_account
        <set>
            <if test="balanceChange != null">
                balance = balance + #{balanceChange},
            </if>
            <if test="lastAccountRecordId > 0">
                last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id} and user_id = #{userId} and user_account_no = #{userAccountNo} and account_type =
        #{accountType} and balance = #{originalBalance}
    </update>


    <update id="updateFrozenBalanceAccountInfoMember">
        update user_account
        <set>
            <if test="frozenBalanceChange != null">
                frozen_balance = frozen_balance + #{frozenBalanceChange},
            </if>
            <if test="lastAccountRecordId > 0">
                last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id} and user_id = #{userId} and user_account_no = #{userAccountNo}
        and account_type = #{accountType} and frozen_balance = #{originalFrozenBalance}
    </update>


    <delete id="deleteByUserId">
        delete from user_account where user_id = #{userId}
    </delete>

    <update id="updateByIdAndUserIdSelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccount">
        update user_account
        <set>
            <if test="userAccountNo != null">
                user_account_no = #{userAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=INTEGER},
            </if>
            <if test="balance != null">
                balance = #{balance,jdbcType=DECIMAL},
            </if>
            <if test="frozenBalance != null">
                frozen_balance = #{frozenBalance,jdbcType=DECIMAL},
            </if>
            <if test="lastAccountRecordId != null">
                last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and user_id = #{userId,jdbcType=BIGINT}
    </update>


    <update id="updateAccountBalance" parameterType="com.youpin.clear.infrastructure.dataobject.UpdateAccountBalanceDO">
        update user_account
        <set>
            <if test="balanceChange != null">
                balance = balance + #{balanceChange},
            </if>
            <if test="frozenBalanceChange != null">
                frozen_balance = frozen_balance + #{frozenBalanceChange},
            </if>

            <if test="lastAccountRecordId > 0">
                last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            id = #{accountId} and user_id = #{userId} and user_account_no = #{userAccountNo} and account_type = #{accountType}
            <if test="balanceChange != null">
                and balance = #{originalBalance}
            </if>
            <if test="frozenBalanceChange != null">
                and frozen_balance = #{originalFrozenBalance}
            </if>
        </where>
    </update>

    <update id="updateAccount">
        update user_account
        <set>
            <if test="targetAccount.balance != null">
                balance = #{targetAccount.balance},
            </if>
            <if test="targetAccount.frozenBalance != null">
                frozen_balance = #{targetAccount.frozenBalance},
            </if>
            <if test="targetAccount.lastAccountRecordId != null">
                last_account_record_id = #{targetAccount.lastAccountRecordId},
            </if>
            <if test="targetAccount.ext != null">
                ext = #{targetAccount.ext},
            </if>
        </set>
        where id = #{originAccount.id}
        and user_id = #{originAccount.userId}
        and user_account_no = #{originAccount.userAccountNo}
        and account_type = #{originAccount.accountType}
        and balance = #{originAccount.balance}
        and frozen_balance = #{originAccount.frozenBalance}
    </update>

    <select id="getAccountAggregatesMapByType"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccount">
        select
        <include refid="Base_Column_List"/>
        from
        user_account
        where
        user_id = #{userId}
        <if test="accountTypeCodeList != null and accountTypeCodeList.size() > 0">
            and account_type in
            <foreach item="item" collection="accountTypeCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


</mapper>