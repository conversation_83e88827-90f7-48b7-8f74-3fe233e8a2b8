<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAssetsTagExtMapper">
    <sql id="Base_Column_List">
        id, user_id, tag_code, tag_value_decimal, tag_value, create_time, update_time
    </sql>

    <select id="getUserAssetsTagByTag" resultType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
        select
        <include refid="Base_Column_List"/>
        from user_assets_tag where user_id = #{userId,jdbcType=BIGINT} and tag_code = #{tagCode,jdbcType=VARCHAR}
    </select>

    <update id="updateByUserId" parameterType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
        update user_assets_tag
        <set>
            <if test="tagCode != null">
                tag_code = #{tagCode,jdbcType=VARCHAR},
            </if>
            <if test="tagValueDecimal != null">
                tag_value_decimal = #{tagValueDecimal,jdbcType=INTEGER},
            </if>
            <if test="tagValue != null">
                tag_value = #{tagValue,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=BIGINT} and tag_code = #{tagCode,jdbcType=VARCHAR} and id =
        #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBigDecimalByUserId">
        update user_assets_tag set tag_value_decimal = tag_value_decimal + #{changeTagValueDecimal}
        where user_id = #{userId,jdbcType=BIGINT} and tag_code = #{tagCode,jdbcType=VARCHAR} and id =
        #{id,jdbcType=BIGINT}
    </update>

    <select id="getUserAssetsTagByUserId" resultType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
        select
        <include refid="Base_Column_List"/>
        from user_assets_tag where user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="selectUserAssetsTagByTableName" resultType="com.youpin.clear.infrastructure.dataobject.UserAssetsTag">
        select id, user_id, tag_code, tag_value_decimal, tag_value from user_assets_tag_${tableSuffix} where 1=1
        <if test="minId != null and minId > 0">
            and id > #{minId,jdbcType=BIGINT}
        </if>
        order by user_id asc limit #{pageIndex,jdbcType=BIGINT},#{pageSize,jdbcType=BIGINT}
    </select>


</mapper>