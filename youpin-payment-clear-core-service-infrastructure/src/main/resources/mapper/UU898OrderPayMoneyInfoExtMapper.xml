<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.ext.UU898OrderPayMoneyInfoExtMapper">

    <sql id="Base_Column_List">
        Id, UserId, OrderNo, PayOrderNo, OnlyTradeMoney, CanWithdrawMoney, AddTime, UpdateTime
    </sql>


    <select id="selectByOrderAndUserId"
            resultType="com.youpin.clear.infrastructure.dataobject.uu898.UU898OrderPayMoneyInfo">
        select
        <include refid="Base_Column_List"/>
        from OrderPayMoneyInfo where order_no = #{orderNo} and user_id = #{userId}
        limit 10
    </select>
</mapper>