<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.polar.UserAssetsRecordPolarExtMapper">


    <sql id="Base_Column_List_As_uar">
        uar.Id as Id, UserId, TypeId, TreadNo, AssetType, Money, ThisMoney, AfterMoney,
        ChargeMoney,
        BlockMoney, ThisBlockMoney, AfterBlockMoney, PurchaseMoney, ThisPurchaseMoney,
        AfterPurchaseMoney,
        SerialNo, OrderNo, PayOrderNo, Remark, AddTime, CompleteTime, Attr, `Status`, PayChannel,
        AccountName, PayWaitExpireTime, GenSource,ut.TypeName as TypeName
    </sql>

    <select id="selectPageByUserId" resultType="com.youpin.clear.infrastructure.dataobject.polar.UserAssetsRecordPolar">
        select
        <include refid="Base_Column_List_As_uar"/>
        from UserAssetsRecordPolar uar LEFT JOIN UserAssetsRecordTypePolar AS ut ON uar.TypeId = ut.Id
        where uar.UserId = #{userId}
        <if test="id != null">
            and uar.Id > #{id}
        </if>
        <if test="typeIdList != null and typeIdList.size() > 0">
            and uar.TypeId in
            <foreach collection="typeIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            <![CDATA[ and uar.AddTime >= #{startTime}]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and uar.AddTime <= #{endTime}]]>
        </if>
        order by uar.Id limit #{pageIndex}, #{pageSize}
    </select>


</mapper>