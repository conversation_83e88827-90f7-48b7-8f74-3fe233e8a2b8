<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountRecordOrderRelateExtMapper">

    <sql id="Base_Column_List">
        id, user_id, user_assets_record_id, type_id, order_no, pay_order_no, create_time,
        update_time
    </sql>

    <select id="getUserAccountRecordOrderRelateByOrderNo"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate">
        select
        <include refid="Base_Column_List"/>
        from user_account_record_order_relate
        where order_no = #{orderNo}
    </select>

    <select id="getUserAccountRecordOrderRelateByPayOrderNo"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate">
        select
        <include refid="Base_Column_List"/>

        from user_account_record_order_relate
        where pay_order_no = #{payOrderNo}
    </select>


    <select id="countByUserAssetsRecordId" resultType="java.lang.Integer">
        select count(1) from user_account_record_order_relate where user_assets_record_id = #{userAssetsRecordId}
    </select>


    <delete id="deleteByUserId">
        delete from user_account_record_order_relate where user_id = #{userId}
    </delete>

    <select id="getByUserAssetsRecordId"
            resultType="com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate">
        select
        <include refid="Base_Column_List"/>
        from user_account_record_order_relate where user_assets_record_id = #{userAssetsRecordId}
    </select>

</mapper>