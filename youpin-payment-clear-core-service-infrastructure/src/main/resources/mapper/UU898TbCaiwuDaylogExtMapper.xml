<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898TbCaiwuDaylogExtMapper">

    <sql id="Base_Column_List">
        id, ddate, ttype, typename, allmoney, alipay, wechat, money, jd, douyinpay, yibao,
        displaystatus, `source`
    </sql>

    <select id="selectByTtypeAndDdate"
            resultType="com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog">
        select
        <include refid="Base_Column_List"/>
        from tb_caiwu_daylog
        where ttype in
        <foreach collection="ttype" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ddate = #{ddate}
        and displaystatus = #{displaystatus}
        and `source` = #{source}
    </select>

    <update id="updateDisplayStatusById">
        update tb_caiwu_daylog set displaystatus = #{displaystatus} where id = #{id}
    </update>


</mapper>