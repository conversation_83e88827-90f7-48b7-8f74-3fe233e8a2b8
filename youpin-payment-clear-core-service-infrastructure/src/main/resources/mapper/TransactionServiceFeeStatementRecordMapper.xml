<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.TransactionServiceFeeStatementRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_assets_record_id" jdbcType="BIGINT" property="userAssetsRecordId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="tread_no" jdbcType="VARCHAR" property="treadNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="order_fee_type" jdbcType="INTEGER" property="orderFeeType" />
    <result column="fee_money" jdbcType="DECIMAL" property="feeMoney" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_assets_record_id, user_id, type_id, tread_no, order_no, serial_no, order_fee_type, 
    fee_money, `status`, complete_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from transaction_service_fee_statement_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from transaction_service_fee_statement_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord" useGeneratedKeys="true">
    insert into transaction_service_fee_statement_record (user_assets_record_id, user_id, type_id, 
      tread_no, order_no, serial_no, 
      order_fee_type, fee_money, `status`, 
      complete_time, create_time, update_time
      )
    values (#{userAssetsRecordId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{typeId,jdbcType=INTEGER}, 
      #{treadNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, 
      #{orderFeeType,jdbcType=INTEGER}, #{feeMoney,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, 
      #{completeTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord" useGeneratedKeys="true">
    insert into transaction_service_fee_statement_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userAssetsRecordId != null">
        user_assets_record_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="treadNo != null">
        tread_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="orderFeeType != null">
        order_fee_type,
      </if>
      <if test="feeMoney != null">
        fee_money,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userAssetsRecordId != null">
        #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="treadNo != null">
        #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderFeeType != null">
        #{orderFeeType,jdbcType=INTEGER},
      </if>
      <if test="feeMoney != null">
        #{feeMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord">
    update transaction_service_fee_statement_record
    <set>
      <if test="userAssetsRecordId != null">
        user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=INTEGER},
      </if>
      <if test="treadNo != null">
        tread_no = #{treadNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderFeeType != null">
        order_fee_type = #{orderFeeType,jdbcType=INTEGER},
      </if>
      <if test="feeMoney != null">
        fee_money = #{feeMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord">
    update transaction_service_fee_statement_record
    set user_assets_record_id = #{userAssetsRecordId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=INTEGER},
      tread_no = #{treadNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      order_fee_type = #{orderFeeType,jdbcType=INTEGER},
      fee_money = #{feeMoney,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>