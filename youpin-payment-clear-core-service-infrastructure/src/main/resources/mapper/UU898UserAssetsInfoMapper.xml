<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
    <id column="UserId" jdbcType="BIGINT" property="userId" />
    <result column="Money" jdbcType="DECIMAL" property="money" />
    <result column="PaymentMoney" jdbcType="DECIMAL" property="paymentMoney" />
    <result column="BlockMoney" jdbcType="DECIMAL" property="blockMoney" />
    <result column="PurchaseMoney" jdbcType="DECIMAL" property="purchaseMoney" />
    <result column="Integral" jdbcType="INTEGER" property="integral" />
    <result column="UpdateTime" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="LastUserRecordId" jdbcType="BIGINT" property="lastUserRecordId" />
    <result column="CashMoney" jdbcType="DECIMAL" property="cashMoney" />
    <result column="OnlyWithDrawMoney" jdbcType="DECIMAL" property="onlyWithDrawMoney" />
    <result column="PurchaseBlockMoney" jdbcType="DECIMAL" property="purchaseBlockMoney" />
    <result column="PurchaseMoneyFromMoney" jdbcType="DECIMAL" property="purchaseMoneyFromMoney" />
  </resultMap>
  <sql id="Base_Column_List">
    UserId, Money, PaymentMoney, BlockMoney, PurchaseMoney, Integral, UpdateTime, LastUserRecordId, 
    CashMoney, OnlyWithDrawMoney, PurchaseBlockMoney, PurchaseMoneyFromMoney
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UserAssetsInfo
    where UserId = #{userId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from UserAssetsInfo
    where UserId = #{userId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
    insert into UserAssetsInfo (UserId, Money, PaymentMoney, 
      BlockMoney, PurchaseMoney, Integral, 
      UpdateTime, LastUserRecordId, CashMoney, 
      OnlyWithDrawMoney, PurchaseBlockMoney, 
      PurchaseMoneyFromMoney)
    values (#{userId,jdbcType=BIGINT}, #{money,jdbcType=DECIMAL}, #{paymentMoney,jdbcType=DECIMAL}, 
      #{blockMoney,jdbcType=DECIMAL}, #{purchaseMoney,jdbcType=DECIMAL}, #{integral,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{lastUserRecordId,jdbcType=BIGINT}, #{cashMoney,jdbcType=DECIMAL}, 
      #{onlyWithDrawMoney,jdbcType=DECIMAL}, #{purchaseBlockMoney,jdbcType=DECIMAL}, 
      #{purchaseMoneyFromMoney,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
    insert into UserAssetsInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        UserId,
      </if>
      <if test="money != null">
        Money,
      </if>
      <if test="paymentMoney != null">
        PaymentMoney,
      </if>
      <if test="blockMoney != null">
        BlockMoney,
      </if>
      <if test="purchaseMoney != null">
        PurchaseMoney,
      </if>
      <if test="integral != null">
        Integral,
      </if>
      <if test="updateTime != null">
        UpdateTime,
      </if>
      <if test="lastUserRecordId != null">
        LastUserRecordId,
      </if>
      <if test="cashMoney != null">
        CashMoney,
      </if>
      <if test="onlyWithDrawMoney != null">
        OnlyWithDrawMoney,
      </if>
      <if test="purchaseBlockMoney != null">
        PurchaseBlockMoney,
      </if>
      <if test="purchaseMoneyFromMoney != null">
        PurchaseMoneyFromMoney,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="paymentMoney != null">
        #{paymentMoney,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoney != null">
        #{purchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="integral != null">
        #{integral,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUserRecordId != null">
        #{lastUserRecordId,jdbcType=BIGINT},
      </if>
      <if test="cashMoney != null">
        #{cashMoney,jdbcType=DECIMAL},
      </if>
      <if test="onlyWithDrawMoney != null">
        #{onlyWithDrawMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseBlockMoney != null">
        #{purchaseBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoneyFromMoney != null">
        #{purchaseMoneyFromMoney,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
    update UserAssetsInfo
    <set>
      <if test="money != null">
        Money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="paymentMoney != null">
        PaymentMoney = #{paymentMoney,jdbcType=DECIMAL},
      </if>
      <if test="blockMoney != null">
        BlockMoney = #{blockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoney != null">
        PurchaseMoney = #{purchaseMoney,jdbcType=DECIMAL},
      </if>
      <if test="integral != null">
        Integral = #{integral,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UpdateTime = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUserRecordId != null">
        LastUserRecordId = #{lastUserRecordId,jdbcType=BIGINT},
      </if>
      <if test="cashMoney != null">
        CashMoney = #{cashMoney,jdbcType=DECIMAL},
      </if>
      <if test="onlyWithDrawMoney != null">
        OnlyWithDrawMoney = #{onlyWithDrawMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseBlockMoney != null">
        PurchaseBlockMoney = #{purchaseBlockMoney,jdbcType=DECIMAL},
      </if>
      <if test="purchaseMoneyFromMoney != null">
        PurchaseMoneyFromMoney = #{purchaseMoneyFromMoney,jdbcType=DECIMAL},
      </if>
    </set>
    where UserId = #{userId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo">
    update UserAssetsInfo
    set Money = #{money,jdbcType=DECIMAL},
      PaymentMoney = #{paymentMoney,jdbcType=DECIMAL},
      BlockMoney = #{blockMoney,jdbcType=DECIMAL},
      PurchaseMoney = #{purchaseMoney,jdbcType=DECIMAL},
      Integral = #{integral,jdbcType=INTEGER},
      UpdateTime = #{updateTime,jdbcType=TIMESTAMP},
      LastUserRecordId = #{lastUserRecordId,jdbcType=BIGINT},
      CashMoney = #{cashMoney,jdbcType=DECIMAL},
      OnlyWithDrawMoney = #{onlyWithDrawMoney,jdbcType=DECIMAL},
      PurchaseBlockMoney = #{purchaseBlockMoney,jdbcType=DECIMAL},
      PurchaseMoneyFromMoney = #{purchaseMoneyFromMoney,jdbcType=DECIMAL}
    where UserId = #{userId,jdbcType=BIGINT}
  </update>
</mapper>