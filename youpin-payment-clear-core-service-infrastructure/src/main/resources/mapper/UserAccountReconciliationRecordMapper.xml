<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.UserAccountReconciliationRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_account_no" jdbcType="VARCHAR" property="userAccountNo" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="last_account_record_id" jdbcType="BIGINT" property="lastAccountRecordId" />
    <result column="check_flag" jdbcType="INTEGER" property="checkFlag" />
    <result column="user_weight" jdbcType="BIGINT" property="userWeight" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, user_account_no, account_type, last_account_record_id, check_flag, user_weight, 
    ext, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_account_reconciliation_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_account_reconciliation_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord" useGeneratedKeys="true">
    insert into user_account_reconciliation_record (user_id, user_account_no, account_type, 
      last_account_record_id, check_flag, user_weight, 
      ext, create_time, update_time
      )
    values (#{userId,jdbcType=BIGINT}, #{userAccountNo,jdbcType=VARCHAR}, #{accountType,jdbcType=INTEGER}, 
      #{lastAccountRecordId,jdbcType=BIGINT}, #{checkFlag,jdbcType=INTEGER}, #{userWeight,jdbcType=BIGINT}, 
      #{ext,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord" useGeneratedKeys="true">
    insert into user_account_reconciliation_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="userAccountNo != null">
        user_account_no,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="lastAccountRecordId != null">
        last_account_record_id,
      </if>
      <if test="checkFlag != null">
        check_flag,
      </if>
      <if test="userWeight != null">
        user_weight,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userAccountNo != null">
        #{userAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="lastAccountRecordId != null">
        #{lastAccountRecordId,jdbcType=BIGINT},
      </if>
      <if test="checkFlag != null">
        #{checkFlag,jdbcType=INTEGER},
      </if>
      <if test="userWeight != null">
        #{userWeight,jdbcType=BIGINT},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord">
    update user_account_reconciliation_record
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userAccountNo != null">
        user_account_no = #{userAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="lastAccountRecordId != null">
        last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
      </if>
      <if test="checkFlag != null">
        check_flag = #{checkFlag,jdbcType=INTEGER},
      </if>
      <if test="userWeight != null">
        user_weight = #{userWeight,jdbcType=BIGINT},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord">
    update user_account_reconciliation_record
    set user_id = #{userId,jdbcType=BIGINT},
      user_account_no = #{userAccountNo,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=INTEGER},
      last_account_record_id = #{lastAccountRecordId,jdbcType=BIGINT},
      check_flag = #{checkFlag,jdbcType=INTEGER},
      user_weight = #{userWeight,jdbcType=BIGINT},
      ext = #{ext,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>