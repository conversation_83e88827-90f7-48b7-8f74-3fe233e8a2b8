<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.TransactionServiceFeeOperateRecordMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operation_date" jdbcType="INTEGER" property="operationDate" />
    <result column="platform_user_id" jdbcType="BIGINT" property="platformUserId" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="order_fee_type" jdbcType="INTEGER" property="orderFeeType" />
    <result column="fee_money" jdbcType="DECIMAL" property="feeMoney" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, operation_date, platform_user_id, type_id, serial_no, order_fee_type, fee_money, 
    `status`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from transaction_service_fee_operate_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from transaction_service_fee_operate_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord" useGeneratedKeys="true">
    insert into transaction_service_fee_operate_record (operation_date, platform_user_id, type_id, 
      serial_no, order_fee_type, fee_money, 
      `status`, create_time, update_time
      )
    values (#{operationDate,jdbcType=INTEGER}, #{platformUserId,jdbcType=BIGINT}, #{typeId,jdbcType=INTEGER}, 
      #{serialNo,jdbcType=VARCHAR}, #{orderFeeType,jdbcType=INTEGER}, #{feeMoney,jdbcType=DECIMAL}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord" useGeneratedKeys="true">
    insert into transaction_service_fee_operate_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operationDate != null">
        operation_date,
      </if>
      <if test="platformUserId != null">
        platform_user_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="orderFeeType != null">
        order_fee_type,
      </if>
      <if test="feeMoney != null">
        fee_money,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operationDate != null">
        #{operationDate,jdbcType=INTEGER},
      </if>
      <if test="platformUserId != null">
        #{platformUserId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderFeeType != null">
        #{orderFeeType,jdbcType=INTEGER},
      </if>
      <if test="feeMoney != null">
        #{feeMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord">
    update transaction_service_fee_operate_record
    <set>
      <if test="operationDate != null">
        operation_date = #{operationDate,jdbcType=INTEGER},
      </if>
      <if test="platformUserId != null">
        platform_user_id = #{platformUserId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=INTEGER},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderFeeType != null">
        order_fee_type = #{orderFeeType,jdbcType=INTEGER},
      </if>
      <if test="feeMoney != null">
        fee_money = #{feeMoney,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord">
    update transaction_service_fee_operate_record
    set operation_date = #{operationDate,jdbcType=INTEGER},
      platform_user_id = #{platformUserId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=INTEGER},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      order_fee_type = #{orderFeeType,jdbcType=INTEGER},
      fee_money = #{feeMoney,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>