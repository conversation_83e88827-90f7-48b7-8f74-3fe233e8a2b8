<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.polar.UserAssetsRecordPolarMapper">
    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.polar.UserAssetsRecordPolar">
        <id column="Id" jdbcType="BIGINT" property="id"/>
        <result column="UserId" jdbcType="BIGINT" property="userId"/>
        <result column="TypeId" jdbcType="INTEGER" property="typeId"/>
        <result column="TreadNo" jdbcType="VARCHAR" property="treadNo"/>
        <result column="AssetType" jdbcType="INTEGER" property="assetType"/>
        <result column="Money" jdbcType="DECIMAL" property="money"/>
        <result column="ThisMoney" jdbcType="DECIMAL" property="thisMoney"/>
        <result column="AfterMoney" jdbcType="DECIMAL" property="afterMoney"/>
        <result column="ChargeMoney" jdbcType="DECIMAL" property="chargeMoney"/>
        <result column="BlockMoney" jdbcType="DECIMAL" property="blockMoney"/>
        <result column="ThisBlockMoney" jdbcType="DECIMAL" property="thisBlockMoney"/>
        <result column="AfterBlockMoney" jdbcType="DECIMAL" property="afterBlockMoney"/>
        <result column="PurchaseMoney" jdbcType="DECIMAL" property="purchaseMoney"/>
        <result column="ThisPurchaseMoney" jdbcType="DECIMAL" property="thisPurchaseMoney"/>
        <result column="AfterPurchaseMoney" jdbcType="DECIMAL" property="afterPurchaseMoney"/>
        <result column="SerialNo" jdbcType="VARCHAR" property="serialNo"/>
        <result column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="PayOrderNo" jdbcType="VARCHAR" property="payOrderNo"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="AddTime" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CompleteTime" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="Attr" jdbcType="INTEGER" property="attr"/>
        <result column="Status" jdbcType="INTEGER" property="status"/>
        <result column="PayChannel" jdbcType="INTEGER" property="payChannel"/>
        <result column="AccountName" jdbcType="VARCHAR" property="accountName"/>
        <result column="PayWaitExpireTime" jdbcType="TIMESTAMP" property="payWaitExpireTime"/>
        <result column="GenSource" jdbcType="VARCHAR" property="genSource"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id, UserId, TypeId, TreadNo, AssetType, Money, ThisMoney, AfterMoney, ChargeMoney,
        BlockMoney, ThisBlockMoney, AfterBlockMoney, PurchaseMoney, ThisPurchaseMoney, AfterPurchaseMoney,
        SerialNo, OrderNo, PayOrderNo, Remark, AddTime, CompleteTime, Attr, `Status`, PayChannel,
        AccountName, PayWaitExpireTime, GenSource
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UserAssetsRecordPolar <!-- UserAssetsRecord 别名查询 UserAssetsRecordPolar -->
        where Id = #{id,jdbcType=BIGINT}
    </select>
</mapper>