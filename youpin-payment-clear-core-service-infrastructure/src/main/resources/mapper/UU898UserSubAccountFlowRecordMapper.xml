<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountFlowRecordMapper">

    <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountNo" column="account_no" jdbcType="VARCHAR"/>
        <result property="accountType" column="account_type" jdbcType="INTEGER"/>
        <result property="payOrderNo" column="pay_order_no" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="journalType" column="journal_type" jdbcType="INTEGER"/>
        <result property="balanceBefore" column="balance_before" jdbcType="DECIMAL"/>
        <result property="balanceChange" column="balance_change" jdbcType="DECIMAL"/>
        <result property="balanceAfter" column="balance_after" jdbcType="DECIMAL"/>
        <result property="extInfo" column="ext_info" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_no,account_type,
        pay_order_no,order_no,serial_no,
        user_id,journal_type,balance_before,
        balance_change,balance_after,ext_info,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uu898_user_sub_account_flow_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from uu898_user_sub_account_flow_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord"
            useGeneratedKeys="true">
        insert into uu898_user_sub_account_flow_record
        ( id, account_no, account_type
        , pay_order_no, order_no, serial_no
        , user_id, journal_type, balance_before
        , balance_change, balance_after, ext_info
        , create_time, update_time)
        values ( #{id,jdbcType=BIGINT}, #{accountNo,jdbcType=VARCHAR}, #{accountType,jdbcType=INTEGER}
               , #{payOrderNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}
               , #{userId,jdbcType=BIGINT}, #{journalType,jdbcType=INTEGER}, #{balanceBefore,jdbcType=DECIMAL}
               , #{balanceChange,jdbcType=DECIMAL}, #{balanceAfter,jdbcType=DECIMAL}, #{extInfo,jdbcType=VARCHAR}
               , #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord" useGeneratedKeys="true">
        insert into uu898_user_sub_account_flow_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="accountNo != null">account_no,</if>
            <if test="accountType != null">account_type,</if>
            <if test="payOrderNo != null">pay_order_no,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="serialNo != null">serial_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="journalType != null">journal_type,</if>
            <if test="balanceBefore != null">balance_before,</if>
            <if test="balanceChange != null">balance_change,</if>
            <if test="balanceAfter != null">balance_after,</if>
            <if test="extInfo != null">ext_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="accountNo != null">#{accountNo,jdbcType=VARCHAR},</if>
            <if test="accountType != null">#{accountType,jdbcType=INTEGER},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="journalType != null">#{journalType,jdbcType=INTEGER},</if>
            <if test="balanceBefore != null">#{balanceBefore,jdbcType=DECIMAL},</if>
            <if test="balanceChange != null">#{balanceChange,jdbcType=DECIMAL},</if>
            <if test="balanceAfter != null">#{balanceAfter,jdbcType=DECIMAL},</if>
            <if test="extInfo != null">#{extInfo,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord">
        update uu898_user_sub_account_flow_record
        <set>
            <if test="accountNo != null">
                account_no = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=INTEGER},
            </if>
            <if test="payOrderNo != null">
                pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="serialNo != null">
                serial_no = #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="journalType != null">
                journal_type = #{journalType,jdbcType=INTEGER},
            </if>
            <if test="balanceBefore != null">
                balance_before = #{balanceBefore,jdbcType=DECIMAL},
            </if>
            <if test="balanceChange != null">
                balance_change = #{balanceChange,jdbcType=DECIMAL},
            </if>
            <if test="balanceAfter != null">
                balance_after = #{balanceAfter,jdbcType=DECIMAL},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord">
        update uu898_user_sub_account_flow_record
        set account_no     = #{accountNo,jdbcType=VARCHAR},
            account_type   = #{accountType,jdbcType=INTEGER},
            pay_order_no   = #{payOrderNo,jdbcType=VARCHAR},
            order_no       = #{orderNo,jdbcType=VARCHAR},
            serial_no      = #{serialNo,jdbcType=VARCHAR},
            user_id        = #{userId,jdbcType=BIGINT},
            journal_type   = #{journalType,jdbcType=INTEGER},
            balance_before = #{balanceBefore,jdbcType=DECIMAL},
            balance_change = #{balanceChange,jdbcType=DECIMAL},
            balance_after  = #{balanceAfter,jdbcType=DECIMAL},
            ext_info       = #{extInfo,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_time    = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
