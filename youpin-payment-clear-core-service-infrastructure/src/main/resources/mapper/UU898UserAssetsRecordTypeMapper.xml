<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordTypeMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType">
    <id column="Id" jdbcType="BIGINT" property="id" />
    <result column="TypeName" jdbcType="VARCHAR" property="typeName" />
    <result column="OperateType" jdbcType="INTEGER" property="operateType" />
    <result column="TypeCode" jdbcType="INTEGER" property="typeCode" />
    <result column="OperateTypeUser" jdbcType="INTEGER" property="operateTypeUser" />
    <result column="CheckType" jdbcType="INTEGER" property="checkType" />
  </resultMap>
  <sql id="Base_Column_List">
    Id, TypeName, OperateType, TypeCode, OperateTypeUser, CheckType
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UserAssetsRecordType
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from UserAssetsRecordType
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType">
    insert into UserAssetsRecordType (Id, TypeName, OperateType, 
      TypeCode, OperateTypeUser, CheckType
      )
    values (#{id,jdbcType=BIGINT}, #{typeName,jdbcType=VARCHAR}, #{operateType,jdbcType=INTEGER}, 
      #{typeCode,jdbcType=INTEGER}, #{operateTypeUser,jdbcType=INTEGER}, #{checkType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType">
    insert into UserAssetsRecordType
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="typeName != null">
        TypeName,
      </if>
      <if test="operateType != null">
        OperateType,
      </if>
      <if test="typeCode != null">
        TypeCode,
      </if>
      <if test="operateTypeUser != null">
        OperateTypeUser,
      </if>
      <if test="checkType != null">
        CheckType,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="typeCode != null">
        #{typeCode,jdbcType=INTEGER},
      </if>
      <if test="operateTypeUser != null">
        #{operateTypeUser,jdbcType=INTEGER},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType">
    update UserAssetsRecordType
    <set>
      <if test="typeName != null">
        TypeName = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        OperateType = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="typeCode != null">
        TypeCode = #{typeCode,jdbcType=INTEGER},
      </if>
      <if test="operateTypeUser != null">
        OperateTypeUser = #{operateTypeUser,jdbcType=INTEGER},
      </if>
      <if test="checkType != null">
        CheckType = #{checkType,jdbcType=INTEGER},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType">
    update UserAssetsRecordType
    set TypeName = #{typeName,jdbcType=VARCHAR},
      OperateType = #{operateType,jdbcType=INTEGER},
      TypeCode = #{typeCode,jdbcType=INTEGER},
      OperateTypeUser = #{operateTypeUser,jdbcType=INTEGER},
      CheckType = #{checkType,jdbcType=INTEGER}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>