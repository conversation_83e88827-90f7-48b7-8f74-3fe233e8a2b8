<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.PlatformAccountRecordExtMapper">

    <sql id="Base_Column_List">
        id, user_id, shard, user_assets_record_id, tread_no, money, `status`, create_time, update_time
    </sql>


    <update id="updateStatusById">
        update platform_account_record
        set status = #{status}
        where id = #{id}
        and status = 0
    </update>

    <select id="selectPageSizeByStatusAndCreateTime" resultType="long">
        select id from platform_account_record where status = #{status}
        <if test="shardIndex != null">
            and shard = #{shardIndex}
        </if>
        and <![CDATA[create_time <= #{createTime} ]]>
        order by id limit #{pageSize}
    </select>


</mapper>