<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AccountAssetsTypeExtMapper">


    <sql id="account_assets_type_base_Column_List">
        id, assets_code, assets_name, direction, assets_type, delete_flag, create_time, update_time
    </sql>

    <sql id="account_assets_type_relate_base_Column_List">
        id, assets_code, relate_type, relate_code, create_time, update_time
    </sql>

    <select id="gatAllAccountAssetsType" resultType="com.youpin.clear.infrastructure.dataobject.AccountAssetsType">
        select
        <include refid="account_assets_type_base_Column_List"/>
        from account_assets_type where delete_flag = 0
    </select>

    <select id="gatAllAccountAssetsTypeRelate"
            resultType="com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate">
        select
        <include refid="account_assets_type_relate_base_Column_List"/>
        from account_assets_type_relate
    </select>

    <select id="gatAccountAssetsTypeByCode"
            resultType="com.youpin.clear.infrastructure.dataobject.AccountAssetsType">
        select
        <include refid="account_assets_type_base_Column_List"/>
        from account_assets_type where delete_flag = 0 and assets_code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>