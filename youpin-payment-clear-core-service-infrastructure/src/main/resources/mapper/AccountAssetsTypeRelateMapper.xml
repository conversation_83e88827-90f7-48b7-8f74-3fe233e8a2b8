<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youpin.clear.infrastructure.mapper.AccountAssetsTypeRelateMapper">
  <resultMap id="BaseResultMap" type="com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="assets_code" jdbcType="INTEGER" property="assetsCode" />
    <result column="relate_type" jdbcType="VARCHAR" property="relateType" />
    <result column="relate_code" jdbcType="VARCHAR" property="relateCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, assets_code, relate_type, relate_code, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from account_assets_type_relate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from account_assets_type_relate
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate" useGeneratedKeys="true">
    insert into account_assets_type_relate (assets_code, relate_type, relate_code, 
      create_time, update_time)
    values (#{assetsCode,jdbcType=INTEGER}, #{relateType,jdbcType=VARCHAR}, #{relateCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate" useGeneratedKeys="true">
    insert into account_assets_type_relate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="relateType != null">
        relate_type,
      </if>
      <if test="relateCode != null">
        relate_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=INTEGER},
      </if>
      <if test="relateType != null">
        #{relateType,jdbcType=VARCHAR},
      </if>
      <if test="relateCode != null">
        #{relateCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate">
    update account_assets_type_relate
    <set>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=INTEGER},
      </if>
      <if test="relateType != null">
        relate_type = #{relateType,jdbcType=VARCHAR},
      </if>
      <if test="relateCode != null">
        relate_code = #{relateCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate">
    update account_assets_type_relate
    set assets_code = #{assetsCode,jdbcType=INTEGER},
      relate_type = #{relateType,jdbcType=VARCHAR},
      relate_code = #{relateCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>