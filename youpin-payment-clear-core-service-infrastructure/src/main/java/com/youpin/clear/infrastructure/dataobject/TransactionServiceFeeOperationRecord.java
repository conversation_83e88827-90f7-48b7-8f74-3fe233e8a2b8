package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: transaction_service_fee_operation_record
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionServiceFeeOperationRecord implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 操作日期 20250416
     */
    private Integer operationDate;

    /**
     * 服务费
     */
    private BigDecimal feeMoney;

    /**
     * 状态，0.进行中，1.转移成功，2.转移失败,3,播报成功,4 播报失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}