package com.youpin.clear.infrastructure.gateway.impl;

import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BaseException;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.gateway.UserSubAccountRecordGateway;
import com.youpin.clear.infrastructure.converter.UserSubAccountRecordConvert;
import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord;
import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecordOrderRelate;
import com.youpin.clear.infrastructure.mapper.UserSubAccountRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.UserSubAccountRecordMapper;
import com.youpin.clear.infrastructure.mapper.UserSubAccountRecordOrderRelateMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserSubAccountRecordGatewayImpl implements UserSubAccountRecordGateway {

    @Autowired
    private UserSubAccountRecordExtMapper userSubAccountRecordExtMapper;

    @Autowired
    private UserSubAccountRecordOrderRelateMapper userSubAccountRecordOrderRelateMapper;

    @Autowired
    private UserSubAccountRecordMapper userSubAccountRecordMapper;

    /**
     * 保存二级账户聚合根
     *
     * @param userSubAccountRecordMembers records
     */
    @Override
    public void save(List<UserSubAccountRecordMember> userSubAccountRecordMembers) {
        if (CollectionUtils.isEmpty(userSubAccountRecordMembers)) {
            return;
        }
        LocalDateTime nowTime = LocalDateTime.now();
        // 转换
        List<UserSubAccountRecord> userSubAccountRecordList = UserSubAccountRecordConvert.MAPPER.toUserSubAccountRecordList(userSubAccountRecordMembers);
        // 获取第1条数据
        UserSubAccountRecord firstUserSubAccountRecord = userSubAccountRecordList.get(0);
        //
        List<Long> orderRelateUserIds = userSubAccountRecordExtMapper.getOrderRelateUserIdByOrderNo(firstUserSubAccountRecord.getOrderNo());
        //
        List<UserSubAccountRecordOrderRelate> recordOrderRelateList = new ArrayList<>();
        //
        userSubAccountRecordList.stream().map(UserSubAccountRecord::getUserId).distinct().forEach(userId -> {
            // 判断当前userId是否已经存在
            if (null == orderRelateUserIds || orderRelateUserIds.isEmpty() || !orderRelateUserIds.contains(userId)) {
                // 创建关系
                UserSubAccountRecordOrderRelate userSubAccountRecordOrderRelate = new UserSubAccountRecordOrderRelate();
                userSubAccountRecordOrderRelate.setUserId(userId);
                userSubAccountRecordOrderRelate.setOrderNo(firstUserSubAccountRecord.getOrderNo());
                userSubAccountRecordOrderRelate.setCreateTime(nowTime);
                recordOrderRelateList.add(userSubAccountRecordOrderRelate);
            }
        });
        // 判断不为空
        if (CollectionUtils.isNotEmpty(recordOrderRelateList)) {
            // 批量插入关系
            int insertOrderRelate = userSubAccountRecordExtMapper.batchInsertRecordOrderRelate(recordOrderRelateList);
            // log
            log.info("[UserSubAccountRecordGatewayImpl]:保存关系结果,recordOrderRelateListSize:{},insertOrderRelate:{}", recordOrderRelateList.size(), insertOrderRelate);
        }
        //
        userSubAccountRecordList.forEach(userSubAccountRecord -> {
            // insert
            int count = userSubAccountRecordMapper.insertSelective(userSubAccountRecord);
            //
            if (count != 1) {
                log.error("[UserSubAccountRecordGatewayImpl]:保存记录失败,userSubAccountRecord:{}", userSubAccountRecord);
                throw BaseException.of(Status.SYSTEM);
            } else {
                log.info("[UserSubAccountRecordGatewayImpl]:保存记录成功,userSubAccountRecord:{}", userSubAccountRecord);
            }
        });
    }

    /**
     * 更新二级账户资金明细
     *
     * @param recordMember 二级账户资金明细
     */
    @Override
    public boolean updateRecord(UserSubAccountRecordMember recordMember) {
        // convert
        UserSubAccountRecord userSubAccountRecord = UserSubAccountRecordConvert.MAPPER.toUserSubAccountRecord(recordMember);
        // 更新二级账户资金明细
        int updated = userSubAccountRecordExtMapper.updateUserSubAccountRecord(userSubAccountRecord, NetStatusEnum.PROCESSING.getCode());
        // check
        if (updated == 1) {
            log.info("[UserSubAccountRecordGatewayImpl]:updateRecord成功,recordMember:{}", recordMember);
            return true;
        }
        log.warn("[UserSubAccountRecordGatewayImpl]:updateRecord失败,recordMember:{}", recordMember);
        return false;
    }

    /**
     * 根据订单号查询二级账户资金明细
     *
     * @param orderNo 订单号
     * @return 二级账户资金明细List
     */
    @Override
    public List<UserSubAccountRecordMember> loadSubAccountRecordMember(String orderNo) {
        //
        List<Long> userIds = userSubAccountRecordExtMapper.getOrderRelateUserIdByOrderNo(orderNo);
        //
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>(0);
        }
        List<UserSubAccountRecord> subAccountRecords = new ArrayList<>();
        // 遍历查询 - 开启多线程查询
        userIds.forEach(userId -> subAccountRecords.addAll(userSubAccountRecordExtMapper.getUserSubAccountRecordByOrderNoAndUserId(userId, orderNo)));
        //
        if (subAccountRecords.isEmpty()) {
            return new ArrayList<>(0);
        }
        //
        return UserSubAccountRecordConvert.MAPPER.toUserSubAccountRecordMemberList(subAccountRecords);
    }

    @Override
    public Long getMinAccountRecordId(Long userId) {
        return userSubAccountRecordExtMapper.getMinAccountRecordId(userId);
    }

}
