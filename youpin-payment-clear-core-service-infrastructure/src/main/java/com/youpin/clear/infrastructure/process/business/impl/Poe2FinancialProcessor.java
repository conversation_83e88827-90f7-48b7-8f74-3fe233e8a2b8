package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 流放之路2
 */
@Slf4j
@Service
public class Poe2FinancialProcessor extends DefaultSubBusFinancialProcessor {
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.POE2);
    }

    @Override
    public void paySuccess(FinancialProcessorDTO financialProcessorDTO) {
        super.paySuccess(financialProcessorDTO);
        //映射平台账户
        platformUserIdConversion(financialProcessorDTO);
    }

    @Override
    public void refundSuccess(FinancialProcessorDTO financialProcessorDTO) {
        super.refundSuccess(financialProcessorDTO);
        //映射平台账户
        platformUserIdConversion(financialProcessorDTO);
    }

    @Override
    public void settlement(FinancialProcessorDTO financialProcessorDTO) {
        super.settlement(financialProcessorDTO);
        //映射平台账户
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            Long convertedToCent = AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney());
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_231.getTypeId())) {
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_230, financialProcessorDTO, getPlatformUserId(UserAssetsTypeEnum.TYPE_230), DoNetPayChannelEnum.Balance.getCode(), financialProcessorDTO.getNetStatusEnum(), convertedToCent));
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_234.getTypeId())) {
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_235, financialProcessorDTO, getPlatformUserId(UserAssetsTypeEnum.TYPE_235), DoNetPayChannelEnum.Balance.getCode(), financialProcessorDTO.getNetStatusEnum(), convertedToCent));
            }
        }
        if (!tempList.isEmpty()) {
            financialProcessorDTO.addAbstractUserAssetsRecordDTO(tempList);
        }
    }

    public void platformUserIdConversion(FinancialProcessorDTO dto) {

        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();

        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            Long convertedToCent = AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney());
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_228.getTypeId())) {
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_229, dto, getPlatformUserId(UserAssetsTypeEnum.TYPE_229), DoNetPayChannelEnum.Balance.getCode(), dto.getNetStatusEnum(), convertedToCent));
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_233.getTypeId())) {
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_232, dto, getPlatformUserId(UserAssetsTypeEnum.TYPE_232), DoNetPayChannelEnum.Balance.getCode(), dto.getNetStatusEnum(), convertedToCent));
            }
        }
        if (!tempList.isEmpty()) {
            dto.addAbstractUserAssetsRecordDTO(tempList);
        }
    }

}
