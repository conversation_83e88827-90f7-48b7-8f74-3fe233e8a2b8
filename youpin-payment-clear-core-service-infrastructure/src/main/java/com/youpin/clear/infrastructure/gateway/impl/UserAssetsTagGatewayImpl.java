package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.common.constant.RedisKey;
import com.youpin.clear.domain.dto.UserAssetsTagDTO;
import com.youpin.clear.domain.gateway.UserAssetsTagGateway;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.dataobject.UserAssetsTag;
import com.youpin.clear.infrastructure.mapper.UserAssetsTagExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAssetsTagMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserAssetsTagGatewayImpl implements UserAssetsTagGateway {

    @Autowired
    UserAssetsTagExtMapper userAssetsTagExtMapper;

    @Autowired
    UserAssetsTagMapper userAssetsTagMapper;

    @Autowired
    RedissonClient redissonClient;

    private static final int USER_MOD = 10000;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Override
    public List<UserAssetsTagDTO> getUserAssetsTagByUserId(Long userId) {
        return BeanUtilsWrapper.convertList(userAssetsTagExtMapper.getUserAssetsTagByUserId(userId), UserAssetsTagDTO::new);
    }

    @Override
    public List<UserAssetsTagDTO> selectUserAssetsTagByTableName(String tableSuffix, Long pageIndex, Integer pageSize, Long minId) {
        return BeanUtilsWrapper.convertList(userAssetsTagExtMapper.selectUserAssetsTagByTableName(tableSuffix, pageIndex, pageSize, minId), UserAssetsTagDTO::new);
    }

    @Override
    public void syncDateCache(List<UserAssetsTagDTO> dtoList) {
        //分组
        Map<Long, List<UserAssetsTagDTO>> collect = dtoList.stream().collect(Collectors.groupingBy(UserAssetsTagDTO::getUserId));
        collect.forEach((userId, list) -> {
            String redisKey = RedisKey.REDIS_KEY_USER_TAG_MAP_CACHE + gerUserModId(userId);
            RMap<Object, Object> rMap = redissonClient.getMap(redisKey);
            list.forEach(dto -> {
                String redisMapKey = dto.getTagCode() + "_" + userId;
                if (StringUtils.isNotBlank(dto.getTagValue())) {
                    rMap.put(redisMapKey, dto.getTagValue());
                }
                if (null != dto.getTagValueDecimal()) {
                    rMap.remove(redisMapKey);
                    rMap.addAndGet(redisMapKey, dto.getTagValueDecimal());
                }
            });
        });
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUserAssetsTag(UserAssetsTagDTO dto) {
        if (null == dto || null == dto.getUserId() || StringUtils.isBlank(dto.getTagCode())) {
            log.error("保存 参数不能为null userAssetsTag:{}", dto);
            return;
        }
        userAssetsTagMapper.insertSelective(BeanUtilsWrapper.convert(dto, UserAssetsTag::new));
        if (Boolean.FALSE.equals(paymentClearParamsConfig.getUserAssetsTagSyncCacheFlag())) {
            return;
        }
        if (null != dto.getTagValueDecimal()) {
            //更新缓存
            addAndGetRedisUserId(dto.getUserId(), dto.getTagCode(), dto.getTagValueDecimal());
        }
        if (StringUtils.isNotBlank(dto.getTagValue())) {
            setRedisUserId(dto.getUserId(), dto.getTagCode(), dto.getTagValue());
        }
    }

    @Override
    public UserAssetsTagDTO getUserAssetsTagByTag(Long userId, String tagCode) {
        if (null == userId || StringUtils.isBlank(tagCode)) {
            log.error("查询 参数不能为null userId:{}  tagCode:{}", userId, tagCode);
            return null;
        }
        return BeanUtilsWrapper.convert(userAssetsTagExtMapper.getUserAssetsTagByTag(userId, tagCode), UserAssetsTagDTO::new);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByUserId(UserAssetsTagDTO dto) {
        if (null == dto || null == dto.getId() || null == dto.getUserId() || StringUtils.isBlank(dto.getTagCode())) {
            log.error("修改 参数不能为null userAssetsTag:{}", dto);
            return;
        }
        userAssetsTagExtMapper.updateByUserId(BeanUtilsWrapper.convert(dto, UserAssetsTag::new));
        if (Boolean.FALSE.equals(paymentClearParamsConfig.getUserAssetsTagSyncCacheFlag())) {
            return;
        }
        if (null != dto.getTagValueDecimal()) {
            //更新缓存
            addAndGetRedisUserId(dto.getUserId(), dto.getTagCode(), dto.getTagValueDecimal());
        }
        if (StringUtils.isNotBlank(dto.getTagValue())) {
            setRedisUserId(dto.getUserId(), dto.getTagCode(), dto.getTagValue());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBigDecimalByUserId(Long id, Long userId, String tagCode, BigDecimal changeTagValueDecimal) {
        if (null == id || null == userId || null == changeTagValueDecimal || changeTagValueDecimal.compareTo(BigDecimal.ZERO) == 0 || StringUtils.isBlank(tagCode)) {
            log.error("修改 参数不能为null或0");
            return;
        }
        userAssetsTagExtMapper.updateBigDecimalByUserId(id, userId, tagCode, changeTagValueDecimal);
        if(Boolean.FALSE.equals(paymentClearParamsConfig.getUserAssetsTagSyncCacheFlag())){
            return;
        }
        //更新缓存
        addAndGetRedisUserId(userId, tagCode, changeTagValueDecimal);
    }

    @Override
    public void assetsTagDelete(Long redisKeySuffix, Long userId, String tagCode) {
        if (null != userId && StringUtils.isNotBlank(tagCode)) {
            removeRedisUserId(userId, tagCode);
        }
        if (null != redisKeySuffix) {
            removeRedisKey(redisKeySuffix);
        }
    }

    @Override
    public List<UserAssetsTagDTO> assetsTagCacheGetUserId(Long userId, String tagCode) {
        if (null == userId) {
            return List.of();
        }
        String redisKey = RedisKey.REDIS_KEY_USER_TAG_MAP_CACHE + gerUserModId(userId);
        RMap<Object, Object> rMap = redissonClient.getMap(redisKey);
        if (StringUtils.isNotBlank(tagCode)) {
            String redisMapKey = tagCode + "_" + userId;
            Object value = rMap.get(redisMapKey);
            if (null == value) {
                return List.of();
            }
            return List.of(UserAssetsTagDTO.builder().tagValue(String.valueOf(value)).tagCode(tagCode).build());
        }
        List<UserAssetsTagDTO> list = new ArrayList<>();
        rMap.forEach((key, value) -> {
            if (StringUtils.isNotBlank(value.toString())) {
                String[] split = key.toString().split("_");
                if (split.length == 2) {
                    Long userId1 = Long.valueOf(split[1]);
                    if (userId1.compareTo(userId) == 0) {
                        list.add(UserAssetsTagDTO.builder().tagValue(String.valueOf(value)).tagCode(String.valueOf(split[0])).build());
                    }
                }
            }
        });
        return list;
    }


    private static long gerUserModId(Long userId) {
        return userId % USER_MOD;
    }

    private void addAndGetRedisUserId(Long userId, String tagCode, BigDecimal tagValueDecimal) {
        String redisKey = RedisKey.REDIS_KEY_USER_TAG_MAP_CACHE + gerUserModId(userId);
        String redisMapKey = tagCode + "_" + userId;
        //保留两位小数
        redissonClient.getMap(redisKey).addAndGet(redisMapKey, tagValueDecimal);
    }

    private void setRedisUserId(Long userId, String tagCode, String tagValue) {
        String redisKey = RedisKey.REDIS_KEY_USER_TAG_MAP_CACHE + gerUserModId(userId);
        String redisMapKey = tagCode + "_" + userId;
        redissonClient.getMap(redisKey).put(redisMapKey, tagValue);
    }

    private void removeRedisUserId(Long userId, String tagCode) {
        String redisKey = RedisKey.REDIS_KEY_USER_TAG_MAP_CACHE + gerUserModId(userId);
        String redisMapKey = tagCode + "_" + userId;
        redissonClient.getMap(redisKey).remove(redisMapKey);
    }

    private void removeRedisKey(Long redisKeySuffix) {
        String redisKey = RedisKey.REDIS_KEY_USER_TAG_MAP_CACHE + redisKeySuffix;
        redissonClient.getMap(redisKey).clear();
    }


}
