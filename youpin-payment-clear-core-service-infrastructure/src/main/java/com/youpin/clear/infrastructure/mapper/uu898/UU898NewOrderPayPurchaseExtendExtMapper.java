package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface UU898NewOrderPayPurchaseExtendExtMapper {

    //查询数据
    List<UU898NewOrderPayPurchaseExtend> selectList(@Param("userId") Long userId, @Param("orderNo") String orderNo, @Param("payOrderNo") String payOrderNo);

    //修改金额
    int updatePurchaseMoneyFromMoney(@Param("id") Long Id, @Param("userId") Long userId, @Param("orderNo") String orderNo,
                                     @Param("payOrderNo") String payOrderNo, @Param("money") BigDecimal money, @Param("updateTime") LocalDateTime updateTime);


}


