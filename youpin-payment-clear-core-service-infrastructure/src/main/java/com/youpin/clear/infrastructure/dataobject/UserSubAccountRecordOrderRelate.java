package com.youpin.clear.infrastructure.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户二级账户订单关系表
 *
 * <AUTHOR>
 * @TableName user_sub_account_record_order_relate
 */
@Data
public class UserSubAccountRecordOrderRelate implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}