package com.youpin.clear.infrastructure.strategy;

import com.youpin.clear.domain.aggregate.uu898.*;
import com.youpin.clear.domain.gateway.UserSubAccountGateway;
import com.youpin.clear.domain.gateway.UserSubAccountFlowRecordGateway;
import com.youpin.clear.domain.process.AbstractDeductionStrategy;
import com.youpin.clear.domain.process.DeductionBusinessInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 退款加款策略
 * 优先退回可提现余额，不足时退到可交易余额
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundAdditionStrategy extends AbstractDeductionStrategy {
    
    @Autowired
    private UserSubAccountGateway userSubAccountGateway;
    
    @Autowired
    private UserSubAccountFlowRecordGateway flowRecordGateway;
    
    @Override
    public List<DeductionStrategyType> supportedTypes() {
        return Arrays.asList(DeductionStrategyType.REFUND_ADDITION);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeductionResult executeDeduction(UserAccount userAccount, 
                                          BigDecimal amount, 
                                          DeductionStrategyConfig config,
                                          DeductionBusinessInfo businessInfo) {
        
        log.info("开始执行退款操作，用户ID：{}，退款金额：{}", userAccount.getUserId(), amount);
        
        // 对于退款，我们不需要检查余额，因为是加款操作
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return DeductionResult.builder()
                    .success(false)
                    .errorMessage("退款金额必须大于0")
                    .totalAmount(amount)
                    .actualAmount(BigDecimal.ZERO)
                    .build();
        }
        
        List<DeductionResult.SubAccountDeductionDetail> details = new ArrayList<>();
        List<Long> flowRecordIds = new ArrayList<>();
        BigDecimal remainingAmount = amount;
        
        try {
            // 1. 优先退回可提现余额
            UserSubAccount withdrawAccount = userAccount.getSubAccountMap().get(UserSubAccountType.WITHDRAW);
            if (withdrawAccount != null && remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal withdrawAddition = processAccountAddition(
                        withdrawAccount, remainingAmount, businessInfo, details, flowRecordIds);
                remainingAmount = remainingAmount.subtract(withdrawAddition);
                log.info("退回可提现余额：{}，剩余待退：{}", withdrawAddition, remainingAmount);
            }
            
            // 2. 如果还有剩余，退到可交易余额
            UserSubAccount tradeAccount = userAccount.getSubAccountMap().get(UserSubAccountType.TRADE);
            if (tradeAccount != null && remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal tradeAddition = processAccountAddition(
                        tradeAccount, remainingAmount, businessInfo, details, flowRecordIds);
                remainingAmount = remainingAmount.subtract(tradeAddition);
                log.info("退回可交易余额：{}，剩余待退：{}", tradeAddition, remainingAmount);
            }
            
            BigDecimal actualAmount = amount.subtract(remainingAmount);
            boolean success = remainingAmount.compareTo(BigDecimal.ZERO) == 0;
            
            log.info("退款完成，用户ID：{}，总金额：{}，实际退款：{}，成功：{}", 
                    userAccount.getUserId(), amount, actualAmount, success);
            
            return DeductionResult.builder()
                    .success(success)
                    .totalAmount(amount)
                    .actualAmount(actualAmount)
                    .deductionDetails(details)
                    .flowRecordIds(flowRecordIds)
                    .build();
                    
        } catch (Exception e) {
            log.error("退款执行异常，用户ID：{}，金额：{}", userAccount.getUserId(), amount, e);
            return DeductionResult.builder()
                    .success(false)
                    .errorMessage("退款执行异常：" + e.getMessage())
                    .totalAmount(amount)
                    .actualAmount(BigDecimal.ZERO)
                    .build();
        }
    }
    
    /**
     * 处理单个账户的加款（退款）
     */
    private BigDecimal processAccountAddition(UserSubAccount account,
                                            BigDecimal requestAmount,
                                            DeductionBusinessInfo businessInfo,
                                            List<DeductionResult.SubAccountDeductionDetail> details,
                                            List<Long> flowRecordIds) {
        
        BigDecimal additionAmount = requestAmount; // 退款时全额退回
        BigDecimal balanceBefore = account.getBalance();
        BigDecimal balanceAfter = balanceBefore.add(additionAmount);
        
        // 更新账户余额
        account.setBalance(balanceAfter);
        account.setUpdateTime(LocalDateTime.now());
        userSubAccountGateway.updateBalance(account);
        
        // 创建流水记录（退款为正数）
        UserSubAccountFlowRecord flowRecord = createRefundFlowRecord(
                account, additionAmount, balanceBefore, balanceAfter, businessInfo);
        Long flowRecordId = flowRecordGateway.insert(flowRecord);
        
        // 记录退款明细
        details.add(DeductionResult.SubAccountDeductionDetail.builder()
                .accountType(UserSubAccountType.values()[account.getAccountType() - 1])
                .balanceBefore(balanceBefore)
                .deductionAmount(additionAmount) // 这里表示退款金额
                .balanceAfter(balanceAfter)
                .flowRecordId(flowRecordId)
                .build());
        
        flowRecordIds.add(flowRecordId);
        
        return additionAmount;
    }
    
    /**
     * 创建退款流水记录
     */
    private UserSubAccountFlowRecord createRefundFlowRecord(UserSubAccount subAccount,
                                                          BigDecimal additionAmount,
                                                          BigDecimal balanceBefore,
                                                          BigDecimal balanceAfter,
                                                          DeductionBusinessInfo businessInfo) {
        return UserSubAccountFlowRecord.builder()
                .accountNo(subAccount.getAccountNo())
                .accountType(subAccount.getAccountType())
                .userId(subAccount.getUserId())
                .payOrderNo(businessInfo.getPayOrderNo())
                .orderNo(businessInfo.getOrderNo())
                .serialNo(businessInfo.getSerialNo())
                .balanceBefore(balanceBefore)
                .balanceChange(additionAmount) // 退款为正数
                .balanceAfter(balanceAfter)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .valid(true)
                .extInfo(businessInfo.getExtInfo())
                .build();
    }
}
