package com.youpin.clear.infrastructure.gateway.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.member.BillItemMemberExtension;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.gateway.ClearUserAssetsRecordGateway;
import com.youpin.clear.infrastructure.dataobject.UserAssetsOrderNoRelate;
import com.youpin.clear.infrastructure.dataobject.UserAssetsPayOrderNoRelate;
import com.youpin.clear.infrastructure.dataobject.UserAssetsRecord;
import com.youpin.clear.infrastructure.dataobject.UserAssetsTreadNoRelate;
import com.youpin.clear.infrastructure.mapper.*;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClearUserAssetsRecordGatewayImpl implements ClearUserAssetsRecordGateway {

    @Autowired
    UserAssetsRecordMapper userAssetsRecordMapper;
    @Autowired
    UserAssetsPayOrderNoRelateMapper userAssetsPayOrderNoRelateMapper;
    @Autowired
    UserAssetsOrderNoRelateMapper userAssetsOrderNoRelateMapper;
    @Autowired
    UserAssetsTreadNoRelateMapper userAssetsTreadNoRelateMapper;
    @Autowired
    UserAssetsTagMapper userAssetsTagMapper;

    @Autowired
    UserAssetsRecordExtMapper userAssetsRecordExtMapper;

    @Override
    public ClearUserAssetsRecordDTO selectUserAssetsRecordById(Long userId, Long userAssetsRecordId) {
        UserAssetsRecord userAssetsRecord = userAssetsRecordExtMapper.getUserAssetsRecordById(userId, userAssetsRecordId);
        return BeanUtilsWrapper.convert(userAssetsRecord, ClearUserAssetsRecordDTO::new);
    }

    @Override
    public void updateByPrimaryKeySelectiveByUserId(ClearUserAssetsRecordDTO dto) {
        UserAssetsRecord userAssetsRecord = BeanUtilsWrapper.convert(dto, UserAssetsRecord::new);
        userAssetsRecordExtMapper.updateByPrimaryKeySelectiveByUserId(userAssetsRecord);
    }

    @Override
    public void insertSelective(ClearUserAssetsRecordDTO dto) {
        UserAssetsRecord userAssetsRecord = BeanUtilsWrapper.convert(dto, UserAssetsRecord::new);
        userAssetsRecordMapper.insertSelective(userAssetsRecord);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ClearUserAssetsRecordDTO> batchClearUserAssetsRecordDTO(List<ClearUserAssetsRecordDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        List<UserAssetsRecord> returnUserAssetsRecordList = new ArrayList<>();
        List<UserAssetsRecord> userAssetsRecordList = BeanUtilsWrapper.convertList(dtoList, UserAssetsRecord::new);
        List<UserAssetsPayOrderNoRelate> userAssetsPayOrderNoRelateList = new ArrayList<>();
        List<UserAssetsOrderNoRelate> userAssetsOrderNoRelatesList = new ArrayList<>();
        List<UserAssetsTreadNoRelate> userAssetsTreadNoRelateList = new ArrayList<>();

        //取出 dtoList 中 userAssetsRecordId 最小ID和最大ID
        Long minId = dtoList.get(0).getUserAssetsRecordId();
        Long maxId = dtoList.get(0).getUserAssetsRecordId();
        for (ClearUserAssetsRecordDTO dto : dtoList) {
            long currentId = dto.getUserAssetsRecordId();
            if (currentId < minId) {
                minId = currentId;
            }
            if (currentId > maxId) {
                maxId = currentId;
            }
        }
//        log.info("[批量保存用户资产记录] 开始");

        Map<Long, UserAssetsRecord> userAssetsRecordsDbIdMap = null;
        Long userId = userAssetsRecordList.get(0).getUserId();
        List<UserAssetsRecord> userAssetsRecordsDbList = userAssetsRecordExtMapper.batchGetUserAssetsRecordById(userId, minId, maxId);
        if (CollectionUtils.isNotEmpty(userAssetsRecordsDbList)) {
            userAssetsRecordsDbIdMap = userAssetsRecordsDbList.stream().collect(Collectors.toMap(UserAssetsRecord::getUserAssetsRecordId, p -> p));
        }
        for (UserAssetsRecord userAssetsRecord : userAssetsRecordList) {

            UserAssetsRecord assetsRecordDbById = null;
            if (userAssetsRecordsDbIdMap != null) {
                assetsRecordDbById = userAssetsRecordsDbIdMap.getOrDefault(userAssetsRecord.getUserAssetsRecordId(), null);
            }

            if (assetsRecordDbById == null) {
                userAssetsRecordMapper.insertSelective(userAssetsRecord);
                returnUserAssetsRecordList.add(userAssetsRecord);
            } else {
                //判断数据库的数据是什么状态
                if (Objects.equals(assetsRecordDbById.getStatus(), NetStatusEnum.PROCESSING.getCode())) {
                    if (Objects.equals(userAssetsRecord.getStatus(), NetStatusEnum.SUCCESS.getCode()) || Objects.equals(userAssetsRecord.getStatus(), NetStatusEnum.FAIL.getCode())) {
                        //参数更新
                        assetsRecordDbById.setStatus(userAssetsRecord.getStatus());
                        assetsRecordDbById.setPayChannel(userAssetsRecord.getPayChannel());
                        assetsRecordDbById.setSerialNo(userAssetsRecord.getSerialNo());
                        assetsRecordDbById.setOrderNo(userAssetsRecord.getOrderNo());
                        assetsRecordDbById.setPayOrderNo(userAssetsRecord.getPayOrderNo());
                        assetsRecordDbById.setRemark(userAssetsRecord.getRemark());
                        assetsRecordDbById.setThisMoney(userAssetsRecord.getThisMoney());
                        assetsRecordDbById.setCompleteTime(userAssetsRecord.getCompleteTime());
                        assetsRecordDbById.setAssetType(userAssetsRecord.getAssetType());
                        assetsRecordDbById.setMoney(userAssetsRecord.getMoney());
                        assetsRecordDbById.setAfterMoney(userAssetsRecord.getAfterMoney());
                        assetsRecordDbById.setChargeMoney(userAssetsRecord.getChargeMoney());
                        assetsRecordDbById.setBlockMoney(userAssetsRecord.getBlockMoney());
                        assetsRecordDbById.setAfterBlockMoney(userAssetsRecord.getAfterBlockMoney());
                        assetsRecordDbById.setPurchaseMoney(userAssetsRecord.getPurchaseMoney());
                        assetsRecordDbById.setAfterPurchaseMoney(userAssetsRecord.getAfterPurchaseMoney());
                        assetsRecordDbById.setThisBlockMoney(userAssetsRecord.getThisBlockMoney());
                        assetsRecordDbById.setThisPurchaseMoney(userAssetsRecord.getThisPurchaseMoney());
                        assetsRecordDbById.setGenSource(userAssetsRecord.getGenSource());
                        assetsRecordDbById.setPayWaitExpireTime(userAssetsRecord.getPayWaitExpireTime());
                        assetsRecordDbById.setTypeName(userAssetsRecord.getTypeName());
                        assetsRecordDbById.setAccountName(userAssetsRecord.getAccountName());
                        assetsRecordDbById.setAttr(userAssetsRecord.getAttr());
                        userAssetsRecordExtMapper.updateByPrimaryKeySelectiveByUserId(assetsRecordDbById);
                        returnUserAssetsRecordList.add(userAssetsRecord);
                    }
                }
            }
        }


        if (CollectionUtils.isEmpty(returnUserAssetsRecordList)) {
            return null;
        }

        for (UserAssetsRecord dto : returnUserAssetsRecordList) {
            userAssetsTreadNoRelateList.add(buildUserAssetsTreadNoRelate(dto));
            if (StringUtils.isNotBlank(dto.getPayOrderNo())) {
                userAssetsPayOrderNoRelateList.add(buildUserAssetsPayOrderNoRelate(dto));
            }
            if (StringUtils.isNotBlank(dto.getOrderNo())) {
                userAssetsOrderNoRelatesList.add(buildUserAssetsOrderNoRelate(dto));
            }
        }

        //去重
        userAssetsPayOrderNoRelateList = userAssetsPayOrderNoRelateList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(p -> p.getUserId() + "," + p.getPayOrderNo(), p -> p, (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        userAssetsOrderNoRelatesList = userAssetsOrderNoRelatesList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(p -> p.getUserId() + "," + p.getOrderNo(), p -> p, (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        userAssetsTreadNoRelateList = userAssetsTreadNoRelateList.stream().collect(Collectors.collectingAndThen(Collectors.toMap(p -> p.getUserId() + "," + p.getTreadNo(), p -> p, (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));

        for (UserAssetsPayOrderNoRelate userAssetsPayOrderNoRelate : userAssetsPayOrderNoRelateList) {
            if (userAssetsRecordExtMapper.countPkUserAssetsPayOrderNoRelateByPayOrderNo(userAssetsPayOrderNoRelate.getUserId(), userAssetsPayOrderNoRelate.getPayOrderNo()) == 0) {
                try {
                    userAssetsPayOrderNoRelateMapper.insertSelective(userAssetsPayOrderNoRelate);
                } catch (Exception ignored) {
                }
            }
        }

        for (UserAssetsOrderNoRelate userAssetsOrderNoRelate : userAssetsOrderNoRelatesList) {
            if (userAssetsRecordExtMapper.countPkUserAssetsOrderNoRelateByOrderNo(userAssetsOrderNoRelate.getUserId(), userAssetsOrderNoRelate.getOrderNo()) == 0) {
                try {
                    userAssetsOrderNoRelateMapper.insertSelective(userAssetsOrderNoRelate);
                } catch (Exception ignored) {
                }
            }
        }

        for (UserAssetsTreadNoRelate userAssetsTreadNoRelate : userAssetsTreadNoRelateList) {
            if (userAssetsRecordExtMapper.countPkUserAssetsTreadNoRelateByTreadNo(userAssetsTreadNoRelate.getUserId(), userAssetsTreadNoRelate.getTreadNo()) == 0) {
                try {
                    userAssetsTreadNoRelateMapper.insertSelective(userAssetsTreadNoRelate);
                } catch (Exception ignored) {
                }
            }
        }

//        log.info("[批量保存用户资产记录] 结束");
        if (!returnUserAssetsRecordList.isEmpty()) {
            return BeanUtilsWrapper.convertList(returnUserAssetsRecordList, ClearUserAssetsRecordDTO::new);
        }
        return null;
    }

    @Override
    public Long maxUserAssetsRecordId(Long userId) {
        return userAssetsRecordExtMapper.maxUserAssetsRecordId(userId);
    }

    @Override
    public List<ClearUserAssetsRecordDTO> selectUserAssetsRecordById(Long userId, Long pageIndex, Integer pageSize, Long userAssetsRecordId) {
        List<UserAssetsRecord> userAssetsRecordList = userAssetsRecordExtMapper.selectUserAssetsRecordById(userId, pageIndex, pageSize, userAssetsRecordId);
        if (CollectionUtils.isEmpty(userAssetsRecordList)) {
            return Collections.emptyList();
        }
        List<ClearUserAssetsRecordDTO> clearUserAssetsRecordDTOList = new ArrayList<>();
        for (UserAssetsRecord userAssetsRecord : userAssetsRecordList) {
            ClearUserAssetsRecordDTO clearUserAssetsRecordDTO = BeanUtilsWrapper.convert(userAssetsRecord, ClearUserAssetsRecordDTO::new);
            if (StringUtils.isNotBlank(userAssetsRecord.getExt())) {
                BillItemMemberExtension billItemMemberExtension = JSON.parseObject(userAssetsRecord.getExt(), BillItemMemberExtension.class);
                clearUserAssetsRecordDTO.setCollectType(billItemMemberExtension.getCollectType());
                clearUserAssetsRecordDTO.setIsLeaseOrder(billItemMemberExtension.getIsLeaseOrder());
                clearUserAssetsRecordDTO.setBalance2TransferAmount(billItemMemberExtension.getBalance2TransferAmount());
            }
            clearUserAssetsRecordDTOList.add(clearUserAssetsRecordDTO);
        }
        return clearUserAssetsRecordDTOList;
    }


    private UserAssetsPayOrderNoRelate buildUserAssetsPayOrderNoRelate(UserAssetsRecord userAssetsRecord) {
        return UserAssetsPayOrderNoRelate.builder().userId(userAssetsRecord.getUserId()).payOrderNo(userAssetsRecord.getPayOrderNo()).createTime(userAssetsRecord.getAddTime()).updateTime(userAssetsRecord.getCompleteTime()).build();
    }

    private UserAssetsOrderNoRelate buildUserAssetsOrderNoRelate(UserAssetsRecord userAssetsRecord) {
        return UserAssetsOrderNoRelate.builder().userId(userAssetsRecord.getUserId()).orderNo(userAssetsRecord.getOrderNo()).createTime(userAssetsRecord.getAddTime()).updateTime(userAssetsRecord.getCompleteTime()).build();
    }

    private UserAssetsTreadNoRelate buildUserAssetsTreadNoRelate(UserAssetsRecord userAssetsRecord) {
        return UserAssetsTreadNoRelate.builder().userId(userAssetsRecord.getUserId()).treadNo(userAssetsRecord.getTreadNo()).createTime(userAssetsRecord.getAddTime()).updateTime(userAssetsRecord.getCompleteTime()).build();
    }

}
