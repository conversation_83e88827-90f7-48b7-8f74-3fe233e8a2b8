package com.youpin.clear.infrastructure.process.business.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorAssetInfoDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 私密交易-资金处理
 */
@Slf4j
@Service
public class PrivateSaleFinancialProcessor extends DefaultSubBusFinancialProcessor {

    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.PRIVATE_SALE);
    }


    /**
     *
     */
    @Override
    public void refundSuccess(FinancialProcessorDTO dto) {
        super.refundSuccess(dto);
        refundSuccess255(dto);

    }

    void refundSuccess255(FinancialProcessorDTO financialProcessorDTO) {
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        financialProcessorDTO.getAbstractUserAssetsRecordDTOList()
                .stream().filter(dto -> dto.getTypeId().equals(UserAssetsTypeEnum.TYPE_254.getTypeId()))
                .findFirst().ifPresent(item -> {

                    AbstractUserAssetsRecordDTO sellFrozenSettlementRecord =
                            buildAssetRecord(UserAssetsTypeEnum.TYPE_255,
                                    financialProcessorDTO, getPlatformUserId(UserAssetsTypeEnum.TYPE_255),
                                    item.getPayChannel(), financialProcessorDTO.getNetStatusEnum(), AmountUtils.convertToCent(item.getChangeMoney()));
                    tempList.add(sellFrozenSettlementRecord);
                });
        financialProcessorDTO.addAbstractUserAssetsRecordDTO(tempList);
    }


    /**
     * 结算特殊处理
     */
    @Override
    public void specialSettlement(FinancialProcessorDTO dto) {
        //判断是否可以重复补贴
        if (dto.isUserAssetsRecordDTOLinkListEmpty()) {
            log(Level.ERROR, "私密交易-补贴资金处理 没有结算数据不能补贴1", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "没有结算数据不能补贴");
        }
        //判断 数据是否重复
        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = dto.getUserAssetsRecordDTOLinkList();
        UU898UserAssetsRecordDTO uu898UserAssetsRecordDTOPay = null;
        boolean isSettlementFlag = false;
        for (UU898UserAssetsRecordDTO userAssetsRecordDTO : userAssetsRecordDTOLinkList) {
            //已退款不容许补贴
            if (userAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_4.getTypeId())) {
                log(Level.ERROR, "私密交易-补贴资金处理 已经退款不能补贴", dto);
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "已经退款不能补贴");
            }
            if (userAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_95.getTypeId())) {
                log(Level.ERROR, "私密交易-补贴资金处理 数据重复", dto);
                throw new PaymentClearBusinessException(AssetsErrorCode.ALREADY_PROCESSED.getCode(), "补贴资金处理重复补贴");
            }
            if (userAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_3.getTypeId())) {
                uu898UserAssetsRecordDTOPay = userAssetsRecordDTO;
            }
            if (userAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_5.getTypeId())) {
                isSettlementFlag = true;
            }
        }

        //未支付 不容许补贴 ,未结算 不容许补贴
        if (null == uu898UserAssetsRecordDTOPay || !isSettlementFlag) {
            log(Level.ERROR, "私密交易-补贴资金处理 没有结算数据不能补贴2", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "没有结算数据不能补贴");
        }
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();

        //处理私密结算特殊处理
        for (FinancialProcessorAssetInfoDTO assetInfoDTO : dto.getAssetInfoDTOList()) {
            if (assetInfoDTO.getTypeId().equals(Constant.CONSTANT_INTEGER_1) || assetInfoDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_94.getTypeId())) {
                if (AmountUtils.convertToDollarToDecimal(new BigDecimal(assetInfoDTO.getMoney())).compareTo(uu898UserAssetsRecordDTOPay.getThisMoney().abs()) >= 0) {
                    log(Level.ERROR, "私密交易-补贴资金处理 补贴金额不能大于支付等于金额", dto);
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "补贴金额不能大于支付等于金额");
                }
                //私密交易-补贴资金处理   95 私密交易平台户
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_95, dto, getPlatformUserId(UserAssetsTypeEnum.TYPE_95), assetInfoDTO.getPayChannel(), dto.getNetStatusEnum(), assetInfoDTO.getMoney()));
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_94, dto, assetInfoDTO.getUserId(), assetInfoDTO.getPayChannel(), dto.getNetStatusEnum(), assetInfoDTO.getMoney()));
                break;
            }
        }
        dto.addAbstractUserAssetsRecordDTO(tempList);
        //校验数据
        if (dto.isAbstractUserAssetsRecordDTOListEmpty()) {
            log(Level.ERROR, "私密交易-补贴资金处理 数据校验异常", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "私密交易-补贴资金处理数据校验异常");
        }
        //256
        sellFrozenSettlement256(dto);
    }

    /**
     * 出售结算冻结逻辑
     */
    void sellFrozenSettlement256(FinancialProcessorDTO financialProcessorDTO) {
        //冻结或结算逻辑开关
        Boolean sellFrozenSettlementFlag = paymentClearParamsConfig.getSellFrozenSettlementFlag();
        if (!Boolean.TRUE.equals(sellFrozenSettlementFlag)) {
            return;
        }
        //冻结或结算入参逻辑开关
        Boolean sellFrozenSettlementParamFlag = paymentClearParamsConfig.getSellFrozenSettlementParamFlag();
        if (Boolean.TRUE.equals(sellFrozenSettlementParamFlag)) {
            if (null != financialProcessorDTO.getSettleAccountType() && financialProcessorDTO.getSettleAccountType().equals(SettleAccountTypeEnum.MONEY.getCode())) {
                log(Level.WARN, "私密交易-补贴资金处理 资金处理 冻结或结算入参逻辑开关:{}", financialProcessorDTO, financialProcessorDTO.getSettleAccountType());
                return;
            }
        }
        AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_94 = financialProcessorDTO.getAbstractUserAssetsRecordDTOList().stream().filter(dto -> dto.getTypeId().equals(UserAssetsTypeEnum.TYPE_94.getTypeId()))
                .findFirst().orElse(null);

        if (null == abstractUserAssetsRecordDTO_94) {
            return;
        }

        log(Level.INFO, "私密交易-补贴资金处理 冻结金额:{}", financialProcessorDTO, abstractUserAssetsRecordDTO_94.getChangeMoney());

        if (abstractUserAssetsRecordDTO_94.getChangeMoney().compareTo(BigDecimal.ZERO) > 0) {
            AbstractUserAssetsRecordDTO sellFrozenSettlementRecord =
                    buildAssetRecord(UserAssetsTypeEnum.TYPE_256,
                            financialProcessorDTO, abstractUserAssetsRecordDTO_94.getUserId(),
                            abstractUserAssetsRecordDTO_94.getPayChannel(), financialProcessorDTO.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO_94.getChangeMoney()));
            List<AbstractUserAssetsRecordDTO> dtoList = new ArrayList<>();
            dtoList.add(sellFrozenSettlementRecord);
            financialProcessorDTO.addAbstractUserAssetsRecordDTO(dtoList);
        }
    }

}
