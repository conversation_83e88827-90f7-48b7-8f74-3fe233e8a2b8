package com.youpin.clear.infrastructure.gateway.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.AccountInfoMemberExtension;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.converter.UserAccountRecordOrderRelateConvertor;
import com.youpin.clear.infrastructure.dataobject.UpdateAccountBalanceDO;
import com.youpin.clear.infrastructure.dataobject.UserAccountRecord;
import com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate;
import com.youpin.clear.infrastructure.mapper.*;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
//@Primary
public class UserAccountRecordGatewayImpl implements UserAccountRecordGateway {

    @Autowired
    UserAccountRecordMapper userAccountRecordMapper;

    @Autowired
    UserAccountRecordExtMapper userAccountRecordExtMapper;

    @Autowired
    UserAccountRecordOrderRelateExtMapper userAccountRecordOrderRelateExtMapper;

    @Autowired
    UserAccountRecordOrderRelateMapper userAccountRecordOrderRelateMapper;

    @Autowired
    UserAccountExtMapper userAccountExtMapper;

    @Autowired
    IdWorkService idWorkService;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByUserId(Long userId) {
        userAccountRecordExtMapper.deleteByUserId(userId);
        userAccountRecordOrderRelateExtMapper.deleteByUserId(userId);
    }

    /**
     * 保存资金明细分账记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long userAccountRecordSave(UserAccountRecordMember userAccountRecordMember) {
        Boolean flag = countUserAccountRecord(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), userAccountRecordMember.getBalanceChange(), userAccountRecordMember.getAccountType());
        if (flag) {
            log.info("[分账处理] 分账记录已存在  用户id:{} 账户类型:{} 资金ID:{} ", userAccountRecordMember.getUserId(), userAccountRecordMember.getAccountType(), userAccountRecordMember.getUserAssetsRecordId());
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_RECORD_EXIST);
        } else {
            UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordMember, UserAccountRecord::new);
            userAccountRecord.setAccountRecordNo(idWorkService.getNextIdLeafKey());
            userAccountRecordMapper.insertSelective(userAccountRecord);
            userAccountRecordMember.setId(userAccountRecord.getId());
            return userAccountRecord.getId();
        }
    }

    /**
     * 更新资金明细分账记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long userAccountRecordUpdateDO(UserAccountRecordMember userAccountRecordMember) {
        UserAccountRecordMember userAccountRecordUpdateStatus = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember);
        if (Objects.nonNull(userAccountRecordUpdateStatus)) {
            if (userAccountRecordUpdateStatus.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
                log.info("[分账处理] 分账记录已存在 状态不是进行中 用户id:{} 账户类型:{} 资金ID:{} ", userAccountRecordMember.getUserId(), userAccountRecordMember.getAccountType(), userAccountRecordMember.getUserAssetsRecordId());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_RECORD_EXIST);
            }
            UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordUpdateStatus, UserAccountRecord::new);
            userAccountRecord.setBalanceBefore(userAccountRecordMember.getBalanceBefore());
            userAccountRecord.setBalanceAfter(userAccountRecordMember.getBalanceAfter());
            userAccountRecord.setPayOrderNo(userAccountRecordMember.getPayOrderNo());
            userAccountRecord.setOrderNo(userAccountRecordMember.getOrderNo());
            userAccountRecord.setStatus(userAccountRecordMember.getStatus());
            userAccountRecord.setFinishTime(userAccountRecordMember.getFinishTime());
            userAccountRecord.setBalanceIsChange(userAccountRecordMember.getBalanceIsChange());
            userAccountRecordExtMapper.updateByPrimaryKeySelectiveUserId(userAccountRecord);
            return userAccountRecord.getId();
        } else {
            // 兜底一下
            return userAccountRecordSave(userAccountRecordMember);
        }
    }

    /**
     * 更新资金明细分账记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void userAccountRecordUpdateStatus(UserAccountRecordMember userAccountRecordMember) {
        UserAccountRecordMember userAccountRecordUpdateStatus = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember);
        if (Objects.nonNull(userAccountRecordUpdateStatus)) {
            if (userAccountRecordUpdateStatus.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
                log.info("[分账处理] 分账记录已存在 状态不是进行中  更新状态 用户id:{} 账户类型:{} 资金ID:{} ", userAccountRecordMember.getUserId(), userAccountRecordMember.getAccountType(), userAccountRecordMember.getUserAssetsRecordId());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_RECORD_EXIST);
            }
            UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordUpdateStatus, UserAccountRecord::new);
            userAccountRecord.setPayOrderNo(userAccountRecordMember.getPayOrderNo());
            userAccountRecord.setStatus(userAccountRecordMember.getStatus());
            userAccountRecord.setFinishTime(userAccountRecordMember.getFinishTime());
            userAccountRecordExtMapper.updateByPrimaryKeySelectiveUserId(userAccountRecord);
        } else {
            // 兜底一下
            userAccountRecordSave(userAccountRecordMember);
        }
    }


    /**
     * 更新资金明细分账记录
     */
    public UserAccountRecordMember getUserAccountRecordByUserIdAndUserAssetsRecordId(UserAccountRecordMember userAccountRecordMember) {
        List<UserAccountRecordMember> userAccountRecordMemberList = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), userAccountRecordMember.getAccountType());
        if (Objects.nonNull(userAccountRecordMemberList) && !userAccountRecordMemberList.isEmpty()) {
            UserAccountRecordMember userAccountRecordUpdateStatus = null;
            for (UserAccountRecordMember userAccountRecordMemberItem : userAccountRecordMemberList) {
                if (userAccountRecordMemberItem.getAccountType().equals(userAccountRecordMember.getAccountType()) && userAccountRecordMemberItem.getUserAccountNo().equals(userAccountRecordMember.getUserAccountNo()) && userAccountRecordMemberItem.getBalanceChange().abs().compareTo(userAccountRecordMember.getBalanceChange().abs()) == 0) {

                    if (StringUtils.isNotBlank(userAccountRecordMemberItem.getPayOrderNo())) {
                        if (userAccountRecordMemberItem.getPayOrderNo().equals(userAccountRecordMember.getPayOrderNo())) {
                            userAccountRecordUpdateStatus = userAccountRecordMemberItem;
                            break;
                        }
                    } else {
                        userAccountRecordUpdateStatus = userAccountRecordMemberItem;
                        break;
                    }
                }
            }
            if (Objects.nonNull(userAccountRecordUpdateStatus)) {
                return userAccountRecordUpdateStatus;
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void userAccountRecordMemberTransactional(List<UserAccountRecordMember> userAccountRecordMemberList, Map<Long, AccountAggregate> accountAggregateMap) {
        //判断平台资金类型 是否需要更新
        List<Integer> platformTypeIdList = List.of();
        if (!paymentClearParamsConfig.getPlatformAccountUpdate()) {
            platformTypeIdList = paymentClearParamsConfig.getPlatformTypeIdList();
        }

        for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberList) {

            Long lastAccountRecordId = null;

            BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();

            BigDecimal frozenBalanceChange = userAccountRecordMember.getFrozenBalanceChange();


            AccountAggregate accountAggregate = accountAggregateMap.get(userAccountRecordMember.getUserId());

            assert accountAggregate != null;

            AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()));

            DoNetPayChannelEnum payChannelEnum = DoNetPayChannelEnum.getByPayChannel(userAccountRecordMember.getPayChannel());

            boolean isBalanceOrPurchase = payChannelEnum.equals(DoNetPayChannelEnum.Balance) || payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance);
            //方向
            DirectionEnum directionEnum = userAccountRecordMember.getDirectionEnum();
            //状态
            NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(userAccountRecordMember.getStatus());

            Integer typeId = userAccountRecordMember.getTypeId();


            //余额
            BigDecimal balance = BigDecimal.ZERO;
            //余额冻结
            BigDecimal frozenBalance = BigDecimal.ZERO;
            //求购余额
            BigDecimal purchaseBalance = BigDecimal.ZERO;
            //求购余额冻结
            BigDecimal frozenPurchaseBalance = BigDecimal.ZERO;

            //是否进行中
            boolean isProcessing = netStatusEnum.equals(NetStatusEnum.PROCESSING);
            //是否失败
            boolean isFail = netStatusEnum.equals(NetStatusEnum.FAIL);
            //是否成功
            boolean isSuccess = netStatusEnum.equals(NetStatusEnum.SUCCESS);

            //平台户出金类型是否需要更新账户数据
            boolean platformAccountUpdate = platformTypeIdList.contains(typeId);

            switch (directionEnum) {
                case RECHARGE:
                    //充值 进行中 不操作 账户 只存记录
                    if (isProcessing || isFail) {
                        log.info("[分账处理] 充值 状态为进行中,不操作账户,只存记录, 资金编号:{}", userAccountRecordMember.getUserAssetsRecordId());
                        userAccountRecordSave(userAccountRecordMember);
                        continue;
                    } else {
                        userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                        lastAccountRecordId = userAccountRecordUpdateDO(userAccountRecordMember);
                        //修改账户金额
                        updateAccountBalance("充值+", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
                        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_1) || typeId.equals(ClearConstants.CONSTANT_INTEGER_82) || typeId.equals(ClearConstants.CONSTANT_INTEGER_219)) {
                            balance = balance.add(balanceChange);
                        } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_43)) {
                            purchaseBalance = purchaseBalance.add(balanceChange);
                        }
                    }
                    break;
                case CASH_WITHDRAWAL:
                    balanceChange = balanceChange.negate();
                    userAccountRecordMember.setBalanceChange(balanceChange);
                    //提现 成功 不操作 账户 只更新记录
                    if (isSuccess || isFail) {
                        //判断数据库中是已经有进行中数据
                        UserAccountRecordMember userIdAndUserAssetsRecordProcessing = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember);
                        if (Objects.nonNull(userIdAndUserAssetsRecordProcessing)) {
                            log.info("[分账处理] 资金类型为提现,数据库已经有数据:{},--->更新状态为{}", userIdAndUserAssetsRecordProcessing.getStatus(), netStatusEnum.getCode());
                            //TODO 此处  setBalanceIsChange 需要 优化
                            userAccountRecordUpdateStatus(userAccountRecordMember);
                        } else {
                            log.info("[分账处理] 资金类型为提现,数据库中没有进行中的数据 ,当前消费状态:{}", netStatusEnum.getCode());
                            throw new PaymentClearBusinessException(ErrorCode.BILL_STATUS_ORDER_ERROR);
                        }
                        continue;
                    } else {
                        //进行中
                        //修改账户金额
                        userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                        lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                        updateAccountBalance("提现-", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
                        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_2)) {
                            balance = balance.add(balanceChange);
                        } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_44)) {
                            purchaseBalance = purchaseBalance.add(balanceChange);
                        }
                    }
                    break;
                case ADDITION:
                    if (isBalanceOrPurchase) {
                        if (isProcessing || isFail) {
                            userAccountRecordSave(userAccountRecordMember);
                            continue;
                        } else {
                            //判断数据库中是已经有进行中数据
                            UserAccountRecordMember userIdAndUserAssetsRecordProcessing = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember);
                            if (Objects.nonNull(userIdAndUserAssetsRecordProcessing)) {
                                userAccountRecordUpdateDO(userAccountRecordMember);
                            } else {
                                lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                            }
                        }
                        updateAccountBalance("加", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
                        if (payChannelEnum.equals(DoNetPayChannelEnum.Balance)) {
                            balance = balance.add(balanceChange);
                        } else {
                            purchaseBalance = purchaseBalance.add(balanceChange);
                        }
                    } else {
                        userAccountRecordSave(userAccountRecordMember);
                        log.info("[分账处理] {} {} 渠道金额不进行 加", typeId, payChannelEnum);
                    }
                    break;
                case REFUND:
                    //退款 并且 金额是负数的
                    boolean isRefundNegate2 = (typeId.equals(ClearConstants.CONSTANT_INTEGER_4) || typeId.equals(ClearConstants.CONSTANT_INTEGER_220)) && balanceChange.compareTo(BigDecimal.ZERO) < 0;
                    if (isBalanceOrPurchase || isRefundNegate2) {
                        if (isProcessing || isFail) {
                            userAccountRecordSave(userAccountRecordMember);
                            continue;
                        } else {
                            //判断数据库中是已经有进行中数据
                            UserAccountRecordMember userIdAndUserAssetsRecordProcessing = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember);
                            if (Objects.nonNull(userIdAndUserAssetsRecordProcessing)) {
                                userAccountRecordUpdateDO(userAccountRecordMember);
                            } else {
                                lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                            }
                        }
                        updateAccountBalance("退款", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);

                        if (payChannelEnum.equals(DoNetPayChannelEnum.Balance) || isRefundNegate2) {
                            balance = balance.add(balanceChange);
                        } else {
                            purchaseBalance = purchaseBalance.add(balanceChange);
                        }
                    } else {
                        userAccountRecordSave(userAccountRecordMember);
                        log.info("[分账处理] {} {} 渠道金额不进行 退款", typeId, payChannelEnum);
                    }
                    break;
                case SUBTRACTION:
                    //改成负数
                    balanceChange = balanceChange.negate();
                    userAccountRecordMember.setBalanceChange(balanceChange);
                    if (platformAccountUpdate) {
                        //修改平台账户数据结构
                        platformAccountUpdate(userAccountRecordMember);
                    }
                    lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                    if (isBalanceOrPurchase) {
                        //是否修改平台账户
                        if (platformAccountUpdate) {
                            log.info("[分账处理] 平台账户不修改资金变动:{}", JSON.toJSONString(userAccountRecordMember));
                            continue;
                        }
                        updateAccountBalance("减", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
                        if (payChannelEnum.equals(DoNetPayChannelEnum.Balance)) {
                            balance = balance.add(balanceChange);
                        } else {
                            purchaseBalance = purchaseBalance.add(balanceChange);
                        }
                    } else {
                        log.info("[分账处理] {} {} 渠道金额不进行 减", typeId, payChannelEnum);
                    }
                    break;
                case FREEZE_ADDITION:
                    balanceChange = balanceChange.negate(); //变-号
                    userAccountRecordMember.setBalanceChange(balanceChange);
                    lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                    if (isBalanceOrPurchase) {
                        updateAccountBalance("余额冻结+", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
                        balance = balance.add(balanceChange); //实际是减
                    } else {
                        updateAccountBalance("余额冻结+", userAccountRecordMember, accountInfoMember, BigDecimal.ZERO, frozenBalanceChange, lastAccountRecordId);
                    }
                    frozenBalance = frozenBalance.add(frozenBalanceChange);
                    break;
                case FREEZE_REFUND:
                    frozenBalanceChange = frozenBalanceChange.negate();
                    userAccountRecordMember.setFrozenBalanceChange(frozenBalanceChange);
                    lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                    if (isBalanceOrPurchase) {
                        updateAccountBalance("冻结余额退", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
                        balance = balance.add(balanceChange);
                    } else {
                        updateAccountBalance("冻结余额退", userAccountRecordMember, accountInfoMember, BigDecimal.ZERO, frozenBalanceChange, lastAccountRecordId);
                    }
                    frozenBalance = frozenBalance.add(frozenBalanceChange);
                    break;
                case CHANNEL_FREEZE:
                    balanceChange = balanceChange.negate();
                    userAccountRecordMember.setBalanceChange(balanceChange);
                    userAccountRecordSave(userAccountRecordMember);
                    log.info("[分账处理] 保存分账数据 类型:{} 渠道:{} 渠道冻结 {}", typeId, payChannelEnum, balanceChange);
                    break;
                case INVARIANT:
                    userAccountRecordSave(userAccountRecordMember);
                    log.info("[分账处理] 保存分账数据 类型:{} 渠道:{} 金额不变 {}", typeId, payChannelEnum, balanceChange);
                    break;
                case CHANNEL_UNFREEZE:
                    userAccountRecordSave(userAccountRecordMember);
                    log.info("[分账处理] 保存分账数据 类型:{} 渠道:{} 渠道解冻 {}", typeId, payChannelEnum, balanceChange);
                    break;
            }
            //更新主账号
            if (balance.compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0 || frozenBalance.compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
                updateAccountInfoMemberBalance(accountAggregate.getAccountByType(AccountTypeEnum.BALANCE), balance, frozenBalance);
            }
            if (purchaseBalance.compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0 || frozenPurchaseBalance.compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
                updateAccountInfoMemberBalance(accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE), purchaseBalance, frozenPurchaseBalance);
            }
        }
        //保存关系数据
        saveUserAccountRecordOrderRelate(UserAccountRecordOrderRelateConvertor.MAPPER.toUserAccountRecordOrderRelateList(userAccountRecordMemberList));
    }

    private void platformAccountUpdate(UserAccountRecordMember userAccountRecordMember) {
        userAccountRecordMember.setBalanceChange(BigDecimal.ZERO);
        userAccountRecordMember.setBalanceAfter(BigDecimal.ZERO);
        userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_0);
    }

    private void updateAccountInfoMemberBalance(AccountInfoMember accountInfoMember, BigDecimal balanceChange, BigDecimal frozenBalanceChange) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(accountInfoMember.getAccountType());

        if (balanceChange.compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0 && frozenBalanceChange.compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0) {
            log.error("分账处理异常，请检查数据，accountInfoMember:{},balanceChange:{},frozenBalanceChange:{}", accountInfoMember, balanceChange, frozenBalanceChange);
        }
        String ext = getExt(accountInfoMember.getAccountInfoMemberExtension(), accountTypeEnum);
        UpdateAccountBalanceDO updateAccountBalanceDO = new UpdateAccountBalanceDO();
        updateAccountBalanceDO.setAccountId(accountInfoMember.getId());
        updateAccountBalanceDO.setUserId(accountInfoMember.getUserId());
        updateAccountBalanceDO.setLastAccountRecordId(null);
        updateAccountBalanceDO.setUserAccountNo(accountInfoMember.getUserAccountNo());
        updateAccountBalanceDO.setAccountType(accountTypeEnum.getCode());
        updateAccountBalanceDO.setOriginalBalance(accountInfoMember.getBalance());
        updateAccountBalanceDO.setBalanceChange(balanceChange);
        updateAccountBalanceDO.setOriginalFrozenBalance(accountInfoMember.getFrozenBalance());
        updateAccountBalanceDO.setFrozenBalanceChange(frozenBalanceChange);
        updateAccountBalanceDO.setExt(ext);
        int result = userAccountExtMapper.updateAccountBalance(updateAccountBalanceDO);
        if (result != Constant.CONSTANT_INTEGER_1) {
            log.warn("[分账处理] 更新主账号 {} 余额失败,用户id:{}", accountTypeEnum.getName(), accountInfoMember.getUserId());
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
        BigDecimal balanceAfter = accountInfoMember.getBalance().add(balanceChange);
        BigDecimal frozenBalanceAfter = accountInfoMember.getFrozenBalance().add(frozenBalanceChange);

        log.info("[分账处理] 更新主账号 {} 用户id:{},变动之前:{} 变动金额:{},变动之后:{} 冻结之前:{} 冻结变动:{},冻结之后:{}", accountTypeEnum.getName(), accountInfoMember.getUserId(), accountInfoMember.getBalance(), balanceChange, balanceAfter, accountInfoMember.getFrozenBalance(), frozenBalanceChange, frozenBalanceAfter);
        accountInfoMember.setBalance(accountInfoMember.getBalance().add(balanceChange));
        accountInfoMember.setFrozenBalance(accountInfoMember.getFrozenBalance().add(frozenBalanceChange));

    }


    /**
     * 根据用户ID 资金主键 账户类型
     *
     * @param userId             必传
     * @param userAssetsRecordId 必传
     * @param accountType        非必传
     */
    @Override
    public List<UserAccountRecordMember> getUserAccountRecordByUserIdAndUserAssetsRecordId(Long userId, Long userAssetsRecordId, Integer accountType) {
        List<UserAccountRecord> userAccountRecordList = userAccountRecordExtMapper.getUserAccountRecordByUserIdAndUserAssetsRecordId(userId, userAssetsRecordId, accountType);
        if (userAccountRecordList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAccountRecordList, UserAccountRecordMember::new);
    }

    /**
     *
     */
    @Override
    public Boolean countUserAccountRecord(Long userId, Long userAssetsRecordId, BigDecimal balanceChange, Integer accountType) {
        Integer i = userAccountRecordExtMapper.countUserAccountRecordByUserIdAndUserAssetsRecordId(userId, userAssetsRecordId, null, accountType);
//        if (i == 0) {
//            List<UserAccountRecord> userAccountRecordList = userAccountRecordExtMapper.getUserAccountRecordByUserIdAndUserAssetsRecordId(userId, userAssetsRecordId, null);
//            //聚合数据
//            if (userAccountRecordList.isEmpty()) {
//                return false;
//            } else {
//                if (null != balanceChange) {
//                    BigDecimal balanceChangeDb = userAccountRecordList.stream().map(UserAccountRecord::getBalanceChange).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    if (balanceChange.abs().compareTo(balanceChangeDb.abs()) == 0) {
//                        return true;
//                    }
//                }
//            }
//        }
        return i > ClearConstants.CONSTANT_INTEGER_0;
    }


    /**
     * 获取用户关联资金信息 orderNo 或 payOrderNo 二选其一
     */
    @Override
    public List<UserAccountRecordMember> getUserAccountRecordByOrderNoOrPayOrderNo(String orderNo, String payOrderNo) {

        List<UserAccountRecord> userAccountRecordList = getUserAccountRecordByOrderNo(orderNo);
        if (userAccountRecordList.isEmpty()) {
            userAccountRecordList = getUserAccountRecordByPayOrderNo(payOrderNo);
            if (userAccountRecordList.isEmpty()) {
                return Collections.emptyList();
            }
        }
        return BeanUtilsWrapper.convertList(userAccountRecordList, UserAccountRecordMember::new);
    }

    List<UserAccountRecord> getUserAccountRecordByPayOrderNo(String payOrderNo) {
        if (null == payOrderNo || payOrderNo.isBlank()) {
            return Collections.emptyList();
        }
        List<UserAccountRecord> userAccountRecordByOrderNo = new ArrayList<>();
        //关系查询
        List<UserAccountRecordOrderRelate> userAccountRecordOrderRelateByOrderNo = userAccountRecordOrderRelateExtMapper.getUserAccountRecordOrderRelateByPayOrderNo(payOrderNo);
        if (null != userAccountRecordOrderRelateByOrderNo && !userAccountRecordOrderRelateByOrderNo.isEmpty()) {
            for (UserAccountRecordOrderRelate userAccountRecordOrderRelate : userAccountRecordOrderRelateByOrderNo) {
                userAccountRecordByOrderNo.addAll(userAccountRecordExtMapper.getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordOrderRelate.getUserId(), userAccountRecordOrderRelate.getUserAssetsRecordId(), null));
            }
        }
        return userAccountRecordByOrderNo;
    }


    List<UserAccountRecord> getUserAccountRecordByOrderNo(String orderNo) {
        if (null == orderNo || orderNo.isBlank()) {
            return Collections.emptyList();
        }
        List<UserAccountRecord> userAccountRecordByOrderNo = new ArrayList<>();
        //关系查询
        List<UserAccountRecordOrderRelate> userAccountRecordOrderRelateByOrderNo = userAccountRecordOrderRelateExtMapper.getUserAccountRecordOrderRelateByOrderNo(orderNo);
        if (null != userAccountRecordOrderRelateByOrderNo && !userAccountRecordOrderRelateByOrderNo.isEmpty()) {
            //获取UserId
            List<Long> userIdList = userAccountRecordOrderRelateByOrderNo.stream().map(UserAccountRecordOrderRelate::getUserId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
            userIdList.removeIf(Objects::isNull);
            for (Long userId : userIdList) {
                userAccountRecordByOrderNo.addAll(userAccountRecordExtMapper.getUserAccountRecordByOrderNo(userId, orderNo));
            }
        }
        return userAccountRecordByOrderNo;
    }

    @Transactional(rollbackFor = Exception.class)
    void saveUserAccountRecordOrderRelate(List<UserAccountRecordOrderRelate> userAccountRecordOrderRelateList) {
        //去重
        userAccountRecordOrderRelateList = userAccountRecordOrderRelateList.stream().distinct().collect(Collectors.toList());
//        log.info("[分账处理] 保存关系数据");
        for (UserAccountRecordOrderRelate row : userAccountRecordOrderRelateList) {
            UserAccountRecordOrderRelate userAccountRecordOrderRelate = userAccountRecordOrderRelateExtMapper.getByUserAssetsRecordId(row.getUserAssetsRecordId());
            if (null == userAccountRecordOrderRelate) {
                userAccountRecordOrderRelateMapper.insertSelective(row);
            } else {
                if ((StringUtils.isNotBlank(row.getOrderNo()) && StringUtils.isBlank(userAccountRecordOrderRelate.getOrderNo())) || (StringUtils.isNotBlank(row.getPayOrderNo()) && StringUtils.isBlank(userAccountRecordOrderRelate.getPayOrderNo()))) {
                    userAccountRecordOrderRelate.setOrderNo(row.getOrderNo());
                    userAccountRecordOrderRelate.setPayOrderNo(row.getPayOrderNo());
                    userAccountRecordOrderRelate.setCreateTime(null);
                    userAccountRecordOrderRelate.setUpdateTime(null);
                    userAccountRecordOrderRelateMapper.updateByPrimaryKeySelective(userAccountRecordOrderRelate);
                    log.info("[分账处理] 关系数据已存在 更新一次:{}", userAccountRecordOrderRelate.getId());
                } else {
                    log.warn("[分账处理] 关系数据已存在 不做处理");
                }
            }
        }
    }


    @Override
    public BigDecimal sumBalanceByIdAndAccountType(Long userId, Long startId, Long entId, Integer accountType) {
        return userAccountRecordExtMapper.sumBalanceByIdAndAccountType(userId, startId, entId, accountType);
    }

    @Override
    public UserAccountRecordMember getByUserIdAndId(Long userId, Long id) {
        UserAccountRecord userAccountRecord = userAccountRecordExtMapper.getByUserIdAndId(userId, id);
        return BeanUtilsWrapper.convert(userAccountRecord, UserAccountRecordMember::new);
    }



    @Override
    public Long minIdByUserId(Long userId, Integer accountType) {
        return userAccountRecordExtMapper.minIdByUserId(userId, accountType);
    }

    @Override
    public Long minUserAssetsRecordId(Long userId) {
        return userAccountRecordExtMapper.minUserAssetsRecordId(userId);
    }

    @Override
    public List<UserAccountRecordMember> selectUserAccountRecordById(Long userId, Long minUserAssetsRecordId, Long maxUserAssetsRecordId) {
        List<UserAccountRecord> userAccountRecordList = userAccountRecordExtMapper.selectUserAccountRecordById(userId, minUserAssetsRecordId, maxUserAssetsRecordId);
        if (userAccountRecordList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAccountRecordList, UserAccountRecordMember::new);
    }

    @Override
    public UserAccountRecordMember selectLastUserAccountRecordByUserIdAndAccountType(Long userId, Long userAssetsRecordId, Integer accountType) {
        Long userAssetsRecordId2 = userAccountRecordExtMapper.selectLastUserAccountRecordByUserIdAndAccountType(userId, userAssetsRecordId, accountType);
        if (null == userAssetsRecordId2) {
            return null;
        }
        UserAccountRecord userAccountRecord = userAccountRecordExtMapper.selectByUserAssetsRecordId(userId, userAssetsRecordId2, accountType);
        return BeanUtilsWrapper.convert(userAccountRecord, UserAccountRecordMember::new);
    }

    /**
     * 更新资金明细分账记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserAccountRecord(UserAccountRecordMember member) {
        UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(member, UserAccountRecord::new);
        userAccountRecordExtMapper.updateByPrimaryKeySelectiveUserId(userAccountRecord);
    }

    private void updateAccountBalance(String logStr, UserAccountRecordMember userAccountRecordMember, AccountInfoMember accountInfoMember, BigDecimal balanceChange, BigDecimal frozenBalanceChange, Long lastAccountRecordId) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType());
        String userAccountNo = userAccountRecordMember.getUserAccountNo();
        //变动金额
        Long accountId = accountInfoMember.getId();
        Long userId = userAccountRecordMember.getUserId();
        Long userAssetsRecordId = userAccountRecordMember.getUserAssetsRecordId();
        String orderNo = userAccountRecordMember.getOrderNo();
        String payOrderNo = userAccountRecordMember.getPayOrderNo();

        //变动之前的金额
        BigDecimal balanceBefore = userAccountRecordMember.getBalanceBefore();
        BigDecimal frozenBalanceBefore = userAccountRecordMember.getFrozenBalanceBefore();
        BigDecimal balanceAfter = userAccountRecordMember.getBalanceAfter();
        BigDecimal frozenBalanceAfter = userAccountRecordMember.getFrozenBalanceAfter();


        if (balanceChange.compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0 && balanceBefore.compareTo(balanceChange.abs()) <= Constant.CONSTANT_NEGATIVE_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_BALANCE_NOT_ENOUGH);
        }
        if (frozenBalanceChange.compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0 && frozenBalanceBefore.compareTo(frozenBalanceChange.abs()) <= Constant.CONSTANT_NEGATIVE_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_FROZEN_BALANCE_NOT_ENOUGH);
        }

        log.info("[分账处理] {} 更新分账号余额;   资金Id:{} 用户Id: {} 订单号:{},支付单号:{}, 账户类型: {} 变动之前: {} 变动金额:{},变动之后:{},冻结余额~变动之前: {} 冻结余额~变动金额:{},冻结余额~变动之后:{}", logStr, userAssetsRecordId, userId, orderNo, payOrderNo, accountTypeEnum.getName(), balanceBefore, balanceChange, balanceAfter, frozenBalanceBefore, frozenBalanceChange, frozenBalanceAfter);
        String ext = getExt(accountInfoMember.getAccountInfoMemberExtension(), accountTypeEnum);
        UpdateAccountBalanceDO updateAccountBalanceDO = new UpdateAccountBalanceDO();
        updateAccountBalanceDO.setAccountId(accountId);
        updateAccountBalanceDO.setUserId(userId);
        updateAccountBalanceDO.setLastAccountRecordId(lastAccountRecordId);
        updateAccountBalanceDO.setUserAccountNo(userAccountNo);
        updateAccountBalanceDO.setAccountType(accountTypeEnum.getCode());
        updateAccountBalanceDO.setOriginalBalance(balanceBefore);
        updateAccountBalanceDO.setBalanceChange(balanceChange);
        updateAccountBalanceDO.setOriginalFrozenBalance(frozenBalanceBefore);
        updateAccountBalanceDO.setFrozenBalanceChange(frozenBalanceChange);
        updateAccountBalanceDO.setExt(ext);
        int result = userAccountExtMapper.updateAccountBalance(updateAccountBalanceDO);
        if (result != Constant.CONSTANT_INTEGER_1) {
            log.warn("[分账处理] {} 更新分账号余额失败;   资金Id:{} 用户Id: {} 订单号:{},支付单号:{}, 账户类型: {} 变动之前: {} 变动金额:{},变动之后:{},冻结余额~变动之前: {} 冻结余额~变动金额:{},冻结余额~变动之后:{}", logStr, userAssetsRecordId, userId, orderNo, payOrderNo, accountTypeEnum.getName(), balanceBefore, balanceChange, balanceBefore.add(balanceChange), frozenBalanceBefore, frozenBalanceChange, frozenBalanceBefore.add(frozenBalanceChange));
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
    }


    private static String getExt(AccountInfoMemberExtension accountInfoMemberExtension, AccountTypeEnum accountTypeEnum) {
        //判断用户余额2 是否发生变动
        if (accountTypeEnum.equals(AccountTypeEnum.BALANCE_2)) {
            if (!Boolean.TRUE.equals(accountInfoMemberExtension.getBalance2Changed())) {
                accountInfoMemberExtension.setBalance2Changed(Boolean.TRUE);
                return JSON.toJSONString(accountInfoMemberExtension);
            }
        }
        return null;
    }


    @Override
    public void insertSelective(UserAccountRecordMember userAccountRecordMember) {
        UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordMember, UserAccountRecord::new);
        userAccountRecordMapper.insertSelective(userAccountRecord);
        userAccountRecordMember.setId(userAccountRecord.getId());
    }

}
