package com.youpin.clear.infrastructure.process.sub.separate;

import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.aggregate.UserAccountRecordAggregate;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.process.SeparateAccountAssetsTypeProcessor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SeparateAccountAssetsType implements SeparateAccountAssetsTypeProcessor {
    @Override
    public List<Integer> support() {
        return List.of(UserAssetsTypeEnum.TYPE_3.getTypeId());
    }

    @Override
    public void process(UserAccountRecordAggregate userAccountRecordAggregate, List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOList) {
        Long userId = userAccountRecordAggregate.getUserId();
        FundingDirectionEnum directionEnum = userAccountRecordAggregate.getDirectionEnum();
        UserAssetsTypeEnum userAssetsTypeEnum = userAccountRecordAggregate.getUserAssetsTypeEnum();
        DoNetPayChannelEnum netPayChannelEnum = userAccountRecordAggregate.getNetPayChannelEnum();


    }
}
