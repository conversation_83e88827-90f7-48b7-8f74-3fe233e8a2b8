package com.youpin.clear.infrastructure.gateway.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.AccountInfoMemberExtension;
import com.youpin.clear.domain.aggregate.model.AccountTemplate;
import com.youpin.clear.domain.dto.UpdateAccountBalanceDTO;
import com.youpin.clear.domain.dto.UserAccountInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.infrastructure.converter.UserAccountConvert;
import com.youpin.clear.infrastructure.dataobject.UpdateAccountBalanceDO;
import com.youpin.clear.infrastructure.dataobject.UserAccount;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo;
import com.youpin.clear.infrastructure.mapper.UserAccountExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserAccountGatewayImpl implements UserAccountGateway {

    @Autowired
    UserAccountExtMapper userAccountExtMapper;

    @Autowired
    UserAccountMapper userAccountMapper;

    @Autowired
    IdWorkService idWorkService;

    @Autowired
    UU898UserAssetsInfoMapper userAssetsInfoMapper;

    @Autowired
    UU898UserAssetsInfoExtMapper userAssetsInfoExtMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByUserId(Long userId) {
        userAccountExtMapper.deleteByUserId(userId);
    }

    /**
     * 创建用户账户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<AccountInfoMember> createUserAccount(Long userId) {
        log.info("[创建用户账户信息] userID:{} 开始 ", userId);
        List<AccountInfoMember> accountInfoMemberList = AccountTemplate.accountTemplate(userId);
        for (AccountInfoMember accountInfoMember : accountInfoMemberList) {
            accountInfoMember.setUserAccountNo(idWorkService.getNextIdLeafKey());

            UserAccount userAccount = BeanUtilsWrapper.convert(accountInfoMember, UserAccount::new);
            userAccountMapper.insertSelective(userAccount);
            accountInfoMember.setId(userAccount.getId());
        }
        log.info("[创建用户账户信息] userID:{} 完成 ", userId);
        return accountInfoMemberList;
    }


    /**
     * 创建用户账户信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createUserAccount(List<AccountInfoMember> accountInfoMemberList) {
        for (AccountInfoMember accountInfoMember : accountInfoMemberList) {
            accountInfoMember.setUserAccountNo(idWorkService.getNextIdLeafKey());
            UserAccount userAccount = BeanUtilsWrapper.convert(accountInfoMember, UserAccount::new);
            userAccountMapper.insertSelective(userAccount);
            accountInfoMember.setId(userAccount.getId());
        }
    }


    /**
     * 获取用户账户信息
     */
    @Override
    public List<AccountInfoMember> getAccountInfoMember(Long userId) {
        //入参不能为null
        if (userId == null) {
            log.warn("[获取用户账户信息] getAccountInfoMember 入参userId不能为null");
            return Collections.emptyList();
        }
        List<UserAccount> userAccountList = userAccountExtMapper.getAccountInfoMember(userId);
        if (userAccountList == null || userAccountList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAccountList, AccountInfoMember::new);

    }

    /**
     * 获取用户账户信息
     */
    @Override
    public AccountAggregate getAccountAggregate(Long userId) {
        //入参不能为null
        if (userId == null) {
            log.warn("[获取用户账户信息] getAccountAggregate 入参userId不能为null");
            return null;
        }
        List<UserAccount> userAccountList = userAccountExtMapper.getAccountInfoMember(userId);
        if (userAccountList == null || userAccountList.isEmpty()) {
            return null;
        }
        AccountAggregate accountAggregate = new AccountAggregate();
        accountAggregate.setUserId(userId);
        List<AccountInfoMember> accountInfoMemberList = BeanUtilsWrapper.convertList(userAccountList, AccountInfoMember::new);
        accountAggregate.setAccountInfoMemberMap(accountInfoMemberList.stream().collect(Collectors.toMap(item -> AccountTypeEnum.getAccountTypeEnum(item.getAccountType()), item -> item)));
        return accountAggregate;
    }

    /**
     * 获取用户账户信息
     */
    @Override
    public AccountAggregate getAccountAggregatesMapByType(Long userId, List<Integer> accountTypeCodeList) {
        //入参不能为null
        if (userId == null) {
            log.warn("[获取用户账户信息] getAccountAggregate 入参userId不能为null");
            return null;
        }
        List<UserAccount> userAccountList = userAccountExtMapper.getAccountAggregatesMapByType(userId, accountTypeCodeList);
        if (userAccountList == null || userAccountList.isEmpty()) {
            return null;
        }
        AccountAggregate accountAggregate = new AccountAggregate();
        accountAggregate.setUserId(userId);
        List<AccountInfoMember> accountInfoMemberList = BeanUtilsWrapper.convertList(userAccountList, AccountInfoMember::new);
        accountAggregate.setAccountInfoMemberMap(accountInfoMemberList.stream().collect(Collectors.toMap(item -> AccountTypeEnum.getAccountTypeEnum(item.getAccountType()), item -> item)));
        return accountAggregate;
    }


    @Override
    public void updateMember(List<AccountInfoMember> accountInfoMemberList) {
        if (accountInfoMemberList == null || accountInfoMemberList.isEmpty()) {
            log.warn("[更新账户信息] accountInfoMemberList 不能为null");
            return;
        }
        for (AccountInfoMember accountInfoMember : accountInfoMemberList) {
            if (null == accountInfoMember.getId()) {
                log.error("[更新账户信息] accountInfoMember ID 不能为null");
                return;
            }
            UserAccount userAccount = BeanUtilsWrapper.convert(accountInfoMember, UserAccount::new);
            userAccountExtMapper.updateByIdAndUserIdSelective(userAccount);
        }
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    @Override
    public AccountAggregate syncUserAccount(Long userId) {
        //事务开锁.
        userAssetsInfoExtMapper.updateTimeById(userId);
        //获取用资金信息
        UU898UserAssetsInfo userAssetsInfo = userAssetsInfoMapper.selectByPrimaryKey(userId);
        if (null == userAssetsInfo) {
            log.error("[同步账户余额数据] syncUserAccount userID:{} 获取用户资金信息为空 逻辑跳出", userId);
            return null;
        }
        List<AccountInfoMember> accountInfoMemberList = AccountTemplate.accountTemplate(userId);
        //同步账户余额
        for (AccountInfoMember infoMember : accountInfoMemberList) {
            if (infoMember.getAccountType().equals(AccountTypeEnum.BALANCE.getCode())) {
                infoMember.setBalance(userAssetsInfo.getMoney());
                infoMember.setFrozenBalance(userAssetsInfo.getBlockMoney());
            }
            if (infoMember.getAccountType().equals(AccountTypeEnum.BALANCE_1.getCode())) {
                infoMember.setBalance(userAssetsInfo.getMoney());
                infoMember.setFrozenBalance(userAssetsInfo.getBlockMoney());
            }
            if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE.getCode())) {
                infoMember.setBalance(userAssetsInfo.getPurchaseMoney());
                infoMember.setFrozenBalance(userAssetsInfo.getPurchaseBlockMoney());
            }
            if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode())) {
                infoMember.setBalance(userAssetsInfo.getPurchaseMoney().subtract(userAssetsInfo.getPurchaseMoneyFromMoney()));
            }
            if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode())) {
                infoMember.setBalance(userAssetsInfo.getPurchaseMoneyFromMoney());
            }
        }
        //创建账户信息
        createUserAccount(accountInfoMemberList);
        AccountAggregate accountAggregate = new AccountAggregate();
        accountAggregate.setUserId(userId);
        accountAggregate.setAccountInfoMemberMap(accountInfoMemberList.stream().collect(Collectors.toMap(item -> AccountTypeEnum.getAccountTypeEnum(item.getAccountType()), item -> item)));
        return accountAggregate;
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    @Override
    public AccountAggregate syncUserAccountUpdate(Long userId) {
        //事务开锁.
        userAssetsInfoExtMapper.updateTimeById(userId);
        //获取用资金信息
        UU898UserAssetsInfo userAssetsInfo = userAssetsInfoMapper.selectByPrimaryKey(userId);

        List<AccountInfoMember> accountInfoMemberList = getAccountInfoMember(userId);
        if (null == userAssetsInfo || null == accountInfoMemberList || accountInfoMemberList.isEmpty()) {
            log.error("[同步账户余额数据] syncUserAccountUpdate userID:{} 获取用户资金信息为空 逻辑跳出", userId);
            return null;
        }
        //同步账户余额
        for (AccountInfoMember infoMember : accountInfoMemberList) {
            if (infoMember.getAccountType().equals(AccountTypeEnum.BALANCE.getCode())) {
                if (infoMember.getBalance().compareTo(userAssetsInfo.getMoney()) != ClearConstants.CONSTANT_INTEGER_0) {
                    infoMember.setBalance(userAssetsInfo.getMoney());
                    infoMember.setLastAccountRecordId(0L);
                }
                infoMember.setFrozenBalance(userAssetsInfo.getBlockMoney());
            } else if (infoMember.getAccountType().equals(AccountTypeEnum.BALANCE_1.getCode())) {
                if (infoMember.getBalance().compareTo(userAssetsInfo.getMoney()) != ClearConstants.CONSTANT_INTEGER_0) {
                    infoMember.setBalance(userAssetsInfo.getMoney());
                    infoMember.setLastAccountRecordId(0L);
                }
                infoMember.setFrozenBalance(userAssetsInfo.getBlockMoney());
            } else if (infoMember.getAccountType().equals(AccountTypeEnum.BALANCE_2.getCode())) {
                infoMember.setBalance(BigDecimal.ZERO);
                infoMember.setLastAccountRecordId(0L);
                infoMember.setFrozenBalance(BigDecimal.ZERO);
            } else if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE.getCode())) {
                if (infoMember.getBalance().compareTo(userAssetsInfo.getPurchaseMoney()) != ClearConstants.CONSTANT_INTEGER_0) {
                    infoMember.setBalance(userAssetsInfo.getPurchaseMoney());
                    infoMember.setLastAccountRecordId(0L);
                }
                infoMember.setFrozenBalance(userAssetsInfo.getPurchaseBlockMoney());
            } else if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode())) {
                if (infoMember.getBalance().compareTo(userAssetsInfo.getPurchaseMoney().subtract(userAssetsInfo.getPurchaseMoneyFromMoney())) != ClearConstants.CONSTANT_INTEGER_0) {
                    infoMember.setBalance(userAssetsInfo.getPurchaseMoney().subtract(userAssetsInfo.getPurchaseMoneyFromMoney()));
                    infoMember.setLastAccountRecordId(0L);
                }
                infoMember.setFrozenBalance(BigDecimal.ZERO);

            } else if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode())) {
                infoMember.setBalance(userAssetsInfo.getPurchaseMoneyFromMoney());
                if (infoMember.getBalance().compareTo(userAssetsInfo.getPurchaseMoneyFromMoney()) != ClearConstants.CONSTANT_INTEGER_0) {
                    infoMember.setBalance(userAssetsInfo.getPurchaseMoneyFromMoney());
                    infoMember.setLastAccountRecordId(0L);
                }
                infoMember.setFrozenBalance(BigDecimal.ZERO);
            } else if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode())) {
                infoMember.setBalance(BigDecimal.ZERO);
                infoMember.setLastAccountRecordId(0L);
                infoMember.setFrozenBalance(BigDecimal.ZERO);

            } else if (infoMember.getAccountType().equals(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode())) {
                infoMember.setBalance(BigDecimal.ZERO);
                infoMember.setLastAccountRecordId(0L);
                infoMember.setFrozenBalance(BigDecimal.ZERO);
            }
        }
        updateMember(accountInfoMemberList);
        log.info("数据同步完成 syncUserAccountUpdate");
        //创建账户信息
        AccountAggregate accountAggregate = new AccountAggregate();
        accountAggregate.setUserId(userId);
        accountAggregate.setAccountInfoMemberMap(accountInfoMemberList.stream().collect(Collectors.toMap(item -> AccountTypeEnum.getAccountTypeEnum(item.getAccountType()), item -> item)));
        return accountAggregate;
    }


    @Override
    public UserAccountInfoDTO queryUserAccountInfoDTO(Long userId) {
        List<UserAccount> userAccountList = userAccountExtMapper.getAccountInfoMember(userId);
        if (CollectionUtils.isEmpty(userAccountList)) {
            return null;
        }
        UserAccountInfoDTO userAccountInfoDTO = new UserAccountInfoDTO();
        userAccountInfoDTO.setUserId(userId);
        for (UserAccount userAccount : userAccountList) {
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(userAccount.getAccountType());
            BigDecimal balance = userAccount.getBalance();
            BigDecimal frozenBalance = userAccount.getFrozenBalance();

            switch (accountTypeEnum) {
                case BALANCE:
                    userAccountInfoDTO.setBalance(balance);
                    break;
                case BALANCE_1:
                    userAccountInfoDTO.setBalance1(balance);
                    userAccountInfoDTO.setBalanceBlock1(frozenBalance);
                    break;
                case BALANCE_2:
                    userAccountInfoDTO.setBalance2(balance);
                    userAccountInfoDTO.setBalanceBlock2(frozenBalance);
                    setBalance2Visibility(userAccount, userAccountInfoDTO);
                    break;
                case PURCHASE_BALANCE:
                    userAccountInfoDTO.setPurchaseBalance(balance);
                    break;
                case PURCHASE_BALANCE_RECHARGE_1:
                    userAccountInfoDTO.setPurchaseBalanceRecharge1(balance);
                    break;
                case PURCHASE_BALANCE_RECHARGE_2:
                    userAccountInfoDTO.setPurchaseBalanceRecharge2(balance);
                    break;
                case PURCHASE_BALANCE_TRANSFER_1:
                    userAccountInfoDTO.setPurchaseBalanceTransfer1(balance);
                    break;
                case PURCHASE_BALANCE_TRANSFER_2:
                    userAccountInfoDTO.setPurchaseBalanceTransfer2(balance);
                    break;
                case BALANCE_WITHDRAW_121:
                    userAccountInfoDTO.setBalance1Withdraw(balance);
                    break;
                case BALANCE_WITHDRAW_122:
                    userAccountInfoDTO.setBalance2Withdraw(balance);
                    break;
                default:
                    // 处理未知的账户类型
                    log.warn("Unknown account type: {}", userAccount.getAccountType());
                    break;
            }
        }
        return userAccountInfoDTO;
    }


    private void setBalance2Visibility(UserAccount userAccount, UserAccountInfoDTO userAccountInfoDTO) {
        String ext = userAccount.getExt();
        if (ext != null && !ext.isBlank()) {
            AccountInfoMemberExtension accountInfoMemberExtension = JSON.parseObject(ext, AccountInfoMemberExtension.class);
            userAccountInfoDTO.setShowBalance2(Boolean.FALSE);
            if (accountInfoMemberExtension == null) {
                if (userAccountInfoDTO.getBalance2() != null && userAccountInfoDTO.getBalance2().compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
                    userAccountInfoDTO.setShowBalance2(Boolean.TRUE);
                }
            } else {
                if (accountInfoMemberExtension.getBalance2Changed() != null) {
                    userAccountInfoDTO.setShowBalance2(accountInfoMemberExtension.getBalance2Changed());
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBalanceAccountInfoMember(Long accountInfoId, Long userId, AccountTypeEnum accountTypeEnum, String userAccountNo, BigDecimal balanceBefore, BigDecimal balanceChange, String ext, Long lastAccountRecordId) {
        if (balanceChange.compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0 && balanceBefore.compareTo(balanceChange.abs()) <= Constant.CONSTANT_NEGATIVE_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_BALANCE_NOT_ENOUGH);
        }
        int result = userAccountExtMapper.updateBalanceAccountInfoMember(accountInfoId, userId, accountTypeEnum.getCode(), userAccountNo, balanceBefore, balanceChange, ext, lastAccountRecordId);
        log.info("[分账处理] 更新分账号余额;  用户Id: {} 账户ID:{}, 账户类型: {} 变动之前: {} 变动金额:{} 结果:{}", userId, userAccountNo, accountTypeEnum.getName(), balanceBefore, balanceChange, result);
        if (result != Constant.CONSTANT_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFrozenBalanceAccountInfoMember(Long accountInfoId, Long userId, AccountTypeEnum accountTypeEnum, String userAccountNo, BigDecimal originalFrozenBalance, BigDecimal balanceFrozenChange, String ext, Long lastAccountRecordId) {
        if (balanceFrozenChange.compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0 && originalFrozenBalance.compareTo(balanceFrozenChange.abs()) <= Constant.CONSTANT_NEGATIVE_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_BALANCE_NOT_ENOUGH);
        }
        int result = userAccountExtMapper.updateFrozenBalanceAccountInfoMember(accountInfoId, userId, accountTypeEnum.getCode(), userAccountNo, originalFrozenBalance, balanceFrozenChange, lastAccountRecordId);
        log.info("[分账处理] 更新分账冻结余额;  用户Id: {} 账户ID:{}, 账户类型: {} 变动之前: {} 变动金额:{} 结果:{}", userId, userAccountNo, accountTypeEnum.getName(), originalFrozenBalance, balanceFrozenChange, result);
        if (result != Constant.CONSTANT_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAccountBalanceList(@Nonnull List<UpdateAccountBalanceDTO> dtoList) {
        for (UpdateAccountBalanceDTO dto : dtoList) {
            updateAccountBalance(dto);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAccountBalance(@Nonnull UpdateAccountBalanceDTO dto) {
        UpdateAccountBalanceDO convertDO = BeanUtilsWrapper.convert(dto, UpdateAccountBalanceDO::new);

        if (null == convertDO.getAccountType()) {
            log.error("[分账处理] 更新账户 参数异常  accountId:{}  userAccountNo:{} userId:{},账户类型为null", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId());
            throw new PaymentClearBusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(convertDO.getAccountType());
        if (null == convertDO.getUserId() || null == convertDO.getUserAccountNo() || null == convertDO.getAccountId()) {
            log.error("[分账处理] 更新账户 参数异常  accountId:{}  userAccountNo:{} userId:{},accountType:{}", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId(), accountTypeEnum.getName());
            throw new PaymentClearBusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }

        if (null == convertDO.getBalanceChange() && null == convertDO.getFrozenBalanceChange()) {
            log.error("[分账处理] 更新账户 参数异常 账户表主键:{} 账户编号:{} 用户id:{},账户类型:{} 冻结/金额变动为null", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId(), accountTypeEnum.getName());
            throw new PaymentClearBusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        if (null != convertDO.getBalanceChange() && null != convertDO.getFrozenBalanceChange()) {
            if (convertDO.getBalanceChange().compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0 && convertDO.getFrozenBalanceChange().compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0) {
                log.error("[分账处理] 更新账户 参数异常 账户表主键:{} 账户编号:{} 用户id:{},账户类型:{} 冻结/金额变动为0", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId(), accountTypeEnum.getName());
                throw new PaymentClearBusinessException(ErrorCode.REQUEST_PARAM_ERROR);
            }
        }
        if (null != convertDO.getBalanceChange() && (null == convertDO.getOriginalBalance() || convertDO.getOriginalBalance().compareTo(BigDecimal.ZERO) < ClearConstants.CONSTANT_INT_0)) {
            log.error("[分账处理] 更新账户 参数异常 账户表主键:{} 账户编号:{} 用户id:{},账户类型:{} balanceChange:{} originalBalance:{} 金额变动,原始金额变动为null或为负数", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId(), accountTypeEnum.getName(), convertDO.getBalanceChange(), convertDO.getOriginalBalance());
            throw new PaymentClearBusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }

        if (null != convertDO.getFrozenBalanceChange() && (null == convertDO.getOriginalFrozenBalance() || convertDO.getOriginalFrozenBalance().compareTo(BigDecimal.ZERO) < ClearConstants.CONSTANT_INT_0)) {
            log.error("[分账处理] 更新账户 参数异常 账户表主键:{} 账户编号:{} 用户id:{},账户类型:{} balanceChange:{} originalBalance:{} 冻结金额变动,原始冻结金额变动为null", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId(), accountTypeEnum.getName(), convertDO.getFrozenBalanceChange(), convertDO.getOriginalFrozenBalance());
            throw new PaymentClearBusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        int result = userAccountExtMapper.updateAccountBalance(convertDO);

        log.info("[分账处理] 更新账户  账户表主键:{} 账户编号:{} 用户id:{},账户类型:{},变动之前:{} 变动金额:{},变动之后:{} 冻结之前:{} 冻结变动:{},冻结之后:{}", convertDO.getAccountId(), convertDO.getUserAccountNo(), convertDO.getUserId(), accountTypeEnum.getName(), convertDO.getOriginalBalance(), convertDO.getBalanceChange(),
                convertDO.calculateBalanceChangeAfter(), convertDO.getOriginalFrozenBalance(),
                convertDO.getFrozenBalanceChange(), convertDO.calculateFrozenBalanceChangeAfter());
        if (result != ClearConstants.CONSTANT_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
    }

    /**
     * 更新账户
     *
     * @param originAccountMember 原始账户信息
     * @param targetAccountMember 目标账户信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAccount(AccountInfoMember originAccountMember, AccountInfoMember targetAccountMember) {
        UserAccount targetUserAccount = UserAccountConvert.MAPPER.toUserAccount(targetAccountMember);
        UserAccount originUserAccount = UserAccountConvert.MAPPER.toUserAccount(originAccountMember);
        int updateCount = userAccountExtMapper.updateAccount(originUserAccount, targetUserAccount);
        if (updateCount == 1) {
            log.info("[UserAccountGatewayImpl]:updateAccount成功,targetAccountMember:{}", targetAccountMember);
            return true;
        }
        log.warn("[UserAccountGatewayImpl]:updateAccount失败,targetAccountMember:{}", targetAccountMember);
        return false;
    }

    /**
     * 补全账户
     *
     * @param needAddAccountInfoMemberList 需要添加的账户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completionUserAccount(List<AccountInfoMember> needAddAccountInfoMemberList) {
        // 遍历
        needAddAccountInfoMemberList.forEach(accountInfoMember -> {
            // 转换
            UserAccount userAccount = UserAccountConvert.MAPPER.toUserAccount(accountInfoMember);
            // insert
            userAccountMapper.insertSelective(userAccount);
        });
    }


}
