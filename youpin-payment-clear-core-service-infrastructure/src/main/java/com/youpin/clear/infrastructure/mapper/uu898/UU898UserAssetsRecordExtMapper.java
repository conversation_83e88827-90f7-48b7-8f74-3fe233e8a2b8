package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface UU898UserAssetsRecordExtMapper {

    List<UU898UserAssetsRecord> queryUserAssetsRecordDTOList(@Param("userId") Long userId, @Param("serialNo") String serialNo, @Param("orderNo") String orderNo, @Param("payOrderNo") String payOrderNo,
                                                             @Param("typeId") Integer typeId, @Param("status") Integer status);

    Integer countUserAssetsRecordDTOList(@Param("userId") Long userId, @Param("serialNo") String serialNo, @Param("orderNo") String orderNo, @Param("payOrderNo") String payOrderNo,
                                                             @Param("typeId") Integer typeId, @Param("status") Integer status);

    Long countByAddTime(@Param("lastId") Long lastId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    List<Long> selectByAddTimePage(@Param("lastId") Long lastId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,
                                   @Param("pageIndex") Long pageIndex, @Param("pageSize") Long pageSize);


    List<Long> selectByIdInterval(@Param("startId") Long startId, @Param("endId") Long endId,
                                  @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,
                                  @Param("pageIndex") Long pageIndex, @Param("pageSize") Long pageSize);

    Long selectMaxId();

    UU898UserAssetsRecord selectByTradeNo(@Param("tradeNo") String tradeNo);


    /**
     * 获取待处理数据最小ID
     */
    Long selectMinIdByUserIdAndTypeIdTime(@Param("userId") Long userId,
                                          @Param("typeIdList") List<Integer> typeIdList,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 获取待处理数据
     */
    List<Long> selectIdListByUserIdAndTypeIdTimePage(@Param("lastId") Long lastId,
                                                     @Param("userId") Long userId,
                                                     @Param("typeIdList") List<Integer> typeIdList,
                                                     @Param("endTime") LocalDateTime endTime,
                                                     @Param("pageSize") Integer pageSize);


    Integer deleteByTest(@Param("userId") Long userId, @Param("serialNo") String serialNo,
                         @Param("orderNo") String orderNo, @Param("payOrderNo") String payOrderNo,
                         @Param("typeId") Integer typeId);
}

