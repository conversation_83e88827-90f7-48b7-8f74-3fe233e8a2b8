package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface UU898UserAssetsRecordTypeExtMapper {

    List<UU898UserAssetsRecordType> selectByIds(@Param("ids") List<Integer> ids);

    List<UU898UserAssetsRecordType> selectByOperateTypeList(@Param("list") List<Integer> list);
}
