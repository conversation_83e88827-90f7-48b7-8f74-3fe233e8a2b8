package com.youpin.clear.infrastructure.feign.impl;

import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.uu898.youpin.commons.base.model.Result;
import com.uu898.youpin.commons.utils.JacksonUtils;
import com.youpin.clear.infrastructure.feign.MessageFeign;
import com.youpin.clear.infrastructure.feign.request.SendSmsRequest;
import com.youpin.clear.infrastructure.feign.response.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageFeignService {

    @Resource
    private MessageFeign messageFeign;

    public SendSmsResponse sendSms(SendSmsRequest request) {
        Result<SendSmsResponse> result;
        try {
            log.info("[短信验证码] 发送短信验证码,request={}", JacksonUtils.writeValueAsString(request));
            result = messageFeign.sendSms(request);
            log.info("[短信验证码] 发送短信验证码,result={}", JacksonUtils.writeValueAsString(result));
        } catch (Exception e) {
            log.error("[短信验证码] 发送短信验证码失败,request={}", JacksonUtils.writeValueAsString(request), e);
            throw new BusinessException(Status.SYSTEM.getCode(), "请稍后再试，若有其他问题，请联系客服处理");
        }

        if (Objects.isNull(result) || !Status.OK.getCode().equals(result.getCode())) {
            log.error("[短信验证码] 发送短信验证码失败 异常,result={}", JacksonUtils.writeValueAsString(result));
            throw new BusinessException(Status.SYSTEM.getCode(), result != null ? result.getMsg() : "请稍后再试，若有其他问题，请联系客服处理");
        }
        return result.getData();
    }
}
