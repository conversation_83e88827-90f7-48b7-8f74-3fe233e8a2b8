package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.client.request.AdjustApplyListQueryRequest;
import com.youpin.clear.domain.dto.AdjustmentApplyDTO;
import com.youpin.clear.domain.gateway.AdjustmentApplyGateway;
import com.youpin.clear.infrastructure.dataobject.AdjustmentApply;
import com.youpin.clear.infrastructure.mapper.AdjustmentApplyExtMapper;
import com.youpin.clear.infrastructure.mapper.AdjustmentApplyMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AdjustmentApplyGatewayImpl implements AdjustmentApplyGateway {

    @Resource
    private AdjustmentApplyMapper adjustmentApplyMapper;

    @Resource
    private AdjustmentApplyExtMapper adjustmentApplyExtMapper;

    @Override
    public Long saveAdjustmentApply(AdjustmentApplyDTO adjustmentApplyDTO) {
        AdjustmentApply adjustmentApply = BeanUtilsWrapper.convert(adjustmentApplyDTO, AdjustmentApply::new);
        adjustmentApplyMapper.insert(adjustmentApply);
        return adjustmentApply.getId();
    }

    @Override
    public AdjustmentApplyDTO queryById(Long id) {
        AdjustmentApply adjustmentApply = adjustmentApplyMapper.selectByPrimaryKey(id);
        return BeanUtilsWrapper.convert(adjustmentApply, AdjustmentApplyDTO::new);
    }

    @Override
    public Long queryCount(AdjustApplyListQueryRequest request) {
        return adjustmentApplyExtMapper.count(request);
    }

    @Override
    public List<AdjustmentApplyDTO> queryList(AdjustApplyListQueryRequest request) {
        request.setOffset((request.getPage() - 1) * request.getPageSize());
        List<AdjustmentApply> adjustmentApplies = adjustmentApplyExtMapper.queryList(request);
        if (CollectionUtils.isEmpty(adjustmentApplies)) {
            return List.of();
        }
        return BeanUtilsWrapper.convertList(adjustmentApplies, AdjustmentApplyDTO::new);
    }

    @Override
    public List<AdjustmentApplyDTO> queryByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        List<AdjustmentApply> adjustmentApplies = adjustmentApplyExtMapper.queryByIds(ids);
        return BeanUtilsWrapper.convertList(adjustmentApplies, AdjustmentApplyDTO::new);
    }

    @Override
    public int updateAdjustmentApply(AdjustmentApplyDTO adjustmentApplyDTO) {
        adjustmentApplyDTO.setUpdateTime(LocalDateTime.now());
        AdjustmentApply toUpdate = BeanUtilsWrapper.convert(adjustmentApplyDTO, AdjustmentApply::new);
        return adjustmentApplyMapper.updateByPrimaryKeySelective(toUpdate);
    }

    @Override
    public List<AdjustmentApplyDTO> queryByStatus(List<Integer> statusList, Long size) {
        List<AdjustmentApply> adjustmentApplies = adjustmentApplyExtMapper.queryByApplyStatus(statusList, size);
        return BeanUtilsWrapper.convertList(adjustmentApplies, AdjustmentApplyDTO::new);
    }

    @Override
    public boolean existRepeatAdjustmentApply(String orderNo) {
        AdjustApplyListQueryRequest request = new AdjustApplyListQueryRequest();
        request.setRelatedOrderNo(orderNo);
        Long count = adjustmentApplyExtMapper.count(request);
        return count >= 1;
    }

    @Override
    public int updateApplyStatus(List<Long> ids, Integer expectStatus, Integer currentStatus) {
        return adjustmentApplyExtMapper.updateApplyStatus(ids, expectStatus, currentStatus);
    }
}
