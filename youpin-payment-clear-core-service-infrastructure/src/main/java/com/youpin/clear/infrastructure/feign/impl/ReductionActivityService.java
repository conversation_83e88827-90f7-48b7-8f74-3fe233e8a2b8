package com.youpin.clear.infrastructure.feign.impl;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.infrastructure.feign.ReductionActivityFeign;
import com.youpin.clear.infrastructure.feign.request.AccountBalanceNoticeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Service
@Slf4j
public class ReductionActivityService {

    @Resource
    ReductionActivityFeign reductionActivityFeign;

    /**
     * 支付账户余额通知
     */
    public void accountBalanceNotice(String account, BigDecimal balance) {
        AccountBalanceNoticeRequest request = AccountBalanceNoticeRequest.builder().account(account).balance(balance).build();
        log.info("支付账户余额通知:{}", request);
        Result<Void> voidResult = reductionActivityFeign.accountBalanceNotice(request);
        log.info("支付账户余额通知完成:{}", voidResult);
    }


}
