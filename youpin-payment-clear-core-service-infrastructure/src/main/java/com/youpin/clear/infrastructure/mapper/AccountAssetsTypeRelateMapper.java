package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AccountAssetsTypeRelateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AccountAssetsTypeRelate row);

    int insertSelective(AccountAssetsTypeRelate row);

    AccountAssetsTypeRelate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AccountAssetsTypeRelate row);

    int updateByPrimaryKey(AccountAssetsTypeRelate row);
}