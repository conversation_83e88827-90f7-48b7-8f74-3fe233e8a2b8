package com.youpin.clear.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 平台户ID 映射
 */
@Component
@ConfigurationProperties(prefix = "assets-type.platform-user-id")
@Data
@RefreshScope
public class AssetsTypePlatformUserIdProperties {
    private Map<Long, Long> configMap;



}
