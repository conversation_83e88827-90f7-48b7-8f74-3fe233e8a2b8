package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UU898UserAssetsInfoExtMapper {
    /**
     * 修改时间
     */
    int updateTimeById(@Param("userId") Long userId);

    List<UU898UserAssetsInfo> getUserAssetsInfoByUserIdMinMax(@Param("userIdMin") Long userIdMin, @Param("userIdMax") Long userIdMax, @Param("easy") Integer easy);

    int updateUserBalance(@Param("origin") UU898UserAssetsInfo origin, @Param("toUpdate") UU898UserAssetsInfo toUpdate);

    int updateUserPurchaseBalance(@Param("origin") UU898UserAssetsInfo origin, @Param("toUpdate") UU898UserAssetsInfo toUpdate);

    List<UU898UserAssetsInfo> selectByUserIdList(@Param("list") List<Long> userIdList);

    UU898UserAssetsInfo selectByUserId(@Param("userId")Long userId);


    int updateUserBalanceAndBlockMoney(@Param("origin") UU898UserAssetsInfo origin, @Param("toUpdate") UU898UserAssetsInfo toUpdate);
}