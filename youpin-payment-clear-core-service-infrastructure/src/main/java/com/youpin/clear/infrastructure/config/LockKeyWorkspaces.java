package com.youpin.clear.infrastructure.config;

import com.youpin.clear.common.constant.RedisKey;
import org.springframework.stereotype.Component;

@Component
public class LockKeyWorkspaces {


    static final String ACCOUNT_KEY = RedisKey.BASE_KEY + "account:%s";


    static final String ASSETS_RECORD_CONSUMER_KEY = RedisKey.BASE_KEY + "account:volatile:%s";


    static final String ASSET_USER_KEY = RedisKey.BASE_KEY + "asset:user:%s";


    public String syncUserAccountKey(Long userId) {
        return String.format(ACCOUNT_KEY, userId);
    }


    public String assetsRecordConsumerKey(String OrderNo) {
        return String.format(ASSETS_RECORD_CONSUMER_KEY, OrderNo);
    }


    public String assetUserKey(Long userId) {
        return String.format(ASSET_USER_KEY, userId);
    }

}