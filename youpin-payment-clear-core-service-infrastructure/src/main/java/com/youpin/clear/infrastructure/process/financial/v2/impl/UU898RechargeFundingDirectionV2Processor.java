package com.youpin.clear.infrastructure.process.financial.v2.impl;

import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.UU898FundingDirectionProcessor;
import com.youpin.clear.domain.process.UU898FundingDirectionV2Processor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 充值 0
 */
@Slf4j
@Service
public class UU898RechargeFundingDirectionV2Processor extends UU898DefaultFundingDirectionV2 implements UU898FundingDirectionV2Processor {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.RECHARGE);
    }


    // 判断余额变动
    private boolean isBalanceRecharge(AbstractUserAssetsRecordDTO dto) {
        return UserAssetsTypeEnum.TYPE_1.getTypeId().equals(dto.getTypeId()) || UserAssetsTypeEnum.TYPE_82.getTypeId().equals(dto.getTypeId());
    }

    // 判断求购余额变动
    private boolean isPurchaseBalanceRecharge(AbstractUserAssetsRecordDTO dto) {
        return UserAssetsTypeEnum.TYPE_43.getTypeId().equals(dto.getTypeId());
    }


    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {

//判断是否余额充值
//    TYPE_1("充值", 1, FundingDirectionEnum.RECHARGE),
//    TYPE_43("求购账户充值", 43, FundingDirectionEnum.RECHARGE),
//    TYPE_82("平台账户充值", 82, FundingDirectionEnum.RECHARGE),
//    充值 分  进行中  成功  失败 三个状态
        //数据状态
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());
        if (isBalanceRecharge(dto)) {
            handleBalanceChange(dto, userAssetsInfoDTO, netStatusEnum);
        } else if (isPurchaseBalanceRecharge(dto)) {
            handlePurchaseBalanceChange(dto, userAssetsInfoDTO, netStatusEnum);
        } else {
            log.error("[账务交易][充值+] 处理类型未知  dto:{} ", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR);
        }
    }


    // 处理余额变动
    private void handleBalanceChange(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO, NetStatusEnum netStatusEnum) {
        boolean isAccountUpdate = false;
        boolean assetsRecordUpdateOrInsert = false;
        BigDecimal changeMoney = dto.getChangeMoney();
        BigDecimal afterMoney = userAssetsInfoDTO.getMoney();
        dto.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
        dto.setAssetType(UserAssetsRecordAssetTypeEnum.Balance.getCode());
        switch (netStatusEnum) {
            case PROCESSING:
                assetsRecordUpdateOrInsert = true;
                break;
            case FAIL:
                //修改数据状态,不更新账户
                if (null == dto.getId()) {
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "状态位:错误,未找到原数据 进行中");
                }
                break;
            case SUCCESS:
                //更新账户 并修改数据状态
                isAccountUpdate = true;
                afterMoney = userAssetsInfoDTO.getMoney().add(changeMoney);
                //兜底逻辑:如果订单不存在,需要插入
                if (null == dto.getId()) {
                    assetsRecordUpdateOrInsert = true;
                }
                break;
        }

        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

        insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
        updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, userAssetsInfoDTO.getBlockMoney(), isAccountUpdate, dto.checkAccountBufferBookkeeping());
    }

    // 处理求购余额变动
    private void handlePurchaseBalanceChange(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO, NetStatusEnum netStatusEnum) {
        boolean isAccountUpdate = false;
        boolean assetsRecordUpdateOrInsert = false;
        BigDecimal changeMoney = dto.getChangeMoney();
        BigDecimal afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney();
        BigDecimal afterPurchaseMoneyFromMoney = userAssetsInfoDTO.getPurchaseMoneyFromMoney();
        dto.setAttr(UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode());
        dto.setAssetType(UserAssetsRecordAssetTypeEnum.PurchaseMoney.getCode());
        switch (netStatusEnum) {
            case PROCESSING:
                assetsRecordUpdateOrInsert = true;
                break;
            case FAIL:
                if (null == dto.getId()) {
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "状态位:错误,未找到原数据 进行中");
                }
                break;
            case SUCCESS:
                isAccountUpdate = true;
                afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney().add(changeMoney);
                //兜底逻辑:如果订单不存在,需要插入
                if (null == dto.getId()) {
                    assetsRecordUpdateOrInsert = true;
                }
                break;
        }

        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getMoney());
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), changeMoney, afterPurchaseMoney);
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

        insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
        updateUserPurchaseBalance(dto.getId(), userAssetsInfoDTO, afterPurchaseMoney, afterPurchaseMoneyFromMoney, isAccountUpdate, dto.checkAccountBufferBookkeeping());

    }

}


