package com.youpin.clear.infrastructure.feign.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.uu898.youpin.commons.base.model.Result;
import com.youpin.asset.account.client.request.CalculateRevokeRequest;
import com.youpin.asset.account.client.request.ConsumeByUserIdRequest;
import com.youpin.asset.account.client.request.RevokeRequest;
import com.youpin.asset.account.client.response.CalculateRevokeResponse;
import com.youpin.asset.account.client.response.ConsumeByUserIdResponse;
import com.youpin.asset.account.client.response.RevokeResponse;
import com.youpin.asset.account.client.service.AssetAccountService;
import com.youpin.clear.common.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class AssetAccountServiceFeign {


    @Autowired
    AssetAccountService assetAccountService;


//    /**
//     *
//     * 增加子账户余额, 账户不存在时createAccountIfNotExist为true则创建，否则报错
//     */
//    public IncreaseAmountResponse increaseAmount(IncreaseAmountRequest increaseRequest) {
//        return null;
//    }
//
//    /**
//     * 减少子账户余额，账户余额不足时抛异常
//     */
//    public DecreaseAmountResponse decreaseAmount(DecreaseAmountRequest increaseRequest) {
//        return null;
//    }

    /**
     * 消费账户余额
     */
    public void consumeByUserId(ConsumeByUserIdRequest request) {
        Result<ConsumeByUserIdResponse> result = null;
        try {
            log.info("[业务订单账户] 消费账户余额,request={}", JSON.toJSONString(request));
            result = assetAccountService.consumeByUserId(request);
            log.info("[业务订单账户] 消费账户余额,result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("[业务订单账户] 消费账户余额 异常1,request={}", JSON.toJSONString(request), e);
//            throw new BusinessException(ErrorCode.CALL_BUSINESS_ACCOUNT_SERVICE_ERROR.getCode(), "消费账户余额异常");
        }
        if (Objects.isNull(result) || !Status.OK.getCode().equals(result.getCode())) {
            log.error("[业务订单账户] 消费账户余额 异常2,result={}", JSON.toJSONString(result));
//            throw new BusinessException(ErrorCode.CALL_BUSINESS_ACCOUNT_SERVICE_ERROR.getCode(), result != null ? result.getMsg() : "消费账户余额异常");
        }
    }


    /**
     * 返还账户余额，outBizNo和消费时传入的outBizNo要一致
     */
    public void revoke(RevokeRequest request) {
        Result<RevokeResponse> result = null;
        try {
            log.info("[业务订单账户] 返还账户余额,request={}", JSON.toJSONString(request));
            result = assetAccountService.revoke(request);
            log.info("[业务订单账户] 返还账户余额,result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("[业务订单账户] 返还账户余额 异常1,request={}", JSON.toJSONString(request), e);
//            throw new BusinessException(ErrorCode.CALL_BUSINESS_ACCOUNT_SERVICE_ERROR.getCode(), "返还账户余额异常");
        }
        if (Objects.isNull(result) || !Status.OK.getCode().equals(result.getCode())) {
            log.error("[业务订单账户] 返还账户余额 异常2,result={}", JSON.toJSONString(result));
//            throw new BusinessException(ErrorCode.CALL_BUSINESS_ACCOUNT_SERVICE_ERROR.getCode(), result != null ? result.getMsg() : "返还账户余额异常");
        }
    }

    /**
     * 计算可以返回的子账户余额
     */
    public CalculateRevokeResponse calculateRevoke(CalculateRevokeRequest request) {
        Result<CalculateRevokeResponse> result;
        try {
            log.info("[业务订单账户] 计算可以返回的子账户余额,request={}", JSON.toJSONString(request));
            result = assetAccountService.calculateRevoke(request);
            log.info("[业务订单账户] 计算可以返回的子账户余额,result={}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("[业务订单账户] 计算可以返回的子账户余额 异常1,request={}", JSON.toJSONString(request), e);
            throw new BusinessException(ErrorCode.CALL_BUSINESS_ACCOUNT_SERVICE_ERROR.getCode(), "返还账户余额异常");
        }
        if (Objects.isNull(result) || !Status.OK.getCode().equals(result.getCode())) {
            log.error("[业务订单账户] 计算可以返回的子账户余额 异常2,result={}", JSON.toJSONString(result));
            throw new BusinessException(ErrorCode.CALL_BUSINESS_ACCOUNT_SERVICE_ERROR.getCode(), result != null ? result.getMsg() : "返还账户余额异常");
        }
        return result.getData();
    }


}
