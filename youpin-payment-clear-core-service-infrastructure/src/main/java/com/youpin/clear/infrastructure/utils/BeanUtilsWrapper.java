package com.youpin.clear.infrastructure.utils;

import org.springframework.beans.BeanUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 对象转换工具（扩展 Spring BeanUtils 工具）
 *
 * @see BeanUtils#copyProperties(Object, Object)
 */
public final class BeanUtilsWrapper extends BeanUtils {

    /**
     * 单对象转换(无回调)
     *
     * @param sources        源对象
     * @param targetSupplier 目标 Supplier<T>函数
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象
     */
    public static <S, T> T convert(S sources, Supplier<T> targetSupplier) {
        return convert(sources, targetSupplier, null);
    }

    /**
     * 单对象转换(有回调)
     *
     * @param sources        源对象
     * @param targetSupplier 目标 Supplier<T>函数
     * @param callBack       回调函数 {@link ConvertCallBack#callBack}
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象
     */
    public static <S, T> T convert(
            S sources, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack) {
        if (Objects.isNull(sources) || Objects.isNull(targetSupplier)) {
            return null;
        }
        T targetClass = targetSupplier.get();
        copyProperties(sources, targetClass);
        if (Objects.nonNull(callBack)) {
            callBack.callBack(sources, targetClass);
        }

        return targetClass;
    }

    /**
     * List对象转换（无回调）
     *
     * @param sources        源对象List
     * @param targetSupplier 目标 Supplier<T>函数
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象List
     */
    public static <S, T> List<T> convertList(List<S> sources, Supplier<T> targetSupplier) {
        return convertList(sources, targetSupplier, null);
    }

    /**
     * List对象转换（有回调）
     *
     * @param sources        源对象List
     * @param targetSupplier 目标 Supplier<T>函数
     * @param callBack       回调函数 {@link ConvertCallBack#callBack}
     * @param <S>            源对象lx
     * @param <T>            目标对象类型
     * @return 目标对象List 可以支持多级内嵌List结构
     */
    public static <S, T> List<T> convertList(
            List<S> sources, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack) {
        if (Objects.isNull(sources) || Objects.isNull(targetSupplier)) {
            return null;
        }

        List<T> targetList = new LinkedList<>();

        sources.forEach(
                source -> {
                    T t = targetSupplier.get();
                    copyProperties(source, t);

                    if (Objects.nonNull(callBack)) {
                        callBack.callBack(source, t);
                    }

                    targetList.add(t);
                });

        return targetList;
    }

    /**
     * 回调函数
     *
     * @param <S>
     * @param <T>
     */
    @FunctionalInterface
    public interface ConvertCallBack<S, T> {
        void callBack(S s, T t);
    }
}
