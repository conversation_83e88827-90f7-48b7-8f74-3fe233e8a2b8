package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: user_assets_tread_no_relate
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsTreadNoRelate implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细分表唯一编号
     */
    private String treadNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}