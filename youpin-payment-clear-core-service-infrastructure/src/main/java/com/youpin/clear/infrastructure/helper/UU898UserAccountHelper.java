package com.youpin.clear.infrastructure.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BaseException;
import com.youpin.asset.account.client.enums.AccountType;
import com.youpin.asset.account.client.request.CalculateRevokeRequest;
import com.youpin.asset.account.client.request.ConsumeByUserIdRequest;
import com.youpin.asset.account.client.request.RevokeRequest;
import com.youpin.asset.account.client.response.CalculateRevokeResponse;
import com.youpin.clear.client.enums.BalanceStrategyEnum;
import com.youpin.clear.client.request.BalanceStrategy;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.UU898UserSubAccountType;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.UU898UserSubAccountGateway;
import com.youpin.clear.infrastructure.config.UU898UserAccountInfraConfig;
import com.youpin.clear.infrastructure.feign.impl.AssetAccountServiceFeign;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UU898UserAccountHelper {

    @Autowired
    private UU898UserSubAccountGateway uu898UserSubAccountGateway;

    @Autowired
    private UU898UserAccountInfraConfig uu898UserAccountInfraConfig;

    @Autowired
    private AssetAccountServiceFeign assetAccountServiceFeign;


    /**
     * 调用子账户业务计算可退还
     */
    public BigDecimal callSubAccountBusinessCalculateRevoke(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO) {
        if (null == abstractUserAssetsRecordDTO || null == abstractUserAssetsRecordDTO.getSubAccountFlowRecordList()) {
            return BigDecimal.ZERO;
        }
        UU898UserSubAccountFlowRecordDTO uu898UserSubAccountFlowRecordDTO = abstractUserAssetsRecordDTO.getSubAccountFlowRecordList().get(0);
        //退款校验变量
        //需要先查询一下
        CalculateRevokeResponse calculateRevokeResponse = assetAccountServiceFeign.calculateRevoke(toCalculateRevokeRequest(uu898UserSubAccountFlowRecordDTO));
        //calculateRevokeResponse 判断 null 判断 里面参数 null
        if (null == calculateRevokeResponse || null == calculateRevokeResponse.getRevokeAmount()) {
            log.error("[业务订单账户] 返还账户余额 错误,toCalculateRevokeRequest={}", JSON.toJSONString(uu898UserSubAccountFlowRecordDTO));
            return BigDecimal.ZERO;
        }
        return calculateRevokeResponse.getRevokeAmount();
    }


    /**
     * 调用子账户业务退还
     */
    public void callSubAccountBusinessRevoke(CallSubAccountBusinessRevokeDTO callSubAccountBusinessRevokeDTO) {
        if (null == callSubAccountBusinessRevokeDTO || null == callSubAccountBusinessRevokeDTO.getUserId() || null == callSubAccountBusinessRevokeDTO.getOrderNo() || null == callSubAccountBusinessRevokeDTO.getSerialNo() || null == callSubAccountBusinessRevokeDTO.getAmount()) {
            return;
        }
        assetAccountServiceFeign.revoke(toRevokeRequest(callSubAccountBusinessRevokeDTO));
    }

    /**
     * 调用子账户业务消耗
     */
    public void callSubAccountBusinessConsume(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO) {
        if (null == abstractUserAssetsRecordDTO || null == abstractUserAssetsRecordDTO.getSubAccountFlowRecordList()) {
            return;
        }
        UU898UserSubAccountFlowRecordDTO uu898UserSubAccountFlowRecordDTO = abstractUserAssetsRecordDTO.getSubAccountFlowRecordList().get(0);
        //消耗校验变量
        Boolean consumeCheck = consumeCheck(abstractUserAssetsRecordDTO);
        if (Boolean.TRUE.equals(consumeCheck)) {
            assetAccountServiceFeign.consumeByUserId(toConsumeByUserIdRequest(uu898UserSubAccountFlowRecordDTO));
        }
    }


    private Boolean consumeCheck(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO) {
        if (abstractUserAssetsRecordDTO.getFundingDirectionEnum() == FundingDirectionEnum.SUBTRACTION) {
            return true;
        }
        if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_270.getTypeId())) {
            return true;
        }
        return null;
    }

    private RevokeRequest toRevokeRequest(CallSubAccountBusinessRevokeDTO dto) {
        RevokeRequest revokeRequest = new RevokeRequest();
        revokeRequest.setUserId(dto.getUserId());
        revokeRequest.setAccountType(AccountType.TRADE_ONLY_ACCOUNT.getCode());
        revokeRequest.setOutBizNo(dto.getOrderNo());
        revokeRequest.setIdempotentKey(dto.getSerialNo());
        revokeRequest.setAmount(dto.getAmount());
        return revokeRequest;
    }

    private CalculateRevokeRequest toCalculateRevokeRequest(UU898UserSubAccountFlowRecordDTO uu898UserSubAccountFlowRecordDTO) {
        CalculateRevokeRequest calculateRevokeRequest = new CalculateRevokeRequest();
        calculateRevokeRequest.setUserId(uu898UserSubAccountFlowRecordDTO.getUserId());
        calculateRevokeRequest.setAccountType(AccountType.TRADE_ONLY_ACCOUNT.getCode());
        calculateRevokeRequest.setOutBizNo(uu898UserSubAccountFlowRecordDTO.getOrderNo());
        return calculateRevokeRequest;
    }

    private ConsumeByUserIdRequest toConsumeByUserIdRequest(UU898UserSubAccountFlowRecordDTO uu898UserSubAccountFlowRecordDTO) {
        ConsumeByUserIdRequest consumeByUserIdRequest = new ConsumeByUserIdRequest();
        consumeByUserIdRequest.setUserId(uu898UserSubAccountFlowRecordDTO.getUserId());
        consumeByUserIdRequest.setAccountType(AccountType.TRADE_ONLY_ACCOUNT.getCode());
        consumeByUserIdRequest.setAmount(uu898UserSubAccountFlowRecordDTO.getBalanceChange().abs());
        consumeByUserIdRequest.setOutBizNo(uu898UserSubAccountFlowRecordDTO.getOrderNo());
        consumeByUserIdRequest.setIdempotentKey(uu898UserSubAccountFlowRecordDTO.getSerialNo());
        return consumeByUserIdRequest;
    }


    /**
     * 获取收入资金
     */
    public ChangeMoneyDTO getAdditionFundingChangeAmount(AbstractUserAssetsRecordDTO assetsRecordDTO) {
        // 获取策略
        BalanceStrategy balanceStrategy = assetsRecordDTO.getBalanceStrategy();
        // 策略为空或者默认
        if (balanceStrategy == null || BalanceStrategyEnum.NONE.getCode().equals(balanceStrategy.getCode())) {
            return ChangeMoneyDTO.builder().tradeChangeMoney(BigDecimal.ZERO).changeMoney(assetsRecordDTO.getChangeMoney()).build();
        }
        // 返回比例
        if (BalanceStrategyEnum.RATE.getCode().equals(balanceStrategy.getCode())) {
            return getRateStrategyChangeAmountDTO(balanceStrategy);
        }
        log.error("[getAdditionFundingChangeAmount]:当前策略不支持添加场景,assetsRecordDTO:{}", assetsRecordDTO);
        throw BaseException.of(Status.BAD_REQUEST);
    }

    /**
     * 获取冻结资金
     */
    public ChangeMoneyDTO getFreezeFundingChangeAmount(AbstractUserAssetsRecordDTO assetsRecordDTO) {
        // 获取策略
        BalanceStrategy balanceStrategy = assetsRecordDTO.getBalanceStrategy();
        // 策略为空或者默认
        if (balanceStrategy == null || BalanceStrategyEnum.NONE.getCode().equals(balanceStrategy.getCode())) {
            return ChangeMoneyDTO.builder().tradeChangeMoney(BigDecimal.ZERO).changeMoney(assetsRecordDTO.getChangeMoney()).build();
        }
        // 返回比例
        if (BalanceStrategyEnum.RATE.getCode().equals(balanceStrategy.getCode())) {
            return getRateStrategyChangeAmountDTO(balanceStrategy);
        }
        log.error("[getFreezeFundingChangeAmount]:当前策略不支持添加场景,assetsRecordDTO:{}", assetsRecordDTO);
        throw BaseException.of(Status.BAD_REQUEST);
    }

    public static void main(String[] args) {
        System.out.println(getSubtractionFundingChangeAmount(new BigDecimal("100"), new BigDecimal("30"), new BigDecimal("100")));
        System.out.println(getSubtractionFundingChangeAmount(new BigDecimal("100"), new BigDecimal("0"), new BigDecimal("100")));
        System.out.println(getSubtractionFundingChangeAmount(new BigDecimal("100"), new BigDecimal("30"), new BigDecimal("70")));
        System.out.println(getSubtractionFundingChangeAmount(new BigDecimal("100"), new BigDecimal("100"), new BigDecimal("100")));
        System.out.println(getSubtractionFundingChangeAmount(new BigDecimal("100"), new BigDecimal("80"), new BigDecimal("120")));

    }

    /**
     * 获取支出资金 --优先提现
     */
    private static ChangeMoneyDTO getSubtractionFundingChangeAmount(BigDecimal money, BigDecimal originTradeBalance, BigDecimal changeMoney) {

        BigDecimal afterTotalMoney = money.subtract(changeMoney);

        BigDecimal tradeChangeMoney = afterTotalMoney.compareTo(originTradeBalance) < 0 ? originTradeBalance : BigDecimal.ZERO;

        return ChangeMoneyDTO.builder().changeMoney(changeMoney).tradeChangeMoney(tradeChangeMoney).build();
    }


    /**
     * 获取支出资金
     */
    public ChangeMoneyDTO getSubtractionFundingChangeAmount(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        // 获取策略
        BalanceStrategy balanceStrategy = dto.getBalanceStrategy();
        // 为空设置为默认
        if (balanceStrategy == null) {
            balanceStrategy = new BalanceStrategy();
            balanceStrategy.setCode(BalanceStrategyEnum.NONE.getCode());
        }
        // 默认策略
        if (BalanceStrategyEnum.NONE.getCode().equals(balanceStrategy.getCode())) {
            //
            return getSubtractionFundingChangeAmount(userAssetsInfoDTO.getMoney(), getSubAccountBalance(userAssetsInfoDTO.getTradeSubAccountDTO()), dto.getChangeMoney());
        }
        // 比例
        if (BalanceStrategyEnum.RATE.getCode().equals(balanceStrategy.getCode())) {
            return getRateStrategyChangeAmountDTO(balanceStrategy);
        }
        // sort
        if (BalanceStrategyEnum.SORT.getCode().equals(balanceStrategy.getCode())) {
            // 获取仅可交易子账户
            UU898UserSubAccountDTO tradeSubAccountDTO = userAssetsInfoDTO.getTradeSubAccountDTO();
            // 获取仅可交易剩余金额 = 子账户余额
            BigDecimal tradeBalance = tradeSubAccountDTO != null ? tradeSubAccountDTO.getBalance() : BigDecimal.ZERO;
            // 获取仅可提现剩余金额 = 总金额 - 仅可交易金额
            BigDecimal withdrawBalance = userAssetsInfoDTO.getMoney().subtract(tradeBalance);
            // 余额map
            Map<Integer, BigDecimal> balanceMap = Map.of(UU898UserSubAccountType.TRADE.getType(), tradeBalance, UU898UserSubAccountType.WITHDRAW.getType(), withdrawBalance);
            // 获取策略
            BalanceStrategy.SortStrategyContent sortStrategyContent = JSONObject.parseObject(balanceStrategy.getContent(), BalanceStrategy.SortStrategyContent.class);
            // 获取金额
            return splitAmountBySort(sortStrategyContent.getTypeList(), dto, balanceMap);
        }
        log.error("[getSubtractionFundingChangeAmount]:当前策略不支持支出场景,dto:{}", dto);
        throw BaseException.of(Status.BAD_REQUEST);
    }

    /**
     * 获取解冻changeAmount
     */
    public ChangeMoneyDTO getUnFreezeFundingChangeAmount(AbstractUserAssetsRecordDTO dto) {
        BalanceStrategy balanceStrategy = dto.getBalanceStrategy();
        if (balanceStrategy == null || BalanceStrategyEnum.NONE.getCode().equals(balanceStrategy.getCode())) {
            return ChangeMoneyDTO.builder().tradeChangeMoney(BigDecimal.ZERO).changeMoney(dto.getChangeMoney()).build();
        }
        if (BalanceStrategyEnum.RATE.getCode().equals(balanceStrategy.getCode())) {
            return getRateStrategyChangeAmountDTO(balanceStrategy);
        }
        log.error("[getUnFreezeFundingChangeAmount]:当前策略不支持,dto:{}", dto);
        throw BaseException.of(Status.BAD_REQUEST);
    }

    /**
     * 处理比率策略
     */
    private ChangeMoneyDTO getRateStrategyChangeAmountDTO(BalanceStrategy balanceStrategy) {
        BalanceStrategy.RateStrategyContent rateStrategyContent = JSONObject.parseObject(balanceStrategy.getContent(), BalanceStrategy.RateStrategyContent.class);

        // 获取仅可交易金额
        Long tradeAmount = rateStrategyContent.getAmountMap().get(UU898UserSubAccountType.TRADE.getType());
        BigDecimal tradeBalanceAmount = tradeAmount == null ? BigDecimal.ZERO : AmountUtils.convertToDollarToDecimal(new BigDecimal(tradeAmount));

        // 获取仅可提现金额
        Long withdrawAmount = rateStrategyContent.getAmountMap().get(UU898UserSubAccountType.WITHDRAW.getType());
        BigDecimal withdrawBalanceAmount = withdrawAmount == null ? BigDecimal.ZERO : AmountUtils.convertToDollarToDecimal(new BigDecimal(withdrawAmount));

        return ChangeMoneyDTO.builder().tradeChangeMoney(tradeBalanceAmount).changeMoney(withdrawBalanceAmount.add(tradeBalanceAmount)).build();
    }

    /**
     * 获取退款金额dto
     */
    public ChangeMoneyDTO getRefundChangeMoneyDTO(AbstractUserAssetsRecordDTO assetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO) {
        if (null == assetsRecordDTO.getAssetsRelationDTO()) {
            log.warn("获取退款金额dto 关系不存在 :{}", assetsRecordDTO);
            return ChangeMoneyDTO.builder().changeMoney(BigDecimal.ZERO).tradeChangeMoney(BigDecimal.ZERO).build();
        }
        List<Integer> addTypeIds = new ArrayList<>(assetsRecordDTO.getAssetsRelationDTO().getAssociatedAdditionList());
        List<Integer> subtractTypeIds = new ArrayList<>(assetsRecordDTO.getAssetsRelationDTO().getDbAssociatedAdditionList());

        addTypeIds.stream().distinct().collect(Collectors.toList()).removeIf(Objects::isNull);
        subtractTypeIds.stream().distinct().collect(Collectors.toList()).removeIf(Objects::isNull);

        //调用子账户业务计算可退还
        BigDecimal bizOrderRefund = callSubAccountBusinessCalculateRevoke(assetsRecordDTO);
        if (bizOrderRefund.compareTo(BigDecimal.ZERO) > 0) {
            CallSubAccountBusinessRevokeDTO callSubAccountBusinessRevokeDTO = new CallSubAccountBusinessRevokeDTO();
            callSubAccountBusinessRevokeDTO.setUserId(assetsRecordDTO.getUserId());
            callSubAccountBusinessRevokeDTO.setOrderNo(assetsRecordDTO.getOrderNo());
            callSubAccountBusinessRevokeDTO.setAmount(bizOrderRefund);
            callSubAccountBusinessRevokeDTO.setSerialNo(assetsRecordDTO.getSerialNo());
            assetsRecordDTO.setCallSubAccountBusinessRevokeDTOList(List.of(callSubAccountBusinessRevokeDTO));
        }

        List<Integer> allTypeIds = new ArrayList<>();
        allTypeIds.addAll(addTypeIds);
        allTypeIds.addAll(subtractTypeIds);
        // 查询当前用户使用的仅可交易余额
        List<UU898UserSubAccountFlowRecordDTO> subAccountRecordList = uu898UserSubAccountGateway.listSubAccountRecord(userAssetsInfoDTO.getTradeSubAccountDTO(), assetsRecordDTO.getOrderNo(), assetsRecordDTO.getPayOrderNo(), allTypeIds);
        // 获取changeMoney
        BigDecimal changeMoney = assetsRecordDTO.getChangeMoney();
        // 仅可交易支出所有金额
        BigDecimal tradeTotalAddAmount = BigDecimal.ZERO;
        // 仅可交易退款所有金额
        BigDecimal tradeTotalRefundAmount = BigDecimal.ZERO;
        // 遍历
        for (UU898UserSubAccountFlowRecordDTO recordDTO : subAccountRecordList) {
            // 支出
            if (addTypeIds.contains(recordDTO.getJournalType())) {
                tradeTotalAddAmount = tradeTotalAddAmount.add(recordDTO.getBalanceChange().abs());
                // 退款
            } else if (subtractTypeIds.contains(recordDTO.getJournalType())) {
                tradeTotalRefundAmount = tradeTotalRefundAmount.add(recordDTO.getBalanceChange().abs());
            }
        }
        // 获取 仅可交易可用退款金额 = 仅可交易支出所有金额 - 仅可交易退款所有金额
        BigDecimal tradeRefundableAmount = tradeTotalAddAmount.subtract(tradeTotalRefundAmount);

        // 判断 仅可交易可用退款金额 >= changeMoney
        if (tradeRefundableAmount.compareTo(changeMoney) >= 0) {
            // tradeChangeMoney = changeMoney
            BigDecimal tradeChangeMoney = changeMoney.subtract(bizOrderRefund);
            tradeChangeMoney = tradeChangeMoney.compareTo(BigDecimal.ZERO) > 0 ? tradeChangeMoney : BigDecimal.ZERO;
            return ChangeMoneyDTO.builder().tradeChangeMoney(tradeChangeMoney).changeMoney(changeMoney).build();
        } else {
            BigDecimal tradeChangeMoney = tradeRefundableAmount.subtract(bizOrderRefund);
            tradeChangeMoney = tradeChangeMoney.compareTo(BigDecimal.ZERO) > 0 ? tradeChangeMoney : BigDecimal.ZERO;
            // 返回tradeChangeMoney = 仅可交易可用退款金额
            return ChangeMoneyDTO.builder().tradeChangeMoney(tradeChangeMoney).changeMoney(changeMoney).build();
        }
    }


    public ChangeMoneyDTO getPurRefundChangeMoneyDTO(AbstractUserAssetsRecordDTO assetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO) {
        // 获取总增加typeList
        List<Integer> addTypeList = assetsRecordDTO.getAssetsRelationDTO().getAssociatedAdditionList();
        // 获取总支出typeList
        List<Integer> subtractTypeList = new ArrayList<>(assetsRecordDTO.getAssetsRelationDTO().getDbAssociatedAdditionList());
        // 获取时间
        LocalDateTime endCreateTime = LocalDateTime.now();
        //
        LocalDateTime startCreateTime = endCreateTime.minusDays(7L);
        // 获取流水typeList
        List<Integer> journalTypeList = new ArrayList<>(addTypeList);
        journalTypeList.addAll(subtractTypeList);
        // 获取子账户所有的子流水
        List<UU898UserSubAccountFlowRecordDTO> subAccountRecordList = uu898UserSubAccountGateway.listSubAccountRecord(userAssetsInfoDTO.getTradeSubAccountDTO(), journalTypeList, startCreateTime, endCreateTime);
        // 判断是否为空
        if (CollectionUtils.isEmpty(subAccountRecordList)) {
            // 返回可交易=0
            return ChangeMoneyDTO.builder().tradeChangeMoney(BigDecimal.ZERO).changeMoney(assetsRecordDTO.getChangeMoney()).build();
        }
        // 获取总增加金额
        BigDecimal totalAddAmount = subAccountRecordList.stream().filter(item -> addTypeList.contains(item.getJournalType())).map(item -> item.getBalanceChange().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取总支出金额
        BigDecimal totalSubtractAmount = subAccountRecordList.stream().filter(item -> subtractTypeList.contains(item.getJournalType())).map(item -> item.getBalanceChange().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取 仅可交易可用退款金额 = 仅可交易支出所有金额 - 仅可交易退款所有金额
        BigDecimal tradeRefundableAmount = totalAddAmount.subtract(totalSubtractAmount);
        //
        BigDecimal changeMoney = assetsRecordDTO.getChangeMoney();
        // 判断 仅可交易可用退款金额 >= changeMoney
        if (tradeRefundableAmount.compareTo(changeMoney) >= 0) {
            // tradeChangeMoney = changeMoney
            BigDecimal tradeChangeMoney = changeMoney.subtract(tradeRefundableAmount);
            tradeChangeMoney = tradeChangeMoney.compareTo(BigDecimal.ZERO) > 0 ? tradeChangeMoney : BigDecimal.ZERO;
            return ChangeMoneyDTO.builder().tradeChangeMoney(tradeChangeMoney).changeMoney(changeMoney).build();
        } else {
            BigDecimal tradeChangeMoney = tradeRefundableAmount.subtract(tradeRefundableAmount);
            tradeChangeMoney = tradeChangeMoney.compareTo(BigDecimal.ZERO) > 0 ? tradeChangeMoney : BigDecimal.ZERO;
            // 返回tradeChangeMoney = 仅可交易可用退款金额
            return ChangeMoneyDTO.builder().tradeChangeMoney(tradeChangeMoney).changeMoney(changeMoney).build();
        }
    }

    /**
     * 根据优先级计算金额
     *
     * @param typeList             typeList
     * @param assetsRecordDTO      资金明细
     * @param subAccountBalanceMap 子账户类型金额map
     * @return 返回
     */
    private ChangeMoneyDTO splitAmountBySort(List<Integer> typeList, AbstractUserAssetsRecordDTO assetsRecordDTO, Map<Integer, BigDecimal> subAccountBalanceMap) {
        // 定义剩余的金额 = changeMoney
        BigDecimal remainingAmount = assetsRecordDTO.getChangeMoney().abs();
        // 定义可交易余额
        BigDecimal tradeBalanceAmount = BigDecimal.ZERO;
        // 定义可提现余额
        BigDecimal withdrawBalanceAmount = BigDecimal.ZERO;
        // 遍历typeList
        for (Integer subAccountType : typeList) {
            // 判断剩余的金额<=0
            if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            // 获取子账户金额
            BigDecimal subAccountBalance = subAccountBalanceMap.get(subAccountType);
            //
            if (subAccountBalance == null || subAccountBalance.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 获取扣款金额
            BigDecimal deductAmount = subAccountBalance.min(remainingAmount);
            // 判断是否是可交易账户
            if (UU898UserSubAccountType.TRADE.getType().equals(subAccountType)) {
                // 可交易余额
                tradeBalanceAmount = tradeBalanceAmount.add(deductAmount);
            } else if (UU898UserSubAccountType.WITHDRAW.getType().equals(subAccountType)) {
                // 可提现余额
                withdrawBalanceAmount = withdrawBalanceAmount.add(deductAmount);
            }
            // 剩余金额 = 剩余金额 - 扣款金额
            remainingAmount = remainingAmount.subtract(deductAmount);
        }
        // 校验剩余金额
        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            log.info("[getSortStrategyChangeAmountDTO]:账户余额不足无法完成全部扣减,subAccountBalanceMap:{}, remainingAmount:{}", subAccountBalanceMap, remainingAmount);
            throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
        }
        return ChangeMoneyDTO.builder().tradeChangeMoney(tradeBalanceAmount).changeMoney(tradeBalanceAmount.add(withdrawBalanceAmount)).build();
    }


    private BigDecimal getSubAccountBalance(UU898UserSubAccountDTO tradeSubAccountDTO) {
        return tradeSubAccountDTO == null ? BigDecimal.ZERO : tradeSubAccountDTO.getBalance();
    }

}
