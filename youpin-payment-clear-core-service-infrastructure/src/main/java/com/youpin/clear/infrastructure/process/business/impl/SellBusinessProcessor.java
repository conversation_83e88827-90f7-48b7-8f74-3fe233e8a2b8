package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo;
import com.youpin.clear.infrastructure.feign.impl.ReductionActivityService;
import com.youpin.clear.infrastructure.mapper.uu898.UU898NewOrderPayPurchaseExtendExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoExtMapper;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SellBusinessProcessor extends DefaultSubBusFinancialProcessor {

    @Autowired
    UU898UserAssetsInfoExtMapper uu898UserAssetsInfoExtMapper;

    @Autowired
    @Qualifier("asyncTaskExecutor")
    ThreadPoolTaskExecutor asyncTaskExecutor;

    @Autowired
    ReductionActivityService reductionActivityService;

    @Autowired
    UU898NewOrderPayPurchaseExtendExtMapper uu898NewOrderPayPurchaseExtendExtMapper;

    /**
     * 营销立减 支持业务 普通、批量、还价、赠送
     */
    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(
                //有品服饰
                SubBusTypeFrontEnum.YOUPIN_CLOTHING,
                //出售-出售大会员
                SubBusTypeFrontEnum.SELL_BIG_VIP_CENTER,
                //出售-自助解封
                SubBusTypeFrontEnum.SELL_AUTO_UNBLOCK,
                //多游戏SDK-购买
                SubBusTypeFrontEnum.MULTI_GAME_SDK_BUY,
                //CDK
                SubBusTypeFrontEnum.SELL_CDK,
                //省钱会员
                SubBusTypeFrontEnum.COST_SAVING_MEMBER,
                //出租大会员
                SubBusTypeFrontEnum.SELL_BIG_VIP,
                //极速发货
                SubBusTypeFrontEnum.FRONT_FAST_SHIP,

                //赠送
                SubBusTypeFrontEnum.FRONT_SELL_GIVE,
                //出售-普通商品
                SubBusTypeFrontEnum.FRONT_NORMAL_GOOD,
                //免押中心
                SubBusTypeFrontEnum.DEPOSIT_FREE_CENTER,
                //批量购买
                SubBusTypeFrontEnum.FRONT_NORMAL_GOOD_BATCH,
                //新还价
                SubBusTypeFrontEnum.COUNTER_OFFER,
                //求购-供应
                SubBusTypeFrontEnum.PURCHASE_SUPPLY,
                //安心涨
                SubBusTypeFrontEnum.AN_XIN_RISING,
                SubBusTypeFrontEnum.RECHARGE_WAIT_REFUND_GOODS

        );
    }


    @Override
    public void paySuccess(FinancialProcessorDTO financialProcessorDTO) {
        super.paySuccess(financialProcessorDTO);
        platformUserIdConversion(financialProcessorDTO);
        try {
            checkActivityAccountBalanceWarning(financialProcessorDTO);
        } catch (Exception e) {
            log(Level.ERROR, "营销立减活动 账户余额不足告警异常", financialProcessorDTO);
        }

    }

    private void checkActivityAccountBalanceWarning(FinancialProcessorDTO financialProcessorDTO) {
        CompletableFuture.runAsync(() -> {
            //增加账户金额限制校验方法
            List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
            abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_221.getTypeId())).collect(Collectors.toList()).stream().findFirst().ifPresent(item -> {
                BigDecimal balance = item.getChangeMoney().abs();
                BigDecimal activityAccountBalanceWarning = paymentClearParamsConfig.getActivityAccountBalanceWarning();
                //用户余额
                BigDecimal userBalance = BigDecimal.ZERO;
                UU898UserAssetsInfo uu898UserAssetsInfo = uu898UserAssetsInfoExtMapper.selectByUserId(item.getUserId());
                if (null != uu898UserAssetsInfo) {
                    userBalance = uu898UserAssetsInfo.getMoney();
                }
                if (userBalance.subtract(balance).compareTo(activityAccountBalanceWarning) <= 0) {
                    log(Level.WARN, "营销立减活动 账户余额不足告警 用户:{} 账户余额:{} 超过:{}", financialProcessorDTO, item.getUserId(), userBalance, activityAccountBalanceWarning);
                    reductionActivityService.accountBalanceNotice(String.valueOf(item.getUserId()), userBalance.subtract(balance));
                }
            });
        }, asyncTaskExecutor).exceptionally(e -> {
            log.error("[账务交易] 营销立减活动 账户余额不足告警 接口调用失败: e:{}", ExceptionUtils.getStackTrace(e));
            return null;
        });
    }

    @Override
    public void refundSuccess(FinancialProcessorDTO financialProcessorDTO) {
        super.refundSuccess(financialProcessorDTO);
        platformUserIdConversion(financialProcessorDTO);
        //退款成功单独250逻辑
        //判断250 退款 是 余额 还是 求购余额(转入/充值)
        refundSuccess_250(financialProcessorDTO);

    }

    private void refundSuccess_250(FinancialProcessorDTO financialProcessorDTO) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        //判断是否存在250
        abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_250.getTypeId())).findFirst().ifPresent(item -> {
            //判断250 退款 是 余额 还是 求购余额(转入/充值)
            boolean anyMatch = financialProcessorDTO.getUserAssetsRecordDTOLinkList().stream().anyMatch(item2 -> item2.getTypeId().equals(UserAssetsTypeEnum.TYPE_3.getTypeId()) && item2.getAssetType().equals(UserAssetsRecordAssetTypeEnum.PurchaseMoney.getCode()));
            log(Level.INFO, "判断250 是否求购余额变动 : {}", financialProcessorDTO, anyMatch ? "是" : "不是");
            //是否求购余额操作
            if (!anyMatch) {
                return;
            }
            item.setPayChannel(DoNetPayChannelEnum.PurchaseBalance.getCode());
            item.setAttr(UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode());
            item.setAssetType(UserAssetsRecordAssetTypeEnum.PurchaseMoney.getCode());
            //求购金额
            BigDecimal purchaseAmount = item.getChangeMoney();
            //转入金额
            BigDecimal purchaseTransferAmount = BigDecimal.ZERO;
            //锁定的剩余金额
            BigDecimal getLeftPurchaseMoneyFromMoney = BigDecimal.ZERO;
            List<UU898NewOrderPayPurchaseExtend> uu898NewOrderPayPurchaseExtends_list = uu898NewOrderPayPurchaseExtendExtMapper.selectList(item.getUserId(), item.getOrderNo(), item.getPayOrderNo());
            if (CollectionUtils.isNotEmpty(uu898NewOrderPayPurchaseExtends_list)) {
                //金额分组汇总
                for (UU898NewOrderPayPurchaseExtend item2 : uu898NewOrderPayPurchaseExtends_list) {
                    if (null != item2.getLeftPurchaseMoneyFromMoney()) {
                        purchaseTransferAmount = getLeftPurchaseMoneyFromMoney.add(item2.getLeftPurchaseMoneyFromMoney());
                    }
                }
                //有且只有一条
                uu898NewOrderPayPurchaseExtends_list.stream().findFirst().ifPresent(uu898NewOrderPayPurchaseExtend -> item.setPurchaseTransferAmountDataID(uu898NewOrderPayPurchaseExtend.getId()));
            }
            log(Level.INFO, "用户:{} 订单:{} 250退款逻辑  求购金额:{} 转入金额:{}", financialProcessorDTO, item.getUserId(), item.getOrderNo(), purchaseAmount, purchaseTransferAmount);
            item.setPurchaseTransferAmount(purchaseTransferAmount);
        });
    }

    @Override
    public void refundFail(FinancialProcessorDTO financialProcessorDTO) {
        super.refundFail(financialProcessorDTO);
        platformUserIdConversion(financialProcessorDTO);
    }

    @Override
    public void settlement(FinancialProcessorDTO financialProcessorDTO) {
        super.settlement(financialProcessorDTO);
        platformUserIdConversion(financialProcessorDTO);
        //营销立减特殊逻辑
        marketingSubsidy(financialProcessorDTO);

    }

    /**
     * 如果发现结算的时候 有221 需要计算出 结算 5 里面 补贴的金额 补贴 = 221-222
     */
    private static void marketingSubsidy(FinancialProcessorDTO financialProcessorDTO) {
        List<FinancialProcessorAssetInfoDTO> assetInfoDTOList = financialProcessorDTO.getAssetInfoDTOList();
        if (CollectionUtils.isEmpty(assetInfoDTOList)) {
            return;
        }
        FinancialProcessorAssetInfoDTO financialProcessorAssetInfoDTO_221 = assetInfoDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_221.getTypeId())).findFirst().orElse(null);
        if (null != financialProcessorAssetInfoDTO_221) {
            FinancialProcessorAssetInfoDTO financialProcessorAssetInfoDTO_222 = assetInfoDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_222.getTypeId())).findFirst().orElse(null);
            //单位是分 转换成元
            //支付立减补贴
            BigDecimal paySubsidy = AmountUtils.convertToDollarToDecimal(new BigDecimal(financialProcessorAssetInfoDTO_221.getMoney()));
            BigDecimal refundSubsidy = BigDecimal.ZERO;
            if (null != financialProcessorAssetInfoDTO_222) {
                refundSubsidy = AmountUtils.convertToDollarToDecimal(new BigDecimal(financialProcessorAssetInfoDTO_222.getMoney()));
            }

            if (paySubsidy.compareTo(refundSubsidy) > 0) {
                //支付立减补贴 - 退款补贴
                BigDecimal subtract = paySubsidy.subtract(refundSubsidy);
                //修改5 的参数 作为 账单分账使用
                List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
                abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_5.getTypeId())).findFirst().ifPresent(item -> {
                    AssetsRecordAccountBillExtInfo assetsRecordAccountBillExtInfo = item.getAssetsRecordAccountBillExtInfo();
                    if (null == assetsRecordAccountBillExtInfo) {
                        assetsRecordAccountBillExtInfo = new AssetsRecordAccountBillExtInfo();
                    }
                    assetsRecordAccountBillExtInfo.setMarketingSubsidy(subtract);
                    item.setAssetsRecordAccountBillExtInfo(assetsRecordAccountBillExtInfo);
                });
            }
        }
    }

    @Override
    public void specialSettlement(FinancialProcessorDTO dto) {
        super.specialSettlement(dto);

        //判断
        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = dto.getUserAssetsRecordDTOLinkList();
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();

        UU898UserAssetsRecordDTO uu898UserAssetsRecordDTOPay = null;

        if (!dto.isUserAssetsRecordDTOLinkListEmpty()) {
            uu898UserAssetsRecordDTOPay = userAssetsRecordDTOLinkList.stream().filter((item) -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_3.getTypeId())).findFirst().orElse(null);

            boolean isSettlementFlag = userAssetsRecordDTOLinkList.stream().anyMatch((item) -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_5.getTypeId()));
            boolean isRefundFlag = userAssetsRecordDTOLinkList.stream().anyMatch((item) -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_4.getTypeId()));
            boolean isSpecialSettlement_155 = abstractUserAssetsRecordDTOList.stream().anyMatch((item) -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_155.getTypeId()));


            for (UU898UserAssetsRecordDTO userAssetsRecordDTO : userAssetsRecordDTOLinkList) {
                if (isSpecialSettlement_155) {
                    checkExtractedDouble(dto, userAssetsRecordDTO, UserAssetsTypeEnum.TYPE_155);
                } else if (abstractUserAssetsRecordDTOList.stream().anyMatch(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_170.getTypeId()))) {
                    checkExtractedDouble(dto, userAssetsRecordDTO, UserAssetsTypeEnum.TYPE_170);

                    UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO_169 = userAssetsRecordDTOLinkList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_169.getTypeId())).findFirst().orElse(null);
                    if (null == uu898UserAssetsRecordDTO_169) {
                        log(Level.ERROR, " 没有:{} 不能处理:{}", dto, UserAssetsTypeEnum.TYPE_169.getName(), UserAssetsTypeEnum.TYPE_170.getName());
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), String.format(" 没有:%s 不能处理:%s", UserAssetsTypeEnum.TYPE_169.getName(), UserAssetsTypeEnum.TYPE_170.getName()));
                    }
                    AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_170 = abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_170.getTypeId())).findFirst().orElse(null);
                    assert abstractUserAssetsRecordDTO_170 != null;
                    if (uu898UserAssetsRecordDTO_169.getThisMoney().abs().compareTo(abstractUserAssetsRecordDTO_170.getChangeMoney().abs()) < 0) {
                        log(Level.ERROR, "{}金额不能大于{}金额", dto, UserAssetsTypeEnum.TYPE_170.getName(), UserAssetsTypeEnum.TYPE_169.getName());
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), String.format(" %s金额不能大于%s金额", UserAssetsTypeEnum.TYPE_170.getName(), UserAssetsTypeEnum.TYPE_169.getName()));
                    }
                } else if (abstractUserAssetsRecordDTOList.stream().anyMatch(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_171.getTypeId()))) {

                    checkExtractedDouble(dto, userAssetsRecordDTO, UserAssetsTypeEnum.TYPE_171);

                    UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO_169 = userAssetsRecordDTOLinkList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_169.getTypeId())).findFirst().orElse(null);
                    if (null == uu898UserAssetsRecordDTO_169) {
                        log(Level.ERROR, " 没有:{} 不能处理:{}", dto, UserAssetsTypeEnum.TYPE_169.getName(), UserAssetsTypeEnum.TYPE_171.getName());
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), String.format(" 没有:%s 不能处理:%s", UserAssetsTypeEnum.TYPE_169.getName(), UserAssetsTypeEnum.TYPE_171.getName()));
                    }
                    AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_171 = abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_171.getTypeId())).findFirst().orElse(null);
                    assert abstractUserAssetsRecordDTO_171 != null;
                    if (uu898UserAssetsRecordDTO_169.getThisMoney().abs().compareTo(abstractUserAssetsRecordDTO_171.getChangeMoney().abs()) < 0) {
                        log(Level.ERROR, "{}金额不能大于{}金额", dto, UserAssetsTypeEnum.TYPE_171.getName(), UserAssetsTypeEnum.TYPE_169.getName());
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), String.format(" %s金额不能大于%s金额", UserAssetsTypeEnum.TYPE_171.getName(), UserAssetsTypeEnum.TYPE_169.getName()));

                    }
                } else if (abstractUserAssetsRecordDTOList.stream().anyMatch(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_248.getTypeId()))) {
                    checkExtractedDouble(dto, userAssetsRecordDTO, UserAssetsTypeEnum.TYPE_248);
                }
            }
            if (isSpecialSettlement_155) {
                //已退款不容许补贴
                if (isRefundFlag) {
                    log(Level.ERROR, " 已经退款不能处理资金", dto);
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "已经退款不能处理资金");
                }

                //未支付 不容许补贴 ,未结算 不容许补贴
                if (null == uu898UserAssetsRecordDTOPay || !isSettlementFlag) {
                    log(Level.ERROR, " 没有结算不能处理资金", dto);
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "没有结算不能处理资金");
                }
            }
        }

        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_155.getTypeId())) {
                assert uu898UserAssetsRecordDTOPay != null;
                if (abstractUserAssetsRecordDTO.getChangeMoney().abs().compareTo(uu898UserAssetsRecordDTOPay.getThisMoney().abs()) >= 0) {
                    log(Level.ERROR, "补贴资金处理 补贴金额不能大于支付等于金额", dto);
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "补贴金额不能大于支付等于金额");
                }
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                //增加平台户出金
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_154, dto, getPlatformUserId(UserAssetsTypeEnum.TYPE_154), DoNetPayChannelEnum.Balance.getCode(), dto.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney())));
            }
        }
        //增加额外资金明细
        if (CollectionUtils.isNotEmpty(tempList)) {
            dto.addAbstractUserAssetsRecordDTO(tempList);
        }

    }

    /**
     * 检查资金明细是否重复处理
     */
    private void checkExtractedDouble(FinancialProcessorDTO dto, UU898UserAssetsRecordDTO userAssetsRecordDTO, UserAssetsTypeEnum userAssetsTypeEnum) {

        if (userAssetsRecordDTO.getTypeId().equals(userAssetsTypeEnum.getTypeId())) {
            log(Level.ERROR, "{}处理 数据重复", dto, userAssetsTypeEnum.getName());
            throw new PaymentClearBusinessException(AssetsErrorCode.ALREADY_PROCESSED.getCode(), "资金已处理:" + userAssetsTypeEnum.getName());
        }
    }

    private void platformUserIdConversion(FinancialProcessorDTO financialProcessorDTO) {
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_221.getTypeId())) {
                abstractUserAssetsRecordDTO.setUserId(getPlatformUserId(UserAssetsTypeEnum.TYPE_221));
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_222.getTypeId())) {
                abstractUserAssetsRecordDTO.setUserId(getPlatformUserId(UserAssetsTypeEnum.TYPE_222));
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_218.getTypeId())) {
                abstractUserAssetsRecordDTO.setUserId(getPlatformUserId(UserAssetsTypeEnum.TYPE_218));
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.None.getCode());
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_215.getTypeId())) {
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_251.getTypeId())) {
                abstractUserAssetsRecordDTO.setUserId(getPlatformUserId(UserAssetsTypeEnum.TYPE_251));
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_252.getTypeId())) {
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_253, financialProcessorDTO, getPlatformUserId(UserAssetsTypeEnum.TYPE_253), DoNetPayChannelEnum.Balance.getCode(), financialProcessorDTO.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney())));
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_109.getTypeId())) {
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_113, financialProcessorDTO, getPlatformUserId(UserAssetsTypeEnum.TYPE_113), DoNetPayChannelEnum.Balance.getCode(), financialProcessorDTO.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney())));
            }
        }
        //增加额外资金明细
        if (CollectionUtils.isNotEmpty(tempList)) {
            financialProcessorDTO.addAbstractUserAssetsRecordDTO(tempList);
        }
    }
}
