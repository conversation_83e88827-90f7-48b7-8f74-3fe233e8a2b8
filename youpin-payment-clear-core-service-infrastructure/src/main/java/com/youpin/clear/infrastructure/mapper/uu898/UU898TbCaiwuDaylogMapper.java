package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898TbCaiwuDaylogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898TbCaiwuDaylog row);

    int insertSelective(UU898TbCaiwuDaylog row);

    UU898TbCaiwuDaylog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898TbCaiwuDaylog row);

    int updateByPrimaryKey(UU898TbCaiwuDaylog row);
}