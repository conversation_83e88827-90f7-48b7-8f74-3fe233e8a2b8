package com.youpin.clear.infrastructure.gateway.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.domain.dto.CompensationRecordDTO;
import com.youpin.clear.domain.dto.CompensationRecordRetryJobDTO;
import com.youpin.clear.domain.gateway.CompensationRecordGateway;
import com.youpin.clear.infrastructure.dataobject.CompensationRecord;
import com.youpin.clear.infrastructure.mapper.CompensationRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.CompensationRecordMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Service
@Slf4j
public class CompensationRecordGatewayImpl implements CompensationRecordGateway {


    @Autowired
    CompensationRecordMapper compensationRecordMapper;

    @Autowired
    CompensationRecordExtMapper compensationRecordExtMapper;

    @Override
    public void saveCompensationRecord(String uniqueKey, String retryMsg, BizCompensationSceneEnum bizSceneEnum) {
        try {
            if (Objects.isNull(retryMsg) || Objects.isNull(uniqueKey) || Objects.isNull(bizSceneEnum)) {
                return;
            }
            if (checkUniqueKeyCount(uniqueKey, bizSceneEnum.getCode())) {
                return;
            }
            CompensationRecord compensationPayOrderRecord = new CompensationRecord();
            compensationPayOrderRecord.setRetryMsg(retryMsg);
            // 构建唯一键
            compensationPayOrderRecord.setUniqueKey(uniqueKey);
            compensationPayOrderRecord.setBizScene(bizSceneEnum.getCode());
            // 是否重试
            compensationPayOrderRecord.setRetryFlag(bizSceneEnum.getRetry() ? Constant.CONSTANT_INTEGER_1 : Constant.CONSTANT_INTEGER_0);
            compensationPayOrderRecord.setBizScene(bizSceneEnum.getCode());
            compensationPayOrderRecord.setCount(Constant.CONSTANT_INTEGER_0);
            compensationPayOrderRecord.setHandleStatus(Constant.CONSTANT_INTEGER_1);
            LocalDateTime now = LocalDateTime.now();
            compensationPayOrderRecord.setCreateTime(now);
            // 下次重试时间
            compensationPayOrderRecord.setNextRetryTime(now.plusSeconds(Constant.CONSTANT_INTEGER_1));
            compensationPayOrderRecord.setUpdateTime(now);
            compensationPayOrderRecord.setValid(Constant.CONSTANT_INTEGER_1);
            compensationRecordMapper.insertSelective(compensationPayOrderRecord);
        } catch (Exception e) {
            log.error("[CompensationPayOrderRecordGatewayImpl] saveCompensationPayOrderRecordWithAlipayNotifyParam Exception = {}", e.getMessage());
        }
    }


    @Override
    public CompensationRecordDTO queryBizExceptionRecordByPrimaryKey(Long id) {
        CompensationRecord record = compensationRecordMapper.selectByPrimaryKey(id);
        if (Objects.isNull(record)) {
            return null;
        }
        return BeanUtilsWrapper.convert(record, CompensationRecordDTO::new);
    }

    @Override
    public void updateRecord(CompensationRecordDTO record) {
        record.setUpdateTime(LocalDateTime.now());
        compensationRecordMapper.updateByPrimaryKeySelective(BeanUtilsWrapper.convert(record, CompensationRecord::new));
    }


    @Override
    public Long countExceptionRetryJob(CompensationRecordRetryJobDTO dto) {
        return compensationRecordExtMapper.countExceptionRetryJob(dto.getCurrentTime(), dto.getMaxRetryCount(), dto.getShardTotal(), dto.getShardIndex(), dto.getBizScene());
    }

    @Override
    public List<Long> selectExceptionRetryJob(CompensationRecordRetryJobDTO dto) {
        List<Long> record = compensationRecordExtMapper.selectExceptionRetryJob(dto.getCurrentTime(), dto.getMaxRetryCount(), dto.getShardTotal(), dto.getShardIndex(), dto.getPageIndex(), dto.getPageSize(), dto.getBizScene());
        if (Objects.isNull(record)) {
            return Collections.emptyList();
        }
        return record;
    }

    @Override
    public void deleteByCountSum(Integer countSum, Integer pageSize) {
        compensationRecordExtMapper.deleteByCountSum(countSum, pageSize);
    }

    @Override
    public void deleteByCreateTime(LocalDateTime createTime, Integer pageSize) {
        compensationRecordExtMapper.deleteByCreateTime(createTime, pageSize);
    }

    @Override
    public List<CompensationRecordDTO> selectMaxRetryCount(Integer maxRetryCount, Integer pageIndex, Integer pageSize) {
        List<CompensationRecord> list = compensationRecordExtMapper.selectMaxRetryCount(maxRetryCount, pageIndex, pageSize);
        if (Objects.isNull(list)) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(list, CompensationRecordDTO::new);

    }

    @Override
    public void deleteByPrimaryKey(Long id) {
        compensationRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void deleteByHandleStatus(Integer handleStatus, Integer pageSize) {
        compensationRecordExtMapper.deleteByHandleStatus(handleStatus, pageSize);
    }

    @Override
    public boolean checkUniqueKeyCount(String uniqueKey, Integer bizScene) {
        return compensationRecordExtMapper.selectUniqueKeyCount(uniqueKey, bizScene) > Constant.CONSTANT_INTEGER_0;
    }


}
