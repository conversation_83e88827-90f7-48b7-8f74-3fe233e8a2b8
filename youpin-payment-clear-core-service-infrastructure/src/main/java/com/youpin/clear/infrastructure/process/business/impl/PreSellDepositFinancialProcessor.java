package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 预售保证金-资金处理
 */
@Slf4j
@Service
public class PreSellDepositFinancialProcessor extends DefaultSubBusFinancialProcessor {

    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.PRE_SALE_DEPOSIT);
    }


    /**
     * 支付
     */
    @Override
    public void paySuccess(FinancialProcessorDTO dto) {
        super.paySuccess(dto);
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_212.getTypeId()) && !abstractUserAssetsRecordDTO.isBalanceChange()) {
                AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_219 = buildAssetRecord(UserAssetsTypeEnum.TYPE_219, dto, abstractUserAssetsRecordDTO.getUserId(), abstractUserAssetsRecordDTO.getPayChannel(), dto.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney()));
                abstractUserAssetsRecordDTO_219.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                tempList.add(abstractUserAssetsRecordDTO_219);
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
            }
        }
        dto.addAbstractUserAssetsRecordDTO(tempList);
    }

    /**
     * 退款
     */
    @Override
    public void refundSuccess(FinancialProcessorDTO dto) {
        super.refundSuccess(dto);
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();

        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_213.getTypeId()) && !abstractUserAssetsRecordDTO.isBalanceChange()) {
                AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_220 = buildAssetRecord(UserAssetsTypeEnum.TYPE_220, dto, abstractUserAssetsRecordDTO.getUserId(), abstractUserAssetsRecordDTO.getPayChannel(), dto.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney()));
                abstractUserAssetsRecordDTO_220.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                tempList.add(abstractUserAssetsRecordDTO_220);
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_218.getTypeId())) {
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.None.getCode());
            }
        }
        dto.addAbstractUserAssetsRecordDTO(tempList);
    }

    /**
     * 退款失败
     */
    @Override
    public void refundFail(FinancialProcessorDTO dto) {
        super.refundFail(dto);
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();

        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        boolean anyMatch211 = abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_211.getTypeId()));
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_213.getTypeId()) && anyMatch211) {

                AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_237 = buildAssetRecord(UserAssetsTypeEnum.TYPE_237, dto, abstractUserAssetsRecordDTO.getUserId(), abstractUserAssetsRecordDTO.getPayChannel(), dto.getNetStatusEnum(), AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney()));
                tempList.add(abstractUserAssetsRecordDTO_237);
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                abstractUserAssetsRecordDTO.setStatus(NetStatusEnum.SUCCESS.getCode());

            }
        }
        dto.addAbstractUserAssetsRecordDTO(tempList);
        abstractUserAssetsRecordDTOList.removeIf(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_211.getTypeId()));

    }


    /**
     * 结算
     */
    @Override
    public void settlement(FinancialProcessorDTO dto) {
        super.settlement(dto);
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();

        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();

        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_214.getTypeId())) {
                //平台户
                Long convertedToCent = AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney());
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_213, dto, abstractUserAssetsRecordDTO.getUserId(), DoNetPayChannelEnum.Balance.getCode(), dto.getNetStatusEnum(), convertedToCent));
            }
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_218.getTypeId())) {
                //平台户
                abstractUserAssetsRecordDTO.setUserId(getPlatformUserId(UserAssetsTypeEnum.TYPE_218));
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.None.getCode());
            }
        }
        dto.addAbstractUserAssetsRecordDTO(tempList);

    }

}
