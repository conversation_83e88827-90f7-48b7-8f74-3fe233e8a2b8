package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.AccountAssetsType;
import com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AccountAssetsTypeExtMapper {

    /**
     * 获取所有的资金类型
     */
    List<AccountAssetsType> gatAllAccountAssetsType();

    /**
     * 获取所有的资金类型
     */
    List<AccountAssetsType> gatAccountAssetsTypeByCode(List<Integer> assetsCodeList);


    /**
     * 获取所有的资金类型关联关系
     */
    List<AccountAssetsTypeRelate> gatAllAccountAssetsTypeRelate();


}