package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.gateway.UU898UserAssetsInfoGateway;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class UU898UserAssetsInfoGatewayImpl implements UU898UserAssetsInfoGateway {


    @Autowired
    UU898UserAssetsInfoMapper userAssetsInfoMapper;

    @Autowired
    UU898UserAssetsInfoExtMapper userAssetsInfoExtMapper;

    /**
     * 获取用户账户信息uu898
     */
    @Override
    public UserAssetsInfoDTO getUserAssetsInfo(Long userId) {
        UU898UserAssetsInfo userAssetsInfo = userAssetsInfoMapper.selectByPrimaryKey(userId);
        if (userAssetsInfo == null) {
            return null;
        }
        return BeanUtilsWrapper.convert(userAssetsInfo, UserAssetsInfoDTO::new);
    }

    /**
     * 批量获取用户信息
     */
    @Override
    public List<UserAssetsInfoDTO> getUserAssetsInfo(Long userIdMin, Long userIdMax, Integer easy) {
        List<UU898UserAssetsInfo> userAssetsInfoList = userAssetsInfoExtMapper.getUserAssetsInfoByUserIdMinMax(userIdMin, userIdMax, easy);
        if (null == userAssetsInfoList || userAssetsInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAssetsInfoList, UserAssetsInfoDTO::new);
    }


    @Override
    public int updateUserBalance(UserAssetsInfoDTO originDTO, UserAssetsInfoDTO toUpdateDTO) {
        UU898UserAssetsInfo origin = BeanUtilsWrapper.convert(originDTO, UU898UserAssetsInfo::new);
        UU898UserAssetsInfo toUpdate = BeanUtilsWrapper.convert(toUpdateDTO, UU898UserAssetsInfo::new);
        return userAssetsInfoExtMapper.updateUserBalance(origin,toUpdate);
    }

    @Override
    public int updateUserPurchaseBalance(UserAssetsInfoDTO originDTO, UserAssetsInfoDTO toUpdateDTO) {
        UU898UserAssetsInfo origin = BeanUtilsWrapper.convert(originDTO, UU898UserAssetsInfo::new);
        UU898UserAssetsInfo toUpdate = BeanUtilsWrapper.convert(toUpdateDTO, UU898UserAssetsInfo::new);
        return userAssetsInfoExtMapper.updateUserPurchaseBalance(origin,toUpdate);
    }

    @Override
    public int updateUserBalanceAndBlockMoney(UserAssetsInfoDTO originDTO, UserAssetsInfoDTO toUpdateDTO) {
        UU898UserAssetsInfo origin = BeanUtilsWrapper.convert(originDTO, UU898UserAssetsInfo::new);
        UU898UserAssetsInfo toUpdate = BeanUtilsWrapper.convert(toUpdateDTO, UU898UserAssetsInfo::new);
        return userAssetsInfoExtMapper.updateUserBalanceAndBlockMoney(origin,toUpdate);
    }
}
