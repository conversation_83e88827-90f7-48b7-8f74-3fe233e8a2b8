package com.youpin.clear.infrastructure.dataobject.uu898;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: NewOrderPayPurchaseExtend
 * @author: kk
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UU898NewOrderPayPurchaseExtend implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 使用的可转出求购金额（钱包转入，不可提现）
     */
    private BigDecimal purchaseMoneyFromMoney;

    /**
     * 剩余的可转出求购金额（钱包转入，不可提现）
     */
    private BigDecimal leftPurchaseMoneyFromMoney;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}