package com.youpin.clear.infrastructure.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Mapper
public interface TransactionServiceFeeStatementRecordExtMapper {


    Long selectMaxId();

    Integer updateSerialNo(@Param("serialNo") String serialNo, @Param("maxId") Long maxId, @Param("pageSize") Integer pageSize, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    BigDecimal selectSumMoney(@Param("serialNo") String serialNo);

//    Integer updateSerialNoStatus(@Param("serialNo") String serialNo, @Param("status") Integer status);
//
//    BigDecimal selectSumMoneyNotSerialNo(@Param("maxId") Long maxId, @Param("pageSize") Integer pageSize);

    BigDecimal reportFinanceSummary(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate,@Param("orderFeeType") Integer orderFeeType, @Param("typeId") Integer typeId);


}