package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: adjustment_apply
 * @author: lizhuangzhuang
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdjustmentApply implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 收入方用户id
     */
    private Long incomeUserId;

    /**
     * 增加调账余额
     */
    private BigDecimal incomeAmount;

    /**
     * 收入-资金明细类型
     */
    private Long incomeAssetType;

    /**
     * 收入方调整支付类型
     */
    private Integer incomePayChannel;

    /**
     * 收入-手续费
     */
    private BigDecimal incomeServiceFee;

    /**
     * 支出方用户id
     */
    private Long expenseUserId;

    /**
     * 减少调账余额
     */
    private BigDecimal expenseAmount;

    /**
     * 支出-资金明细类型
     */
    private Long expenseAssetType;

    /**
     * 支出方调整支付类型
     */
    private Integer expensePayChannel;

    /**
     * 支出-手续费
     */
    private BigDecimal expenseServiceFee;

    /**
     * 调账状态
     */
    private Integer status;

    /**
     * 申请人
     */
    private String applyBy;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 申请来源,1:风控 2:财务 3:运营 4:产研
     */
    private Integer adjustSource;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 扩展信息
     */
    private String extend;

    private static final long serialVersionUID = 1L;
}