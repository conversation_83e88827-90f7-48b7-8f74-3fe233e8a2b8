package com.youpin.clear.infrastructure.servcie.impl;

import com.uu898.youpin.commons.Logger;
import com.uu898.youpin.commons.base.enums.Constant;
import com.uu898.youpin.commons.base.utils.IdWorker;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.leaf.service.SegmentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;


@Service
public class IdWorkServiceImpl implements IdWorkService {

    /**
     * yyyyMMdd
     */
    static final String DATE_FORMAT_YMD_CODE = "yyyyMMdd";


    @Autowired(required = false)
    SegmentService segmentService;

    @Value("${payment.leaf.key.default:}")
    private String defaultKey;

    @Value("${payment.leaf.key.tag:}")
    private String tagKey;


    static String localDate2Str(LocalDate fromDate) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT_YMD_CODE);
        return fromDate.format(df);
    }

    @Override
    public String getYmdNextId() {
        String ymd = localDate2Str(LocalDate.now());
        return ymd + StringUtils.rightPad(leafKey(), 10, "0");
    }


    /**
     * 获取统一CODE
     *
     * @return
     */
    @Override
    public String getYmdNextIdByWholeKey() {
        String ymd = localDate2Str(LocalDate.now());
        return ymd + tagKey + getSegmentIdByWholeKey();
    }


    @Override
    public String getNextIdLeafKey() {
        return leafKey();
    }

    public String leafKey() {
        int retryCount = Constant.CONSTANT_INTEGER_3;
        String id = "";
        for (int i = 0; i < retryCount; i++) {
            try {
                id = segmentService.getSegmentId();
                if (StringUtils.isNotBlank(id)) {
                    return id;
                }
            } catch (Exception e) {
                Logger.error("[账务中心] CHARGE_CODE:[{}], 原因: {}", ErrorCode.ID_FAIL.getCode(), ErrorCode.ID_FAIL.getMessage(), e.fillInStackTrace());
            }
            if (i >= retryCount - 1) {
                Logger.error("[账务中心] CHARGE_CODE:[{}], 原因: {}", ErrorCode.ID_FAIL.getCode(), ErrorCode.ID_FAIL.getMessage());
                throw new PaymentClearBusinessException(ErrorCode.ID_FAIL);
            }
        }
        // 如果循环结束仍未获取到有效的 ID，则抛出异常
        if (StringUtils.isEmpty(id)) {
            Logger.warn("[账务中心] getSegmentIdByWholeKey CHARGE_CODE:[{}], 获取统一id异常,选择兜底策略: 雪花算法生成唯一Id", ErrorCode.SNOW_ID.getCode());
            id = String.valueOf(IdWorker.getNextId());
        }
        return id;
    }


    public String getSegmentIdByWholeKey() {
        int retryCount = Constant.CONSTANT_INTEGER_3;
        String id = "";
        for (int i = 0; i < retryCount; i++) {
            try {
                id = segmentService.getSegmentIdByWholeKey(defaultKey);
                if (StringUtils.isNotBlank(id)) {
                    return id;
                }
            } catch (Exception e) {
                Logger.error("[账务中心] CHARGE_CODE:[{}], 原因: {}", ErrorCode.ID_FAIL.getCode(), ErrorCode.ID_FAIL.getMessage(), e.fillInStackTrace());
            }
            if (i >= retryCount - 1) {
                Logger.error("[账务中心] CHARGE_CODE:[{}], 原因: {}", ErrorCode.ID_FAIL.getCode(), ErrorCode.ID_FAIL.getMessage());
                throw new PaymentClearBusinessException(ErrorCode.ID_FAIL);
            }
        }
        // 如果循环结束仍未获取到有效的 ID，则抛出异常
        if (StringUtils.isEmpty(id)) {
            Logger.warn("[账务中心] getSegmentIdByWholeKey CHARGE_CODE:[{}], 获取统一id异常,选择兜底策略: 雪花算法生成唯一Id", ErrorCode.SNOW_ID.getCode());
            id = String.valueOf(IdWorker.getNextId());
        }
        return id;
    }


}
