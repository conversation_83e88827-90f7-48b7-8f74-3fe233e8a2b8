package com.youpin.clear.infrastructure.process.separate.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import com.youpin.clear.domain.process.SeparateFundingDirectionProcessor;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 加 减 不变 冻结, 解冻
 */
@Slf4j
public abstract class DefaultSeparateFundingDirectionProcessor implements SeparateFundingDirectionProcessor {

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;
    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;


    @Override
    public void process(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        doProcess(billMember, billMemberList, billMemberListIsReference);
    }

    @Override
    public void process(List<BillMember> billMemberList) {
        doProcess(billMemberList);
    }

    public void doProcess(List<BillMember> billMemberList) {
    }

    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
    }


    /**
     * 账单金额校验
     */
    static void checkBillAmount(AccountBalanceDTO accountBalanceDTO, BillItemMember billItemMember) {
        if (null == accountBalanceDTO) {
            log.error("[分账处理] 分账对象为null  用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 账单金额:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId(), billItemMember.getAmount());
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        if (accountBalanceDTO.getTotalFrozenAmountAbs().compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0 && accountBalanceDTO.getTotalFrozenAmountAbs().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
            log.error("[分账处理] 冻结金额校验失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 计算金额(绝对值):{} 账单金额:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId(), accountBalanceDTO.getTotalAmountAbs(), billItemMember.getAmount());
            throw new PaymentClearBusinessException(ErrorCode.FROZEN_AMOUNT_CHECK_FAIL);
        }
        if (accountBalanceDTO.getTotalAmountAbs().compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0 && accountBalanceDTO.getTotalAmountAbs().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
            log.error("[分账处理] 金额校验失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 计算金额(绝对值):{} 账单金额:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId(), accountBalanceDTO.getTotalAmountAbs(), billItemMember.getAmount());
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }

    }

    /**
     * 支出反向收入 余额2 优先
     */
    static AccountBalanceDTO reverseIncomeFromExpenditure2(BigDecimal balance1, BigDecimal balance2, BigDecimal addAmount, AccountBalanceDTO subtractDto) {
        if (subtractDto != null) {
            balance1 = balance1.subtract(subtractDto.getBalance1());
            balance2 = balance2.subtract(subtractDto.getBalance2());
        }
        BigDecimal diff1;
        BigDecimal diff2 = BigDecimal.ZERO;
        if (balance2.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            if (addAmount.subtract(balance2).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                diff2 = balance2;
            } else {
                diff2 = addAmount;
            }
        }
        diff1 = addAmount.subtract(diff2);
        return AccountBalanceDTO.builder().balance2(diff2).balance1(diff1).build();
    }

    /**
     * 支出反向退款 余额1 优先
     */
    static AccountBalanceDTO reverseIncomeFromExpenditure1(BigDecimal balance1, BigDecimal balance2, BigDecimal addAmount, AccountBalanceDTO subtractDto) {
        if (subtractDto != null) {
            if (balance1.subtract(subtractDto.getBalance1()).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0 && balance2.subtract(subtractDto.getBalance2()).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                balance1 = balance1.subtract(subtractDto.getBalance1());
                balance2 = balance2.subtract(subtractDto.getBalance2());
            }
        }
        BigDecimal diff1 = BigDecimal.ZERO;
        BigDecimal diff2;
        if (balance1.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            if (addAmount.subtract(balance1).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                diff1 = balance1;
            } else {
                diff1 = addAmount;
            }
        }
        diff2 = addAmount.subtract(diff1);
        return AccountBalanceDTO.builder().balance2(diff2).balance1(diff1).build();
    }


    static AccountBalanceDTO getAccountBalanceDTO(List<UserAccountRecordMember> userAccountRecordData) {
        Map<Integer, BigDecimal> balanceMap = userAccountRecordData.stream().collect(Collectors.toMap(UserAccountRecordMember::getAccountType, userAccountRecordDatum -> userAccountRecordDatum.getBalanceChange() == null ? BigDecimal.ZERO : userAccountRecordDatum.getBalanceChange().abs(), BigDecimal::add));

        BigDecimal balance1 = calculateBalance(balanceMap, AccountTypeEnum.BALANCE_1.getCode(), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode(), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode());

        BigDecimal balance2 = calculateBalance(balanceMap, AccountTypeEnum.BALANCE_2.getCode(), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode(), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode());

        return AccountBalanceDTO.builder().balance2(balance2).balance1(balance1).build();
    }

    static AccountBalanceDTO getAccountBalancePurchaseDTO(List<UserAccountRecordMember> userAccountRecordData) {
        Map<Integer, BigDecimal> balanceMap = userAccountRecordData.stream().collect(Collectors.toMap(UserAccountRecordMember::getAccountType, userAccountRecordDatum -> userAccountRecordDatum.getBalanceChange() == null ? BigDecimal.ZERO : userAccountRecordDatum.getBalanceChange().abs(), BigDecimal::add));

        BigDecimal purchaseBalanceRecharge1 = calculateBalance(balanceMap, AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode());

        BigDecimal purchaseBalanceRecharge2 = calculateBalance(balanceMap, AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode());

        BigDecimal purchaseBalanceTransfer1 = calculateBalance(balanceMap, AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode());

        BigDecimal purchaseBalanceTransfer2 = calculateBalance(balanceMap, AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode());

        return AccountBalanceDTO.builder().purchaseBalanceTransfer1(purchaseBalanceTransfer1).purchaseBalanceTransfer2(purchaseBalanceTransfer2).purchaseBalanceRecharge1(purchaseBalanceRecharge1).purchaseBalanceRecharge2(purchaseBalanceRecharge2).build();
    }

    public static BigDecimal calculateBalance(Map<Integer, BigDecimal> balanceMap, Integer... accountTypes) {
        return Arrays.stream(accountTypes).map(balanceMap::get).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 等比分账-余额2优先 第三位小数放到余额2
     *
     * @param amount1 余额1支付金额
     * @param amount2 余额2支付金额
     * @param amount  服务费
     */
    static AccountBalanceDTO equalRatiosAccount2(BigDecimal amount1, BigDecimal amount2, BigDecimal amount) {
        if (amount1.compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0 && amount2.compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_NOT_LESS_ZERO);
        }
        BigDecimal accountAmount1 = amount1.divide(amount1.add(amount2), Constant.CONSTANT_INTEGER_6, RoundingMode.DOWN).multiply(amount).setScale(Constant.CONSTANT_INTEGER_2, RoundingMode.DOWN);
        BigDecimal accountAmount2 = amount.subtract(accountAmount1);
        return AccountBalanceDTO.builder().balance1(accountAmount1).balance2(accountAmount2).build();
    }

    /**
     * 冻结扣减 2
     */
    static AccountBalanceDTO subtractionFrozenAccount2(BigDecimal amount, AccountInfoMember accountAmount) {
        // 输入验证
        if (amount == null || accountAmount == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        BigDecimal balance = accountAmount.getFrozenBalance();
        if (balance == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        // 计算绝对值
        BigDecimal absAmount = amount.abs();
        BigDecimal diff2 = subtraction(balance, absAmount);
        BigDecimal diff1 = absAmount.subtract(diff2.abs());
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setFrozenBalance2(diff2);
        accountBalanceDTO.setFrozenBalance1(diff1);
        return accountBalanceDTO;
    }


    /**
     * 扣减 2
     */
    static AccountBalanceDTO subtractionAccount2(BigDecimal amount, AccountInfoMember accountAmount) {
        // 输入验证
        if (amount == null || accountAmount == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        BigDecimal balance = accountAmount.getBalance();
        if (balance == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        // 计算绝对值
        BigDecimal absAmount = amount.abs();
        BigDecimal diff2 = subtraction(balance, absAmount);
        BigDecimal diff1 = absAmount.subtract(diff2.abs());
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setBalance2(diff2);
        accountBalanceDTO.setBalance1(diff1);
        return accountBalanceDTO;
    }

    /**
     * 扣减 1
     */
    static AccountBalanceDTO subtractionAccount1(BigDecimal amount, AccountInfoMember accountAmount) {
        // 输入验证
        if (amount == null || accountAmount == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        BigDecimal balance = accountAmount.getBalance();
        if (balance == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        // 计算绝对值
        BigDecimal absAmount = amount.abs();
        BigDecimal diff1 = subtraction(balance, absAmount);
        BigDecimal diff2 = absAmount.subtract(diff1.abs());
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setBalance2(diff2);
        accountBalanceDTO.setBalance1(diff1);
        return accountBalanceDTO;
    }


    /**
     * 提现-求购2优先
     * 求购充值余额2 求购充值余额1 求购转入余额2 求购转入余额1
     */
    static AccountBalanceDTO subtractionPurchaseRechargeAccount2(BigDecimal amount, AccountAggregate accountAggregate) {
        AccountInfoMember purchaseBalanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2);
        AccountInfoMember purchaseBalanceRecharge1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1);
        AccountInfoMember purchaseBalanceTransfer2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2);
        AccountInfoMember purchaseBalanceTransfer1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1);

        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        //优先 余额2 扣减
        BigDecimal recharge2 = subtraction(purchaseBalanceRecharge2.getBalance(), amount);

        BigDecimal subtract = amount.subtract(recharge2);
        BigDecimal recharge1 = subtraction(purchaseBalanceRecharge1.getBalance(), subtract);

        subtract = subtract.subtract(recharge1);
        BigDecimal transfer2 = subtraction(purchaseBalanceTransfer2.getBalance(), subtract);

        subtract = subtract.subtract(transfer2);
        BigDecimal transfer1 = subtraction(purchaseBalanceTransfer1.getBalance(), subtract);

        accountBalanceDTO.setPurchaseBalanceRecharge1(recharge1);
        accountBalanceDTO.setPurchaseBalanceTransfer1(transfer1);
        accountBalanceDTO.setPurchaseBalanceRecharge2(recharge2);
        accountBalanceDTO.setPurchaseBalanceTransfer2(transfer2);
        return accountBalanceDTO;
    }


    /**
     * 扣减-求购2优先
     * 求购充值余额2 求购转入余额2 求购充值余额1  求购转入余额1
     */
    static AccountBalanceDTO subtractionPurchaseAccount2(BigDecimal amount, AccountAggregate accountAggregate) {
        AccountInfoMember purchaseBalanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2);
        AccountInfoMember purchaseBalanceTransfer2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2);
        AccountInfoMember purchaseBalanceRecharge1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1);
        AccountInfoMember purchaseBalanceTransfer1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1);
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        //优先 余额2 扣减
        BigDecimal recharge2 = subtraction(purchaseBalanceRecharge2.getBalance(), amount);

        BigDecimal subtract = amount.subtract(recharge2);
        BigDecimal transfer2 = subtraction(purchaseBalanceTransfer2.getBalance(), subtract);

        subtract = subtract.subtract(transfer2);
        BigDecimal recharge1 = subtraction(purchaseBalanceRecharge1.getBalance(), subtract);

        subtract = subtract.subtract(recharge1);
        BigDecimal transfer1 = subtraction(purchaseBalanceTransfer1.getBalance(), subtract);

        accountBalanceDTO.setPurchaseBalanceRecharge1(recharge1);
        accountBalanceDTO.setPurchaseBalanceTransfer1(transfer1);
        accountBalanceDTO.setPurchaseBalanceRecharge2(recharge2);
        accountBalanceDTO.setPurchaseBalanceTransfer2(transfer2);
        return accountBalanceDTO;
    }

    /**
     * 求购支出反向退款 余额1 优先
     * 求购转入余额1 求购充值余额1  求购转入余额2  求购充值余额2
     */
    static AccountBalanceDTO reverseIncomeFromPurchaseAccountRefund(AccountBalanceDTO relationAccountBalanceDTO, BigDecimal addAmount, AccountBalanceDTO subtractDto) {


        BigDecimal recharge1 = relationAccountBalanceDTO.getPurchaseBalanceRecharge1();
        BigDecimal recharge2 = relationAccountBalanceDTO.getPurchaseBalanceRecharge2();
        BigDecimal transfer1 = relationAccountBalanceDTO.getPurchaseBalanceTransfer1();
        BigDecimal transfer2 = relationAccountBalanceDTO.getPurchaseBalanceTransfer2();


        if (subtractDto != null) {
            recharge1 = recharge1.subtract(subtractDto.getPurchaseBalanceRecharge1());
            recharge2 = recharge2.subtract(subtractDto.getPurchaseBalanceRecharge2());
            transfer1 = transfer1.subtract(subtractDto.getPurchaseBalanceTransfer1());
            transfer2 = transfer2.subtract(subtractDto.getPurchaseBalanceTransfer2());
        }

        BigDecimal diffTransfer1 = BigDecimal.ZERO;
        BigDecimal diffRecharge1 = BigDecimal.ZERO;
        BigDecimal diffTransfer2 = BigDecimal.ZERO;
        BigDecimal diffRecharge2;

        if (transfer1.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            if (addAmount.subtract(transfer1).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                diffTransfer1 = transfer1;
            } else {
                diffTransfer1 = addAmount;
            }
        }
        BigDecimal subtract = addAmount.subtract(diffTransfer1);

        if (subtract.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0 && recharge1.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            if (subtract.subtract(recharge1).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                diffRecharge1 = recharge1;
            } else {
                diffRecharge1 = subtract;
            }
        }
        subtract = subtract.subtract(diffRecharge1);

        if (subtract.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0 && transfer2.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            if (subtract.subtract(transfer2).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                diffTransfer2 = transfer2;
            } else {
                diffTransfer2 = subtract;
            }
        }
        diffRecharge2 = subtract.subtract(diffTransfer2);

        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setPurchaseBalanceRecharge1(diffRecharge1);
        accountBalanceDTO.setPurchaseBalanceRecharge2(diffRecharge2);
        accountBalanceDTO.setPurchaseBalanceTransfer1(diffTransfer1);
        accountBalanceDTO.setPurchaseBalanceTransfer2(diffTransfer2);
        return accountBalanceDTO;
    }


    /**
     * 求购支出反向退款 余额1 优先
     * 求购转入余额1 求购充值余额1  求购转入余额2  求购充值余额2
     */
    static AccountBalanceDTO reverseIncomeFromPurchaseAccountRefund(AccountBalanceDTO relationAccountBalanceDTO, BigDecimal addAmount) {
        BigDecimal recharge1 = relationAccountBalanceDTO.getPurchaseBalanceRecharge1();
        BigDecimal recharge2 = relationAccountBalanceDTO.getPurchaseBalanceRecharge2();
        BigDecimal transfer1 = relationAccountBalanceDTO.getPurchaseBalanceTransfer1();
        BigDecimal transfer2 = relationAccountBalanceDTO.getPurchaseBalanceTransfer2();

        BigDecimal diffTransfer1 = BigDecimal.ZERO;
        BigDecimal diffRecharge1 = BigDecimal.ZERO;
        BigDecimal diffTransfer2 = BigDecimal.ZERO;
        BigDecimal diffRecharge2;

        if (transfer1.compareTo(BigDecimal.ZERO) > 0) {
            if (addAmount.subtract(transfer1).compareTo(BigDecimal.ZERO) >= 0) {
                diffTransfer1 = transfer1;
            } else {
                diffTransfer1 = addAmount;
            }
        }
        BigDecimal subtract = addAmount.subtract(diffTransfer1);

        if (subtract.compareTo(BigDecimal.ZERO) > 0 && recharge1.compareTo(BigDecimal.ZERO) > 0) {
            if (subtract.subtract(recharge1).compareTo(BigDecimal.ZERO) >= 0) {
                diffRecharge1 = recharge1;
            } else {
                diffRecharge1 = subtract;
            }
        }
        subtract = subtract.subtract(diffRecharge1);

        if (subtract.compareTo(BigDecimal.ZERO) > 0 && transfer2.compareTo(BigDecimal.ZERO) > 0) {
            if (subtract.subtract(transfer2).compareTo(BigDecimal.ZERO) >= 0) {
                diffTransfer2 = transfer2;
            } else {
                diffTransfer2 = subtract;
            }
        }
        diffRecharge2 = subtract.subtract(diffTransfer2);

        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setPurchaseBalanceRecharge1(diffRecharge1);
        accountBalanceDTO.setPurchaseBalanceRecharge2(diffRecharge2);
        accountBalanceDTO.setPurchaseBalanceTransfer1(diffTransfer1);
        accountBalanceDTO.setPurchaseBalanceTransfer2(diffTransfer2);
        return accountBalanceDTO;
    }


    /**
     * 充值
     * 扣减-求购转入余额 2优先
     * 求购转入余额2 求购转入余额1 求购充值余额2 求购充值余额1
     */
    static AccountBalanceDTO subtractionPurchaseAccount2transfer(BigDecimal amount, AccountAggregate accountAggregate) {

        AccountInfoMember purchaseBalanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2);
        AccountInfoMember purchaseBalanceTransfer2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2);
        AccountInfoMember purchaseBalanceRecharge1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1);
        AccountInfoMember purchaseBalanceTransfer1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1);

        //优先 转入余额2 扣减
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();

        BigDecimal transfer2 = subtraction(purchaseBalanceTransfer2.getBalance(), amount);

        BigDecimal subtract = amount.subtract(transfer2);
        BigDecimal transfer1 = subtraction(purchaseBalanceTransfer1.getBalance(), subtract);

        subtract = subtract.subtract(transfer1);
        BigDecimal recharge2 = subtraction(purchaseBalanceRecharge2.getBalance(), subtract);

        subtract = subtract.subtract(recharge2);
        BigDecimal recharge1 = subtraction(purchaseBalanceRecharge1.getBalance(), subtract);

        accountBalanceDTO.setPurchaseBalanceTransfer2(transfer2);
        accountBalanceDTO.setPurchaseBalanceTransfer1(transfer1);
        accountBalanceDTO.setPurchaseBalanceRecharge2(recharge2);
        accountBalanceDTO.setPurchaseBalanceRecharge1(recharge1);

        return accountBalanceDTO;
    }


    /**
     * 减法 得到减去的值
     */
    static BigDecimal subtraction(BigDecimal balance, BigDecimal subtractAmount) {
        BigDecimal subtract = balance.subtract(subtractAmount);
        if (subtract.compareTo(BigDecimal.ZERO) <= ClearConstants.CONSTANT_INTEGER_0) {
            return balance;
        }
        return subtractAmount;
    }


    List<UserAccountRecordMember> toCreateUserAccountRecordMember(AccountBalanceDTO accountBalanceDTO, BillItemMember billItemMember, AccountAggregate accountAggregate, DoNetPayChannelEnum doNetPayChannelEnum) {
        List<UserAccountRecordMember> list = new ArrayList<>();
        if (accountBalanceDTO.getBalance1().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0 || accountBalanceDTO.getFrozenBalance1().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            list.add(createUserAccountRecordMember(billItemMember, accountBalanceDTO.getBalance1(), accountBalanceDTO.getFrozenBalance1(), AccountTypeEnum.BALANCE_1, accountAggregate, doNetPayChannelEnum));
        }
        if (accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0 || accountBalanceDTO.getFrozenBalance2().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            list.add(createUserAccountRecordMember(billItemMember, accountBalanceDTO.getBalance2(), accountBalanceDTO.getFrozenBalance2(), AccountTypeEnum.BALANCE_2, accountAggregate, doNetPayChannelEnum));
        }
        if (accountBalanceDTO.getPurchaseBalanceRecharge1().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            list.add(createUserAccountRecordMember(billItemMember, accountBalanceDTO.getPurchaseBalanceRecharge1(), BigDecimal.ZERO, AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1, accountAggregate, doNetPayChannelEnum));
        }
        if (accountBalanceDTO.getPurchaseBalanceRecharge2().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            list.add(createUserAccountRecordMember(billItemMember, accountBalanceDTO.getPurchaseBalanceRecharge2(), BigDecimal.ZERO, AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2, accountAggregate, doNetPayChannelEnum));
        }
        if (accountBalanceDTO.getPurchaseBalanceTransfer1().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            list.add(createUserAccountRecordMember(billItemMember, accountBalanceDTO.getPurchaseBalanceTransfer1(), BigDecimal.ZERO, AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1, accountAggregate, doNetPayChannelEnum));
        }
        if (accountBalanceDTO.getPurchaseBalanceTransfer2().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            list.add(createUserAccountRecordMember(billItemMember, accountBalanceDTO.getPurchaseBalanceTransfer2(), BigDecimal.ZERO, AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2, accountAggregate, doNetPayChannelEnum));
        }
        return list;
    }


    /**
     * 创建用户账户记录
     */
    UserAccountRecordMember createUserAccountRecordMember(BillItemMember billItemMember, BigDecimal balanceChange, BigDecimal frozenBalanceChange, AccountTypeEnum accountTypeEnum, AccountAggregate accountAggregate, DoNetPayChannelEnum doNetPayChannelEnum) {
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(accountTypeEnum);
        UserAccountRecordMember userAccountRecordMember = new UserAccountRecordMember();
        userAccountRecordMember.setAccountType(accountTypeEnum.getCode());
        userAccountRecordMember.setUserId(billItemMember.getUserId());
        userAccountRecordMember.setTypeId(billItemMember.getTypeId());
        userAccountRecordMember.setPayChannel(billItemMember.getPayChannelEnum().getCode());
        userAccountRecordMember.setDirectionEnum(billItemMember.getDirectionEnum());

        userAccountRecordMember.setUserAssetsRecordId(billItemMember.getUserAssetsRecordId());
        userAccountRecordMember.setOrderNo(billItemMember.getOrderNo());
        userAccountRecordMember.setPayOrderNo(billItemMember.getPayOrderNo());
        userAccountRecordMember.setSerialNo(billItemMember.getSerialNo());
        userAccountRecordMember.setTreadNo(billItemMember.getTreadNo());

        userAccountRecordMember.setStatus(billItemMember.getNetStatusEnum().getCode());
        userAccountRecordMember.setUserAccountNo(accountInfoMember.getUserAccountNo());
        userAccountRecordMember.setFinishTime(billItemMember.getFinishTime());
        userAccountRecordMember.setCreateTime(billItemMember.getAddTime());
        //使用权重
        accountInfoMember.setUseNumber(accountInfoMember.getUseNumber() + Constant.CONSTANT_INTEGER_1);
        //排序权重
        userAccountRecordMember.setCompareSort(accountInfoMember.getUseNumber());

        userAccountRecordMember.setBalanceChange(balanceChange);
        userAccountRecordMember.setFrozenBalanceChange(frozenBalanceChange);


        //变动之前账户余额
        userAccountRecordMember.setBalanceBefore(accountInfoMember.getBalance());
        //变动之前的冻结余额
        userAccountRecordMember.setFrozenBalanceBefore(accountInfoMember.getFrozenBalance());

        userAccountRecordMember.setBalanceChange(balanceChange);
        userAccountRecordMember.setFrozenBalanceChange(frozenBalanceChange);

        userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_0);

        if (null != billItemMember.getBillItemMemberExtension()) {
            userAccountRecordMember.setExt(JSON.toJSONString(billItemMember.getBillItemMemberExtension()));
        }
        NetStatusEnum netStatusEnum = billItemMember.getNetStatusEnum();

        //是否进行中
        boolean isProcessing = netStatusEnum.equals(NetStatusEnum.PROCESSING);
        //是否失败
        boolean isFail = netStatusEnum.equals(NetStatusEnum.FAIL);
        //是否成功
        boolean isSuccess = netStatusEnum.equals(NetStatusEnum.SUCCESS);
        //是否余额或求购
        boolean isBalanceOrPurchase = doNetPayChannelEnum.equals(DoNetPayChannelEnum.Balance) || doNetPayChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance);

        switch (billItemMember.getDirectionEnum()) {
            case RECHARGE:
                if (isProcessing || isFail) {
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                }
                if (isSuccess) {
                    userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance().add(balanceChange));
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                }
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance());
                break;
            case CASH_WITHDRAWAL:
                if (isProcessing) {
                    userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance().subtract(balanceChange));
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                }
                if (isSuccess || isFail) {
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                }
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance());
                break;
            case FREEZE_ADDITION:
                if (isBalanceOrPurchase) {
                    userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance().subtract(balanceChange));
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                } else {
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                }
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance().add(frozenBalanceChange));
                accountInfoMember.setFrozenBalance(userAccountRecordMember.getFrozenBalanceAfter());
                break;
            case FREEZE_REFUND:
                if (isBalanceOrPurchase) {
                    userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance().add(balanceChange));
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                } else {
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                }
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance().subtract(frozenBalanceChange));
                accountInfoMember.setFrozenBalance(userAccountRecordMember.getFrozenBalanceAfter());
                break;
            case ADDITION:
            case REFUND:
                if (isBalanceOrPurchase) {
                    if (isProcessing || isFail) {
                        userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                    } else if (isSuccess) {
                        userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                        userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance().add(balanceChange));
                        accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                    }
                } else {
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                }
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance());
                break;
            case SUBTRACTION:
                if (isBalanceOrPurchase) {
                    userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance().subtract(balanceChange));
                    accountInfoMember.setBalance(userAccountRecordMember.getBalanceAfter());
                } else {
                    userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                }
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance());
                break;
            case INVARIANT:
            case CHANNEL_FREEZE:
            case CHANNEL_UNFREEZE:
                userAccountRecordMember.setBalanceAfter(accountInfoMember.getBalance());
                userAccountRecordMember.setFrozenBalanceAfter(accountInfoMember.getFrozenBalance());

                break;
        }
        return userAccountRecordMember;
    }

    AccountBalanceDTO getAccountBalanceDTOByAssociatedAdditionTypeIdList(List<BillMember> billMemberList, List<Integer> associatedAdditionTypeIdList) {
        List<List<UserAccountRecordMember>> list = new ArrayList<>();
        for (BillMember billMember : billMemberList) {
            if (associatedAdditionTypeIdList.contains(billMember.getBillItemMember().getTypeId())) {
                List<UserAccountRecordMember> userAccountRecordMemberList = billMember.getUserAccountRecordMemberList();
                if (null == userAccountRecordMemberList || userAccountRecordMemberList.isEmpty()) {
                    continue;
                }
                list.add(userAccountRecordMemberList);
            }
        }
        List<UserAccountRecordMember> userAccountRecordDataLists = list.stream().flatMap(Collection::stream).collect(Collectors.toList());
        if (userAccountRecordDataLists.isEmpty()) {
            return null;
        }
        return getAccountBalanceDTO(userAccountRecordDataLists);
    }

    AccountBalanceDTO getDBAccountBalanceDTOByAssociatedAdditionTypeIdList(BillItemMember billItemMember, List<Integer> associatedAdditionTypeIdList, Boolean isPayOrderNo) {
        List<UserAccountRecordMember> userAccountRecordDataLists = findUserAccountRecordData(billItemMember, associatedAdditionTypeIdList, Boolean.FALSE, isPayOrderNo);
        userAccountRecordDataLists = userAccountRecordDataLists.stream().filter(item -> item.getStatus().equals(NetStatusEnum.SUCCESS.getCode()))
                //剔除自身
                .filter(item -> !item.getUserAssetsRecordId().equals(billItemMember.getUserAssetsRecordId())).collect(Collectors.toList());
        if (userAccountRecordDataLists.isEmpty()) {
            return null;
        }
        if (billItemMember.getPayChannelEnum().equals(DoNetPayChannelEnum.PurchaseBalance)) {
            return getAccountBalancePurchaseDTO(userAccountRecordDataLists);
        }
        return getAccountBalanceDTO(userAccountRecordDataLists);
    }


    void printRelationDBLog(BillItemMember billItemMember, List<UserAccountRecordMember> userAccountRecordData) {
        if (Boolean.TRUE.equals(paymentClearParamsConfig.getSeparatePrintLog())) {
            userAccountRecordData.forEach(userAccountRecordMember -> {
                StringBuilder sb = new StringBuilder();
                NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(userAccountRecordMember.getStatus());
                sb.append(StringUtils.rightPad(" 资金类型: " + billItemMember.getTypeId() + " " + billItemMember.getAssetsName(), 20, "\u3000"));
                sb.append(StringUtils.rightPad(" 关系类型: " + userAccountRecordMember.getTypeId(), 20, "\u3000"));
                sb.append(StringUtils.rightPad(" 账户类型: " + AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()).getName(), 6));
                sb.append(StringUtils.rightPad(" 分账金额: " + userAccountRecordMember.getBalanceChange(), 10));
                sb.append(StringUtils.rightPad(" 冻结金额: " + userAccountRecordMember.getFrozenBalanceChange(), 10));
                sb.append(StringUtils.rightPad(" 资金编号: " + userAccountRecordMember.getUserAssetsRecordId(), 16));
                sb.append(StringUtils.rightPad(" 流水号: " + userAccountRecordMember.getSerialNo(), 20));
                sb.append(StringUtils.rightPad(" 支付单号: " + userAccountRecordMember.getPayOrderNo(), 20));
                sb.append(StringUtils.rightPad(" 用户Id: " + userAccountRecordMember.getUserId(), 10, "\u3000"));
                sb.append(StringUtils.rightPad(" 状态: " + netStatusEnum.getName(), 6));
                log.info("[分账处理] 关系日志:{}", sb);
            });
        }
    }

    public List<UserAccountRecordMember> findBillMemberListTo(List<BillMember> billMemberList, List<Integer> typeIdList) {
        if (null == billMemberList || billMemberList.isEmpty() || null == typeIdList || typeIdList.isEmpty()) {
            return List.of();
        }
        return billMemberList.stream().filter(item -> typeIdList.contains(item.getBillItemMember().getTypeId()))
                .filter(item -> item.getUserAccountRecordMemberList() != null && !item.getUserAccountRecordMemberList().isEmpty())
                .map(BillMember::getUserAccountRecordMemberList).flatMap(Collection::stream).collect(Collectors.toList());
    }

    AccountBalanceDTO findRelationList2(BillItemMember billItemMember, List<BillMember> billMemberList) {
        //分账明细DB数据
        List<Integer> typeIdList = billItemMember.getTypeRelationList();
        if (null == typeIdList || typeIdList.isEmpty()) {
            log.warn("[分账处理] 映射关系为空  关系查询失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId());
            return null;
        }
        //需要增加的金额
        BigDecimal addAmount = billItemMember.getAmount();
        //资金类型
        Integer typeId = billItemMember.getTypeId();
        List<UserAccountRecordMember> userAccountRecordData = new ArrayList<>();

        log.info("[分账处理] 收支关系 {}  : {}", typeId, typeIdList);

        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_111) || typeId.equals(UserAssetsTypeEnum.TYPE_250.getTypeId())) {
            List<UserAccountRecordMember> userAccountRecordDataByBill = findBillMemberListTo(billMemberList, typeIdList);
            List<UserAccountRecordMember> userAccountRecordDataByDB = findUserAccountRecordData(billItemMember, typeIdList, Boolean.FALSE, true);
            if (null != userAccountRecordDataByBill && !userAccountRecordDataByBill.isEmpty()) {
                userAccountRecordData.addAll(userAccountRecordDataByBill);
            }
            if (null != userAccountRecordDataByDB && !userAccountRecordDataByDB.isEmpty()) {
                userAccountRecordData.addAll(userAccountRecordDataByDB);
            }
        } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_1000)) {
            userAccountRecordData = findUserAccountRecordData(billItemMember, typeIdList, Boolean.FALSE, true);
            if (null == userAccountRecordData || userAccountRecordData.isEmpty()) {
                userAccountRecordData = findBillMemberListTo(billMemberList, typeIdList);
            }
        } else {
            userAccountRecordData = findBillMemberListTo(billMemberList, typeIdList);
            if (null == userAccountRecordData || userAccountRecordData.isEmpty()) {
                userAccountRecordData = findUserAccountRecordData(billItemMember, typeIdList, Boolean.FALSE, true);
            }
        }

        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_83) && !userAccountRecordData.isEmpty()) {
            userAccountRecordData = userAccountRecordData.stream().filter(item -> !item.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_221)).collect(Collectors.toList());
        }

        if (typeId.equals(UserAssetsTypeEnum.TYPE_250.getTypeId()) && billItemMember.getPayChannelEnum().equals(DoNetPayChannelEnum.PurchaseBalance) && !userAccountRecordData.isEmpty()) {
            userAccountRecordData = findUserAccountRecordData(billItemMember, List.of(3, 10, 243), Boolean.FALSE, true);
            billItemMember.setDbAssociatedAdditionList(List.of(4, 83, 244));
        }


        DoNetPayChannelEnum payChannelEnum = billItemMember.getPayChannelEnum();
        //是否手续费
        boolean isPlatformFee = billItemMember.getAssetsTypeEnum().equals(AssetsTypeEnum.PLATFORM_FEE);
        //是否退款类型
        boolean isRefund = billItemMember.getAssetsTypeEnum().equals(AssetsTypeEnum.REFUND);

        if (!userAccountRecordData.isEmpty()) {

            printRelationDBLog(billItemMember, userAccountRecordData);

            AccountBalanceDTO accountBalanceDTO;
            AccountBalanceDTO relationAccountBalanceDTO;
            if (isRefund && payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
                relationAccountBalanceDTO = getAccountBalancePurchaseDTO(userAccountRecordData);
            } else {
                relationAccountBalanceDTO = getAccountBalanceDTO(userAccountRecordData);
            }
            // 入参里面的关联关系
            List<Integer> associatedAdditionTypeIdList = billItemMember.getAssociatedAdditionList();
            AccountBalanceDTO subtractDto = new AccountBalanceDTO();
            AccountBalanceDTO subtractDto1;
            if (null != associatedAdditionTypeIdList && !associatedAdditionTypeIdList.isEmpty()) {
                log.info("[分账处理] typeId {} 关系查询 关联减项金额 关联关系 {}", typeId, associatedAdditionTypeIdList);
                subtractDto1 = getAccountBalanceDTOByAssociatedAdditionTypeIdList(billMemberList, associatedAdditionTypeIdList);
                if (null != subtractDto1) {
                    log.info("[分账处理] 入参 关联减项金额 {}", subtractDto1);
                    subtractDto.add(subtractDto1);
                }
            }

            //数据库的关联关系
            List<Integer> dBAssociatedAdditionTypeIdList = billItemMember.getDbAssociatedAdditionList();
            AccountBalanceDTO subtractDto2;
            if (null != dBAssociatedAdditionTypeIdList && !dBAssociatedAdditionTypeIdList.isEmpty()) {
                boolean isPayOrderNo = !typeId.equals(ClearConstants.CONSTANT_INTEGER_83);
                log.info("[分账处理] 数据库2 {}  关联减项金额 关联关系 {}", typeId, dBAssociatedAdditionTypeIdList);
                subtractDto2 = getDBAccountBalanceDTOByAssociatedAdditionTypeIdList(billItemMember, dBAssociatedAdditionTypeIdList, isPayOrderNo);
                if (null != subtractDto2) {
                    log.info("[分账处理] 数据库2 关联减项金额 {} {}", dBAssociatedAdditionTypeIdList, subtractDto2);
                    subtractDto.add(subtractDto2);
                }
            }

            if (subtractDto.getTotalAmountAbs().compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
                log.info("[分账处理] 最终 关联减项金额 {}", subtractDto);
            }
            if (isPlatformFee) {
                accountBalanceDTO = equalRatiosAccount2(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), addAmount);
            } else if (isRefund && !payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
                accountBalanceDTO = reverseIncomeFromExpenditure1(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), addAmount, subtractDto);
            } else if (isRefund) {
                //是退款 又是 求购退款的
                accountBalanceDTO = reverseIncomeFromPurchaseAccountRefund(relationAccountBalanceDTO, addAmount, subtractDto);
            } else {
                accountBalanceDTO = reverseIncomeFromExpenditure2(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), addAmount, subtractDto);
                if (payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
                    accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer1(accountBalanceDTO.getBalance1()).purchaseBalanceTransfer2(accountBalanceDTO.getBalance2()).build();
                }
            }
            return accountBalanceDTO;
        } else {
            if (billItemMember.getIsLeaseOrder().equals(Boolean.TRUE) && !billItemMember.getDirectionEnum().equals(DirectionEnum.ADDITION)) {
                if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
                    return AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                } else if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
                    return AccountBalanceDTO.builder().balance2(billItemMember.getAmount()).build();
                } else {
                    //默认自有账户
                    return AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                }
            } else {
                log.info("[分账处理] 关系查询 分账明细DB数据 未找到资金记录，尝试从入参里面找 TypeId:{} 关系:{}", billItemMember.getTypeId(), typeIdList);
                return null;
            }
        }
    }


    AccountBalanceDTO findDBRelationList(BillItemMember billItemMember, Boolean flag) {
        log.info("[分账处理] 关系查询 分账明细DB数据 ， TypeId:{}", billItemMember.getTypeId());
        //分账明细DB数据
        List<Integer> typeIdList = billItemMember.getTypeRelationList();
        log.info("[分账处理] 关系查询 分账明细DB数据 ， TypeId:{} 关系:{}", billItemMember.getTypeId(), typeIdList);

        List<UserAccountRecordMember> userAccountRecordData = findUserAccountRecordData(billItemMember, typeIdList, flag, true);
        if (null != userAccountRecordData && !userAccountRecordData.isEmpty()) {
            return getAccountBalanceDTO(userAccountRecordData);
        }
        return null;
    }

    /**
     * 分账明细DB数据查询
     */
    List<UserAccountRecordMember> findUserAccountRecordData(BillItemMember billItemMember, List<Integer> typeIdList, Boolean flag, Boolean isPayOrderNo) {
        if (null == typeIdList || typeIdList.isEmpty()) {
            return List.of();
        }
        String orderNo = billItemMember.getOrderNo();
        String serialNo = billItemMember.getSerialNo();
        String payOrderNo = billItemMember.getPayOrderNo();
        Long userAssetsRecordId = billItemMember.getUserAssetsRecordId();

        List<UserAccountRecordMember> userAccountRecordMemberDBList = userAccountRecordGateway.getUserAccountRecordByOrderNoOrPayOrderNo(orderNo, payOrderNo);
        if (null == userAccountRecordMemberDBList || userAccountRecordMemberDBList.isEmpty()) {
            log.info("[分账处理] 关系查询 分账明细DB数据 ， TypeId:{} 关系:{} 未找到资金记录 {} {}", billItemMember.getTypeId(), typeIdList, orderNo, payOrderNo);
            return List.of();
        }
        List<UserAccountRecordMember> newuserAccountRecordMemberDBList = userAccountRecordMemberDBList.stream()
                .filter(userAccountRecordMember -> typeIdList.contains(userAccountRecordMember.getTypeId())).collect(Collectors.toList());

        boolean anyMatch_10 = newuserAccountRecordMemberDBList.stream().anyMatch(userAccountRecordMember -> userAccountRecordMember.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_10));
        if (anyMatch_10) {
            isPayOrderNo = false;
        }

        if (null != serialNo && !serialNo.isBlank()) {
            //判断一下是否存在相同的 流水号的数据
            List<UserAccountRecordMember> userAccountRecordMemberDBList2 = newuserAccountRecordMemberDBList.stream().filter(userAssetsRecordDTO -> serialNo.equals(userAssetsRecordDTO.getSerialNo())).collect(Collectors.toList());
            if (!userAccountRecordMemberDBList2.isEmpty()) {
                log.info("[分账处理] 关系查询 分账明细DB数据 ， TypeId:{} 关系:{} 存在相同流水号", billItemMember.getTypeId(), userAccountRecordMemberDBList2);
                return userAccountRecordMemberDBList2;
            }
        }
        if (isPayOrderNo && null != payOrderNo && !payOrderNo.isBlank()) {
            //判断一下是否存在相同的 支付单号
            List<UserAccountRecordMember> userAccountRecordMemberDBList3 = newuserAccountRecordMemberDBList.stream().filter(userAssetsRecordDTO -> payOrderNo.equals(userAssetsRecordDTO.getPayOrderNo())).collect(Collectors.toList());
            if (!userAccountRecordMemberDBList3.isEmpty()) {
                log.info("[分账处理] 关系查询 分账明细DB数据 ， TypeId:{} 关系:{} 存在相同支付单号", billItemMember.getTypeId(), userAccountRecordMemberDBList3);
                return userAccountRecordMemberDBList3;
            }
        }
        //剔除自身
        newuserAccountRecordMemberDBList = newuserAccountRecordMemberDBList.stream().filter(userAssetsRecordDTO -> !userAssetsRecordDTO.getUserAssetsRecordId().equals(userAssetsRecordId)).collect(Collectors.toList());
        //再查查询一下防止出错
        if (!typeIdList.contains(ClearConstants.CONSTANT_INTEGER_212) && !typeIdList.contains(ClearConstants.CONSTANT_INTEGER_4)) {

            for (Iterator<UserAccountRecordMember> iterator = newuserAccountRecordMemberDBList.iterator(); iterator.hasNext(); ) {
                UserAccountRecordMember newuserAccountRecordMember = iterator.next();
                long count = userAccountRecordMemberDBList.stream().filter(userAccountRecordMember -> billItemMember.getTypeId().equals(userAccountRecordMember.getTypeId()))
                        .filter(userAccountRecordMember -> {
                            if (newuserAccountRecordMember.getPayOrderNo() != null) {
                                return newuserAccountRecordMember.getPayOrderNo().equals(billItemMember.getPayOrderNo());
                            }
                            return false;
                        }).count();
                if (count > 0) {
                    iterator.remove();
                }
            }
        }
        //是否强校验
        if (flag) {
            return null;
        }
        return newuserAccountRecordMemberDBList;
    }


    /**
     * 反转金额类型
     */
    AccountBalanceDTO accountBalanceAbs(AccountBalanceDTO accountBalanceDTO) {
        AccountBalanceDTO.AccountBalanceDTOBuilder builder = AccountBalanceDTO.builder();
        builder.balance1(accountBalanceDTO.getBalance1().abs());
        builder.balance2(accountBalanceDTO.getBalance2().abs());
        builder.purchaseBalanceRecharge1(accountBalanceDTO.getPurchaseBalanceRecharge1().abs());
        builder.purchaseBalanceRecharge2(accountBalanceDTO.getPurchaseBalanceRecharge2().abs());
        builder.purchaseBalanceTransfer1(accountBalanceDTO.getPurchaseBalanceTransfer1().abs());
        builder.purchaseBalanceTransfer2(accountBalanceDTO.getPurchaseBalanceTransfer2().abs());
        return builder.build();
    }


    /**
     * 反转金额类型为负数
     */
    static AccountBalanceDTO reverseAccountBalanceNegate(AccountBalanceDTO accountBalanceDTO) {
        AccountBalanceDTO.AccountBalanceDTOBuilder builder = AccountBalanceDTO.builder();
        builder.balance1(accountBalanceDTO.getBalance1().negate());
        builder.balance2(accountBalanceDTO.getBalance2().negate());
        builder.purchaseBalanceRecharge1(accountBalanceDTO.getPurchaseBalanceRecharge1().negate());
        builder.purchaseBalanceRecharge2(accountBalanceDTO.getPurchaseBalanceRecharge2().negate());
        builder.purchaseBalanceTransfer1(accountBalanceDTO.getPurchaseBalanceTransfer1().negate());
        builder.purchaseBalanceTransfer2(accountBalanceDTO.getPurchaseBalanceTransfer2().negate());
        return builder.build();
    }




}
