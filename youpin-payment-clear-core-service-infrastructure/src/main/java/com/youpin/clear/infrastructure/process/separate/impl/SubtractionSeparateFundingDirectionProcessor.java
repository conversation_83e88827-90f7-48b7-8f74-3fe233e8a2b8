package com.youpin.clear.infrastructure.process.separate.impl;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 支出
 */
@Slf4j
@Service
public class SubtractionSeparateFundingDirectionProcessor extends DefaultSeparateFundingDirectionProcessor {


    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.SUBTRACTION);
    }

    @Override
    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        AccountAggregate accountAggregate = billMember.getAccountAggregate();
        //资金分账抽象
        BillItemMember billItemMember = billMember.getBillItemMember();

        DoNetPayChannelEnum doNetPayChannelEnum = billItemMember.getPayChannelEnum();
        //资金类型
        Integer typeId = billItemMember.getTypeId();
        //是否平台出金
        boolean isPlatformOut = billItemMember.getAssetsTypeEnum().equals(AssetsTypeEnum.PLATFORM_OUT);
        //是否手续费
        boolean isPlatformFee = billItemMember.getAssetsTypeEnum().equals(AssetsTypeEnum.PLATFORM_FEE);

        List<UserAccountRecordMember> userAccountRecordMemberList;
        AccountBalanceDTO accountBalanceDTO;
        if (Objects.equals(typeId, ClearConstants.CONSTANT_INTEGER_169)
                || Objects.equals(typeId, ClearConstants.CONSTANT_INTEGER_232)
                || Objects.equals(typeId, ClearConstants.CONSTANT_INTEGER_230)
                || Objects.equals(typeId, UserAssetsTypeEnum.TYPE_249.getTypeId())
                || Objects.equals(typeId, UserAssetsTypeEnum.TYPE_254.getTypeId())) {
            accountBalanceDTO = findRelationList2(billItemMember, billMemberList);
        } else {
            switch (doNetPayChannelEnum) {
                case Balance:
                    if (typeId.equals(ClearConstants.CONSTANT_INTEGER_221)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                        break;
                    }
                    //资金平台户需要默认余额1
                    //服务费需要等比计算
                    //其余支出默认余额2
                    if (isPlatformOut) {
                        accountBalanceDTO = subtractionAccount1(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1));
                    } else if (isPlatformFee) {
                        //关系查找
                        if (typeId.equals(ClearConstants.CONSTANT_INTEGER_181)) {
                            log.warn("[分账处理] 181   入参里面找 5 就好  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), typeId);
                            billMember.setIsSuccess(Boolean.FALSE);
                            return;
                        }
                        accountBalanceDTO = findDBRelationList(billItemMember, Boolean.TRUE);
                        if (null == accountBalanceDTO) {
                            log.warn("[分账处理] 分账关系金额计算失败  尝试需要从入参里面找  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), typeId);
                            billMember.setIsSuccess(Boolean.FALSE);
                            return;
                        }
                        accountBalanceDTO = equalRatiosAccount2(accountBalanceDTO.getBalance1(), accountBalanceDTO.getBalance2(), billItemMember.getAmount());
                    } else {
                        accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2));
                    }
                    break;
                case PurchaseBalance:
                    if (typeId.equals(ClearConstants.CONSTANT_INTEGER_100)) {
                        accountBalanceDTO = subtractionPurchaseAccount2transfer(billItemMember.getAmount(), accountAggregate);
                    } else {
                        accountBalanceDTO = subtractionPurchaseRechargeAccount2(billItemMember.getAmount(), accountAggregate);
                    }
                    break;
                default:
                    //渠道
                    accountBalanceDTO = subtractionChannelAccount(billItemMember);
                    break;
            }
        }

        //金额校验
        if (accountBalanceDTO.getTotalAmountAbs().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
            log.error("[分账处理] 金额校验失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 计算金额:{} 账单金额:{}", billItemMember.getUserId(),
                    billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), typeId,
                    accountBalanceDTO.getTotalAmountAbs(), billItemMember.getAmount());
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }

//         userAccountRecordMemberList 过滤 balanceAfter 是否为负数 ---兜底
        if (doNetPayChannelEnum.equals(DoNetPayChannelEnum.Balance) && accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) > 0 && accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2).getBalance().compareTo(accountBalanceDTO.getBalance2()) < 0) {
            accountBalanceDTO = AccountBalanceDTO.builder().balance1(accountBalanceDTO.getBalance1().add(accountBalanceDTO.getBalance2().abs())).build();
        }

        userAccountRecordMemberList = toCreateUserAccountRecordMember(accountBalanceDTO, billItemMember, accountAggregate, billItemMember.getPayChannelEnum());

        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
        billMember.setIsSuccess(Boolean.TRUE);

    }

    /**
     * 扣减 1
     */
    static AccountBalanceDTO subtractionAccount1(BigDecimal amount, AccountInfoMember accountAmount) {
        // 输入验证
        if (amount == null || accountAmount == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        BigDecimal balance = accountAmount.getBalance();
        if (balance == null) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        // 计算绝对值
        BigDecimal absAmount = amount.abs();
        BigDecimal diff1 = subtraction(balance, absAmount);
        BigDecimal diff2 = absAmount.subtract(diff1.abs());
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        accountBalanceDTO.setBalance2(diff2);
        accountBalanceDTO.setBalance1(diff1);
        return accountBalanceDTO;
    }

    /**
     * 渠道扣减
     */
    static AccountBalanceDTO subtractionChannelAccount(BillItemMember billItemMember) {
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
            accountBalanceDTO.setBalance1(billItemMember.getAmount());
        }
        if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
            accountBalanceDTO.setBalance2(billItemMember.getAmount());
        }
        if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.DEFAULT)) {
            log.warn("[分账处理] 支出 渠道扣减 收款方类型为默认 默认为余额1 用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId());
            accountBalanceDTO.setBalance1(billItemMember.getAmount());
        }
        return accountBalanceDTO;
    }
}
