package com.youpin.clear.infrastructure.process.financial.v2.impl;

import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 提现 -
 */
@Slf4j
@Service
public class UU898CashWithdrawalFundingDirectionV2Processor extends UU898DefaultFundingDirectionV2 {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.CASH_WITHDRAWAL);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        //数据状态
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());
        UserAssetsTypeEnum userAssetsTypeEnum = UserAssetsTypeEnum.getByTypeCode(dto.getTypeId());
        if (userAssetsTypeEnum.equals(UserAssetsTypeEnum.TYPE_2)) {
            //校验可提现金额减少
            checkWithdrawMoneySubtract(dto, userAssetsInfoDTO);
            //余额提现
            balanceWithdrawal(netStatusEnum, dto, userAssetsInfoDTO);
        } else if (userAssetsTypeEnum.equals(UserAssetsTypeEnum.TYPE_44)) {
            //求购提现
            log.info("[账务交易][求购提现] dto:{}", dto);
            purchaseWithdrawal(netStatusEnum, dto, userAssetsInfoDTO);
        }
    }

    private void purchaseWithdrawal(NetStatusEnum netStatusEnum, AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        boolean isAccountUpdate = false;
        boolean assetsRecordUpdateOrInsert = false;
        BigDecimal changeMoney = dto.getChangeMoney();
        BigDecimal afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney();
        dto.setAttr(UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode());
        dto.setAssetType(UserAssetsRecordAssetTypeEnum.PurchaseMoney.getCode());
        switch (netStatusEnum) {
            case PROCESSING:
                isAccountUpdate = true;
                assetsRecordUpdateOrInsert = true;
                //增加一个判断 求购总金额 - 提现金额 < 转入金额 则不允许提现
                if (changeMoney.compareTo(BigDecimal.ZERO) > 0 && userAssetsInfoDTO.getPurchaseMoney().subtract(changeMoney).compareTo(userAssetsInfoDTO.getPurchaseMoneyFromMoney()) < 0) {
                    log.error("[账务交易][求购提现] 提现金额大于求购可用提现金额 userId:{} 用户求购总金额:{} 转入金额:{} 可提现金额:{} 提现金额:{} ", dto.getUserId(), userAssetsInfoDTO.getPurchaseMoney(), userAssetsInfoDTO.getPurchaseMoneyFromMoney(), userAssetsInfoDTO.getPurchaseMoney().subtract(userAssetsInfoDTO.getPurchaseMoneyFromMoney()), changeMoney);
                    throw new PaymentClearBusinessException(ErrorCode.PURCHASE_MONEY_GREATER_AVAILABLE_MONEY_ERROR);
                }
                afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney().subtract(changeMoney);
                setBalanceDto(dto, userAssetsInfoDTO.getMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getMoney());
                setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), changeMoney.negate(), afterPurchaseMoney);
                setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
                break;
            case SUCCESS:
                //兜底逻辑:如果订单不存在,直接发送成功处理
                if (null == dto.getId()) {
                    isAccountUpdate = true;
                    assetsRecordUpdateOrInsert = true;
                    afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney().subtract(changeMoney);
                    setBalanceDto(dto, userAssetsInfoDTO.getMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getMoney());
                    setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), changeMoney.negate(), afterPurchaseMoney);
                    setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
                }
                break;
            case FAIL:
                break;
            default:
                log.error("[账务交易][求购提现] 暂无提现失败逻辑处理");
                throw new PaymentClearBusinessException(ErrorCode.NOT_SUPPORT_LOGIC_ERROR);
        }
        insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
        updateUserPurchaseBalance(dto.getId(), userAssetsInfoDTO, afterPurchaseMoney, userAssetsInfoDTO.getBlockMoney(), isAccountUpdate, dto.checkAccountBufferBookkeeping());
    }

    void balanceWithdrawal(NetStatusEnum netStatusEnum, AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        boolean isAccountUpdate = false;
        boolean assetsRecordUpdateOrInsert = false;
        BigDecimal changeMoney = dto.getChangeMoney();
        BigDecimal afterMoney = userAssetsInfoDTO.getMoney();
        dto.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
        dto.setAssetType(UserAssetsRecordAssetTypeEnum.Balance.getCode());
        switch (netStatusEnum) {
            case PROCESSING:
                isAccountUpdate = true;
                assetsRecordUpdateOrInsert = true;
                afterMoney = userAssetsInfoDTO.getMoney().subtract(changeMoney);
                setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney.negate(), afterMoney);
                setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
                setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
                break;
            case SUCCESS:
                //兜底逻辑:如果订单不存在,直接发送成功处理
                if (null == dto.getId()) {
                    isAccountUpdate = true;
                    assetsRecordUpdateOrInsert = true;
                    afterMoney = userAssetsInfoDTO.getMoney().subtract(changeMoney);
                    setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney.negate(), afterMoney);
                    setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
                    setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
                }
                break;
            case FAIL:
                break;
            default:
                throw new PaymentClearBusinessException(ErrorCode.NOT_SUPPORT_LOGIC_ERROR);
        }
        insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
        updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, userAssetsInfoDTO.getBlockMoney(), isAccountUpdate, dto.checkAccountBufferBookkeeping());
    }


}

