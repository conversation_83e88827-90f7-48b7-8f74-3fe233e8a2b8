package com.youpin.clear.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 钉钉消息配置
 */
@Component
@RefreshScope
@Data
@ConfigurationProperties(prefix = "ali.dingtalk")
public class DingTalkMapParamsConfig {

    /**
     * 钉钉消息配置
     */
    private Map<String, DingTalkConfig> configs;
}
