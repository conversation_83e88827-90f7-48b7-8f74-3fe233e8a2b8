package com.youpin.clear.infrastructure.process.separate.impl;


import com.youpin.clear.common.enums.AssetsTypeEnum;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 入参查找
 */
@Slf4j
@Service
public class ParamFindDirectionProcessorSeparate extends DefaultSeparateFundingDirectionProcessor {

    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.PARAM_FIND);
    }

    @Override
    public void doProcess(List<BillMember> billMemberList) {
        //循环判断是否需要查找自身
        for (BillMember billMember : billMemberList) {
            //是否分账成功
            if (Boolean.TRUE.equals(billMember.getIsSuccess())) {
                continue;
            }
            //查找入参
            BillItemMember billItemMember = billMember.getBillItemMember();

            DoNetPayChannelEnum payChannelEnum = billItemMember.getPayChannelEnum();
            //关系
            List<Integer> typeRelationList = billItemMember.getTypeRelationList();
            if (null == typeRelationList || typeRelationList.isEmpty()) {
                log.warn("[分账处理] 入参查找 映射关系为空 用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getAssetsName() + ":" + billItemMember.getTypeId());
                continue;
            }
            log.info("[分账处理] 入参查找 用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 关系:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId(), typeRelationList);

            //关系ID
            List<UserAccountRecordMember> userAccountRecordDataLists = findBillMemberList(billMemberList, typeRelationList);

            if (null != userAccountRecordDataLists && !userAccountRecordDataLists.isEmpty()) {
                //关系日志
                printRelationDBLog(billItemMember, userAccountRecordDataLists);
                //数据映射
                billItemMemberDataMapping(billMember, userAccountRecordDataLists, billMemberList);


                billMember.setIsSuccess(Boolean.TRUE);
            } else {
                AccountBalanceDTO accountBalanceDTO;
                if (payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
                    if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer1(billItemMember.getAmount()).build();
                    } else if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer2(billItemMember.getAmount()).build();
                    } else {
                        accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer1(billItemMember.getAmount()).build();
                    }
                } else {
                    if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                    } else if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance2(billItemMember.getAmount()).build();
                    } else {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                    }
                }
                List<UserAccountRecordMember> userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTO, billMember.getBillItemMember(), billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());
                billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
                billMember.setIsSuccess(Boolean.TRUE);
            }
        }
    }

    void billItemMemberDataMapping(BillMember billMember, List<UserAccountRecordMember> userAccountRecordDataLists, List<BillMember> billMemberList) {

        BillItemMember billItemMember = billMember.getBillItemMember();

        DoNetPayChannelEnum payChannelEnum = billItemMember.getPayChannelEnum();
        //是否手续费
        boolean isPlatformFee = billItemMember.getAssetsTypeEnum().equals(AssetsTypeEnum.PLATFORM_FEE);
        //是否退款类型
        boolean isRefund = billItemMember.getAssetsTypeEnum().equals(AssetsTypeEnum.REFUND);

        //获取关系金额
        AccountBalanceDTO relationAccountBalanceDTO;
        AccountBalanceDTO accountBalanceDTO;
        if (isRefund && payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
            relationAccountBalanceDTO = getAccountBalancePurchaseDTO(userAccountRecordDataLists);
        } else {
            relationAccountBalanceDTO = getAccountBalanceDTO(userAccountRecordDataLists);
        }

        List<Integer> associatedAdditionTypeIdList = billItemMember.getAssociatedAdditionList();

        AccountBalanceDTO subtractDto = null;
        if (null != associatedAdditionTypeIdList && !associatedAdditionTypeIdList.isEmpty()) {
            subtractDto = getAccountBalanceDTOByAssociatedAdditionTypeIdList(billMemberList, associatedAdditionTypeIdList);
        }
        if (null != subtractDto) {
            log.info("[分账处理] 入参查找 关联减项金额 {}", subtractDto);
        }

        if (isPlatformFee) {
            accountBalanceDTO = equalRatiosAccount2(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), billItemMember.getAmount());
        } else if (isRefund && !payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
            accountBalanceDTO = reverseIncomeFromExpenditure1(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), billItemMember.getAmount(), subtractDto);
        } else if (isRefund) {
            //是退款 又是 求购退款的
            accountBalanceDTO = reverseIncomeFromPurchaseAccountRefund(relationAccountBalanceDTO, billItemMember.getAmount(), subtractDto);
        } else {
            accountBalanceDTO = reverseIncomeFromExpenditure2(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), billItemMember.getAmount(), subtractDto);
            if (payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
                accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer1(accountBalanceDTO.getBalance1()).purchaseBalanceTransfer2(accountBalanceDTO.getBalance2()).build();
            }
        }

        List<UserAccountRecordMember> userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTO, billMember.getBillItemMember(), billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());
        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
    }

    List<UserAccountRecordMember> findBillMemberList(List<BillMember> billMemberList, List<Integer> typeIdList) {
        if (null == billMemberList || billMemberList.isEmpty() || null == typeIdList || typeIdList.isEmpty()) {
            return List.of();
        }
        List<BillMember> billItemMemberRelationList = billMemberList.stream()
                .filter(item -> typeIdList.contains(item.getBillItemMember().getTypeId()))
//                .filter(item -> item.getBillItemMember().getNetStatusEnum().equals(NetStatusEnum.SUCCESS))
                .collect(Collectors.toList());

        if (billItemMemberRelationList.isEmpty()) {
            return List.of();
        }
        List<List<UserAccountRecordMember>> list = new ArrayList<>();
        for (BillMember member : billItemMemberRelationList) {
            List<UserAccountRecordMember> accountRecordMemberList = member.getUserAccountRecordMemberList();
            if (null == accountRecordMemberList || accountRecordMemberList.isEmpty()) {
                continue;
            }
            list.add(accountRecordMemberList);
        }
        return list.stream().flatMap(Collection::stream).collect(Collectors.toList());
    }
}
