package com.youpin.clear.infrastructure.process.separate.impl;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 冻结-减
 */
@Slf4j
@Service
public class FreezeSubtractionSeparateFundingDirectionProcessor extends DefaultSeparateFundingDirectionProcessor {

    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.FREEZE_ADDITION, DirectionEnum.FREEZE_REFUND);
    }

    @Override
    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        DirectionEnum directionEnum = billMember.getDirectionEnum();
        AccountAggregate accountAggregate = billMember.getAccountAggregate();
        BillItemMember billItemMember = billMember.getBillItemMember();
        List<UserAccountRecordMember> userAccountRecordMemberList;
        DoNetPayChannelEnum doNetPayChannelEnum = billItemMember.getPayChannelEnum();
        CollectTypeEnum collectTypeEnum = billItemMember.getCollectTypeEnum();

        AccountBalanceDTO accountBalanceDTO = null;
        //冻结 +   一定是 有减钱的
        if (directionEnum.equals(DirectionEnum.FREEZE_ADDITION)) {
            switch (doNetPayChannelEnum) {
                case Balance:
                    accountBalanceDTO = findRelationList2(billItemMember, billMemberList);
                    if (null == accountBalanceDTO) {
                        if (collectTypeEnum.equals(CollectTypeEnum.OWN)) {
                            accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                        } else if (collectTypeEnum.equals(CollectTypeEnum.SUPERVISION)) {
                            accountBalanceDTO = AccountBalanceDTO.builder().balance2(billItemMember.getAmount()).build();
                        } else if (collectTypeEnum.equals(CollectTypeEnum.DEFAULT)) {
                            accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2));
                        }
                    }
                        AccountInfoMember balance1 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1);
                        AccountInfoMember balance2 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2);

                        if (accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) > 0 && balance2.getBalance().compareTo(accountBalanceDTO.getBalance2()) < 0) {
                            accountBalanceDTO = subtractionAccount1(billItemMember.getAmount(), balance1);
                        }
                        if (accountBalanceDTO.getBalance1().compareTo(BigDecimal.ZERO) > 0 && balance1.getBalance().compareTo(accountBalanceDTO.getBalance1()) < 0) {
                            accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), balance2);
                        }
//                    }
                    break;
                case PurchaseBalance:
                    //暂无
                    break;
                default:
                    //渠道
                    accountBalanceDTO = subtractionChannelAccount(billItemMember);
                    break;
            }
            assert accountBalanceDTO != null;
            accountBalanceDTO.setFrozenBalance1(accountBalanceDTO.getBalance1());
            accountBalanceDTO.setFrozenBalance2(accountBalanceDTO.getBalance2());

        } else if (directionEnum.equals(DirectionEnum.FREEZE_REFUND)) {
            //冻结退还
            switch (doNetPayChannelEnum) {
                case PurchaseBalance:
                    //暂无
                    break;
                case Balance:
                default:
                    accountBalanceDTO = findRelationListFrozen(billItemMember, billMemberList);
                    if (accountBalanceDTO == null) {
                        accountBalanceDTO = subtractionAccount1(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1));
                    }
                        AccountInfoMember balance1 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1);
                        AccountInfoMember balance2 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2);

                        if (accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) > 0 && balance2.getBalance().compareTo(accountBalanceDTO.getBalance2()) < 0) {
                            accountBalanceDTO = subtractionAccount1(billItemMember.getAmount(), balance1);
                        }
                        if (accountBalanceDTO.getBalance1().compareTo(BigDecimal.ZERO) > 0 && balance1.getBalance().compareTo(accountBalanceDTO.getBalance1()) < 0) {
                            accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), balance2);
                        }
                    accountBalanceDTO.setFrozenBalance1(accountBalanceDTO.getBalance1());
                    accountBalanceDTO.setFrozenBalance2(accountBalanceDTO.getBalance2());
                    break;
            }
            //账户金额校验
            assert accountBalanceDTO != null;
            checkBillAmountAccount(accountBalanceDTO, billMember.getAccountAggregate());

        }
        checkBillAmount(accountBalanceDTO, billItemMember);

        billMember.setIsSuccess(Boolean.TRUE);
        assert accountBalanceDTO != null;
        userAccountRecordMemberList = toCreateUserAccountRecordMember(accountBalanceDTO, billItemMember, billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());
        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);

    }

    private void checkBillAmountAccount(AccountBalanceDTO accountBalanceDTO, AccountAggregate accountAggregate) {
        if (accountBalanceDTO.getBalance1().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0
                || accountBalanceDTO.getFrozenBalance1().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1);
            BigDecimal subtract1 = accountInfoMember.getFrozenBalance().subtract(accountBalanceDTO.getFrozenBalance1());
            if (subtract1.compareTo(BigDecimal.ZERO) < 0) {
                accountBalanceDTO.setFrozenBalance1(accountBalanceDTO.getFrozenBalance1().subtract(subtract1.abs()));
                accountBalanceDTO.setFrozenBalance2(accountBalanceDTO.getFrozenBalance2().add(subtract1.abs()));
                accountBalanceDTO.setBalance1(accountBalanceDTO.getBalance1().subtract(subtract1.abs()));
                accountBalanceDTO.setBalance2(accountBalanceDTO.getBalance2().add(subtract1.abs()));
            }
        }
        if (accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0
                || accountBalanceDTO.getFrozenBalance2().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
            AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2);
            BigDecimal subtract2 = accountInfoMember.getFrozenBalance().subtract(accountBalanceDTO.getFrozenBalance2());
            if (subtract2.compareTo(BigDecimal.ZERO) < 0) {
                accountBalanceDTO.setFrozenBalance1(accountBalanceDTO.getFrozenBalance1().add(subtract2.abs()));
                accountBalanceDTO.setFrozenBalance2(accountBalanceDTO.getFrozenBalance2().subtract(subtract2.abs()));
                accountBalanceDTO.setBalance1(accountBalanceDTO.getBalance1().add(subtract2.abs()));
                accountBalanceDTO.setBalance2(accountBalanceDTO.getBalance2().subtract(subtract2.abs()));
            }
        }
    }

    AccountBalanceDTO findRelationListFrozen(BillItemMember billItemMember, List<BillMember> billMemberList) {
        //分账明细DB数据
        List<Integer> typeIdList = billItemMember.getTypeRelationList();
        if (null == typeIdList || typeIdList.isEmpty()) {
            log.warn("[分账处理] 映射关系为空  关系查询失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId());
            return null;
        }
        //需要变动的金额
        BigDecimal addAmount = billItemMember.getAmount();

        List<UserAccountRecordMember> userAccountRecordData = findBillMemberListTo(billMemberList, typeIdList);
        if (null == userAccountRecordData || userAccountRecordData.isEmpty()) {
            userAccountRecordData = findUserAccountRecordData(billItemMember, typeIdList, Boolean.FALSE, true);
        }

        // 入参里面的关联关系
        List<Integer> associatedAdditionTypeIdList = billItemMember.getAssociatedAdditionList();
        AccountBalanceDTO subtractDto = new AccountBalanceDTO();
        AccountBalanceDTO subtractDto1;
        if (null != associatedAdditionTypeIdList && !associatedAdditionTypeIdList.isEmpty()) {
            log.info("[分账处理] typeId {} 关系查询 关联减项金额 关联关系 {}", billItemMember.getTypeId(), associatedAdditionTypeIdList.size());
            subtractDto1 = getAccountBalanceDTOByAssociatedAdditionTypeIdList(billMemberList, associatedAdditionTypeIdList);
            if (null != subtractDto1) {
                log.info("[分账处理] 入参 关联减项金额 {}", subtractDto1);
                subtractDto.add(subtractDto1);
            }
        }

        //数据库的关联关系
        List<Integer> dBAssociatedAdditionTypeIdList = billItemMember.getDbAssociatedAdditionList();
        AccountBalanceDTO subtractDto2;
        if (null != dBAssociatedAdditionTypeIdList && !dBAssociatedAdditionTypeIdList.isEmpty()) {
            subtractDto2 = getDBAccountBalanceDTOByAssociatedAdditionTypeIdList(billItemMember, dBAssociatedAdditionTypeIdList, false);
            if (null != subtractDto2) {
                log.info("[分账处理] 数据库 关联减项金额 {} {}", dBAssociatedAdditionTypeIdList,subtractDto2);
                subtractDto.add(subtractDto2);
            }
        }

        if (subtractDto.getTotalAmountAbs().compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            log.info("[分账处理] 最终 关联减项金额 {}", subtractDto);
        }

        if (!userAccountRecordData.isEmpty()) {
            log.info("[分账处理] 关系查询 分账明细DB数据 {} {} {}", billItemMember.getTypeId(), billItemMember.getOrderNo(), billItemMember.getPayOrderNo());
            printRelationDBLog(billItemMember, userAccountRecordData);
            AccountBalanceDTO relationAccountBalanceDTO = getAccountFrozenBalanceDTO(userAccountRecordData);
            return reverseFrozenIncomeFromExpenditure1(relationAccountBalanceDTO.getBalance1(), relationAccountBalanceDTO.getBalance2(), addAmount, subtractDto);
        }
        return null;
    }


    static AccountBalanceDTO reverseFrozenIncomeFromExpenditure1(BigDecimal balance1, BigDecimal balance2, BigDecimal addAmount, AccountBalanceDTO subtractDto) {
        if (subtractDto != null) {
            if (balance1.subtract(subtractDto.getBalance1()).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0 && balance2.subtract(subtractDto.getBalance2()).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                balance1 = balance1.subtract(subtractDto.getBalance1());
                balance2 = balance2.subtract(subtractDto.getBalance2());
            }
        }
        BigDecimal diff1 = BigDecimal.ZERO;
        BigDecimal diff2;
        if (balance1.compareTo(BigDecimal.ZERO) > ClearConstants.CONSTANT_INTEGER_0) {
            if (addAmount.subtract(balance1).compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INTEGER_0) {
                diff1 = balance1;
            } else {
                diff1 = addAmount;
            }
        }
        diff2 = addAmount.subtract(diff1);
        return AccountBalanceDTO.builder().balance2(diff2).balance1(diff1).build();
    }


    static AccountBalanceDTO getAccountFrozenBalanceDTO(List<UserAccountRecordMember> userAccountRecordData) {

        Map<Integer, BigDecimal> balanceMap = userAccountRecordData.stream()
                .collect(Collectors.toMap(UserAccountRecordMember::getAccountType, userAccountRecordDatum -> userAccountRecordDatum.getFrozenBalanceChange() == null ? BigDecimal.ZERO : userAccountRecordDatum.getFrozenBalanceChange().abs(), BigDecimal::add));

        BigDecimal balance1 = calculateBalance(balanceMap, AccountTypeEnum.BALANCE_1.getCode(), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode(), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode());

        BigDecimal balance2 = calculateBalance(balanceMap, AccountTypeEnum.BALANCE_2.getCode(), AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode(), AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode());


        return AccountBalanceDTO.builder().balance2(balance2).balance1(balance1).build();
    }


    /**
     * 渠道扣减
     */
    static AccountBalanceDTO subtractionChannelAccount(BillItemMember billItemMember) {
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
            accountBalanceDTO.setBalance1(billItemMember.getAmount());
        }
        if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
            accountBalanceDTO.setBalance2(billItemMember.getAmount());
        }
        if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.DEFAULT)) {
            log.warn("[分账处理] 支出 渠道扣减 收款方类型为默认 默认为余额1 用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId());
            accountBalanceDTO.setBalance1(billItemMember.getAmount());
        }
        return accountBalanceDTO;
    }


}
