package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: transaction_service_fee_statement_record
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionServiceFeeStatementRecord implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细类型ID
     */
    private Integer typeId;

    /**
     * 资金明细唯一单号
     */
    private String treadNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 流水批次号
     */
    private String serialNo;

    /**
     * 订单费用类型:0,非预售,1,预售
     */
    private Integer orderFeeType;

    /**
     * 服务费
     */
    private BigDecimal feeMoney;

    /**
     * 状态，0.失败，1.成功，2.进行中
     */
    private Integer status;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}