package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeStatementRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TransactionServiceFeeStatementRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TransactionServiceFeeStatementRecord row);

    int insertSelective(TransactionServiceFeeStatementRecord row);

    TransactionServiceFeeStatementRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TransactionServiceFeeStatementRecord row);

    int updateByPrimaryKey(TransactionServiceFeeStatementRecord row);
}