package com.youpin.clear.infrastructure.process.calculate.impl;

import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Slf4j
@Service
public class SubtractionAccountCalculateProcessor extends DefaultAccountCalculateProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.SUBTRACTION);
    }

    @Override
    public void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember) {
        assert accountAggregate != null;
        BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();
        BigDecimal frozenBalanceChange = userAccountRecordMember.getFrozenBalanceChange();
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()));
        long lastAccountRecordId;
        Integer typeId = userAccountRecordMember.getTypeId();
        //------------------------------------------------------------------------------------------------------------------------------------
        //改成负数
        balanceChange = balanceChange.negate();
        userAccountRecordMember.setBalanceChange(balanceChange);
        lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
        if (userAccountRecordMember.isBalanceOrPurchase()) {
            updateAccountBalance("减", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
            if (userAccountRecordMember.isBalance()) {
                publicBalance.setBalance(publicBalance.getBalance().add(balanceChange));
            } else {
                publicBalance.setPurchaseBalance(publicBalance.getPurchaseBalance().add(balanceChange));
            }
        } else {
            log.info("[分账处理] {} {} 渠道金额不进行 减", typeId, DoNetPayChannelEnum.getByPayChannel(userAccountRecordMember.getPayChannel()));
        }
        //------------------------------------------------------------------------------------------------------------------------------------
    }
}
