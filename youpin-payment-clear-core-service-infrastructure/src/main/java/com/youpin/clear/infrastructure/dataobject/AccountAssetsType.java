package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: account_assets_type
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountAssetsType implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 资金code
     */
    private Integer assetsCode;

    /**
     * 资金名字
     */
    private String assetsName;

    /**
     * 资金方向 
     */
    private String direction;

    /**
     * 资金类型
     */
    private String assetsType;

    /**
     * 删除标记 0：有效 1：无效
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}