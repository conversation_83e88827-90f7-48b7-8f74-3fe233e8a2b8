package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AdjustmentApplyDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AdjustmentApplyDetail row);

    int insertSelective(AdjustmentApplyDetail row);

    AdjustmentApplyDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AdjustmentApplyDetail row);

    int updateByPrimaryKey(AdjustmentApplyDetail row);
}