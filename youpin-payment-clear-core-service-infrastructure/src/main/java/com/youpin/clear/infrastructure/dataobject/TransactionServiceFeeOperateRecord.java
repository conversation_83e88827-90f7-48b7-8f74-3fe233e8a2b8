package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: transaction_service_fee_operate_record
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionServiceFeeOperateRecord implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     */
    private Integer operationDate;

    /**
     * 平台户id
     */
    private Long platformUserId;

    /**
     * 资金明细类型ID
     */
    private Integer typeId;

    /**
     * 流水批次号
     */
    private String serialNo;

    /**
     * 订单费用类型:0,非预售,1,预售
     */
    private Integer orderFeeType;

    /**
     * 服务费
     */
    private BigDecimal feeMoney;

    /**
     * 状态，0.失败，1.成功，2.进行中,3.放弃
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}