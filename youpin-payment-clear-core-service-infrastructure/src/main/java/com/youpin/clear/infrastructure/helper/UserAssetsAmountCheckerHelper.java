package com.youpin.clear.infrastructure.helper;


import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsAmountCheckerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class UserAssetsAmountCheckerHelper {


    /**
     * 金额校验汇总
     */
    public static UserAssetsAmountCheckerDTO getUserAssetsAmountCheckerDTO(List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList) {
        if (abstractUserAssetsRecordDTOList == null || abstractUserAssetsRecordDTOList.isEmpty()) {
            log.warn("结算金额校验,结算记录为空");
            return UserAssetsAmountCheckerDTO.builder().build();
        }
        UserAssetsAmountCheckerDTO userAssetsAmountCheckerDTO = new UserAssetsAmountCheckerDTO();
        for (AbstractUserAssetsRecordDTO record : abstractUserAssetsRecordDTOList) {
            final BigDecimal amount = record.getChangeMoney() != null ? record.getChangeMoney() : BigDecimal.ZERO;
            UserAssetsTypeEnum userAssetsTypeEnum = UserAssetsTypeEnum.getByTypeCode(record.getTypeId());
            switch (userAssetsTypeEnum) {
                case TYPE_3:
                case TYPE_228:
                case TYPE_212:
                case TYPE_221:
                    userAssetsAmountCheckerDTO.getPayMoney().add(amount);
                    break;
                case TYPE_5:
                case TYPE_231:
//                case TYPE_215:
//                case TYPE_218:
                case TYPE_213:
                    userAssetsAmountCheckerDTO.getSettlementMoney().add(amount);
                    break;
                case TYPE_248:
                case TYPE_257:
                    userAssetsAmountCheckerDTO.getSettlementFreezeMoney().add(amount);
                    break;
                case TYPE_181:
                    userAssetsAmountCheckerDTO.getServiceCharge().add(amount);
                    break;
                case TYPE_4:
                case TYPE_83:
//                case TYPE_214:
                case TYPE_222:
                    userAssetsAmountCheckerDTO.getRefundMoney().add(amount);
                    break;
                case TYPE_182:
                case TYPE_234:
                    userAssetsAmountCheckerDTO.getServiceChargeRefund().add(amount);
                    break;
                default:
                    log.warn("结算金额校验,未知业务类型,请检查数据,业务类型:{}", record.getTypeId());
                    break;
            }
        }
        return userAssetsAmountCheckerDTO;
    }

    /**
     * 根据自定义顺序对对象列表进行排序
     */
    public static void customSort(List<AbstractUserAssetsRecordDTO> objectList, List<Integer> customOrderList) {
        if (CollectionUtils.isEmpty(objectList) || CollectionUtils.isEmpty(customOrderList)) {
            return;
        }
        // 创建值到索引的映射
        int maxValue = customOrderList.stream().max(Integer::compare).orElse(0);
        int[] orderMap = new int[maxValue + 1];
        Arrays.fill(orderMap, Integer.MAX_VALUE);
        for (int i = 0; i < customOrderList.size(); i++) {
            orderMap[customOrderList.get(i)] = i;
        }
        // 使用反射获取字段值并排序
        objectList.sort((a, b) -> {
            int valueA = a.getTypeId();
            int valueB = b.getTypeId();
            // 根据自定义顺序排序
            // 检查值是否超出数组范围
            int indexA = valueA <= maxValue ? orderMap[valueA] : Integer.MAX_VALUE;
            int indexB = valueB <= maxValue ? orderMap[valueB] : Integer.MAX_VALUE;
            return Integer.compare(indexA, indexB);
        });
    }

}
