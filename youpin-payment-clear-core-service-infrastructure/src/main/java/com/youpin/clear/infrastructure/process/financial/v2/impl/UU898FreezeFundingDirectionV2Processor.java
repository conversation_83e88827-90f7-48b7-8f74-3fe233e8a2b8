package com.youpin.clear.infrastructure.process.financial.v2.impl;

import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.UU898FundingDirectionV2Processor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冻结 3
 */
@Slf4j
@Service
public class UU898FreezeFundingDirectionV2Processor extends UU898DefaultFundingDirectionV2 implements UU898FundingDirectionV2Processor {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.FREEZE);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        //数据状态
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());

        //判断是否需要更新账户数据
        boolean isAccountUpdate = netStatusEnum.equals(NetStatusEnum.SUCCESS);

        /*
          余额冻结 - 变动金额
          冻结金额 + 变动金额
         */
        //变动金额
        BigDecimal changeMoney = dto.getChangeMoney();

        if (dto.isBalanceChange()) {
            //校验可提现金额减少
            checkWithdrawMoneySubtract(dto, userAssetsInfoDTO);

            BigDecimal afterMoney = userAssetsInfoDTO.getMoney().subtract(changeMoney);
            //校验余额
            if (afterMoney.compareTo(BigDecimal.ZERO) < 0) {
                log.error("[账务交易][余额冻结] 余额不足 userId:{} 用户余额:{} 需要支付:{} 结果:{}", dto.getUserId(), userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);
                throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
            }
            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney.negate(), afterMoney);

            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());

            //补充冻结余额信息
            BigDecimal afterBlockMoney = userAssetsInfoDTO.getBlockMoney().add(changeMoney);
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), changeMoney, afterBlockMoney);

            //更新
            insertOrUpdateUserAssetsRecord(dto, true);
            updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, afterBlockMoney, isAccountUpdate, dto.checkAccountBufferBookkeeping());

            //更新对象
            userAssetsInfoDTO.setMoney(afterMoney);
            userAssetsInfoDTO.setBlockMoney(afterBlockMoney);

            ChangeMoneyDTO changeMoneyDTO = uu898UserAccountHelper.getFreezeFundingChangeAmount(dto);
            //处理子账户
            if (isAccountUpdate) {
                // 处理交易子账户
                handleSubAccount(dto, userAssetsInfoDTO.getTradeSubAccountDTO(),changeMoneyDTO.getTradeChangeMoney(), false);
            }

        } else if (dto.isPayChannelChange()) { //只需要记录资金明细
            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney.negate(), userAssetsInfoDTO.getMoney());
            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
            //补充冻结余额信息
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

            //记录资金明细
            insertOrUpdateUserAssetsRecord(dto, true);

        } else {
            log.error("[账务交易][资金冻结] 处理类型未知  dto:{} ", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR);
        }
    }


}
