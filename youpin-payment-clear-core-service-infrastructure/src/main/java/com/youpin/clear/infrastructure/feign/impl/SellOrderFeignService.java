package com.youpin.clear.infrastructure.feign.impl;

import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.model.Result;
import com.uu898.youpin.commons.utils.JacksonUtils;
import com.youpin.trade.order.query.api.OrderQueryService;
import com.youpin.trade.order.query.dto.request.QueryIsPresaleRequest;
import com.youpin.trade.order.query.dto.response.QueryIsPresaleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 出售 api
 */
@Service
@Slf4j
public class SellOrderFeignService {

    @Autowired
    OrderQueryService orderQueryService;


    /**
     * 订单是否预售订单
     * 如果出问题默认不是预售订单
     */
    public Map<String, Integer> sellOrderIsPresale(List<String> orderList) {
        QueryIsPresaleRequest request = new QueryIsPresaleRequest();
        request.setOrderNoList(orderList);
        Result<List<QueryIsPresaleResponse>> result;
        try {
            log.info("[出售API] 订单是否预售订单,request={}", JacksonUtils.writeValueAsString(request));
            result = orderQueryService.isPresale(request);
            log.info("[出售API] 订单是否预售订单,result={}", JacksonUtils.writeValueAsString(result));
        } catch (Exception e) {
            //走默认逻辑
            log.error("[出售API] 订单是否预售订单,异常 request={}", JacksonUtils.writeValueAsString(request), e);
            return Map.of();
        }
        if (Objects.isNull(result) || !Status.OK.getCode().equals(result.getCode())|| Objects.isNull(result.getData())) {
            log.error("[出售API] 订单是否预售订单  状态异常 ,result={}", JacksonUtils.writeValueAsString(result));
            return Map.of();
        }
        Map<String, Integer> resultMap = new HashMap<>();
        for (QueryIsPresaleResponse item : result.getData()) {
            resultMap.put(item.getOrderNo(), item.getIsPresale());
        }
        return resultMap;
    }


}
