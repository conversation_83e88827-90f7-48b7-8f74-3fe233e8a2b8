package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface UserAssetsRecordExtMapper {


    /**
     * 原资金明细幂等判断
     */
    Integer countPkUserAssetsRecordById(@Param("userId") Long userId, @Param("userAssetsRecordId") Long userAssetsRecordId);

    /**
     * 获取数据
     */
    UserAssetsRecord getUserAssetsRecordById(@Param("userId") Long userId, @Param("userAssetsRecordId") Long userAssetsRecordId);

    /**
     * 获取数据
     */
    List<UserAssetsRecord> batchGetUserAssetsRecordById(@Param("userId") Long userId, @Param("minUserAssetsRecordId") Long minUserAssetsRecordId,@Param("maxUserAssetsRecordId") Long maxUserAssetsRecordId);


    /**
     * 幂等判断支付单号关系
     */
    Integer countPkUserAssetsPayOrderNoRelateByPayOrderNo(@Param("userId") Long userId, @Param("payOrderNo") String payOrderNo);

    /**
     * 幂等判断订单号关系
     */
    Integer countPkUserAssetsOrderNoRelateByOrderNo(@Param("userId") Long userId, @Param("orderNo") String orderNo);

    /**
     * 幂等判断流水号关系
     */
    Integer countPkUserAssetsTreadNoRelateByTreadNo(@Param("userId") Long userId, @Param("treadNo") String treadNo);

    Long maxUserAssetsRecordId(@Param("userId") Long userId);


    void updateByPrimaryKeySelectiveByUserId(UserAssetsRecord assetsRecordById);


    /**
     * 获取数据
     */
    List<UserAssetsRecord> selectUserAssetsRecordById(@Param("userId") Long userId,
                                                      @Param("pageIndex") Long pageIndex,
                                                      @Param("pageSize") Integer pageSize,
                                                      @Param("userAssetsRecordId") Long userAssetsRecordId);


}
