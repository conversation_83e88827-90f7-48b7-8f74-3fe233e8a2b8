package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class RechargeBusinessProcessor extends DefaultSubBusFinancialProcessor {

    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(
                //充值-钱包
                SubBusTypeFrontEnum.RECHARGE_WALLET,
                //充值-求购
                SubBusTypeFrontEnum.RECHARGE_BUY,
                //充值-求购发布
                SubBusTypeFrontEnum.RECHARGE_BUY_PUBLISH,
                //充值-求购
                SubBusTypeFrontEnum.FRONT_RECHARGE_BUY,
                //充值-求购发布
                SubBusTypeFrontEnum.FRONT_RECHARGE_BUY_PUBLISH,
                //充值-守约充值
                SubBusTypeFrontEnum.KEEP_RECHARGE);
    }


    @Override
    public void payIng(FinancialProcessorDTO dto) {
        super.payIng(dto);
        rechargePay(dto);
    }

    @Override
    public void paySuccess(FinancialProcessorDTO dto) {
        super.paySuccess(dto);
        rechargePay(dto);
    }

    @Override
    public void payFail(FinancialProcessorDTO dto) {
        super.payFail(dto);
        rechargePay(dto);
    }


    void rechargePay(FinancialProcessorDTO dto) {
        boolean isPurchaseBalanceRecharge = SubBusTypeFrontEnum.RECHARGE_BUY.equals(dto.getSubBusTypeFrontEnum()) || SubBusTypeFrontEnum.RECHARGE_BUY_PUBLISH.equals(dto.getSubBusTypeFrontEnum()) || SubBusTypeFrontEnum.FRONT_RECHARGE_BUY.equals(dto.getSubBusTypeFrontEnum()) || SubBusTypeFrontEnum.FRONT_RECHARGE_BUY_PUBLISH.equals(dto.getSubBusTypeFrontEnum());
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (isPurchaseBalanceRecharge) {
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode());
                abstractUserAssetsRecordDTO.setAssetType(UserAssetsRecordAssetTypeEnum.PurchaseMoney.getCode());
            } else {
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                abstractUserAssetsRecordDTO.setAssetType(UserAssetsRecordAssetTypeEnum.Balance.getCode());
            }
        }
    }
    @Override
    public void refundIng(FinancialProcessorDTO dto) {
        super.refundIng(dto);
        rechargeRefund(dto);
    }

    @Override
    public void refundFail(FinancialProcessorDTO dto) {
        super.refundFail(dto);
        rechargeRefund(dto);
    }

    @Override
    public void refundSuccess(FinancialProcessorDTO dto) {
        super.refundSuccess(dto);
        //充值退款方法
        rechargeRefund(dto);
    }

    private void rechargeRefund(FinancialProcessorDTO dto) {
        //
        boolean isRechargeRefund = SubBusTypeFrontEnum.KEEP_RECHARGE.equals(dto.getSubBusTypeFrontEnum()) || SubBusTypeFrontEnum.RECHARGE_WALLET.equals(dto.getSubBusTypeFrontEnum());
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_4.getTypeId())) {
                if (abstractUserAssetsRecordDTO.getChangeMoney().compareTo(BigDecimal.ZERO) < ClearConstants.CONSTANT_INT_0) {
                    //是余额充值退款 需要增加金额
                    abstractUserAssetsRecordDTO.setIsBalanceRechargeRefund(Boolean.TRUE);
                } else {
                    if (isRechargeRefund) {
                        log(Level.ERROR, "充值退款-金额参数异常 金额 changeMoney :{} ", dto, abstractUserAssetsRecordDTO.getChangeMoney());
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "充值退款-金额参数异常");
                    }
                }
            }
        }
    }
}
