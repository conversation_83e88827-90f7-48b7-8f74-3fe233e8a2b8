package com.youpin.clear.infrastructure.feign;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.infrastructure.feign.request.AccountBalanceNoticeRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(
        name = "${reduction.activity.serverName:}",
        contextId = "reductionActivityFeign"
)
public interface ReductionActivityFeign {

    /**
     * 支付账户余额通知
     */
    @PostMapping("/account/balance/notice")
    Result<Void> accountBalanceNotice(AccountBalanceNoticeRequest request);

}
