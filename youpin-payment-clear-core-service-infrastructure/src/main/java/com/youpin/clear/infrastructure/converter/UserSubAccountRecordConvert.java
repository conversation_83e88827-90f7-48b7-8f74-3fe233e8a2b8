package com.youpin.clear.infrastructure.converter;

import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserSubAccountRecordConvert {

    UserSubAccountRecordConvert MAPPER = Mappers.getMapper(UserSubAccountRecordConvert.class);

    /**
     * convert
     *
     * @param userSubAccountRecord record
     * @return 返回
     */
    UserSubAccountRecordMember toUserSubAccountRecordMember(UserSubAccountRecord userSubAccountRecord);

    /**
     * convert
     *
     * @param userSubAccountRecordMember record
     * @return 返回
     */
    UserSubAccountRecord toUserSubAccountRecord(UserSubAccountRecordMember userSubAccountRecordMember);


    /**
     * convert
     *
     * @param userSubAccountRecordList list
     * @return 返回
     */
    List<UserSubAccountRecordMember> toUserSubAccountRecordMemberList(List<UserSubAccountRecord> userSubAccountRecordList);

    /**
     * convert
     *
     * @param userSubAccountRecordMemberList list
     * @return 返回
     */
    List<UserSubAccountRecord> toUserSubAccountRecordList(List<UserSubAccountRecordMember> userSubAccountRecordMemberList);


}
