package com.youpin.clear.infrastructure.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class DateTimeUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DAY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DAY_FORMATTER_2 = DateTimeFormatter.ofPattern("M月d日");


    public static String timeFormat(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return time.format(FORMATTER);
    }

    public static String dayTimeFormat(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return time.format(DAY_FORMATTER);
    }

    public static String dayTimeFormat2(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return time.format(DAY_FORMATTER_2);
    }





}
