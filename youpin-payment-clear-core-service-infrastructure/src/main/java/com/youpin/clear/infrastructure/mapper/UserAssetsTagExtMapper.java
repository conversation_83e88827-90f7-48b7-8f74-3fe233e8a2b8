package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface UserAssetsTagExtMapper {

    UserAssetsTag getUserAssetsTagByTag(@Param("userId") Long userId, @Param("tagCode") String tagCode);

    List<UserAssetsTag> getUserAssetsTagByUserId(@Param("userId") Long userId);

    void updateByUserId(UserAssetsTag userAssetsTag);

    /**
     * 数值累计
     */
    int updateBigDecimalByUserId(@Param("id") Long id, @Param("userId") Long userId, @Param("tagCode") String tagCode, @Param("changeTagValueDecimal") BigDecimal changeTagValueDecimal);

    List<UserAssetsTag> selectUserAssetsTagByTableName(@Param("tableSuffix") String tableSuffix, @Param("pageIndex") Long pageIndex, @Param("pageSize") Integer pageSize, @Param("minId") Long minId);


}