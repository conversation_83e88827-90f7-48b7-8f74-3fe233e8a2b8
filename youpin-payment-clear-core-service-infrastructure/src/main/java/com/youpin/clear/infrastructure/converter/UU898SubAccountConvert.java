package com.youpin.clear.infrastructure.converter;

import com.youpin.clear.domain.dto.UU898UserSubAccountDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UU898SubAccountConvert {

    UU898SubAccountConvert MAPPER = Mappers.getMapper(UU898SubAccountConvert.class);


    UU898UserSubAccount toUU898UserSubAccount(UU898UserSubAccountDTO dto);


    UU898UserSubAccountDTO toUU898UserSubAccountDTO(UU898UserSubAccount subAccount);


    UU898UserSubAccountFlowRecord toUU898UserSubAccountFlowRecord(UU898UserSubAccountFlowRecordDTO dto);


    UU898UserSubAccountFlowRecordDTO toUU898UserSubAccountFlowRecordDTO(UU898UserSubAccountFlowRecord record);

}
