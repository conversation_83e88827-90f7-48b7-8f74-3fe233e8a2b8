package com.youpin.clear.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountBalanceNoticeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户
     */
    private String account;

    /**
     * 余额
     */
    private BigDecimal balance;
    
}
