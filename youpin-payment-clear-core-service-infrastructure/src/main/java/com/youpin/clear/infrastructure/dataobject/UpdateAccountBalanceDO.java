package com.youpin.clear.infrastructure.dataobject;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAccountBalanceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long accountId;

    private String userAccountNo;

    private Long userId;

    private Integer accountType;

    private BigDecimal originalBalance;

    /**
     * 账户余额变动(不判断正负) - 正数代表增加余额，负数代表减少余额
     */
    private BigDecimal balanceChange;

    private BigDecimal originalFrozenBalance;

    /**
     * 冻结账户余额变动(不判断正负) - 正数代表增加余额，负数代表减少余额
     */
    private BigDecimal frozenBalanceChange;

    private String ext;

    private Long lastAccountRecordId;


    public BigDecimal calculateBalanceChangeAfter() {
        return this.originalBalance.add(this.balanceChange);
    }

    public BigDecimal calculateFrozenBalanceChangeAfter() {
        return this.originalFrozenBalance.add(this.frozenBalanceChange);
    }

}
