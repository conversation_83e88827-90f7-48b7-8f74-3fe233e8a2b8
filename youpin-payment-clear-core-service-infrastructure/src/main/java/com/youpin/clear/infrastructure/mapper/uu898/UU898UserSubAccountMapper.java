package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898UserSubAccountMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898UserSubAccount row);

    int insertSelective(UU898UserSubAccount row);

    UU898UserSubAccount selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898UserSubAccount row);

    int updateByPrimaryKey(UU898UserSubAccount row);
}