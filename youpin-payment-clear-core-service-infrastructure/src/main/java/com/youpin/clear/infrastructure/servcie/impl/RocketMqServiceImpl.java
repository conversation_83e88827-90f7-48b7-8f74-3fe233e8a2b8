package com.youpin.clear.infrastructure.servcie.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.MQConfig;
import com.youpin.clear.common.enums.BillStatus;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.PayChannelEnum;
import com.youpin.clear.domain.aggregate.member.BillItemMemberExtension;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import com.youpin.clear.domain.dto.mq.SyncBillMessage;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.servcie.RocketMqService;
import com.youpin.clear.infrastructure.converter.UU898UserAssetsRecordConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import youpin.commons.rocketmq.supper.RocketMQUtil;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class RocketMqServiceImpl implements RocketMqService {


    @Autowired
    @Qualifier(MQConfig.DEFAULT_PAYMENT_MQ)
    RocketMQUtil rocketMQUtil;

    @Autowired
    @Qualifier(MQConfig.PAYMENT_CLEAR_MQ)
    RocketMQUtil rocketMQUtilClear;

    @Autowired
    @Qualifier(MQConfig.PAYMENT_ACCOUNT_MQ)
    RocketMQUtil rocketMQUtilAccount;


    private RocketMQUtil getRocketMQUtil(String rocketMQUtilType) {
        RocketMQUtil rocketMQUtilTemp;
        if (rocketMQUtilType.equals(MQConfig.PAYMENT_CLEAR_MQ)) {
            rocketMQUtilTemp = rocketMQUtilClear;
        } else if (rocketMQUtilType.equals(MQConfig.PAYMENT_ACCOUNT_MQ)) {
            rocketMQUtilTemp = rocketMQUtilAccount;
        } else {
            rocketMQUtilTemp = rocketMQUtil;
        }
        return rocketMQUtilTemp;
    }


    /**
     * 动态发送 topic
     */
    @Override
    public void sendDynamicsMQMsg(String rocketMQUtilType, String topic, String jsonStr, Boolean sendAsync) {
        if (StringUtils.isEmpty(topic) || StringUtils.isEmpty(jsonStr)) {
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
        if (StringUtils.isEmpty(rocketMQUtilType)) {
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
        RocketMQUtil rocketMQUtilTemp = getRocketMQUtil(rocketMQUtilType);

        if (null == sendAsync) {
            sendAsync = Boolean.FALSE;
        }
        SendReceipt sendNormalMessage = null;
        log.info("{} [MQ发送] sendDynamicsMQMsg topic:{}  msg:{}", rocketMQUtilType, topic, jsonStr);

        try {
            if (sendAsync.equals(Boolean.TRUE)) {
                rocketMQUtilTemp.asyncSendNormalMessage(topic, jsonStr);
            } else {
                sendNormalMessage = rocketMQUtilTemp.sendNormalMessage(topic, jsonStr);
            }
            if (Objects.nonNull(sendNormalMessage) && Objects.nonNull(sendNormalMessage.getMessageId())) {
                log.info("{} [MQ发送] sendDynamicsMQMsg topic:{}  MessageId:{}", rocketMQUtilType, topic, sendNormalMessage.getMessageId());
            }
        } catch (Exception e) {
            log.error("{} [MQ发送] sendDynamicsMQMsg topic:{}  failed ex: {}", rocketMQUtilType, topic, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
    }

    /**
     * 动态发送 topic tag
     */
    @Override
    public void sendDynamicsMQMsg(String rocketMQUtilType, String topic, String tag, String jsonStr, Boolean sendAsync) {
        if (StringUtils.isEmpty(topic) || StringUtils.isEmpty(tag)) {
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
        if (StringUtils.isEmpty(rocketMQUtilType)) {
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
        RocketMQUtil rocketMQUtilTemp = getRocketMQUtil(rocketMQUtilType);

        if (null == sendAsync) {
            sendAsync = Boolean.FALSE;
        }
        SendReceipt sendNormalMessage = null;
        log.info("{} [MQ发送] sendDynamicsMQMsg topic:{} tag:{} ，msg:{}", rocketMQUtilType, topic, tag, jsonStr);
        try {
            if (sendAsync.equals(Boolean.TRUE)) {
                rocketMQUtilTemp.asyncSendNormalMessage(topic, tag, jsonStr);
            } else {
                sendNormalMessage = rocketMQUtilTemp.sendNormalMessage(topic, tag, jsonStr);
            }
            if (Objects.nonNull(sendNormalMessage) && Objects.nonNull(sendNormalMessage.getMessageId())) {
                log.info("{} [MQ发送] sendDynamicsMQMsg topic:{} tag:{} MessageId:{}", rocketMQUtilType, topic, tag, sendNormalMessage.getMessageId());
            }
        } catch (Exception e) {
            log.error("{} [MQ发送] sendDynamicsMQMsg topic:{} tag:{} failed ex: {}", rocketMQUtilType, topic, tag, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
    }


    /**
     * 顺序发送 topic tag group
     */
    @Override
    public void sendDynamicsMQFifoMsg(String rocketMQUtilType, String topic, String tag, String group, String jsonStr, Boolean sendAsync) {
        if (StringUtils.isEmpty(topic) || StringUtils.isEmpty(tag) || StringUtils.isEmpty(group)) {
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
        if (StringUtils.isEmpty(rocketMQUtilType)) {
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
        RocketMQUtil rocketMQUtilTemp = getRocketMQUtil(rocketMQUtilType);
        if (null == sendAsync) {
            sendAsync = Boolean.FALSE;
        }
        SendReceipt sendNormalMessage = null;
        log.info("{} [MQ发送] sendDynamicsMQFifoMsg topic:{} tag:{} group:{},msg:{}", rocketMQUtilType, topic, tag, group, jsonStr);
        try {
            if (sendAsync.equals(Boolean.TRUE)) {
                rocketMQUtilTemp.asyncSendFifoMessage(topic, tag, group, jsonStr);
            } else {
                sendNormalMessage = rocketMQUtilTemp.sendFifoMessage(topic, tag, group, jsonStr);
            }
            if (Objects.nonNull(sendNormalMessage) && Objects.nonNull(sendNormalMessage.getMessageId())) {
                log.info("{} [MQ发送] sendDynamicsMQFifoMsg topic:{} tag:{} group:{} MessageId:{}", rocketMQUtilType, topic, tag, group, sendNormalMessage.getMessageId());
            }
        } catch (Exception e) {
            log.error("{} [MQ发送] sendDynamicsMQFifoMsg topic:{} tag:{} group:{} failed ex: {}", rocketMQUtilType, topic, tag, group, ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.MQ_SEND_FAILED);
        }
    }

    @Override
    public void sendBillMessage(List<AbstractUserAssetsRecordDTO> assetsRecordDTOList) {
        if (CollectionUtils.isEmpty(assetsRecordDTOList)) {
            log.info("[账单MQ发送] sendBillMessage assetsRecordDTOList is null");
            return;
        }
        for (AbstractUserAssetsRecordDTO assetsRecordDTO : assetsRecordDTOList) {
            if (assetsRecordDTO.isSendBill()) {
                sendDynamicsMQMsg(MQConfig.PAYMENT_ACCOUNT_MQ, MQConfig.SYNC_BILL_FROM_PAYCENTER_TOPIC, JSON.toJSONString(convert(assetsRecordDTO)), Boolean.TRUE);
            }
        }
    }

    static SyncBillMessage convert(AbstractUserAssetsRecordDTO agg) {
        String bizCreatedTime = Objects.isNull(agg.getCompleteTime()) ? agg.getAddTime().toString() : agg.getCompleteTime().toString();
        BigDecimal money = agg.getThisMoney().abs();
        int billPaySubChannel = Constant.CONSTANT_INTEGER_0;
        if (agg.getAssetType().equals(30)) {
            money = agg.getThisPurchaseMoney().abs();
            billPaySubChannel = Constant.CONSTANT_INTEGER_2;
        } else {
            if (agg.getPayChannel().equals(DoNetPayChannelEnum.Balance.getCode())) {
                billPaySubChannel = Constant.CONSTANT_INTEGER_1;
            }
        }
        DoNetPayChannelEnum netPayChannelEnum = DoNetPayChannelEnum.getByPayChannel(agg.getPayChannel());
        PayChannelEnum billPayChannel = PayChannelEnum.getPayChannelByNetPayChannel(netPayChannelEnum);
        BillStatus billStatus = BillStatus.getByNetStatus(agg.getStatus());
        return SyncBillMessage.builder().orderNo(agg.getOrderNo()).outTradeNo(agg.getPayOrderNo()).requestNo(agg.getSerialNo()).title("")
                .bizCreatedTime(bizCreatedTime).userId(agg.getUserId()).typeId(agg.getTypeId()).money(money)
                .billPayChannel(billPayChannel.getCode()).billPaySubChannel(billPaySubChannel)
                .billStatus(billStatus.getCode()).balanceAmount(agg.getAfterMoney())
                .chargeMoney(agg.getChargeMoney())
                .purchaseBalanceAmount(agg.getAfterPurchaseMoney()).build();
    }

    /**
     * 分账消息
     */
    @Override
    public void sendSeparateMessage(List<AbstractUserAssetsRecordDTO> assetsRecordDTOList) {
        if (CollectionUtils.isEmpty(assetsRecordDTOList)) {
            log.warn("[分账消息MQ发送] sendSeparateMessage assetsRecordDTOList is null");
            return;
        }
        String orderNo = getOrderNo(assetsRecordDTOList);
        if (StringUtils.isBlank(orderNo)) {
            log.warn("[分账消息MQ发送] sendSeparateMessage orderNo is null data :{}", JSON.toJSONString(assetsRecordDTOList));
            return;
        }
        sendDynamicsMQFifoMsg(MQConfig.PAYMENT_CLEAR_MQ, MQConfig.USER_ACCOUNT_RECORD_TOPIC, MQConfig.USER_ACCOUNT_RECORD_TAG, orderNo, JSON.toJSONString(getUserAssetRecordMsgList(assetsRecordDTOList)), Boolean.TRUE);
    }

    /**
     * 资金消息
     */
    @Override
    public void sendAssetsMessage(List<ClearUserAssetsRecordDTO> userAssetsRecordDTOList) {
        if (CollectionUtils.isEmpty(userAssetsRecordDTOList)) {
            log.warn("[资金消息MQ发送] sendAssetsMessage userAssetsRecordDTOList is null");
            return;
        }
        for (ClearUserAssetsRecordDTO item : userAssetsRecordDTOList) {
            BillItemMemberExtension.BillItemMemberExtensionBuilder billItemMemberExtensionBuilder = BillItemMemberExtension.builder();
            billItemMemberExtensionBuilder.collectType(item.getCollectType());
            billItemMemberExtensionBuilder.isLeaseOrder(item.getIsLeaseOrder());
            billItemMemberExtensionBuilder.balance2TransferAmount(item.getBalance2TransferAmount());
            billItemMemberExtensionBuilder.merchantId(item.getMerchantId());
            billItemMemberExtensionBuilder.businessType(item.getBusinessType());
            billItemMemberExtensionBuilder.subBusType(item.getSubBusType());
            //扩展字段
            item.setExt(JSON.toJSONString(billItemMemberExtensionBuilder.build()));
            sendDynamicsMQFifoMsg(MQConfig.PAYMENT_CLEAR_MQ, MQConfig.USER_ACCOUNT_RECORD_SYNC_TOPIC, MQConfig.USER_ACCOUNT_RECORD_SYNC_TAG, String.valueOf(item.getUserId()), JSON.toJSONString(item), Boolean.TRUE);
        }
    }

    /**
     * 发送平台户流水MQ
     */
    @Override
    public void sendPlatformUserAssetsRecordMq(List<PlatformAccountRecordDTO> platformAccountRecordDTOList, Boolean sendAsync) {
        if (CollectionUtils.isEmpty(platformAccountRecordDTOList)) {
            log.warn("[发送平台户流水MQ] sendPlatformUserAssetsRecordMq platformAccountRecordDTOList is null");
            return;
        }
        //顺序发送
        for (PlatformAccountRecordDTO dto : platformAccountRecordDTOList) {
            sendDynamicsMQFifoMsg(MQConfig.PAYMENT_CLEAR_MQ, MQConfig.PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TOPIC, MQConfig.PLATFORM_ACCOUNT_TRANSACTION_RECORDS_TAG, String.valueOf(dto.getUserId()), JSON.toJSONString(dto), sendAsync);
        }
    }


    private static String getOrderNo(List<AbstractUserAssetsRecordDTO> assetsRecordDTOList) {
        return assetsRecordDTOList.stream().map(AbstractUserAssetsRecordDTO::getOrderNo).filter(Objects::nonNull).distinct() // 如果需要去重保留此行，否则可以去掉
                .findFirst().orElse(null);
    }

    private List<UserAssetsRecordMessage> getUserAssetRecordMsgList(List<AbstractUserAssetsRecordDTO> assetList) {
        return assetList.stream().map(agg -> {
            UserAssetsRecordMessage msgAgg = UU898UserAssetsRecordConvertor.MAPPER.toUserAssetsRecordMessage(agg);
            msgAgg.setCompleteTime(Objects.isNull(agg.getCompleteTime()) ? null : agg.getCompleteTime().toString());
            msgAgg.setAddTime(Objects.isNull(agg.getAddTime()) ? null : agg.getAddTime().toString());
            msgAgg.setAccountBufferBookkeeping(agg.getAccountBufferBookkeeping());
            msgAgg.setPlatformAccountRecordId(agg.getPlatformAccountRecordId());
            if (null != agg.getAssetsRecordAccountBillExtInfo()) {
                msgAgg.setAssetsRecordAccountBillExtInfo(agg.getAssetsRecordAccountBillExtInfo());
            }
            // 判断子账户流水不为空
            if (CollectionUtils.isNotEmpty(agg.getSubAccountFlowRecordList())) {
                // 设置子账户流水
                msgAgg.setSubAccountFlowRecordList(agg.getSubAccountFlowRecordList());
            }
            return msgAgg;
        }).collect(Collectors.toList());
    }


}
