package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.gateway.UserAssetsRecordPolarGateway;
import com.youpin.clear.infrastructure.dataobject.polar.UserAssetsRecordPolar;
import com.youpin.clear.infrastructure.mapper.polar.UserAssetsRecordPolarExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserAssetsRecordPolarGatewayImpl implements UserAssetsRecordPolarGateway {


    @Autowired
    UserAssetsRecordPolarExtMapper userAssetsRecordPolarExtMapper;


    @Override
    public List<ClearUserAssetsRecordDTO> selectPageByUserId(Long userId, Long pageIndex, Integer pageSize, List<Integer> typeIdList, Long maxUserAssetsRecordId,
                                                             LocalDateTime startTime, LocalDateTime endTime) {
        List<UserAssetsRecordPolar> userAssetsRecordPolars = userAssetsRecordPolarExtMapper.selectPageByUserId(
                userId, pageIndex, pageSize, typeIdList, maxUserAssetsRecordId, startTime, endTime);

        if (null == userAssetsRecordPolars) {
            return Collections.emptyList();
        }
        log.info("polar查询用户资产记录分页数据：{}", userAssetsRecordPolars.size());
        //数据转换
        return userAssetsRecordPolars.stream().map(this::convertClearUserAssetsRecordUserDTO).collect(Collectors.toList());
    }

    ClearUserAssetsRecordDTO convertClearUserAssetsRecordUserDTO(UserAssetsRecordPolar userAssetsRecordPolarDO) {
        ClearUserAssetsRecordDTO dto = new ClearUserAssetsRecordDTO();
        dto.setUserAssetsRecordId(userAssetsRecordPolarDO.getId());
        dto.setUserAssetsRecordNo(String.valueOf(userAssetsRecordPolarDO.getId()));
        dto.setAccountName(userAssetsRecordPolarDO.getAccountName());
        dto.setAddTime(userAssetsRecordPolarDO.getAddTime());
        dto.setAttr(userAssetsRecordPolarDO.getAttr());
        dto.setUserId(userAssetsRecordPolarDO.getUserId());
        dto.setTypeId(userAssetsRecordPolarDO.getTypeId());
        dto.setStatus(userAssetsRecordPolarDO.getStatus());
        dto.setPayChannel(userAssetsRecordPolarDO.getPayChannel());
        dto.setTreadNo(userAssetsRecordPolarDO.getTreadNo());
        dto.setSerialNo(userAssetsRecordPolarDO.getSerialNo());
        dto.setOrderNo(userAssetsRecordPolarDO.getOrderNo());
        dto.setPayOrderNo(userAssetsRecordPolarDO.getPayOrderNo());
        dto.setRemark(userAssetsRecordPolarDO.getRemark());
        dto.setThisMoney(userAssetsRecordPolarDO.getThisMoney());
        dto.setCompleteTime(userAssetsRecordPolarDO.getCompleteTime());
        dto.setAssetType(userAssetsRecordPolarDO.getAssetType());
        dto.setMoney(userAssetsRecordPolarDO.getMoney());
        dto.setAfterMoney(userAssetsRecordPolarDO.getAfterMoney());
        dto.setChargeMoney(userAssetsRecordPolarDO.getChargeMoney());
        dto.setBlockMoney(userAssetsRecordPolarDO.getBlockMoney());
        dto.setAfterBlockMoney(userAssetsRecordPolarDO.getAfterBlockMoney());
        dto.setPurchaseMoney(userAssetsRecordPolarDO.getPurchaseMoney());
        dto.setAfterPurchaseMoney(userAssetsRecordPolarDO.getAfterPurchaseMoney());
        dto.setThisBlockMoney(userAssetsRecordPolarDO.getThisBlockMoney());
        dto.setThisPurchaseMoney(userAssetsRecordPolarDO.getThisPurchaseMoney());
        dto.setGenSource(userAssetsRecordPolarDO.getGenSource());
        dto.setPayWaitExpireTime(userAssetsRecordPolarDO.getPayWaitExpireTime());
        dto.setTypeName(userAssetsRecordPolarDO.getTypeName());
        return dto;
    }


}

