package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: account_assets_type_relate
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountAssetsTypeRelate implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 资金code
     */
    private Integer assetsCode;

    /**
     * 关系类型 
     */
    private String relateType;

    /**
     * 关系映射
     */
    private String relateCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}