package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.AdjustmentApplyDetailDTO;
import com.youpin.clear.domain.gateway.AdjustmentApplyDetailGateway;
import com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail;
import com.youpin.clear.infrastructure.mapper.AdjustmentApplyDetailExtMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AdjustmentApplyDetailGatewayImpl implements AdjustmentApplyDetailGateway {

    @Resource
    private AdjustmentApplyDetailExtMapper adjustmentApplyDetailExtMapper;


    @Override
    public int batchInsert(List<AdjustmentApplyDetailDTO> list) {
        List<AdjustmentApplyDetail> adjustmentApplyDetails = BeanUtilsWrapper.convertList(list, AdjustmentApplyDetail::new);
        return adjustmentApplyDetailExtMapper.batchInsert(adjustmentApplyDetails);
    }

    @Override
    public List<AdjustmentApplyDetailDTO> queryByApplyNo(String applyNo) {
        List<AdjustmentApplyDetail> adjustmentApplyDetails = adjustmentApplyDetailExtMapper.selectByApplyNo(applyNo);
        return BeanUtilsWrapper.convertList(adjustmentApplyDetails, AdjustmentApplyDetailDTO::new);
    }

    @Override
    public List<AdjustmentApplyDetailDTO> queryByApplyNoAndDirection(String applyNo, Integer direction) {
        List<AdjustmentApplyDetail> adjustmentApplyDetails = adjustmentApplyDetailExtMapper.selectByApplyNoAndDirection(applyNo, direction);
        return BeanUtilsWrapper.convertList(adjustmentApplyDetails, AdjustmentApplyDetailDTO::new);
    }
}
