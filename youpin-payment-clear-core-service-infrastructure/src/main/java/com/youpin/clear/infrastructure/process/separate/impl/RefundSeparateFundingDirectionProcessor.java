package com.youpin.clear.infrastructure.process.separate.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款 逻辑
 */
@Slf4j
@Service
public class RefundSeparateFundingDirectionProcessor extends DefaultSeparateFundingDirectionProcessor {

    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.REFUND);
    }

    @Override
    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        //资金分账抽象
        BillItemMember billItemMember = billMember.getBillItemMember();

        List<UserAccountRecordMember> userAccountRecordMemberList;

        DoNetPayChannelEnum payChannelEnum = billItemMember.getPayChannelEnum();
        //资金类型
        Integer typeId = billItemMember.getTypeId();
        //关系查找
        AccountBalanceDTO accountBalanceDTO = findRelationList2(billItemMember, billMemberList);
        if (null == accountBalanceDTO) {
            log.warn("[分账处理] 分账关系金额计算失败  尝试从入参里面找  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId());
            billMember.setIsSuccess(Boolean.FALSE);
            //查询不到兜底
            if (payChannelEnum.equals(DoNetPayChannelEnum.PurchaseBalance)) {
                if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
                    accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer1(billItemMember.getAmount()).build();
                } else if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
                    accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer2(billItemMember.getAmount()).build();
                } else {
                    accountBalanceDTO = AccountBalanceDTO.builder().purchaseBalanceTransfer1(billItemMember.getAmount()).build();
                }
            } else {
                if (typeId.equals(ClearConstants.CONSTANT_INTEGER_222)) {
                    accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                } else {
                    if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                    } else if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance2(billItemMember.getAmount()).build();
                    } else {
                        accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
                    }
                }
            }
        }

        assert accountBalanceDTO != null;

        AccountBalanceDTO accountBalanceDTOAbs = accountBalanceAbs(accountBalanceDTO);
        //金额校验
        checkBillAmount(accountBalanceDTOAbs, billItemMember);

        if (billItemMember.getOriginalAmount().compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0) {
            log.info("[分账处理] 退款金额为负数 需要减钱");
            //转换负数
            AccountBalanceDTO accountBalanceDTONegate = reverseAccountBalanceNegate(accountBalanceDTO);
            //转换渠道
            userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTONegate, billItemMember, billMember.getAccountAggregate(), DoNetPayChannelEnum.Balance);
        } else {
            userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTOAbs, billItemMember, billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());
        }
        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
        billMember.setIsSuccess(Boolean.TRUE);
    }

}
