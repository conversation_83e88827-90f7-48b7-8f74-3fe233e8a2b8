package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: user_assets_record
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsRecord implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 商户id
     */
    private Integer merchantId;

    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;

    /**
     * 资金明细分表唯一编号
     */
    private String userAssetsRecordNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;

    /**
     * 资金明细类型
     */
    private String typeName;

    /**
     * 资金明细唯一单号
     */
    private String treadNo;

    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 变化资产类型，1.帐户余额，10.积分
     */
    private Integer assetType;

    /**
     * 变动之前账户余额
     */
    private BigDecimal money;

    /**
     * 变动余额
     */
    private BigDecimal thisMoney;

    /**
     * 变动后的金额
     */
    private BigDecimal afterMoney;

    /**
     * 服务费金额
     */
    private BigDecimal chargeMoney;

    /**
     * 变动前的冻结金额
     */
    private BigDecimal blockMoney;

    /**
     * 本次冻结金额
     */
    private BigDecimal thisBlockMoney;

    /**
     * 变动后冻结金额
     */
    private BigDecimal afterBlockMoney;

    /**
     * 变动前的求购金额
     */
    private BigDecimal purchaseMoney;

    /**
     * 本次变动求购金额
     */
    private BigDecimal thisPurchaseMoney;

    /**
     * 变动后的求购金额
     */
    private BigDecimal afterPurchaseMoney;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 备注信息
     */
    private String ext;

    /**
     * 特性，二进制保存，从低位至高位依次为：余额是否变动
     */
    private Integer attr;

    /**
     * 状态，0.失败，1.成功，2.进行中
     */
    private Integer status;

    /**
     * 支付渠道：0.余额，1.微信，2.支付宝，3.积分，4额度，5押金卡，6临时额度，7固定额度，8求购余额，9支付宝原路退还
     */
    private Integer payChannel;

    /**
     * 提现支付宝账号
     */
    private String accountName;

    /**
     * 支付等待时间
     */
    private LocalDateTime payWaitExpireTime;

    /**
     * 生成来源信息
     */
    private String genSource;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    private static final long serialVersionUID = 1L;
}