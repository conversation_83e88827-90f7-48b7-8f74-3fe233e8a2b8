package com.youpin.clear.infrastructure.handle.mow.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.MowMoneyEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898OrderPayMoneyInfoDTO;
import com.youpin.clear.domain.gateway.UU898OrderPayMoneyInfoGateWay;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserSubAccountRecordGateway;
import com.youpin.clear.domain.handle.mow.MowMoneyHandle;
import com.youpin.clear.infrastructure.helper.MowMoneyHandleHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * LeaseMoney
 */
@Slf4j
@Service
public class MowMoneyHandleLeaseMoney implements MowMoneyHandle {

    @Autowired
    UserAccountGateway userAccountGateway;

    @Autowired
    UserSubAccountRecordGateway userSubAccountRecordGateway;

    @Autowired
    UU898OrderPayMoneyInfoGateWay uu898OrderPayMoneyInfoGateWay;


    @Override
    public List<Integer> support() {
        return List.of(MowMoneyEnum.lease_money.getCode());
    }

    @Override
    public void process(ClearUserAssetsRecordDTO clearUserAssetsRecordDTO, AccountAggregate accountAggregate) {
        //租赁 支出 默认 1 -->2  收入  默认 2

        //可以跳过状态非成功的逻辑
        if (!clearUserAssetsRecordDTO.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
            return;
        }

        //变更前金额
        BigDecimal beforeMoney = clearUserAssetsRecordDTO.getMoney();
        //变化金额
        BigDecimal thisMoney = clearUserAssetsRecordDTO.getThisMoney();
        //变更后金额
        BigDecimal afterMoney = clearUserAssetsRecordDTO.getAfterMoney();

        //判断金额有无发生变化 true 有 false 无
        boolean isChangeMoneyFlag = thisMoney.abs().compareTo(beforeMoney.subtract(afterMoney).abs()) == ClearConstants.CONSTANT_INT_0;
        if (!isChangeMoneyFlag) {
            log.warn("金额无发生变化: beforeMoney:{}  thisMoney:{} afterMoney:{} data:{}", beforeMoney, thisMoney, afterMoney, JSON.toJSONString(clearUserAssetsRecordDTO));
            return;
        }

        //使用的仅交易金额
        BigDecimal subThisMoney = getOnlyTradeMoney(clearUserAssetsRecordDTO.getOrderNo(), clearUserAssetsRecordDTO.getUserId());
        //提现变动差值
        BigDecimal subtract = thisMoney.abs().subtract(subThisMoney.abs());
        //提现变动差值
        BigDecimal subMoneyDifference = subtract.compareTo(BigDecimal.ZERO) == 0 ? thisMoney.abs() : subtract;
        //判断subThisMoney的符号
        BigDecimal subThisMoneySignum = subThisMoney.signum() < 0 ? subMoneyDifference.negate() : subMoneyDifference;
        
        //原始账户明细
        AccountInfoMember originAccountMember = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_WITHDRAW_121);
        //复制originAccountMember 到 targetAccountMember
        AccountInfoMember targetAccountMember = MowMoneyHandleHelper.originInitTargetAccountMember(originAccountMember, subThisMoneySignum);
        //更新账户
        userAccountGateway.updateAccount(originAccountMember, targetAccountMember);
        //更新原始金额变化
        originAccountMember.setBalance(targetAccountMember.getBalance());
        //组装资金流水
        UserSubAccountRecordMember userSubAccountRecordMember = MowMoneyHandleHelper.toUserSubAccountRecordMember(clearUserAssetsRecordDTO, targetAccountMember, subThisMoneySignum);
        //插入资金流水
        userSubAccountRecordGateway.save(List.of(userSubAccountRecordMember));

    }


    BigDecimal getOnlyTradeMoney(String orderNo, Long userId) {
        List<UU898OrderPayMoneyInfoDTO> uu898OrderPayMoneyInfoDTOList = uu898OrderPayMoneyInfoGateWay.selectByOrderAndUserId(orderNo, userId);
        if (CollectionUtils.isEmpty(uu898OrderPayMoneyInfoDTOList)) {
            return BigDecimal.ZERO;
        }
        return uu898OrderPayMoneyInfoDTOList.stream().map(UU898OrderPayMoneyInfoDTO::getOnlyTradeMoney).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


}
