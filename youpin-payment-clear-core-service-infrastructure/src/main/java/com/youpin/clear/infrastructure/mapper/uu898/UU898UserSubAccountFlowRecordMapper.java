package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898UserSubAccountFlowRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898UserSubAccountFlowRecord row);

    int insertSelective(UU898UserSubAccountFlowRecord row);

    UU898UserSubAccountFlowRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898UserSubAccountFlowRecord row);

    int updateByPrimaryKey(UU898UserSubAccountFlowRecord row);
}