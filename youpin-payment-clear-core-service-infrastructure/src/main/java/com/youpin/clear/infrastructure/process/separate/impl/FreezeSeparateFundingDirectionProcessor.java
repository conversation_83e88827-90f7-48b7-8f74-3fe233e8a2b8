package com.youpin.clear.infrastructure.process.separate.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 冻结,解冻
 */
@Slf4j
@Service
public class FreezeSeparateFundingDirectionProcessor extends DefaultSeparateFundingDirectionProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.CHANNEL_FREEZE, DirectionEnum.CHANNEL_UNFREEZE);
    }

    @Override
    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        //资金分账抽象
        BillItemMember billItemMember = billMember.getBillItemMember();
        //资金方向
        DirectionEnum directionEnum = billItemMember.getDirectionEnum();

        CollectTypeEnum collectTypeEnum = billItemMember.getCollectTypeEnum();

        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        switch (collectTypeEnum) {
            case OWN:
                accountBalanceDTO.setBalance1(billItemMember.getAmount());
                break;
            case SUPERVISION:
                accountBalanceDTO.setBalance2(billItemMember.getAmount());
                break;
            case DEFAULT:
                if (directionEnum.equals(DirectionEnum.CHANNEL_FREEZE)) {
                    accountBalanceDTO.setBalance1(billItemMember.getAmount());
                } else {
                    accountBalanceDTO = findRelationList2(billItemMember, billMemberList);
                }
                break;
        }
        //兜底默
        if (null == accountBalanceDTO) {
            accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
        }
        //反转资金符号
        AccountBalanceDTO accountBalanceDTOAbs = this.accountBalanceAbs(accountBalanceDTO);
        //金额校验
        if (accountBalanceDTOAbs.getTotalAmount().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
            log.error("[分账处理] {}, 金额校验失败 billItemMember:{}", directionEnum.getName(), JSON.toJSONString(billItemMember));
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        List<UserAccountRecordMember> userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTO, billItemMember, billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());

        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
        billMember.setIsSuccess(Boolean.TRUE);

    }


}
