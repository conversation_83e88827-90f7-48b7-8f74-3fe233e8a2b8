package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898UserAssetsRecordTypeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898UserAssetsRecordType row);

    int insertSelective(UU898UserAssetsRecordType row);

    UU898UserAssetsRecordType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898UserAssetsRecordType row);

    int updateByPrimaryKey(UU898UserAssetsRecordType row);
}