package com.youpin.clear.infrastructure.converter;

import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.infrastructure.dataobject.UserAccount;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserAccountConvert {

    UserAccountConvert MAPPER = Mappers.getMapper(UserAccountConvert.class);


    /**
     * 转换
     *
     * @param member member
     * @return account
     */
    UserAccount toUserAccount(AccountInfoMember member);

}
