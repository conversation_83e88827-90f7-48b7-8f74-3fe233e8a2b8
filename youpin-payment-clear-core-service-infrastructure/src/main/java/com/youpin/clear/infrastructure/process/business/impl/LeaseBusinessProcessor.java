package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 租赁大类处理器
 * 89  185  用户不一定是平台户
 */
@Slf4j
@Service
public class LeaseBusinessProcessor extends DefaultSubBusFinancialProcessor {


    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(
                //续租
                SubBusTypeFrontEnum.RE_LET,
                //租赁免押
                SubBusTypeFrontEnum.LEASE_CREDIT,
                //租赁买断
                SubBusTypeFrontEnum.LEASE_BUYOUT,
                //租赁守约
                SubBusTypeFrontEnum.KEEP_PROMISE
        );
    }

    //解冻类型
    final static List<Integer> unFreezeTypeList = List.of(UserAssetsTypeEnum.TYPE_13.getTypeId(), UserAssetsTypeEnum.TYPE_14.getTypeId(), UserAssetsTypeEnum.TYPE_91.getTypeId(), UserAssetsTypeEnum.TYPE_184.getTypeId());


    @Override
    public FinancialProcessorResultDTO process(FinancialProcessorDTO financialProcessorDTO) {
        //设置订单类型为租赁
        financialProcessorDTO.setCollectType(CollectTypeEnum.OWN.getCode());
        financialProcessorDTO.setIsLeaseOrder(Boolean.TRUE);
        return super.process(financialProcessorDTO);
    }


    @Override
    public void paySuccess(FinancialProcessorDTO financialProcessorDTO) {
        super.paySuccess(financialProcessorDTO);
        //映射平台账户
        platformUserIdConversion(financialProcessorDTO);
    }


    /**
     * TRANSACTION_RECORDS_TOP
     * 平台户转换数据
     */
    private void platformUserIdConversion(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_33.getTypeId())) {
                //支付补贴租金平台户
                Long privateTradingPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_33);

                abstractUserAssetsRecordDTO.setUserId(privateTradingPlatformUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_58.getTypeId())) {
                //押金额外扣除平台户
                Long depositDeductionPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_58);
                abstractUserAssetsRecordDTO.setUserId(depositDeductionPlatformUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_64.getTypeId())) {
                //补充数据
                Long convertedToCent = AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney());
                //预收安全保障费用
                Long securityDepositPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_65);
                //追回安全保障费用
                Long securityDepositReturnPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_66);
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_65, dto, securityDepositPlatformUserId, abstractUserAssetsRecordDTO.getPayChannel(), dto.getNetStatusEnum(), convertedToCent));
                tempList.add(buildAssetRecord(UserAssetsTypeEnum.TYPE_66, dto, securityDepositReturnPlatformUserId, abstractUserAssetsRecordDTO.getPayChannel(), dto.getNetStatusEnum(), convertedToCent));
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_90.getTypeId())) {
                //安心租收取平台户
                Long enjoyRentPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_90); //
                abstractUserAssetsRecordDTO.setUserId(enjoyRentPlatformUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_137.getTypeId())) {
                //赔饰品差额调拨平台户
                Long payDifferencePlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_137); //197149
                abstractUserAssetsRecordDTO.setUserId(payDifferencePlatformUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_189.getTypeId())) {
                //赔饰品追回平台户
                Long payDifferenceReturnPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_189);//197149
                abstractUserAssetsRecordDTO.setUserId(payDifferenceReturnPlatformUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_121.getTypeId())) {
                //暂收期数资金
                Long temporaryFundsPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_121);
                abstractUserAssetsRecordDTO.setUserId(temporaryFundsPlatformUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_22.getTypeId())) {
                Long advanceFundsPlatformTwoUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_22);
                abstractUserAssetsRecordDTO.setUserId(advanceFundsPlatformTwoUserId);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_21.getTypeId())) {
                //垫付资金 / 追回资金
                Long advanceFundsPlatformUserId = getPlatformUserId(UserAssetsTypeEnum.TYPE_21);
                abstractUserAssetsRecordDTO.setUserId(advanceFundsPlatformUserId);
            }
        }

        dto.addAbstractUserAssetsRecordDTO(tempList);
    }


    @Override
    public void settlement(FinancialProcessorDTO dto) {
        super.settlement(dto);
        platformUserIdConversion(dto);
    }

    @Override
    public void refundSuccess(FinancialProcessorDTO dto) {
        super.refundSuccess(dto);
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            BigDecimal changeMoney = abstractUserAssetsRecordDTO.getChangeMoney();
            if (unFreezeTypeList.contains(abstractUserAssetsRecordDTO.getTypeId())) {
                //解冻判断
                unFreezeCheck(dto, abstractUserAssetsRecordDTO, changeMoney);
            } else {
                log(Level.ERROR, "租赁大类处理异常:1 类型未匹配", dto);
            }
        }
    }


    private void unFreezeCheck(FinancialProcessorDTO dto, AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, BigDecimal changeMoney) {

        BigDecimal actualPaymentAmount;
        List<Integer> typeIdList = List.of();
        Integer typeId = abstractUserAssetsRecordDTO.getTypeId();
        if (typeId.equals(UserAssetsTypeEnum.TYPE_14.getTypeId())) {
            typeIdList = List.of(UserAssetsTypeEnum.TYPE_13.getTypeId());
        } else if (typeId.equals(UserAssetsTypeEnum.TYPE_91.getTypeId())) {
            typeIdList = List.of(UserAssetsTypeEnum.TYPE_88.getTypeId());
        } else if (typeId.equals(UserAssetsTypeEnum.TYPE_184.getTypeId())) {
            typeIdList = List.of(UserAssetsTypeEnum.TYPE_183.getTypeId());
        } else if (typeId.equals(UserAssetsTypeEnum.TYPE_198.getTypeId())) {
            typeIdList = List.of(UserAssetsTypeEnum.TYPE_197.getTypeId());
        } else if (typeId.equals(UserAssetsTypeEnum.TYPE_78.getTypeId()) || typeId.equals(UserAssetsTypeEnum.TYPE_25.getTypeId())) {
            typeIdList = List.of(UserAssetsTypeEnum.TYPE_24.getTypeId());
        } else {
            log(Level.ERROR, "租赁大类处理异常:2 类型未匹配 typeId:{} ", dto, typeId);
        }
        log(Level.INFO, "租赁大类处理: typeId:{} ", dto, typeId);
        actualPaymentAmount = findUserAssetsRecordDTOLinkList(dto, typeIdList,false,null);
        //增加特殊逻辑
        if (actualPaymentAmount.compareTo(BigDecimal.ZERO) == 0) {
            //根据支付单号查找逻辑
            //1.会存在 租赁过户 之后 会出现 订单不同  然后 和 支付前置约定 使用支付单号查询
            List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = queryAssembleDataByPayOrderNo(dto);
            actualPaymentAmount = findUserAssetsRecordDTOByPayOrderNo(dto, userAssetsRecordDTOLinkList, typeId, typeIdList);
        }
        if (actualPaymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log(Level.ERROR, "冻结押金为0  解冻押金:{}  资金编号:{} 关系编号:{} ", dto, changeMoney, typeId, typeIdList);
        }
        if (changeMoney.compareTo(actualPaymentAmount) > 0) {
            log(Level.ERROR, "解冻押金大于冻结押金  冻结押金:{} 解冻押金:{}  资金编号:{} 关系编号:{} ", dto, actualPaymentAmount, changeMoney, typeId, typeIdList);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "解冻押金大于冻结押金");
        }
    }


    private List<UU898UserAssetsRecordDTO> queryAssembleDataByPayOrderNo(FinancialProcessorDTO dto) {
        //查询订单关联数据
        List<UU898UserAssetsRecord> uu898UserAssetsRecordList = userAssetsRecordExtMapper.queryUserAssetsRecordDTOList(null, null, null, dto.getPayOrderNo(), null, null);
        return BeanUtilsWrapper.convertList(uu898UserAssetsRecordList, UU898UserAssetsRecordDTO::new);
    }


    /**
     * 正向查找-支付单号 -数据库
     * 后续增加负向查找逻辑
     */
    private BigDecimal findUserAssetsRecordDTOByPayOrderNo(FinancialProcessorDTO dto, List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList, Integer typeId, List<Integer> typeIdList) {
        if (CollectionUtils.isEmpty(userAssetsRecordDTOLinkList) || CollectionUtils.isEmpty(typeIdList)) {
            return BigDecimal.ZERO;
        }
        // 使用 HashSet 提高查找效率
        HashSet<Integer> typeIdSet = new HashSet<>(typeIdList);
        // 缓存过滤结果供后续复用
        List<UU898UserAssetsRecordDTO> filteredList = userAssetsRecordDTOLinkList.stream().filter(Objects::nonNull).filter(item -> typeIdSet.contains(item.getTypeId())).collect(Collectors.toList());
        // 计算金额总和
        BigDecimal result = filteredList.stream().map(item -> {
            BigDecimal thisMoney = Objects.requireNonNullElse(item.getThisMoney(), BigDecimal.ZERO);
            BigDecimal thisPurchaseMoney = Objects.requireNonNullElse(item.getThisPurchaseMoney(), BigDecimal.ZERO);
            return thisMoney.abs().add(thisPurchaseMoney.abs());
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (result.compareTo(BigDecimal.ZERO) == 0) {
            log(Level.INFO, "查询订单资金关联信息-支付单号关系: {} --- {}----无", dto, typeId, typeIdList);
            return BigDecimal.ZERO;
        } else {
            //打印日志  精简结构体
            CompletableFuture.runAsync(() -> {
                List<String> logDataList = filteredList.stream().map(item -> String.format("%s %s %s %s %s", item.getId(), item.getUserId(), item.getTypeId(), item.getThisMoney(), item.getThisPurchaseMoney())).collect(Collectors.toList());
                log(Level.INFO, "查询订单资金关联信息=支付单号关系:  {} --- {}----{}", dto, typeIdList, logDataList);
            }, asyncTaskExecutor).exceptionally(ex -> {
                log(Level.WARN, "异步日志记录失败-支付单号关系", dto, ExceptionUtils.getStackTrace(ex));
                return null;
            });
        }
        return result;
    }
}


