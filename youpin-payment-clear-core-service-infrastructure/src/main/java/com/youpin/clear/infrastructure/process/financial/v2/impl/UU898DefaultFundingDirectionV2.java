package com.youpin.clear.infrastructure.process.financial.v2.impl;

import com.uu898.youpin.commons.utils.JacksonUtils;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.UU898UserAssetsInfoGateway;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.domain.gateway.UU898UserSubAccountGateway;
import com.youpin.clear.domain.process.UU898FundingDirectionV2Processor;
import com.youpin.clear.infrastructure.converter.UU898UserAssetsRecordConvertor;
import com.youpin.clear.infrastructure.helper.UU898UserAccountHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;

@Service
@Slf4j
public abstract class UU898DefaultFundingDirectionV2 implements UU898FundingDirectionV2Processor {

    @Autowired
    UU898UserAssetsInfoGateway userAssetsInfoGateway;

    @Autowired
    UU898UserAssetsRecordGateway userAssetsRecordGateway;

    @Autowired
    UU898UserAccountHelper uu898UserAccountHelper;

    @Autowired
    private UU898UserSubAccountGateway uu898UserSubAccountGateway;


    @Override
    public void process(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO) {
        this.beforeDataIdempotenceCheck(abstractUserAssetsRecordDTO, userAssetsInfoDTO);
        this.processAssets(abstractUserAssetsRecordDTO, userAssetsInfoDTO);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO) {

    }

    @Override
    public void beforeDataIdempotenceCheck(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO) {
    }


    public static void setBalanceDto(AbstractUserAssetsRecordDTO dto, BigDecimal beforeMoney, BigDecimal changeMoney, BigDecimal afterMoney) {
        dto.setMoney(beforeMoney);
        dto.setThisMoney(changeMoney);
        dto.setAfterMoney(afterMoney);
    }

    public static void setPurchaseBalanceDto(AbstractUserAssetsRecordDTO dto, BigDecimal beforeMoney, BigDecimal changeMoney, BigDecimal afterMoney) {
        dto.setPurchaseMoney(beforeMoney);
        dto.setThisPurchaseMoney(changeMoney);
        dto.setAfterPurchaseMoney(afterMoney);
    }

    public static void setBlockBalanceDto(AbstractUserAssetsRecordDTO dto, BigDecimal beforeMoney, BigDecimal changeMoney, BigDecimal afterMoney) {
        dto.setBlockMoney(beforeMoney);
        dto.setThisBlockMoney(changeMoney);
        dto.setAfterBlockMoney(afterMoney);
    }


    private void insertUserAssetsRecord(AbstractUserAssetsRecordDTO dto) {
        dto.setCompleteTime(null);
        if (!dto.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
            dto.setCompleteTime(LocalDateTime.now());
        }
        UU898UserAssetsRecordDTO uu898UserAssetsRecordDto = UU898UserAssetsRecordConvertor.MAPPER.toUU898UserAssetsRecordDto(dto);
        Long assetsRecordId = userAssetsRecordGateway.insert(uu898UserAssetsRecordDto);
        dto.setId(assetsRecordId);
        if (null == assetsRecordId || assetsRecordId <= 0) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "资金明细新增失败");
        }
    }

    private void updateUserAssetsRecord(AbstractUserAssetsRecordDTO dto) {
        if (!dto.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
            dto.setCompleteTime(LocalDateTime.now());
        }
        UU898UserAssetsRecordDTO uu898UserAssetsRecordDto = UU898UserAssetsRecordConvertor.MAPPER.toUU898UserAssetsRecordDto(dto);
        Integer updateRow = userAssetsRecordGateway.update(uu898UserAssetsRecordDto);
        if (null == updateRow || updateRow <= 0) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "资金明细更新失败");
        }
    }

    /**
     * 更新求购余额账户
     *
     * @param lastUserRecordId            资金流水Id
     * @param originUserAssetsInfoDTO     原始账户对象
     * @param afterPurchaseMoney          变更后的求购余额
     * @param afterPurchaseMoneyFromMoney 变更后的求购转入余额
     * @param isAccountUpdate             是否更新账户 true 更新账户 false 不更新账户
     * @param accountBufferBookkeeping    是否开启缓冲记账 true 开启缓冲记账 false 不开启缓冲记账
     */
    void updateUserPurchaseBalance(Long lastUserRecordId, UserAssetsInfoDTO originUserAssetsInfoDTO, BigDecimal afterPurchaseMoney, BigDecimal afterPurchaseMoneyFromMoney, boolean isAccountUpdate, boolean accountBufferBookkeeping) {
        if (!isAccountUpdate || accountBufferBookkeeping) {
            log.info("不更新余额账户1:{}", lastUserRecordId);
            return;
        }
        UserAssetsInfoDTO toUpdateUserAssetsInfoDTO = UserAssetsInfoDTO.builder().purchaseMoney(afterPurchaseMoney).purchaseMoneyFromMoney(afterPurchaseMoneyFromMoney).lastUserRecordId(lastUserRecordId).updateTime(LocalDateTime.now()).build();
        int affect = userAssetsInfoGateway.updateUserPurchaseBalance(originUserAssetsInfoDTO, toUpdateUserAssetsInfoDTO);
        if (affect <= 0) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "求购余额账户更新失败");
        }

    }

    /**
     * 更新余额账户(余额或冻结余额)
     *
     * @param lastUserRecordId         资金流水Id
     * @param originUserAssetsInfoDTO  原始账户对象
     * @param afterMoney               变更后的余额
     * @param afterBlockMoney          变更后的冻结余额
     * @param isAccountUpdate          是否更新账户 true 更新账户 false 不更新账户
     * @param accountBufferBookkeeping 是否开启缓冲记账 true 开启缓冲记账 false 不开启缓冲记账
     */
    void updateUserBalanceAndBlockMoney(Long lastUserRecordId, UserAssetsInfoDTO originUserAssetsInfoDTO, BigDecimal afterMoney, BigDecimal afterBlockMoney, boolean isAccountUpdate, boolean accountBufferBookkeeping) {
        if (!isAccountUpdate || accountBufferBookkeeping) {
            log.info("不更新余额账户2:{}", lastUserRecordId);
            return;
        }
        UserAssetsInfoDTO toUpdateUserAssetsInfoDTO = UserAssetsInfoDTO.builder().money(afterMoney).blockMoney(afterBlockMoney).lastUserRecordId(lastUserRecordId).updateTime(LocalDateTime.now()).build();
        int affect = userAssetsInfoGateway.updateUserBalanceAndBlockMoney(originUserAssetsInfoDTO, toUpdateUserAssetsInfoDTO);
        if (affect <= 0) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "余额或冻结账户更新失败");
        }
    }

    /**
     * 插入或更新流水
     *
     * @param dto                        资金流水对象
     * @param assetsRecordUpdateOrInsert 是否更新流水 true 插入流水 false 更新流水
     */
    void insertOrUpdateUserAssetsRecord(AbstractUserAssetsRecordDTO dto, Boolean assetsRecordUpdateOrInsert) {

        if (assetsRecordUpdateOrInsert) {
            //是否开启缓冲记账
            if (dto.checkAccountBufferBookkeeping()) {
                dto.setStatus(NetStatusEnum.PROCESSING.getCode());
                dto.setMoney(BigDecimal.ZERO);
                dto.setBlockMoney(BigDecimal.ZERO);
                dto.setPurchaseMoney(BigDecimal.ZERO);
                dto.setAfterMoney(BigDecimal.ZERO);
                dto.setAfterBlockMoney(BigDecimal.ZERO);
                dto.setAfterPurchaseMoney(BigDecimal.ZERO);
            }
            insertUserAssetsRecord(dto);
        } else {
            updateUserAssetsRecord(dto);
        }
    }


    /**
     * 校验可提现金额
     */
    protected void checkWithdrawMoneySubtract(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        // 获取可交易子账户
        UU898UserSubAccountDTO tradeSubAccountDTO = userAssetsInfoDTO.getTradeSubAccountDTO();
        // 获取可交易金额
        BigDecimal tradeBalance = tradeSubAccountDTO == null ? BigDecimal.ZERO : tradeSubAccountDTO.getBalance();
        // 获取可提现金额 = 总金额 - 可交易金额
        BigDecimal canWithdrawMoney = userAssetsInfoDTO.getMoney().subtract(tradeBalance);
        // 判断可提现金额是否小于changeMoney
        if (canWithdrawMoney.compareTo(dto.getChargeMoney()) < 0) {
            log.info("[checkWithdrawMoneySubtract]:可提现余额校验余额不足,userId:{},tradeBalance:{},canWithdrawMoney:{}", userAssetsInfoDTO.getUserId(), canWithdrawMoney, tradeBalance);
            throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
        }
    }


    /**
     * 处理子账户
     *
     * @param dto           dto
     * @param subAccountDTO 子账户dto
     * @param addFlag       是否添加 true-add false-subtract
     */
    protected void handleSubAccount(AbstractUserAssetsRecordDTO dto, UU898UserSubAccountDTO subAccountDTO, BigDecimal changeBalance, boolean addFlag) {
        if (subAccountDTO == null) {
            return;
        }
        //
        if (changeBalance == null || changeBalance.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        // 获取balanceBefore
        BigDecimal balanceBefore = subAccountDTO.getBalance();
        // 获取balanceAfter
        BigDecimal balanceAfter;
        // 判断是否添加
        if (addFlag) {
            balanceAfter = balanceBefore.add(changeBalance);
        } else {
            balanceAfter = balanceBefore.subtract(changeBalance);
        }
        // 判断balanceAfter是否为负数
        if (balanceAfter.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("[handleSubAccount]:子账户扣款失败出现负数,subAccountDTO:{},dto:{},addFlag:{}", subAccountDTO, dto, addFlag);
            throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
        }
        // 获取扩展信息
        UU898UserSubAccountFlowRecordDTO.ExtendInfo extendInfo = new UU898UserSubAccountFlowRecordDTO.ExtendInfo();
        // 设置主流水thisMoney
        extendInfo.setAssetRecordThisMoney(dto.getThisMoney());
        // 创建子账户明细
        UU898UserSubAccountFlowRecordDTO subAccountFlowRecordDTO = UU898UserSubAccountFlowRecordDTO.builder()
                .id(null)
                .accountNo(subAccountDTO.getAccountNo())
                .accountType(subAccountDTO.getAccountType())
                .payOrderNo(dto.getPayOrderNo())
                .orderNo(dto.getOrderNo())
                .serialNo(dto.getSerialNo())
                .userId(dto.getUserId())
                .journalType(dto.getTypeId())
                .balanceChange(addFlag ? changeBalance : changeBalance.negate())
                .balanceBefore(balanceBefore)
                .balanceAfter(balanceAfter)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .extInfo(JacksonUtils.writeValueAsString(extendInfo))
                .build();
        if (dto.getSubAccountFlowRecordList() == null) {
            dto.setSubAccountFlowRecordList(new ArrayList<>());
        }
        // 保存子流水
        uu898UserSubAccountGateway.saveSubAccountRecord(subAccountFlowRecordDTO);
        // 添加到list里面
        dto.getSubAccountFlowRecordList().add(subAccountFlowRecordDTO);

        // 设置子账户余额
        subAccountDTO.setBalance(balanceAfter);
        // 更新子账户
        uu898UserSubAccountGateway.updateSubAccount(subAccountDTO, subAccountFlowRecordDTO);
    }

}
