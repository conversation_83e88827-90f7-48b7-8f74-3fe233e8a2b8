package com.youpin.clear.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@RefreshScope
@Data
@ConfigurationProperties(prefix = "adjustment-apply-config")
public class AdjustmentApplyConfig {

    /**
     * 申请备注最大长度
     */
    private Long applyRemarkLength = 500L;

    /**
     * 审核备注最大长度
     */
    private Long auditRemarkLength = 500L;

    /**
     * 单笔调账金额上限单位分
     */
    private Long singleAdjustmentAmountLimit = 200000L;

    /**
     * 单日调账金额上限，单位分
     */
    private Long singleDayAmountLimit = 2000000L;

    /**
     * 单账户单日调账金额上限-内部户,单位分
     */
    private Long singleInnerAccountAmountLimit = 20000000L;

    /**
     * 单账户单日调账金额上限-外部户，单位分
     */
    private Long singleOuterAccountAmountLimit = 300000000L;

    /**
     * 内部账户
     */
    private Set<Long> innerAccountIds = Set.of(54923L,47739L,13099L,2737274L,3231464L,3231589L,3237283L,
            2610925L,211423L,3623863L,197149L,4624376L,3230302L,311359L,5885868L);

    /**
     * 统计金额缓存时间,单位小时
     */
    private Long statisticAmountCacheTime = 24L;

    /**
     * 处理中数据批量查询大小
     */
    private Long batchQuerySize = 100L;

    /**
     * 分账账户异常处理次数
     */
    private Integer separateAccountRetryHandleCount = 3;

    /**
     * 验证码最大验证次数
     */
    private Integer verifyCodeCheckCount = 3;

    /**
     * 批量撤销最大数量
     */
    private Integer revokeBatchMaxSize = 200;

    /**
     * 批量导入模版地址
     */
    private String importTemplateUrl = "https://youpin898-images.oss-cn-shenzhen.aliyuncs.com/adjustApplyTemplate.xlsx";

    /**
     * 批量审核最大数量
     */
    private Integer auditBatchMaxSize = 200;
}
