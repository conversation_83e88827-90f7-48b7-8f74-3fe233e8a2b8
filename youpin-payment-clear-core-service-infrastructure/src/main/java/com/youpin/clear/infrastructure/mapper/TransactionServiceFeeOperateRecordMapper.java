package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TransactionServiceFeeOperateRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TransactionServiceFeeOperateRecord row);

    int insertSelective(TransactionServiceFeeOperateRecord row);

    TransactionServiceFeeOperateRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TransactionServiceFeeOperateRecord row);

    int updateByPrimaryKey(TransactionServiceFeeOperateRecord row);
}