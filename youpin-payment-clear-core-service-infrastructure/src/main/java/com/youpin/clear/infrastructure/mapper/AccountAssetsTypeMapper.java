package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.AccountAssetsType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AccountAssetsTypeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AccountAssetsType row);

    int insertSelective(AccountAssetsType row);

    AccountAssetsType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AccountAssetsType row);

    int updateByPrimaryKey(AccountAssetsType row);
}