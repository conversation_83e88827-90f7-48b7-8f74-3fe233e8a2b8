package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898OrderPayMoneyInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898OrderPayMoneyInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898OrderPayMoneyInfo row);

    int insertSelective(UU898OrderPayMoneyInfo row);

    UU898OrderPayMoneyInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898OrderPayMoneyInfo row);

    int updateByPrimaryKey(UU898OrderPayMoneyInfo row);
}