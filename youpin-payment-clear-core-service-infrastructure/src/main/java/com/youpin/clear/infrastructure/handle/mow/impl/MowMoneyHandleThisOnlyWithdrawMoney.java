package com.youpin.clear.infrastructure.handle.mow.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserSubAccountRecordGateway;
import com.youpin.clear.domain.handle.mow.MowMoneyHandle;
import com.youpin.clear.infrastructure.helper.MowMoneyHandleHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * This_Only_Withdraw_Money
 */
@Slf4j
@Service
public class MowMoneyHandleThisOnlyWithdrawMoney implements MowMoneyHandle {

    @Autowired
    UserAccountGateway userAccountGateway;


    @Autowired
    UserSubAccountRecordGateway userSubAccountRecordGateway;


    @Override
    public List<Integer> support() {
        return List.of(MowMoneyEnum.This_Only_Withdraw_Money.getCode());
    }

    @Override
    public void process(ClearUserAssetsRecordDTO clearUserAssetsRecordDTO, AccountAggregate accountAggregate) {
        //收单类型
        CollectTypeEnum collectTypeEnum = CollectTypeEnum.getCollectTypeEnum(clearUserAssetsRecordDTO.getCollectType());
        //收单类型为null || 非自由
        if (!CollectTypeEnum.OWN.equals(collectTypeEnum)) {
            return;
        }
        //可以跳过状态非成功的逻辑
        if (!clearUserAssetsRecordDTO.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
            return;
        }

        //变更前金额
        BigDecimal beforeMoney = clearUserAssetsRecordDTO.getMoney();
        //变化金额
        BigDecimal thisMoney = clearUserAssetsRecordDTO.getThisMoney();
        //变更后金额
        BigDecimal afterMoney = clearUserAssetsRecordDTO.getAfterMoney();

        //判断金额有无发生变化 true 有 false 无
        boolean isChangeMoneyFlag = thisMoney.abs().compareTo(beforeMoney.subtract(afterMoney).abs()) == ClearConstants.CONSTANT_INT_0;
        if (!isChangeMoneyFlag) {
            log.warn("金额无发生变化: beforeMoney:{}  thisMoney:{} afterMoney:{} data:{}", beforeMoney, thisMoney, afterMoney, JSON.toJSONString(clearUserAssetsRecordDTO));
            return;
        }

        //获取子流水变动
        UU898UserSubAccountFlowRecordDTO uu898UserSubAccountFlowRecordDTO = clearUserAssetsRecordDTO.getSubAccountFlowRecordList().stream()
                .filter(uu898UserSubAccountFlowRecordDTOLambda -> uu898UserSubAccountFlowRecordDTOLambda.getAccountType().equals(UU898UserSubAccountType.TRADE.getType())).findFirst().orElse(null);
        //如果 上层没有使用 仅可交易 需要 虚拟一个子流水消息 传递到下游
        if (null == uu898UserSubAccountFlowRecordDTO) {
            log.warn("上层没有使用 提现金额: data:{}", JSON.toJSONString(clearUserAssetsRecordDTO));
            return;
        }

        //应该有且只有一条 仅交易余额变动
        BigDecimal subThisMoney = uu898UserSubAccountFlowRecordDTO.getBalanceChange();
        //提现变动差值
        BigDecimal subtract = thisMoney.abs().subtract(subThisMoney.abs());
        //提现变动差值
        BigDecimal subMoneyDifference = subtract.compareTo(BigDecimal.ZERO) == 0 ? thisMoney.abs() : subtract;
        //判断subThisMoney的符号
        BigDecimal subThisMoneySignum = subThisMoney.signum() < 0 ? subMoneyDifference.negate() : subMoneyDifference;

        //原始账户明细
        AccountInfoMember originAccountMember = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_WITHDRAW_121);
        //复制originAccountMember 到 targetAccountMember
        AccountInfoMember targetAccountMember = MowMoneyHandleHelper.originInitTargetAccountMember(originAccountMember, subThisMoneySignum);
        //更新账户
        userAccountGateway.updateAccount(originAccountMember, targetAccountMember);
        //更新原始金额变化
        originAccountMember.setBalance(targetAccountMember.getBalance());
        //组装资金流水
        UserSubAccountRecordMember userSubAccountRecordMember = MowMoneyHandleHelper.toUserSubAccountRecordMember(clearUserAssetsRecordDTO, targetAccountMember, subThisMoneySignum);
        //插入资金流水
        userSubAccountRecordGateway.save(List.of(userSubAccountRecordMember));

    }


}
