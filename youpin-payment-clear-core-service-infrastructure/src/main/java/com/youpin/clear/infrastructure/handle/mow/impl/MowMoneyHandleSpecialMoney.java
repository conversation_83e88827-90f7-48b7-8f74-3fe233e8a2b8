package com.youpin.clear.infrastructure.handle.mow.impl;

import com.youpin.clear.common.enums.MowMoneyEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserSubAccountRecordGateway;
import com.youpin.clear.domain.handle.mow.MowMoneyHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Special_Mow_Money
 */
@Slf4j
@Service
public class MowMoneyHandleSpecialMoney implements MowMoneyHandle {

    @Autowired
    UserAccountGateway userAccountGateway;


    @Autowired
    UserSubAccountRecordGateway userSubAccountRecordGateway;


    @Override
    public List<Integer> support() {
        return List.of(MowMoneyEnum.Special_Mow_Money.getCode());
    }

    @Override
    public void process(ClearUserAssetsRecordDTO clearUserAssetsRecordDTO, AccountAggregate accountAggregate) {


    }


}
