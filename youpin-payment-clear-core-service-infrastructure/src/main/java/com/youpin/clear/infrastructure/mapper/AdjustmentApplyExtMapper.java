package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.client.request.AdjustApplyListQueryRequest;
import com.youpin.clear.infrastructure.dataobject.AdjustmentApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AdjustmentApplyExtMapper {

    Long count(AdjustApplyListQueryRequest request);

    List<AdjustmentApply> queryList(AdjustApplyListQueryRequest request);

    List<AdjustmentApply> queryByIds(@Param("ids") List<Long> ids);

    List<AdjustmentApply> queryByApplyStatus(@Param("statusList") List<Integer> statusList,@Param("size") Long size);

    int updateApplyStatus(@Param("ids") List<Long> ids,@Param("expectStatus") Integer expectStatus,@Param("currentStatus") Integer currentStatus);

}
