package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.CompensationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface CompensationRecordExtMapper {

    int selectUniqueKeyCount(@Param("uniqueKey") String uniqueKey, @Param("bizScene") Integer bizScene);


    Long countExceptionRetryJob(@Param("currentTime") LocalDateTime currentTime, @Param("maxRetryCount") Integer maxRetryCount, @Param("shardTotal") int shardTotal, @Param("shardIndex") int shardIndex, @Param("bizScene") Integer bizScene);

    List<Long> selectExceptionRetryJob(@Param("currentTime") LocalDateTime currentTime, @Param("maxRetryCount") Integer maxRetryCount, @Param("shardTotal") int shardTotal, @Param("shardIndex") int shardIndex, @Param("pageIndex") Long pageIndex, @Param("pageSize") Long pageSize, @Param("bizScene") Integer bizScene);

    void deleteByCountSum(@Param("countSum") Integer countSum, @Param("pageSize") Integer pageSize);

    int deleteByCreateTime(@Param("createTime") LocalDateTime createTime, @Param("pageSize") Integer pageSize);

    List<CompensationRecord> selectMaxRetryCount(@Param("maxRetryCount") Integer maxRetryCount, @Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    void deleteByHandleStatus(@Param("handleStatus") Integer handleStatus, @Param("pageSize") Integer pageSize);
}
