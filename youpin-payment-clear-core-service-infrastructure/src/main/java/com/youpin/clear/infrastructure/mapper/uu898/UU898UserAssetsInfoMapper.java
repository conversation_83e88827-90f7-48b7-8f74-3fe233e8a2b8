package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898UserAssetsInfoMapper {
    int deleteByPrimaryKey(Long userId);

    int insert(UU898UserAssetsInfo row);

    int insertSelective(UU898UserAssetsInfo row);

    UU898UserAssetsInfo selectByPrimaryKey(Long userId);

    int updateByPrimaryKeySelective(UU898UserAssetsInfo row);

    int updateByPrimaryKey(UU898UserAssetsInfo row);
}