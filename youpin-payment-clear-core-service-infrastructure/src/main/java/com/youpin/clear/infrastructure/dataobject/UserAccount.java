package com.youpin.clear.infrastructure.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Table: user_account
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccount implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户账户号
     */
    private String userAccountNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 账户可用余额
     */
    private BigDecimal balance;

    /**
     * 账户冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * 最后更新记录ID
     */
    private Long lastAccountRecordId;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}