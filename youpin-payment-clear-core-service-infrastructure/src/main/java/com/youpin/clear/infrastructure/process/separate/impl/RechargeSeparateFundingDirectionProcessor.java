package com.youpin.clear.infrastructure.process.separate.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.UserAccountRecordGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 充值,提现
 */
@Slf4j
@Service
public class RechargeSeparateFundingDirectionProcessor extends DefaultSeparateFundingDirectionProcessor {

    @Autowired
    UserAccountRecordGateway userAccountRecordGateway;

    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.RECHARGE, DirectionEnum.CASH_WITHDRAWAL);
    }

    @Override
    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        //资金分账抽象
        BillItemMember billItemMember = billMember.getBillItemMember();
        //资金ID
        Integer typeId = billItemMember.getTypeId();

        AccountAggregate accountAggregate = billMember.getAccountAggregate();

        //收单类型
        CollectTypeEnum collectTypeEnum = billItemMember.getCollectTypeEnum();
        AccountBalanceDTO.AccountBalanceDTOBuilder accountBalanceDTOBuilder = AccountBalanceDTO.builder();
        AccountBalanceDTO accountBalanceDTO = null;
        //余额充值
        if (Objects.equals(typeId, ClearConstants.CONSTANT_INTEGER_1) || typeId.equals(ClearConstants.CONSTANT_INTEGER_82) || typeId.equals(ClearConstants.CONSTANT_INTEGER_219)) {
            if (collectTypeEnum.equals(CollectTypeEnum.OWN)) {
                accountBalanceDTO = accountBalanceDTOBuilder.balance1(billItemMember.getAmount()).build();
            } else if (collectTypeEnum.equals(CollectTypeEnum.SUPERVISION)) {
                accountBalanceDTO = accountBalanceDTOBuilder.balance2(billItemMember.getAmount()).build();
            } else if (collectTypeEnum.equals(CollectTypeEnum.DEFAULT)) {
                log.info("[分账处理] 余额充值 没有收单类型 默认 余额1 自有 :{}", JSON.toJSONString(billItemMember));
                accountBalanceDTO = accountBalanceDTOBuilder.balance1(billItemMember.getAmount()).build();
            }
        }

        //钱包提现
        else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_2)) {

            if (collectTypeEnum.equals(CollectTypeEnum.OWN)) {
                accountBalanceDTO = accountBalanceDTOBuilder.balance1(billItemMember.getAmount()).build();
            } else if (collectTypeEnum.equals(CollectTypeEnum.SUPERVISION)) {
                accountBalanceDTO = accountBalanceDTOBuilder.balance2(billItemMember.getAmount()).build();
            } else if (collectTypeEnum.equals(CollectTypeEnum.DEFAULT)) {
                log.info("[分账处理] 余额提现 没有收单类型 优先2扣减  :{}", JSON.toJSONString(billItemMember));
                accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2));
            }

            //兜底
            AccountInfoMember balanceRecharge1 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_1);
            AccountInfoMember balanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2);
            //钱包提现兜底
            if (accountBalanceDTO != null && accountBalanceDTO.getBalance1().compareTo(BigDecimal.ZERO) > 0 && balanceRecharge1.getBalance().compareTo(accountBalanceDTO.getBalance1()) < 0) {
                accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2));
            }
            if (accountBalanceDTO != null && accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) > 0 && balanceRecharge2.getBalance().compareTo(accountBalanceDTO.getBalance2()) < 0) {
                accountBalanceDTO = subtractionAccount2(billItemMember.getAmount(), accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_2));
            }

            if (billItemMember.getNetStatusEnum().equals(NetStatusEnum.SUCCESS)) {
                //如果提现是成功的 并且数据库已经有数据了
                List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(billItemMember.getUserId(), billItemMember.getUserAssetsRecordId(), null);
                if (CollectionUtils.isNotEmpty(userAccountRecordMemberList)) {
                    userAccountRecordMemberList.forEach(item -> {
                        item.setPayOrderNo(billItemMember.getPayOrderNo());
                        item.setDirectionEnum(DirectionEnum.CASH_WITHDRAWAL);
                        item.setStatus(NetStatusEnum.SUCCESS.getCode());
                    });
                    billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
                    billMember.setIsSuccess(Boolean.TRUE);
                    return;
                }
            } else if (billItemMember.getNetStatusEnum().equals(NetStatusEnum.FAIL)) {
                //如果提现是失败的
                List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(billItemMember.getUserId(), billItemMember.getUserAssetsRecordId(), null);
                if (CollectionUtils.isNotEmpty(userAccountRecordMemberList)) {
                    userAccountRecordMemberList.forEach(item -> {
                        item.setPayOrderNo(billItemMember.getPayOrderNo());
                        item.setDirectionEnum(DirectionEnum.CASH_WITHDRAWAL);
                        item.setStatus(NetStatusEnum.FAIL.getCode());
                    });
                    billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
                    billMember.setIsSuccess(Boolean.TRUE);
                    return;
                }
            }

        }
        //求购余额充值
        else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_43)) {
            if (collectTypeEnum.equals(CollectTypeEnum.OWN)) {
                accountBalanceDTO = accountBalanceDTOBuilder.purchaseBalanceRecharge1(billItemMember.getAmount()).build();
            } else if (collectTypeEnum.equals(CollectTypeEnum.SUPERVISION)) {
                accountBalanceDTO = accountBalanceDTOBuilder.purchaseBalanceRecharge2(billItemMember.getAmount()).build();
            } else if (collectTypeEnum.equals(CollectTypeEnum.DEFAULT)) {
                log.info("[分账处理] 求购充值 没有收单类型 默认 充值1 自有 :{}", JSON.toJSONString(billItemMember));
                accountBalanceDTO = accountBalanceDTOBuilder.purchaseBalanceRecharge1(billItemMember.getAmount()).build();
            }
        }

        //求购余额提现
        else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_44)) {
            //如果提现是的时候
            BigDecimal balance2TransferAmount = billItemMember.getBillItemMemberExtension().getBalance2TransferAmount();
            if (null != balance2TransferAmount && balance2TransferAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("[分账处理] 求购提现 优先2扣减 :{}", JSON.toJSONString(billItemMember));
                accountBalanceDTO = accountBalanceDTOBuilder
                        .purchaseBalanceRecharge1(billItemMember.getAmount().subtract(balance2TransferAmount))
                        .purchaseBalanceRecharge2(balance2TransferAmount).build();
            } else {
                if (collectTypeEnum.equals(CollectTypeEnum.OWN)) {
                    accountBalanceDTO = accountBalanceDTOBuilder.purchaseBalanceRecharge1(billItemMember.getAmount()).build();
                } else if (collectTypeEnum.equals(CollectTypeEnum.SUPERVISION)) {
                    accountBalanceDTO = accountBalanceDTOBuilder.purchaseBalanceRecharge2(billItemMember.getAmount()).build();
                } else if (collectTypeEnum.equals(CollectTypeEnum.DEFAULT)) {
                    log.info("[分账处理] 求购提现 没有收单类型 优先2扣减 :{}", JSON.toJSONString(billItemMember));
                    accountBalanceDTO = subtractionPurchaseRechargeAccount2(billItemMember.getAmount(), accountAggregate);
                }
            }

            if (billItemMember.getNetStatusEnum().equals(NetStatusEnum.SUCCESS)) {
                //如果提现是成功的 并且数据库已经有数据了
                List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(billItemMember.getUserId(), billItemMember.getUserAssetsRecordId(), null);
                if (CollectionUtils.isNotEmpty(userAccountRecordMemberList)) {
                    userAccountRecordMemberList.forEach(item -> {
                        item.setPayOrderNo(billItemMember.getPayOrderNo());
                        item.setDirectionEnum(DirectionEnum.CASH_WITHDRAWAL);
                        item.setStatus(NetStatusEnum.SUCCESS.getCode());
                    });
                    billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
                    billMember.setIsSuccess(Boolean.TRUE);
                    return;
                }
            } else if (billItemMember.getNetStatusEnum().equals(NetStatusEnum.FAIL)) {
                //如果提现是失败的
                List<UserAccountRecordMember> userAccountRecordMemberList = userAccountRecordGateway.getUserAccountRecordByUserIdAndUserAssetsRecordId(billItemMember.getUserId(), billItemMember.getUserAssetsRecordId(), null);
                if (CollectionUtils.isNotEmpty(userAccountRecordMemberList)) {
                    userAccountRecordMemberList.forEach(item -> {
                        item.setPayOrderNo(billItemMember.getPayOrderNo());
                        item.setDirectionEnum(DirectionEnum.CASH_WITHDRAWAL);
                        item.setStatus(NetStatusEnum.FAIL.getCode());
                    });
                    billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
                    billMember.setIsSuccess(Boolean.TRUE);
                    return;
                }
            }

            //兜底
            AccountInfoMember purchaseBalanceRecharge2 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2);
            AccountInfoMember purchaseBalanceRecharge1 = accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1);
            //增加一个判断
            if ((accountBalanceDTO.getPurchaseBalanceRecharge1().compareTo(BigDecimal.ZERO) > 0 && purchaseBalanceRecharge1.getBalance().subtract(accountBalanceDTO.getPurchaseBalanceRecharge1()).compareTo(BigDecimal.ZERO) < 0) || (accountBalanceDTO.getPurchaseBalanceRecharge2().compareTo(BigDecimal.ZERO) > 0 && purchaseBalanceRecharge2.getBalance().subtract(accountBalanceDTO.getPurchaseBalanceRecharge2()).compareTo(BigDecimal.ZERO) < 0)) {
                accountBalanceDTO = subtractionPurchaseRechargeAccount2(billItemMember.getAmount(), accountAggregate);
            }
        }
        if (null == accountBalanceDTO) {
            log.error("[分账处理-充值/提现]  分账金额计算失败，billItemMember:{}", JSON.toJSONString(billItemMember));
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CALCULATE_FAIL);
        }
        if (accountBalanceDTO.getBalance1().compareTo(BigDecimal.ZERO) < 0 || accountBalanceDTO.getBalance2().compareTo(BigDecimal.ZERO) < 0) {
            log.error("[分账处理-充值/提现]  分账金额计算失败 出现负数，accountBalanceDTO:{}", JSON.toJSONString(accountBalanceDTO));
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CALCULATE_FAIL);
        }

        //金额校验
        if (accountBalanceDTO.getTotalAmountAbs().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
            log.error("[分账处理] 充值/提现 金额校验失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 计算金额:{} 账单金额:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), billItemMember.getTypeId(), accountBalanceDTO.getTotalAmountAbs(), billItemMember.getAmount());
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }

        List<UserAccountRecordMember> userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTO, billItemMember, billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());

        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
        billMember.setIsSuccess(Boolean.TRUE);

    }


}
