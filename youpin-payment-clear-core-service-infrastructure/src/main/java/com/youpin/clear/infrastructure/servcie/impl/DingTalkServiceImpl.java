package com.youpin.clear.infrastructure.servcie.impl;

import com.youpin.clear.domain.servcie.DingTalkService;
import com.youpin.clear.infrastructure.config.DingTalkConfig;
import com.youpin.clear.infrastructure.config.DingTalkMapParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class DingTalkServiceImpl implements DingTalkService {
    @Autowired
    DingTalkMapParamsConfig dingTalkMapParamsConfig;

    /**
     * 钉钉消息提示
     */
    public void sendDingTalkMessage(@NotNull String compareType, @NotNull String msg) {
        //判断当前数据是否需要提示
        CompletableFuture.runAsync(() -> {
            try {
                DingTalkConfig config = dingTalkMapParamsConfig.getConfigs().get(compareType);
                if (!config.isSwitchTag()) {
                    log.warn("钉钉消息发送失败开关未打开compareType:{},msg:{}", compareType, msg);
                    return;
                }
                //钉钉预警
                String message = RebootUtil.setMessage(false, msg, List.of());
                RebootUtil.sendRebootWithSec(message, config.getSecret(), config.getToken());
            } catch (Exception e) {
                log.warn("钉钉消息提示失败:{}", ExceptionUtils.getStackTrace(e));
            }
        });
    }

}

