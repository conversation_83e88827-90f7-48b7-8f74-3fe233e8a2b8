package com.youpin.clear.infrastructure.process.business.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.gateway.AccountAssetsTypeGateway;
import com.youpin.clear.domain.process.FinancialTypeProcessor;
import com.youpin.clear.domain.process.SubBusFinancialProcessor;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.domain.servcie.RocketMqService;
import com.youpin.clear.infrastructure.config.AssetsTypePlatformUserIdProperties;
import com.youpin.clear.infrastructure.config.LockKeyWorkspaces;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord;
import com.youpin.clear.infrastructure.helper.UU898UserAccountHelper;
import com.youpin.clear.infrastructure.helper.UserAssetsAmountCheckerHelper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordExtMapper;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import com.youpin.clear.infrastructure.utils.RLockUtilsComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.youpin.clear.common.constant.ClearConstants.FILTER_243_TYPE_ID;
import static com.youpin.clear.common.enums.UserAssetsTypeEnum.*;

@Slf4j
@Service
public abstract class DefaultSubBusFinancialProcessor implements SubBusFinancialProcessor, FinancialTypeProcessor {

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    UU898UserAssetsRecordExtMapper userAssetsRecordExtMapper;

    @Autowired
    RLockUtilsComponent rLockUtilsComponent;

    @Autowired
    LockKeyWorkspaces lockKeyWorkspaces;

    @Autowired
    IdWorkService idWorkService;

    @Autowired
    @Qualifier("asyncTaskExecutor")
    ThreadPoolTaskExecutor asyncTaskExecutor;

    @Autowired
    AssetsTypePlatformUserIdProperties assetsTypePlatformUserIdProperties;

    @Autowired
    TransactionalProductService transactionalProductService;

    @Autowired
    RocketMqService rocketMqService;

    @Autowired
    AccountAssetsTypeGateway accountAssetsTypeGateway;

    @Autowired
    private UU898UserAccountHelper uu898UserAccountHelper;


    @Override
    public FinancialProcessorResultDTO process(FinancialProcessorDTO financialProcessorDTO) {
        try {
            //设置是否平台户流水缓冲标记 防止数据错乱
            financialProcessorDTO.setPlatformAccountBuffer(paymentClearParamsConfig.getPlatformAccountBufferBookkeepingFlag());
            //查询订单资金关联信息
            queryAssembleDataByOrderNo(financialProcessorDTO);
            //业务逻辑资金处理
            businessAssetsLogic(financialProcessorDTO);
            //数据排序
            subAssetsCustomOrder(financialProcessorDTO);
            //设置资金关系
            setAssetsRelation(financialProcessorDTO);
            //用户属性判断
            assetsTypeUserIdCheck(financialProcessorDTO);
            //前置幂等校验
            beforeDataIdempotenceCheck(financialProcessorDTO);
            //资金处理
            FinancialProcessorResultDTO processorResultDTO = accountUpdateLock(financialProcessorDTO);

            //如果开启 缓存记账 ,发送到下游的数据 状态需要改成成功
            if (financialProcessorDTO.checkPlatformAccountBuffer()) {
                financialProcessorDTO.getAbstractUserAssetsRecordDTOList().forEach(abstractUserAssetsRecordDTO -> {
                    if (abstractUserAssetsRecordDTO.checkAccountBufferBookkeeping()) {
                        abstractUserAssetsRecordDTO.setStatus(NetStatusEnum.SUCCESS.getCode());
                    }
                });
            }
            //异步发送 分账信息  账单信息
            asyncNotification(financialProcessorDTO);
            //同步发送平台户流水MQ
            //sendPlatformUserAssetsRecordMq(financialProcessorDTO);
            //异步调用子账户业务消耗
            asyncCallSubAccountBusinessConsume(financialProcessorDTO);

            return processorResultDTO;
        } catch (PaymentClearBusinessException e) {
            if (!(Objects.equals(financialProcessorDTO.getFinancialTypeEnum(), FinancialTypeEnum.Settlement) || Objects.equals(financialProcessorDTO.getFinancialTypeEnum(), FinancialTypeEnum.Special_Settlement))) {
                throw e;
            }
            if (!Objects.equals(e.getCode(), AssetsErrorCode.ALREADY_PROCESSED.getCode())) {
                throw e;
            }
            fillSettlementFrozenAmount(financialProcessorDTO, e);
            return FinancialProcessorResultDTO.builder().code(e.getCode()).msg(e.getMessage()).build();
        }
    }

    private void setAssetsRelation(FinancialProcessorDTO financialProcessorDTO) {
        Map<String, List<Integer>> allAccountAssetsTypeRelate = gatAllAccountAssetsTypeRelate();
        financialProcessorDTO.getAbstractUserAssetsRecordDTOList().forEach(item -> {
            AssetsRelationDTO assetsRelationDTO = new AssetsRelationDTO();
            assetsRelationDTO.setAssociatedAdditionList(allAccountAssetsTypeRelate.getOrDefault(AccountAssetsTypeRelateEnum.INCOME_FROM_EXPENDITURE.getPrefix() + item.getTypeId(), List.of()));
            assetsRelationDTO.setDbAssociatedAdditionList(allAccountAssetsTypeRelate.getOrDefault(AccountAssetsTypeRelateEnum.INCOME_FROM_EXPENDITURE.getPrefix() + item.getTypeId(), List.of()));
            assetsRelationDTO.setAssociatedSubtractionList(allAccountAssetsTypeRelate.getOrDefault(AccountAssetsTypeRelateEnum.ASSOCIATED_ADDITION.getPrefix() + item.getTypeId(), List.of()));
            assetsRelationDTO.setDbAssociatedSubtractionList(allAccountAssetsTypeRelate.getOrDefault(AccountAssetsTypeRelateEnum.DB_ASSOCIATED_ADDITION.getPrefix() + item.getTypeId(), List.of()));
            item.setAssetsRelationDTO(assetsRelationDTO);
        });
    }

    private void asyncCallSubAccountBusinessConsume(FinancialProcessorDTO financialProcessorDTO) {
        CompletableFuture.runAsync(() -> {
            try {
                // 查询数据
                List<AbstractUserAssetsRecordDTO> assetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
                for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : assetsRecordDTOList) {
                    uu898UserAccountHelper.callSubAccountBusinessConsume(abstractUserAssetsRecordDTO);
                    log.info("异步调用子账户业务消耗 完成 json:{}", JSON.toJSONString(abstractUserAssetsRecordDTO));
                    if (CollectionUtils.isNotEmpty(abstractUserAssetsRecordDTO.getCallSubAccountBusinessRevokeDTOList())) {
                        for (CallSubAccountBusinessRevokeDTO callSubAccountBusinessRevokeDTO : abstractUserAssetsRecordDTO.getCallSubAccountBusinessRevokeDTOList()) {
                            log.info("异步调用子账户业务撤销开始 json:{}", JSON.toJSONString(callSubAccountBusinessRevokeDTO));
                            uu898UserAccountHelper.callSubAccountBusinessRevoke(callSubAccountBusinessRevokeDTO);
                            log.info("异步调用子账户业务撤销完成 json:{}", JSON.toJSONString(callSubAccountBusinessRevokeDTO));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("异步调用子账户业务消耗 异常:  e:{}", ExceptionUtils.getStackTrace(e));
            }
        }, asyncTaskExecutor).exceptionally(e -> {
            log.error("异步调用子账户业务消耗 失败 :  e:{}", ExceptionUtils.getStackTrace(e));
            return null;
        });
    }

    private void fillSettlementFrozenAmount(FinancialProcessorDTO financialProcessorDTO, PaymentClearBusinessException e) {
        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = financialProcessorDTO.getUserAssetsRecordDTOLinkList();
        if (CollectionUtils.isEmpty(userAssetsRecordDTOLinkList)) {
            log(Level.ERROR, "关联资金明细为空,订单号:{}", financialProcessorDTO, financialProcessorDTO.getOrderNo());
            throw e;
        }
        // 获取用户资金
        FinancialProcessorAssetInfoDTO userSettleAssetInfo = financialProcessorDTO.getAssetInfoDTOList().stream().filter(abstractUserAssetsRecordDTO -> paymentClearParamsConfig.getSettlementTypeIdList().contains(abstractUserAssetsRecordDTO.getTypeId())).findFirst().orElse(null);

        if (Objects.isNull(userSettleAssetInfo)) {
            log(Level.ERROR, "正向结算资金信息为空,订单号:{}", financialProcessorDTO, financialProcessorDTO.getOrderNo());
            throw e;
        }

        UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO = userAssetsRecordDTOLinkList.stream().filter(item -> Objects.equals(item.getOrderNo(), financialProcessorDTO.getOrderNo()) && Objects.equals(item.getPayOrderNo(), financialProcessorDTO.getPayOrderNo()) && Objects.equals(item.getSerialNo(), financialProcessorDTO.getSerialNo()) && Objects.equals(item.getUserId(), userSettleAssetInfo.getUserId())).filter(item -> {
            if (Objects.equals(userSettleAssetInfo.getTypeId(), TYPE_5.getTypeId())) {
                return Objects.equals(item.getTypeId(), TYPE_247.getTypeId());
            } else if (Objects.equals(userSettleAssetInfo.getTypeId(), TYPE_94.getTypeId())) {
                return Objects.equals(item.getTypeId(), TYPE_256.getTypeId());
            } else {
                log(Level.INFO, "无正向结算资金信息,订单号:{}", financialProcessorDTO, financialProcessorDTO.getOrderNo());
            }
            return false;
        }).findFirst().orElse(null);
        if (Objects.isNull(uu898UserAssetsRecordDTO)) {
            financialProcessorDTO.setSettleFrozeMoney(BigDecimal.ZERO);
            financialProcessorDTO.setPrivateSettleFrozeMoney(BigDecimal.ZERO);
        } else {
            if (Objects.equals(uu898UserAssetsRecordDTO.getTypeId(), TYPE_247.getTypeId())) {
                financialProcessorDTO.setSettleFrozeMoney(uu898UserAssetsRecordDTO.getThisMoney());
            } else if (Objects.equals(uu898UserAssetsRecordDTO.getTypeId(), TYPE_256.getTypeId())) {
                financialProcessorDTO.setPrivateSettleFrozeMoney(uu898UserAssetsRecordDTO.getThisMoney());
            }
        }
    }


    private void subAssetsCustomOrder(FinancialProcessorDTO financialProcessorDTO) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        if (abstractUserAssetsRecordDTOList.size() > Constant.CONSTANT_INTEGER_1) {
            List<Integer> customOrderList = SubAssetsCustomOrderEnum.getByTypeCode(financialProcessorDTO.getSubBusTypeFrontEnum());
            if (CollectionUtils.isNotEmpty(customOrderList)) {
                UserAssetsAmountCheckerHelper.customSort(abstractUserAssetsRecordDTOList, customOrderList);
                //修改一下排序时间
                abstractUserAssetsRecordDTOList.forEach(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.setAddTime(LocalDateTime.now()));
            }
        }
    }

    /**
     * 用户属性判断
     * 1.判断用户是否平台户
     */
    private void assetsTypeUserIdCheck(FinancialProcessorDTO financialProcessorDTO) {
        // 判断用户是否平台户
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            // 判断是否开启平台户流水缓冲标记&&设置是否开启缓冲记账
            if (financialProcessorDTO.checkPlatformAccountBuffer()) {
                abstractUserAssetsRecordDTO.setAccountBufferBookkeeping(checkPlatformAccountUserId(abstractUserAssetsRecordDTO.getUserId()));
            }
        }
    }

    private void sendPlatformUserAssetsRecordMq(FinancialProcessorDTO financialProcessorDTO) {
        //同步发送平台户流水MQ
        if (!financialProcessorDTO.checkPlatformAccountBuffer()) {
            return;
        }
        List<PlatformAccountRecordDTO> platformAccountRecordDTOList = financialProcessorDTO.getPlatformAccountRecordDTOList();
        if (null == platformAccountRecordDTOList || platformAccountRecordDTOList.isEmpty()) {
            log.info("[账务交易] 缓冲记账发送平台户流水MQ sendPlatformUserAssetsRecordMq 数据为null 不发送");
            return;
        }
        rocketMqService.sendPlatformUserAssetsRecordMq(platformAccountRecordDTOList, Boolean.FALSE);
    }

    /**
     * 获取资金类型关系
     */
    private Map<String, List<Integer>> gatAllAccountAssetsTypeRelate() {
        return accountAssetsTypeGateway.gatAllAccountAssetsTypeRelate();
    }

    private static Long getAbstractUserAssetsRecordDTOByUserId(List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList, List<Integer> assetsCodeList) {
        AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_byUserId = abstractUserAssetsRecordDTOList.stream().filter(item -> assetsCodeList.contains(item.getTypeId())).findFirst().orElse(null);
        if (abstractUserAssetsRecordDTO_byUserId == null) {
            return null;
        }
        return abstractUserAssetsRecordDTO_byUserId.getUserId();
    }


    @Override
    public FinancialProcessorResultDTO accountUpdateLock(FinancialProcessorDTO dto) {
        //获取支持资金变动的用户id
        List<Long> userIdList = dto.getSupportUserIdList();
        //获取支持资金变动的用户id--不包含平台户
        List<Long> userIdListNotPlatformList = dto.getSupportUserIdListNotPlatform(userIdList);
        //获取批量锁 --不锁平台户-剩余用户ID
        List<String> lockKeyList = getLockUserIdList(userIdListNotPlatformList);
        //如果使用缓冲记账  lockKeyList 可能会null 特殊情况 可能产生锁冲突 这个时候 需要正常加锁
        if (null == userIdListNotPlatformList || userIdListNotPlatformList.isEmpty()) {
            lockKeyList = getLockUserIdList(userIdList);
        }
        rLockUtilsComponent.lockMultipleHandle(lockKeyList, paymentClearParamsConfig.getAssetsMultiLockWaitTime(), tm -> transactionalProductService.accountUpdateTransactional(dto, userIdList));
        return FinancialProcessorResultDTO.SUCCESS;
    }


    private void asyncNotification(FinancialProcessorDTO dto) {
        CompletableFuture.runAsync(() -> {
            try {
                // 查询数据
                List<AbstractUserAssetsRecordDTO> assetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
                // 异步发送分账MQ
                rocketMqService.sendSeparateMessage(assetsRecordDTOList);
                // 异步发送账单MQ
                if (dto.isSendBill()) {
                    rocketMqService.sendBillMessage(assetsRecordDTOList);
                }
            } catch (Exception e) {
                log.error("异步发送信息失败: orderNo:{} e:{}", dto.getOrderNo(), ExceptionUtils.getStackTrace(e));
            }
            log.info("异步发送信息结束 orderNo:{}", dto.getOrderNo());
        }, asyncTaskExecutor).exceptionally(e -> {
            log.error("异步线程内部 异步发送信息失败: orderNo:{} e:{}", dto.getOrderNo(), ExceptionUtils.getStackTrace(e));
            return null;
        });
    }

    private void queryAssembleDataByOrderNo(FinancialProcessorDTO dto) {
        if (!dto.isNeedQueryLinkData()) {
            return;
        }
        if (StringUtils.isBlank(dto.getOrderNo())) {
            log(Level.WARN, "前置数据校验:订单不能为空.", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "订单号不能为空");
        }
        //查询订单关联数据
        List<UU898UserAssetsRecord> uu898UserAssetsRecordList = userAssetsRecordExtMapper.queryUserAssetsRecordDTOList(null, null, dto.getOrderNo(), null, null, null);
        dto.setUserAssetsRecordDTOLinkList(BeanUtilsWrapper.convertList(uu898UserAssetsRecordList, UU898UserAssetsRecordDTO::new));
    }

    private void businessAssetsLogic(FinancialProcessorDTO financialProcessorDTO) {
        FinancialTypeEnum financialTypeEnum = financialProcessorDTO.getFinancialTypeEnum();
        NetStatusEnum netStatusEnum = financialProcessorDTO.getNetStatusEnum();
        switch (financialTypeEnum) {
            case Pay:
                handlePay(netStatusEnum, financialProcessorDTO);
                break;
            case Refund:
                handleRefund(netStatusEnum, financialProcessorDTO);
                break;
            case Settlement:
                handleSettlement(financialProcessorDTO);
                break;
            case Special_Settlement:
                handleSpecialSettlement(financialProcessorDTO);
                break;
            case Withdrawal:
                handleWithdrawal(netStatusEnum, financialProcessorDTO);
                break;
            default:
                throw new IllegalStateException("Unexpected financial type: " + financialTypeEnum);
        }
        if (financialProcessorDTO.isAbstractUserAssetsRecordDTOListEmpty()) {
            log(Level.ERROR, "业务:{} 类型:{} 资金明细解析失败 {} ", financialProcessorDTO, financialProcessorDTO.getSubBusTypeFrontEnum().getName(), financialTypeEnum.getName(), financialProcessorDTO.getAssetInfoDTOList());
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "资金明细解析失败");
        }
    }


    private void handleWithdrawal(NetStatusEnum netStatusEnum, FinancialProcessorDTO financialProcessorDTO) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(financialProcessorDTO);
        financialProcessorDTO.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
        switch (netStatusEnum) {
            case SUCCESS:
                withdrawalSuccess(financialProcessorDTO);
                break;
            case FAIL:
                withdrawalFail(financialProcessorDTO);
                break;
            case PROCESSING:
                withdrawalIng(financialProcessorDTO);
                break;
            default:
                throw new IllegalStateException("Unexpected network status for Pay: " + netStatusEnum);
        }
    }

    private void handlePay(NetStatusEnum netStatusEnum, FinancialProcessorDTO financialProcessorDTO) {
        switch (netStatusEnum) {
            case SUCCESS:
                paySuccess(financialProcessorDTO);
                break;
            case FAIL:
                payFail(financialProcessorDTO);
                break;
            case PROCESSING:
                payIng(financialProcessorDTO);
                break;
            default:
                throw new IllegalStateException("Unexpected network status for Pay: " + netStatusEnum);
        }
        //支付服务费处理
        payServiceCharge(financialProcessorDTO);
    }

    private void handleRefund(NetStatusEnum netStatusEnum, FinancialProcessorDTO financialProcessorDTO) {
        switch (netStatusEnum) {
            case SUCCESS:
                refundSuccess(financialProcessorDTO);
                break;
            case FAIL:
                refundFail(financialProcessorDTO);
                break;
            case PROCESSING:
                refundIng(financialProcessorDTO);
                break;
            default:
                throw new IllegalStateException("Unexpected network status for Refund: " + netStatusEnum);
        }
    }

    private void handleSpecialSettlement(FinancialProcessorDTO financialProcessorDTO) {
        specialSettlement(financialProcessorDTO);
        //2025-07-16 出售结算冻结逻辑
//        sellFrozenSettlement(financialProcessorDTO);
        //2025-07-17 单独传247的逻辑
        sellFrozenSettlement247(financialProcessorDTO);
        fillPrivateSettleFrozeMoney(financialProcessorDTO);
    }

    private void sellFrozenSettlement247(FinancialProcessorDTO financialProcessorDTO) {

        //资金侧
        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = financialProcessorDTO.getUserAssetsRecordDTOLinkList();

        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();

        //判断是否存在247
        abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(TYPE_247.getTypeId())).findFirst().ifPresent(item -> {
            //是否跳过 硬处理
            if (Boolean.TRUE.equals(financialProcessorDTO.getHardProcess())) {
                log.info(" 247资金处理  247 跳过硬处理");
                return;
            }

            List<Integer> settlementTypeIdList = paymentClearParamsConfig.getSettlementTypeIdList();
            List<Integer> settlementTypeIdListSubtract = paymentClearParamsConfig.getSettlementTypeIdListSubtract();

            //修整247的资金明细
            if (userAssetsRecordDTOLinkList == null || userAssetsRecordDTOLinkList.isEmpty()) {
                log(Level.ERROR, " 247资金处理 资金明细缺失,未找到原订单1", financialProcessorDTO);
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "资金明细缺失,未找到原订单1:" + item.getOrderNo() + "::" + item.getUserId());
            }
            //获取 用户ID 批量适用
            List<Long> uu898UserAssetsRecordDTO_settlement_userId = userAssetsRecordDTOLinkList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdList.contains(abstractUserAssetsRecordDTO.getTypeId())).map(UU898UserAssetsRecordDTO::getUserId).collect(Collectors.toList());


            if (uu898UserAssetsRecordDTO_settlement_userId.isEmpty()) {
                log(Level.ERROR, " 247资金处理 资金明细缺失,未找到原订单2", financialProcessorDTO);
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "资金明细缺失,未找到原订单2:" + item.getOrderNo() + "::" + item.getUserId());
            }
            //判断用户对不对
            if (!uu898UserAssetsRecordDTO_settlement_userId.contains(item.getUserId())) {
                log(Level.ERROR, " 247资金处理 用户对不对 {} {}", financialProcessorDTO, uu898UserAssetsRecordDTO_settlement_userId, item.getUserId());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "用户不对:" + item.getOrderNo() + "::" + item.getUserId());

            }
            BigDecimal settlementMoney = userAssetsRecordDTOLinkList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdList.contains(abstractUserAssetsRecordDTO.getTypeId())).map(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getThisMoney().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal settlementMoney2 = BigDecimal.ZERO;
            if (abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(TYPE_94.getTypeId()))) {
                settlementMoney2 = abstractUserAssetsRecordDTOList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdList.contains(abstractUserAssetsRecordDTO.getTypeId())).map(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getChangeMoney().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
            //需要减去的金额
            BigDecimal subtractMoney = userAssetsRecordDTOLinkList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdListSubtract.contains(abstractUserAssetsRecordDTO.getTypeId())).map(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getThisMoney().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (settlementMoney.compareTo(BigDecimal.ZERO) <= 0) {
                log(Level.ERROR, " 247资金处理 结算金额异常1: settlementMoney:{}  subtractMoney:{}", financialProcessorDTO, settlementMoney, subtractMoney);
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "结算金额异常1:" + item.getOrderNo() + "::" + item.getUserId());
            }
            //判断金额
            BigDecimal sellFrozenSettlementMoney = settlementMoney.add(settlementMoney2).subtract(subtractMoney);
            log(Level.INFO, " 247资金处理 结算金额: settlementMoney:{}  subtractMoney:{} sellFrozenSettlementMoney:{} getChangeMoney:{}", financialProcessorDTO, settlementMoney, subtractMoney, sellFrozenSettlementMoney, item.getChangeMoney());
            if (item.getChangeMoney().abs().compareTo(sellFrozenSettlementMoney) > 0) {
                log(Level.ERROR, " 247资金处理 结算金额异常2: settlementMoney:{}  subtractMoney:{} sellFrozenSettlementMoney:{}", financialProcessorDTO, settlementMoney, subtractMoney, sellFrozenSettlementMoney);
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "结算金额异常2:" + item.getOrderNo() + "::" + item.getUserId());
            }
        });
    }

    private void handleSettlement(FinancialProcessorDTO financialProcessorDTO) {
        settlement(financialProcessorDTO);
        settlementCheck(financialProcessorDTO);
        //2025-07-16 出售结算冻结逻辑
        sellFrozenSettlement(financialProcessorDTO);
        fillSettleFrozeMoney(financialProcessorDTO);
    }

    private void fillSettleFrozeMoney(FinancialProcessorDTO financialProcessorDTO) {
        AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO = getFilterAbstractUserAssetsRecordDTO(financialProcessorDTO.getAbstractUserAssetsRecordDTOList(), TYPE_247.getTypeId());
        if (Objects.isNull(abstractUserAssetsRecordDTO)) {
            financialProcessorDTO.setSettleFrozeMoney(BigDecimal.ZERO);
            return;
        }
        financialProcessorDTO.setSettleFrozeMoney(abstractUserAssetsRecordDTO.getChangeMoney().abs());
    }

    private void fillPrivateSettleFrozeMoney(FinancialProcessorDTO financialProcessorDTO) {
        AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO = getFilterAbstractUserAssetsRecordDTO(financialProcessorDTO.getAbstractUserAssetsRecordDTOList(), TYPE_256.getTypeId());
        if (Objects.isNull(abstractUserAssetsRecordDTO)) {
            financialProcessorDTO.setPrivateSettleFrozeMoney(BigDecimal.ZERO);
            return;
        }
        financialProcessorDTO.setPrivateSettleFrozeMoney(abstractUserAssetsRecordDTO.getChangeMoney().abs());
    }


    private AbstractUserAssetsRecordDTO getFilterAbstractUserAssetsRecordDTO(List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList, Integer typeId) {
        return abstractUserAssetsRecordDTOList.stream().filter(item -> Objects.equals(item.getTypeId(), typeId)).findFirst().orElse(null);
    }

    @Override
    public void withdrawalSuccess(FinancialProcessorDTO dto) {
    }

    @Override
    public void withdrawalIng(FinancialProcessorDTO dto) {
    }

    @Override
    public void withdrawalFail(FinancialProcessorDTO dto) {
    }

    /**
     * 出售结算冻结逻辑
     */
    void sellFrozenSettlement(FinancialProcessorDTO financialProcessorDTO) {
        //冻结或结算逻辑开关
        Boolean sellFrozenSettlementFlag = paymentClearParamsConfig.getSellFrozenSettlementFlag();
        if (!Boolean.TRUE.equals(sellFrozenSettlementFlag)) {
            return;
        }
        //冻结或结算入参逻辑开关
        Boolean sellFrozenSettlementParamFlag = paymentClearParamsConfig.getSellFrozenSettlementParamFlag();
        if (Boolean.TRUE.equals(sellFrozenSettlementParamFlag)) {
            if (null != financialProcessorDTO.getSettleAccountType() && financialProcessorDTO.getSettleAccountType().equals(SettleAccountTypeEnum.MONEY.getCode())) {
                log(Level.WARN, " 247资金处理 冻结或结算入参逻辑开关:{}", financialProcessorDTO, financialProcessorDTO.getSettleAccountType());
                return;
            }
        }
        List<Integer> settlementTypeIdList = paymentClearParamsConfig.getSettlementTypeIdList();
        List<Integer> settlementTypeIdListSubtract = paymentClearParamsConfig.getSettlementTypeIdListSubtract();

        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        // 获取用户资金5的 金额
        AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_settlement = abstractUserAssetsRecordDTOList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdList.contains(abstractUserAssetsRecordDTO.getTypeId())).findFirst().orElse(null);

        BigDecimal settlementMoney = abstractUserAssetsRecordDTOList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdList.contains(abstractUserAssetsRecordDTO.getTypeId())).map(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getChangeMoney().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        //需要减去的金额
        BigDecimal subtractMoney = abstractUserAssetsRecordDTOList.stream().filter(abstractUserAssetsRecordDTO -> settlementTypeIdListSubtract.contains(abstractUserAssetsRecordDTO.getTypeId())).map(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getChangeMoney().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        if (null == abstractUserAssetsRecordDTO_settlement || settlementMoney.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        BigDecimal sellFrozenSettlementMoney = settlementMoney.subtract(subtractMoney);

        log.info("用户:{} 结算金额:{} 需要减去的金额:{} 冻结金额:{}", abstractUserAssetsRecordDTO_settlement.getUserId(), settlementMoney, subtractMoney, sellFrozenSettlementMoney);

        if (sellFrozenSettlementMoney.compareTo(BigDecimal.ZERO) > 0) {
            AbstractUserAssetsRecordDTO sellFrozenSettlementRecord = buildAssetRecord(UserAssetsTypeEnum.TYPE_247, financialProcessorDTO, abstractUserAssetsRecordDTO_settlement.getUserId(), abstractUserAssetsRecordDTO_settlement.getPayChannel(), financialProcessorDTO.getNetStatusEnum(), AmountUtils.convertToCent(sellFrozenSettlementMoney));
            List<AbstractUserAssetsRecordDTO> dtoList = new ArrayList<>();
            dtoList.add(sellFrozenSettlementRecord);
            financialProcessorDTO.addAbstractUserAssetsRecordDTO(dtoList);
        }
    }

    @Override
    public void payIng(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
    }

    @Override
    public void paySuccess(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
    }

    @Override
    public void payFail(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
    }


    private void processingToSuccessOrFail(FinancialProcessorDTO dto) {
        if (dto.isUserAssetsRecordDTOLinkListEmpty()) {
            log(Level.INFO, "业务:{} 数据库关联资金记录为空", dto, dto.getSubBusTypeFrontEnum().getName());
            return;
        }
        //账务侧流水
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        //资金测流水
        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = dto.getUserAssetsRecordDTOLinkList();

        //数据幂等判断
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            String serialNo = StringUtils.isNotBlank(abstractUserAssetsRecordDTO.getSerialNo()) ? abstractUserAssetsRecordDTO.getSerialNo().trim() : null;
            String payOrderNo = StringUtils.isNotBlank(abstractUserAssetsRecordDTO.getPayOrderNo()) ? abstractUserAssetsRecordDTO.getPayOrderNo().trim() : null;
            Integer status = abstractUserAssetsRecordDTO.getStatus();
            Long userId = abstractUserAssetsRecordDTO.getUserId();
            Integer typeId = abstractUserAssetsRecordDTO.getTypeId();
            BigDecimal changeMoney = abstractUserAssetsRecordDTO.getChangeMoney();

            if (null == userId) {
                log(Level.ERROR, "数据校验:用户ID不能为空. typeId:{}", dto, typeId);
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "用户ID不能为空");
            }

            for (UU898UserAssetsRecordDTO userAssetsRecordDTO : userAssetsRecordDTOLinkList) {
                String serialNoEquals = StringUtils.isNotBlank(userAssetsRecordDTO.getSerialNo()) ? userAssetsRecordDTO.getSerialNo().trim() : null;
                String payOrderNoEquals = StringUtils.isNotBlank(userAssetsRecordDTO.getPayOrderNo()) ? userAssetsRecordDTO.getPayOrderNo().trim() : null;
                Integer statusUu898 = userAssetsRecordDTO.getStatus();
                Long userIdEquals = userAssetsRecordDTO.getUserId();
                Integer typeIdEquals = userAssetsRecordDTO.getTypeId();
                BigDecimal changeMoneyUu898 = BigDecimal.ZERO;

                if (null == userIdEquals) {
                    log(Level.ERROR, "前置数据校验:用户ID不能为空.", dto);
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "用户ID不能为空");
                }

                if (null != userAssetsRecordDTO.getThisMoney() && userAssetsRecordDTO.getThisMoney().compareTo(BigDecimal.ZERO) != 0) {
                    changeMoneyUu898 = userAssetsRecordDTO.getThisMoney();
                } else if (null != userAssetsRecordDTO.getThisBlockMoney() && userAssetsRecordDTO.getThisBlockMoney().compareTo(BigDecimal.ZERO) != 0) {
                    changeMoneyUu898 = userAssetsRecordDTO.getThisBlockMoney();
                } else if (null != userAssetsRecordDTO.getThisPurchaseMoney() && userAssetsRecordDTO.getThisPurchaseMoney().compareTo(BigDecimal.ZERO) != 0) {
                    changeMoneyUu898 = userAssetsRecordDTO.getThisPurchaseMoney();
                }
                boolean flag = userId.equals(userIdEquals) && StringUtils.equals(serialNo, serialNoEquals) && typeId.equals(typeIdEquals);

                boolean payOrderNoFlag2 = null != payOrderNo && null != payOrderNoEquals && StringUtils.equals(payOrderNo, payOrderNoEquals);

                boolean payOrderNoIsEmpty = null == payOrderNo && null == payOrderNoEquals;

                boolean changeMoneyFlag = changeMoney.abs().compareTo(changeMoneyUu898.abs()) == ClearConstants.CONSTANT_INTEGER_0;

                //参数比对是否相同
                if (flag) {
                    if ((payOrderNoFlag2 || payOrderNoIsEmpty) && changeMoneyFlag && statusUu898.equals(status)) {
                        log(Level.WARN, "前置数据校验:支付单号校验流水号,支付单号 重复.", dto);
                        throw new PaymentClearBusinessException(FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getMsg());
                    }
                    if ((payOrderNoFlag2 || payOrderNoIsEmpty) && statusUu898.equals(status)) {
                        log(Level.WARN, "流水号相同,订单相同,类型相同,用户相同,金额不同 数据疑似重复", dto);
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "校验异常,入参有误");
                    }
                    //再判断一次状态
                    // 进行中 >>  成功
                    // 进行中 >>  失败
                    if (statusUu898.equals(NetStatusEnum.PROCESSING.getCode()) && (status.equals(NetStatusEnum.FAIL.getCode()) || status.equals(NetStatusEnum.SUCCESS.getCode()))) {
                        //说明数据相同 赋值 替换现有数据
                        abstractUserAssetsRecordDTO.setId(userAssetsRecordDTO.getId());

                        abstractUserAssetsRecordDTO.setMoney(userAssetsRecordDTO.getMoney());
                        abstractUserAssetsRecordDTO.setThisMoney(userAssetsRecordDTO.getThisMoney());
                        abstractUserAssetsRecordDTO.setAfterMoney(userAssetsRecordDTO.getAfterMoney());

                        abstractUserAssetsRecordDTO.setBlockMoney(userAssetsRecordDTO.getAfterBlockMoney());
                        abstractUserAssetsRecordDTO.setThisBlockMoney(userAssetsRecordDTO.getThisBlockMoney());
                        abstractUserAssetsRecordDTO.setAfterBlockMoney(userAssetsRecordDTO.getAfterBlockMoney());

                        abstractUserAssetsRecordDTO.setPurchaseMoney(userAssetsRecordDTO.getPurchaseMoney());
                        abstractUserAssetsRecordDTO.setThisPurchaseMoney(userAssetsRecordDTO.getThisPurchaseMoney());
                        abstractUserAssetsRecordDTO.setAfterPurchaseMoney(userAssetsRecordDTO.getAfterPurchaseMoney());

                        abstractUserAssetsRecordDTO.setAddTime(userAssetsRecordDTO.getAddTime());
                        abstractUserAssetsRecordDTO.setSerialNo(userAssetsRecordDTO.getSerialNo());
                        abstractUserAssetsRecordDTO.setPayOrderNo(userAssetsRecordDTO.getPayOrderNo());
//                        abstractUserAssetsRecordDTO.setTreadNo(userAssetsRecordDTO.getTreadNo());

                        abstractUserAssetsRecordDTO.setRemark(userAssetsRecordDTO.getRemark());
                        abstractUserAssetsRecordDTO.setAccountName(userAssetsRecordDTO.getAccountName());
                        abstractUserAssetsRecordDTO.setGenSource(userAssetsRecordDTO.getGenSource());

                    } else {
                        log(Level.ERROR, "前置数据校验:状态位校验异常  资金ID: {} 数据库状态:{} 入参状态:{}", dto, userAssetsRecordDTO.getId(), statusUu898, status);
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "状态位校验异常,入参有误");
                    }
                }
            }
        }

    }

    @Override
    public void refundIng(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
        log(Level.WARN, "退款进行中 ", dto);

    }

    @Override
    public void refundSuccess(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
    }

    @Override
    public void refundFail(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
        // 判断 211 不能大于 失败金额
        //211 属于 余额补偿入金 再退款失败的事时候使用
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_211.getTypeId())) {
                //去除 211 对应的 支付金额
                BigDecimal refundMoney = abstractUserAssetsRecordDTOList.stream().filter(item -> item.getStatus().equals(NetStatusEnum.FAIL.getCode()) && ClearConstants.TYPE_211_BALANCE_COMPENSATION_RELATIONS.contains(item.getTypeId())).map(item -> item.getChangeMoney().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (abstractUserAssetsRecordDTO.getChangeMoney().abs().compareTo(refundMoney) > 0) {
                    log(Level.ERROR, "退款失败:入金补偿大于退款金额,请检查数据 refundMoney:{} ChangeMoney:{}", dto, refundMoney, abstractUserAssetsRecordDTO.getChangeMoney());
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "入金补偿大于退款金额,入参有误");
                }
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                abstractUserAssetsRecordDTO.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
            }
            if (ClearConstants.TYPE_211_BALANCE_COMPENSATION_RELATIONS.contains(abstractUserAssetsRecordDTO.getTypeId())) {
                continue;
            }
            abstractUserAssetsRecordDTO.setStatus(NetStatusEnum.SUCCESS.getCode());
        }
//        abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_211.getTypeId())).findFirst().ifPresent(abstractUserAssetsRecordDTO_211 -> {
//            //取出失败状态的资金明细
//            BigDecimal refundMoney = abstractUserAssetsRecordDTOList.stream().filter(item -> item.getStatus().equals(NetStatusEnum.FAIL.getCode()) && !item.getTypeId().equals(UserAssetsTypeEnum.TYPE_211.getTypeId())).map(item -> item.getChangeMoney().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
//            if (abstractUserAssetsRecordDTO_211.getChangeMoney().abs().compareTo(refundMoney) > 0) {
//                log(Level.ERROR, "退款失败:金额入金补偿大于退款金额,请检查数据 refundMoney:{} ChangeMoney:{}", dto, refundMoney, abstractUserAssetsRecordDTO_211.getChangeMoney());
//                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "金额入金补偿大于退款金额,入参有误");
//            } else {
//                abstractUserAssetsRecordDTO_211.setStatus(NetStatusEnum.SUCCESS.getCode());
//                abstractUserAssetsRecordDTO_211.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
//                abstractUserAssetsRecordDTO_211.setPayChannel(DoNetPayChannelEnum.Balance.getCode());
//            }
//        });
    }

    @Override
    public void settlement(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
    }

    @Override
    public void beforeDataIdempotenceCheck(FinancialProcessorDTO dto) {
        //判断订单号是否为null
        if (StringUtils.isBlank(dto.getOrderNo())) {
            log(Level.WARN, "前置数据校验:订单不能为空.", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "订单号不能为空");
        }
        if (StringUtils.isBlank(dto.getSerialNo())) {
            log(Level.WARN, "前置数据校验:流水号不能为空.", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR.getCode(), "流水号不能为空");
        }

        //数据变动检查
        processingToSuccessOrFail(dto);
        //资金校验
        if (dto.getFinancialTypeEnum().equals(FinancialTypeEnum.Refund)) {

            //TODO  这个地方需要测试
            Map<String, List<Integer>> allAccountAssetsTypeRelate = gatAllAccountAssetsTypeRelate();

            List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();

            for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {

                Integer typeId = abstractUserAssetsRecordDTO.getTypeId();
                if (typeId.equals(UserAssetsTypeEnum.TYPE_214.getTypeId()) || typeId.equals(UserAssetsTypeEnum.TYPE_215.getTypeId()) || typeId.equals(UserAssetsTypeEnum.TYPE_218.getTypeId()) || typeId.equals(UserAssetsTypeEnum.TYPE_93.getTypeId())) {
                    continue;
                }

                /*
                  收入支出关系
                 */
                List<Integer> reverseTypeEnumList = allAccountAssetsTypeRelate.get(AccountAssetsTypeRelateEnum.INCOME_FROM_EXPENDITURE.getPrefix() + typeId);

                /*
                  关联需要减的数据
                 */
                List<Integer> integerList = allAccountAssetsTypeRelate.getOrDefault(AccountAssetsTypeRelateEnum.ASSOCIATED_ADDITION.getPrefix() + typeId, new ArrayList<>());

                integerList.addAll(allAccountAssetsTypeRelate.getOrDefault(AccountAssetsTypeRelateEnum.DB_ASSOCIATED_ADDITION.getPrefix() + typeId, new ArrayList<>()));
                if (typeId.equals(UserAssetsTypeEnum.TYPE_4.getTypeId())) {
                    integerList.add(UserAssetsTypeEnum.TYPE_222.getTypeId()); //  收支使用 分账不使用
                    reverseTypeEnumList.add(UserAssetsTypeEnum.TYPE_221.getTypeId()); //  收支使用 分账不使用
                }
                //把自身放进去
                integerList.add(typeId);
                //支出金额
                BigDecimal subtractBigDecimal;
                if (typeId.equals(UserAssetsTypeEnum.TYPE_231.getTypeId()) || typeId.equals(UserAssetsTypeEnum.TYPE_233.getTypeId()) || typeId.equals(UserAssetsTypeEnum.TYPE_237.getTypeId())) {
                    subtractBigDecimal = findUserAssetsRecordDTOLinkList(dto, reverseTypeEnumList, false, null).add(findAbstractUserAssetsRecordDTOList(dto, reverseTypeEnumList));
                } else {
                    subtractBigDecimal = findUserAssetsRecordDTOLinkList(dto, reverseTypeEnumList, false, null);
                }

                //减去金额
                BigDecimal integerBigDecimal = findUserAssetsRecordDTOLinkList(dto, integerList, false, null);

                if (typeId.equals(UserAssetsTypeEnum.TYPE_4.getTypeId())) {
                    //得到本次退款总金额
                    BigDecimal refundSumBigDecimal = findAbstractUserAssetsRecordDTOList(dto, List.of(UserAssetsTypeEnum.TYPE_4.getTypeId(), UserAssetsTypeEnum.TYPE_222.getTypeId()));
                    if (null != refundSumBigDecimal && subtractBigDecimal.subtract(integerBigDecimal).compareTo(refundSumBigDecimal) < 0) {
                        log(Level.ERROR, "退款 收支关系 总支付金额小于总退款金额 总支付金额:{},已支出金额:{} 总退款金额:{}", dto, subtractBigDecimal, integerBigDecimal, refundSumBigDecimal);
                        throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "总支付金额小于总退款金额,不能退款");
                    }
                }


                if (null != reverseTypeEnumList && subtractBigDecimal.compareTo(BigDecimal.ZERO) == 0) {
                    log(Level.ERROR, "退款 收支关系 typeId:{} 未找到资金正向信息,不能退款", dto, typeId);
                    throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "未找到资金正向信息,不能退款");
                }

                log(Level.INFO, "退款 收支关系 typeId:{} reverseTypeList:{}  integerList:{} 退款金额:{}  支出金额:{}  需要减去的金额:{}", dto, typeId, reverseTypeEnumList, integerList, abstractUserAssetsRecordDTO.getChangeMoney(), subtractBigDecimal, integerBigDecimal);

                if (abstractUserAssetsRecordDTO.getChangeMoney().compareTo(subtractBigDecimal.subtract(integerBigDecimal)) > 0) {
                    log(Level.ERROR, " 退款金额大于支出金额 {} -- {} --{}   退款金额:{}  支出金额:{}  需要减去的金额:{}", dto, typeId, reverseTypeEnumList, integerList, abstractUserAssetsRecordDTO.getChangeMoney(), subtractBigDecimal, integerBigDecimal);
                    throw new PaymentClearBusinessException(ErrorCode.REFUND_AMOUNT_GREATER_THAN_EXPEND_AMOUNT, "退款金额大于支出金额");
                }

            }
            UserAssetsAmountCheckerDTO settlementCheckerDTO = UserAssetsAmountCheckerHelper.getUserAssetsAmountCheckerDTO(abstractUserAssetsRecordDTOList);
            if (settlementCheckerDTO.getSettlementFreezeMoney().getValue().compareTo(BigDecimal.ZERO) > 0) {
                //获取需要的 用户ID
                Long abstractUserAssetsRecordDTO_byUserId = getAbstractUserAssetsRecordDTOByUserId(abstractUserAssetsRecordDTOList, List.of(248, 257));
                List<Integer> actualFrozenAmountList = new ArrayList<>(), actualFrozenSettlementAmountList = new ArrayList<>();

                if (abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_248.getTypeId()))) {
                    actualFrozenAmountList = List.of(UserAssetsTypeEnum.TYPE_247.getTypeId());
                    actualFrozenSettlementAmountList = List.of(UserAssetsTypeEnum.TYPE_248.getTypeId());
                }
                if (abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_257.getTypeId()))) {
                    actualFrozenAmountList = List.of(UserAssetsTypeEnum.TYPE_256.getTypeId());
                    actualFrozenSettlementAmountList = List.of(UserAssetsTypeEnum.TYPE_257.getTypeId());

                }

                //已冻结金额
                BigDecimal actualFrozenAmount = findUserAssetsRecordDTOLinkList(dto, actualFrozenAmountList, true, abstractUserAssetsRecordDTO_byUserId);
                //已结算解冻金额
                BigDecimal actualFrozenSettlementAmount = findUserAssetsRecordDTOLinkList(dto, actualFrozenSettlementAmountList, true, abstractUserAssetsRecordDTO_byUserId);

                //待结算解冻金额
                BigDecimal actualFrozenPendingSettlementAmount = settlementCheckerDTO.getSettlementFreezeMoney().getValue().add(actualFrozenSettlementAmount);

                log(Level.INFO, "退款解冻 已结算解冻金额,请检查数据,已冻结金额:{} 已结算解冻金额:{},待结算解冻金额:{}", dto, actualFrozenAmount, actualFrozenSettlementAmount, settlementCheckerDTO.getSettlementFreezeMoney());

                if (actualFrozenPendingSettlementAmount.compareTo(actualFrozenAmount) > 0) {
                    log(Level.ERROR, "退款解冻 校验,结算解冻金额  大于 剩余结算冻结金额,请检查数据,已冻结金额:{} 已结算解冻金额:{},结算解冻金额:{}", dto, actualFrozenAmount, actualFrozenSettlementAmount, settlementCheckerDTO.getSettlementFreezeMoney());
                    throw new PaymentClearBusinessException(ErrorCode.SETTLEMENT_FROZEN_GREATER_SETTLEMENT_FROZEN_ERROR, "结算解冻金额大于剩余结算冻结金额");
                }
            }

        }
        log(Level.INFO, "前置数据幂等校验-结果:通过", dto);
    }

    private void settlementCheck(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();

        if (Boolean.TRUE.equals(dto.getIsLeaseOrder())) {
            return;
        }

        //*********************************************************************************************出售特有逻辑********************************************************************************************************************************************
        //需要优化逻辑
        UserAssetsAmountCheckerDTO settlementCheckerDTO = UserAssetsAmountCheckerHelper.getUserAssetsAmountCheckerDTO(abstractUserAssetsRecordDTOList);

        if (settlementCheckerDTO.getPayMoney().getValue().compareTo(BigDecimal.ZERO) > 0 && !settlementCheckerDTO.checkSettlementMoney()) {
            log(Level.ERROR, "结算金额大于支付金额,请检查数据 支付金额:{} 结算金额:{} ", dto, settlementCheckerDTO.getPayMoney(), settlementCheckerDTO.getSettlementMoney());
            throw new PaymentClearBusinessException(ErrorCode.SETTLEMENT_AMOUNT_GREATER_THAN_PAY_AMOUNT);
        }

        if (!settlementCheckerDTO.checkServiceCharge()) {
            log(Level.ERROR, "交易服务费金额大于支付金额,请检查数据 支付金额:{} 结算金额:{} 交易服务费:{} ", dto, settlementCheckerDTO.getPayMoney(), settlementCheckerDTO.getSettlementMoney(), settlementCheckerDTO.getServiceCharge());
            throw new PaymentClearBusinessException(ErrorCode.SETTLEMENT_AMOUNT_GREATER_THAN_PAY_AMOUNT.getCode(), "服务费金额大于支付金额");
        }

        if (settlementCheckerDTO.getSettlementMoney().getValue().compareTo(BigDecimal.ZERO) > 0) {
            //实际支付金额
            BigDecimal actualPaymentAmount = findUserAssetsRecordDTOLinkList(dto, List.of(3, 228, 212, 221, 10), false, null);
            //已退款金额
            BigDecimal refundBigDecimal = findUserAssetsRecordDTOLinkList(dto, List.of(4, 83, 233, 222, 11), false, null);
            //已结算金额
            BigDecimal actualSettlementAmount = findUserAssetsRecordDTOLinkList(dto, List.of(5, 231, 213), false, null);

            //待结算金额
            BigDecimal settlementAmount = settlementCheckerDTO.getSettlementMoney().getValue().add(settlementCheckerDTO.getRefundMoney().getValue());
            //可支出金额
            BigDecimal expenditureMoney = actualPaymentAmount.subtract(refundBigDecimal).subtract(actualSettlementAmount);

            log(Level.INFO, "结算 收支关系  支付汇总金额:{}  已退款金额:{}  已结算金额:{}  待结算金额:{} 可支出金额:{}", dto, actualPaymentAmount, refundBigDecimal, actualSettlementAmount, settlementAmount, expenditureMoney);

            if (settlementAmount.compareTo(expenditureMoney) > 0) {
                log(Level.ERROR, "结算金额大于(可支出金额)  支付汇总金额:{} 已退款金额:{} 已结算金额:{} 待结算金额:{} 可支出金额:{}", dto, actualPaymentAmount, refundBigDecimal, actualSettlementAmount, settlementAmount, expenditureMoney);
                throw new PaymentClearBusinessException(ErrorCode.SETTLEMENT_AMOUNT_GREATER_THAN_PAY_AMOUNT, "结算金额大于(可支出金额)");
            }
        }

        if (settlementCheckerDTO.getSettlementFreezeMoney().getValue().compareTo(BigDecimal.ZERO) > 0) {
            //获取需要的 用户ID
            Long abstractUserAssetsRecordDTO_byUserId = getAbstractUserAssetsRecordDTOByUserId(abstractUserAssetsRecordDTOList, List.of(248, 257));
            List<Integer> actualFrozenAmountList = new ArrayList<>(), actualFrozenSettlementAmountList = new ArrayList<>();

            if (abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_248.getTypeId()))) {
                actualFrozenAmountList = List.of(UserAssetsTypeEnum.TYPE_247.getTypeId());
                actualFrozenSettlementAmountList = List.of(UserAssetsTypeEnum.TYPE_248.getTypeId());
            }
            if (abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_257.getTypeId()))) {
                actualFrozenAmountList = List.of(UserAssetsTypeEnum.TYPE_256.getTypeId());
                actualFrozenSettlementAmountList = List.of(UserAssetsTypeEnum.TYPE_257.getTypeId());

            }

            //已冻结金额
            BigDecimal actualFrozenAmount = findUserAssetsRecordDTOLinkList(dto, actualFrozenAmountList, true, abstractUserAssetsRecordDTO_byUserId);
            //已结算解冻金额
            BigDecimal actualFrozenSettlementAmount = findUserAssetsRecordDTOLinkList(dto, actualFrozenSettlementAmountList, true, abstractUserAssetsRecordDTO_byUserId);

            //待结算解冻金额
            BigDecimal actualFrozenPendingSettlementAmount = settlementCheckerDTO.getSettlementFreezeMoney().getValue().add(actualFrozenSettlementAmount);

            log(Level.INFO, "冻结结算 已结算解冻金额,请检查数据,已冻结金额:{} 已结算解冻金额:{},待结算解冻金额:{}", dto, actualFrozenAmount, actualFrozenSettlementAmount, settlementCheckerDTO.getSettlementFreezeMoney());

            if (actualFrozenPendingSettlementAmount.compareTo(actualFrozenAmount) > 0) {
                log(Level.ERROR, "结算金额校验,结算解冻金额  大于 剩余结算冻结金额,请检查数据,已冻结金额:{} 已结算解冻金额:{},结算解冻金额:{}", dto, actualFrozenAmount, actualFrozenSettlementAmount, settlementCheckerDTO.getSettlementFreezeMoney());
                throw new PaymentClearBusinessException(ErrorCode.SETTLEMENT_FROZEN_GREATER_SETTLEMENT_FROZEN_ERROR, "结算解冻金额大于剩余结算冻结金额");
            }
        }

        //结算需要 剔除支付资金明细
        abstractUserAssetsRecordDTOList.removeIf(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_3.getTypeId()) || abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_228.getTypeId()) || abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_221.getTypeId()));

        //********************************************************************************************************************************************************************************************************************************************
    }


    @Override
    public void specialSettlement(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = toAbstractUserAssetsRecordDTO(dto);
        dto.setAbstractUserAssetsRecordDTOList(abstractUserAssetsRecordDTOList);
    }

    private String getTreadNo(String orderNo, Integer typeId) {
        return idWorkService.getYmdNextId() + orderNo + String.format("%06d", typeId);
    }


    /**
     * 判断是否包含相同数据
     */
    public boolean containsSameData(UU898UserAssetsRecordDTO uu898UserAssetsRecordDTO, List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList) {
        return abstractUserAssetsRecordDTOList.stream().anyMatch(abstractUserAssetsRecordDTO -> abstractUserAssetsRecordDTO.getSerialNo().equals(uu898UserAssetsRecordDTO.getSerialNo()) && abstractUserAssetsRecordDTO.getOrderNo().equals(uu898UserAssetsRecordDTO.getOrderNo()) && abstractUserAssetsRecordDTO.getPayOrderNo().equals(uu898UserAssetsRecordDTO.getPayOrderNo()));
    }

    /**
     * 正向查找
     * 后续增加负向查找逻辑
     */
    public BigDecimal findUserAssetsRecordDTOLinkList(FinancialProcessorDTO dto, List<Integer> typeIdList, Boolean isUserId, Long userId) {

        List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList = dto.getUserAssetsRecordDTOLinkList();
        if (CollectionUtils.isEmpty(userAssetsRecordDTOLinkList) || CollectionUtils.isEmpty(typeIdList)) {
            return BigDecimal.ZERO;
        }
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();

        // 使用 HashSet 提高查找效率
        HashSet<Integer> typeIdSet = new HashSet<>(typeIdList);
        // 缓存过滤结果供后续复用
        List<UU898UserAssetsRecordDTO> filteredList = userAssetsRecordDTOLinkList.stream().filter(Objects::nonNull)
                //剔除相同的自身
                .filter(item -> !containsSameData(item, abstractUserAssetsRecordDTOList)).filter(item -> typeIdSet.contains(item.getTypeId())).collect(Collectors.toList());

        if (isUserId) {
            filteredList = filteredList.stream().filter(item -> item.getUserId().equals(userId)).collect(Collectors.toList());
        }
        // 计算金额总和
        BigDecimal result = filteredList.stream().map(item -> {
            BigDecimal thisMoney = Objects.requireNonNullElse(item.getThisMoney(), BigDecimal.ZERO);
            BigDecimal thisPurchaseMoney = Objects.requireNonNullElse(item.getThisPurchaseMoney(), BigDecimal.ZERO);
            return thisMoney.abs().add(thisPurchaseMoney.abs());
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (result.compareTo(BigDecimal.ZERO) == 0) {
            log(Level.INFO, "查询订单资金关联信息: 无 {}", dto, typeIdList);
            return BigDecimal.ZERO;
        } else {
            //打印日志  精简结构体
            filteredListLog(dto, filteredList);
        }
        return result;
    }


    public BigDecimal findAbstractUserAssetsRecordDTOList(FinancialProcessorDTO dto, List<Integer> typeIdList) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        if (CollectionUtils.isEmpty(abstractUserAssetsRecordDTOList) || CollectionUtils.isEmpty(typeIdList)) {
            return BigDecimal.ZERO;
        }
        // 使用 HashSet 提高查找效率
        HashSet<Integer> typeIdSet = new HashSet<>(typeIdList);
        // 缓存过滤结果供后续复用
        List<AbstractUserAssetsRecordDTO> filteredList = abstractUserAssetsRecordDTOList.stream().filter(Objects::nonNull).filter(item -> typeIdSet.contains(item.getTypeId())).collect(Collectors.toList());
        // 计算金额总和
        BigDecimal result = filteredList.stream().map(item -> {
            BigDecimal thisMoney = Objects.requireNonNullElse(item.getChangeMoney(), BigDecimal.ZERO);
            return thisMoney.abs();
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (result.compareTo(BigDecimal.ZERO) == 0) {
            log(Level.INFO, "查询订单资金关联信息: 无 {}", dto, typeIdList);
            return BigDecimal.ZERO;
        } else {
            //打印日志  精简结构体
            filteredListLog2(dto, filteredList);
        }
        return result;
    }

    private void filteredListLog2(FinancialProcessorDTO dto, List<AbstractUserAssetsRecordDTO> filteredList) {
        CompletableFuture.runAsync(() -> {
            List<String> logDataList = filteredList.stream().map(item -> String.format("资金ID:%s 用户ID:%s 类型:%s 金额:%s", item.getId(), item.getUserId(), item.getTypeId(), item.getChangeMoney())).collect(Collectors.toList());
            log(Level.INFO, "查询入参资金关联信息: {}", dto, logDataList);
        }, asyncTaskExecutor).exceptionally(ex -> {
            log(Level.WARN, "异步日志记录失败:{}", dto, ExceptionUtils.getStackTrace(ex));
            return null;
        });
    }

    private void filteredListLog(FinancialProcessorDTO dto, List<UU898UserAssetsRecordDTO> filteredList) {
        CompletableFuture.runAsync(() -> {
            List<String> logDataList = filteredList.stream().map(item -> String.format("资金ID:%s 用户ID:%s 类型:%s 余额:%s 冻结:%s 求购:%s", item.getId(), item.getUserId(), item.getTypeId(), item.getThisMoney(), item.getThisBlockMoney(), item.getThisPurchaseMoney())).collect(Collectors.toList());
            log(Level.INFO, "查询订单资金关联信息: {}", dto, logDataList);
        }, asyncTaskExecutor).exceptionally(ex -> {
            log(Level.WARN, "异步日志记录失败:{}", dto, ExceptionUtils.getStackTrace(ex));
            return null;
        });
    }

    /**
     * @param money 金额 分单位
     */
    public AbstractUserAssetsRecordDTO buildAssetRecord(UserAssetsTypeEnum userAssetsTypeEnum, FinancialProcessorDTO dto, Long userId, Integer payChannel, NetStatusEnum netStatusEnum, Long money) {
        AbstractUserAssetsRecordDTO assetRecord = new AbstractUserAssetsRecordDTO();
        assetRecord.setUserAssetsTypeEnum(userAssetsTypeEnum);
        assetRecord.setFundingDirectionEnum(userAssetsTypeEnum.getDirectionEnum());
        assetRecord.setTreadNo(getTreadNo(dto.getOrderNo(), userAssetsTypeEnum.getTypeId()));
        assetRecord.setUserId(userId);
        assetRecord.setTypeId(userAssetsTypeEnum.getTypeId());
        assetRecord.setSerialNo(dto.getSerialNo());
        assetRecord.setOrderNo(dto.getOrderNo());
        assetRecord.setPayOrderNo(dto.getPayOrderNo());
        assetRecord.setStatus(netStatusEnum.getCode());
        assetRecord.setPayChannel(payChannel);

        //金额是否变动
        assetRecord.setAttr(assetRecord.balanceChangeAttr());

        assetRecord.setAssetType(assetRecord.balanceChangeAssetType());
        assetRecord.setAddTime(LocalDateTime.now());
        assetRecord.setRemark("");
        assetRecord.setChargeMoney(BigDecimal.ZERO);
        assetRecord.setCollectType(dto.getCollectType());
        if (null == dto.getIsLeaseOrder()) {
            assetRecord.setIsLeaseOrder(Boolean.FALSE);
        } else {
            assetRecord.setIsLeaseOrder(dto.getIsLeaseOrder());
        }
        //金额分转元
        assetRecord.setChangeMoney(AmountUtils.convertToDollarToDecimal(new BigDecimal(money)));
        assetRecord.setMerchantId(dto.getMerchantId());
        assetRecord.setBusinessType(dto.getBusinessType());
        assetRecord.setSubBusType(dto.getSubBusTypeFrontEnum().getCode());
        return assetRecord;
    }

    // 渠道 用户 变动金额 变动类型 typeId
    private List<AbstractUserAssetsRecordDTO> toAbstractUserAssetsRecordDTO(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> userAssetsRecordDTOLinkList = new ArrayList<>();
        for (FinancialProcessorAssetInfoDTO assetInfoDTO : dto.getAssetInfoDTOList()) {
            UserAssetsTypeEnum userAssetsTypeEnum;
            if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.SELL_ORDER_BUY_PAY.getCode())) {
                //支出6--> 3
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_3;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.SELL_ORDER_REFUND.getCode())) {
                //退款12--> 4
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_4;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.SELL_ORDER_RECEIVE.getCode())) {
                //出售8--> 5
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_5;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.GIVE_SERVICE_CHARGE.getCode())) {
                //支付赠送服务费 20 -->192
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_192;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.REFUND_GIVE_SERVICE_CHARGE.getCode())) {
                //退还赠送服务费 21 -->193
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_193;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.SELL_ORDER_GIVEBACK.getCode())) {
                //退还买家资金处理9--> 83
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_83;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.SELL_COMMISSION.getCode())) {
                // 交易服务费18--> 181
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_181;
            } else if (assetInfoDTO.getTypeId().equals(SellOrderSettleTypeEnum.SELL_COMMISSION_REFUND.getCode())) {
                // 退还交易服务费19--> 182
                userAssetsTypeEnum = UserAssetsTypeEnum.TYPE_182;
            } else {
                userAssetsTypeEnum = UserAssetsTypeEnum.getByTypeCode(assetInfoDTO.getTypeId());
            }
            if (null == userAssetsTypeEnum) {
                log(Level.ERROR, "业务:{} 资金明细解析失败 未匹配typeId {} ", dto, dto.getSubBusTypeFrontEnum().getName(), dto.getAssetInfoDTOList());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR, "资金明细解析失败,未匹配typeId");
            }
            AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO = buildAssetRecord(userAssetsTypeEnum, dto, assetInfoDTO.getUserId(), assetInfoDTO.getPayChannel(), dto.getNetStatusEnum(), assetInfoDTO.getMoney());
            // 设置策略
            abstractUserAssetsRecordDTO.setBalanceStrategy(assetInfoDTO.getBalanceStrategy());
            userAssetsRecordDTOLinkList.add(abstractUserAssetsRecordDTO);
        }
        return userAssetsRecordDTOLinkList;
    }

    private List<String> getLockUserIdList(List<Long> userIdList) {
        if (userIdList == null || userIdList.isEmpty()) {
            return Collections.emptyList();
        }
        return userIdList.stream().map(lockKeyWorkspaces::assetUserKey).distinct().collect(Collectors.toList());
    }

    void log(Level level, String msg, FinancialProcessorDTO financialProcessorDTO, Object... dyMsg) {

        List<Object> argumentsList = new ArrayList<>();
        argumentsList.add(StringUtils.isBlank(financialProcessorDTO.getSerialNo()) ? "" : financialProcessorDTO.getSerialNo());
        argumentsList.add(StringUtils.isBlank(financialProcessorDTO.getOrderNo()) ? "" : financialProcessorDTO.getOrderNo());
        argumentsList.add(StringUtils.isBlank(financialProcessorDTO.getPayOrderNo()) ? "" : financialProcessorDTO.getPayOrderNo());
        if (financialProcessorDTO.checkPlatformAccountBuffer()) {
            argumentsList.add("开启");
        }

        if (dyMsg != null && dyMsg.length > ClearConstants.CONSTANT_INTEGER_0) {
            argumentsList.addAll(Arrays.asList(dyMsg));
        }
        String logStr = "[账务交易] 流水号:{} 订单号:{} 支付单号:{} ";
        if (financialProcessorDTO.checkPlatformAccountBuffer()) {
            logStr = logStr + " 缓冲记账:{} ";
        }
        logStr = logStr + msg;
        switch (level) {
            case INFO:
                if (log.isInfoEnabled()) {
                    log.info(logStr, argumentsList.toArray());
                }
                break;
            case ERROR:
                if (log.isErrorEnabled()) {
                    log.error(logStr, argumentsList.toArray());
                }
                break;
            case WARN:
            default:
                if (log.isWarnEnabled()) {
                    log.warn(logStr, argumentsList.toArray());
                }
                break;
        }
    }


    /**
     * 获取平台用户ID
     */
    public Long getPlatformUserId(UserAssetsTypeEnum userAssetsTypeEnum) {
        return assetsTypePlatformUserIdProperties.getConfigMap().get(Long.valueOf(userAssetsTypeEnum.getTypeId()));
    }


    /**
     * 判断是否平台户ID
     */
    private Boolean checkPlatformAccountUserId(Long userId) {
        if (null == userId) {
            return false;
        }
        List<Long> platformAccountBufferBookkeepingUserIdList = paymentClearParamsConfig.getPlatformAccountBufferBookkeepingUserIdList();
        if (null == platformAccountBufferBookkeepingUserIdList || platformAccountBufferBookkeepingUserIdList.isEmpty()) {
            return false;
        }
        return platformAccountBufferBookkeepingUserIdList.contains(userId);
    }

    private void payServiceCharge(FinancialProcessorDTO financialProcessorDTO) {
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        Optional<AbstractUserAssetsRecordDTO> payServiceChargeOptional_243 = abstractUserAssetsRecordDTOList.stream().filter(item -> item.getTypeId().equals(UserAssetsTypeEnum.TYPE_243.getTypeId())).findFirst();
        payServiceChargeOptional_243.ifPresent(payServiceCharge -> {
            for (AbstractUserAssetsRecordDTO item : abstractUserAssetsRecordDTOList) {
                UserAssetsTypeEnum userAssetsTypeEnum = UserAssetsTypeEnum.getByTypeCode(item.getTypeId());
                if (userAssetsTypeEnum.equals(UserAssetsTypeEnum.TYPE_243)) {
                    continue;
                }
                if (UserAssetsTypeEnum.TYPE_219.getTypeId().equals(item.getTypeId())) {
                    item.setChargeMoney(payServiceCharge.getChangeMoney().abs());
                    continue;
                }
                if (FILTER_243_TYPE_ID.contains(item.getTypeId())) {
                    item.setChargeMoney(payServiceCharge.getChangeMoney().abs());
                }
            }
        });
    }
}
