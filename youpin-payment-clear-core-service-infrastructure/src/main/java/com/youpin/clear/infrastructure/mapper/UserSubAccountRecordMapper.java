package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【user_sub_account_record(用户二级账户明细)】的数据库操作Mapper
* @createDate 2025-07-29 15:16:59
* @Entity com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord
*/
@Mapper
public interface UserSubAccountRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(UserSubAccountRecord record);

    int insertSelective(UserSubAccountRecord record);

    UserSubAccountRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserSubAccountRecord record);

    int updateByPrimaryKey(UserSubAccountRecord record);

}
