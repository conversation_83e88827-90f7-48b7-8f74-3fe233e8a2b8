package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.AdjustmentApplyDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AdjustmentApplyDetailExtMapper {
    List<AdjustmentApplyDetail> selectByApplyNo(@Param("applyNo") String applyNo);

    int batchInsert(@Param("list") List<AdjustmentApplyDetail> list);

    List<AdjustmentApplyDetail> selectByApplyNoAndDirection(@Param("applyNo") String applyNo,@Param("direction") Integer direction);

}