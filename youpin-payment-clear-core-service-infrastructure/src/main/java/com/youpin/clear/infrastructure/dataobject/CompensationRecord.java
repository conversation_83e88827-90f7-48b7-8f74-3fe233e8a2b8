package com.youpin.clear.infrastructure.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Table: compensation_record
 *
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompensationRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    private Long id;
    /**
     * 唯一键
     */
    private String uniqueKey;
    /**
     * 处理状态，1：初始化；2：推送中；3：推送成功；4：推送失败
     */
    private Integer handleStatus;
    /**
     * 推送次数
     */
    private Integer count;
    /**
     * 下次重试时间
     */
    private LocalDateTime nextRetryTime;
    /**
     * 业务场景，详见BizSceneEnum
     */
    private Integer bizScene;
    /**
     * 是否重试，1重试 0不重试
     */
    private Integer retryFlag;
    /**
     * 分片键
     */
    private Integer shard;
    /**
     * 是否有效,0无效 1有效
     */
    private Integer valid;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 重试信息
     */
    private String retryMsg;
}