package com.youpin.clear.infrastructure.utils;

import com.uu898.youpin.commons.base.enums.Constant;
import com.uu898.youpin.commons.redis.utils.RedisUtils;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.concurrent.TimeUnit;


/**
 *
 */
@Component
public class ClearRedisUtils {

    /**
     * 判断Key 是否超过设定的次数
     *
     * @param key       key
     * @param threshold 次数
     * @param timeout   超时秒
     * @param unit      单位
     * @return true 超过次数 false 没有超过次数
     */
    public static boolean incNumberFlag(@NotNull String key, @NotNull Long threshold, @NotNull Long timeout, @NotNull TimeUnit unit) {
        Long increment = RedisUtils.getStringRedisTemplate().opsForValue().increment(key);
        if (null != increment) {
            if (increment % threshold == Constant.CONSTANT_INTEGER_0) {
                //删除 已经告警的 key
                RedisUtils.getStringRedisTemplate().delete(key);
                return true;
            } else {
                if (Constant.CONSTANT_INTEGER_1 == increment.intValue()) {
                    RedisUtils.getStringRedisTemplate().expire(key, timeout, unit);
                }
                return false;
            }
        }
        return true;
    }

    public static void setKey(String key, Object value, Long timeout, TimeUnit unit) {
        RedisUtils.getRedisTemplate().opsForValue().set(key, value, timeout, unit);
    }

    public static Object getValue(String key) {
        return RedisUtils.getRedisTemplate().opsForValue().get(key);
    }


    public static Long increment(String key,Long value,Long timeout,TimeUnit unit) {
        Long increment = RedisUtils.getRedisTemplate().opsForValue().increment(key, value);
        RedisUtils.getRedisTemplate().expire(key,timeout,unit);
        return increment;
    }

    /**
     * 判断key 是否存在
     */
    public static boolean hasKey(String key) {
        return RedisUtils.getRedisTemplate().hasKey(key);
    }


    public static void delete(String key) {
        RedisUtils.getRedisTemplate().delete(key);
    }
}