package com.youpin.clear.infrastructure.dataobject.uu898;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: UserAssetsRecordType
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UU898UserAssetsRecordType implements Serializable {
    /**
     * 资金明细类型主键ID
     */
    private Long id;

    /**
     * 资金明细类型名称
     */
    private String typeName;

    /**
     * 资金流向：0.减少(支出)，1.增加(收入)，2.不变
     */
    private Integer operateType;

    /**
     * 资金类型编码
     */
    private Integer typeCode;

    /**
     * 资金方向对用户来说是加还是减，java重构时用到，0减1加
     */
    private Integer operateTypeUser;

    /**
     * 资金校验分类，0不校验,1,冻结,2解冻,3支付,4原路退还,5提现(转账)
     */
    private Integer checkType;

    private static final long serialVersionUID = 1L;
}