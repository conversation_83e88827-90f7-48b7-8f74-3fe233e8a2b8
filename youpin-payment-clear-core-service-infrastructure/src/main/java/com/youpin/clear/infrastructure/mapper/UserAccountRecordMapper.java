package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAccountRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAccountRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAccountRecord row);

    int insertSelective(UserAccountRecord row);

    UserAccountRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAccountRecord row);

    int updateByPrimaryKey(UserAccountRecord row);
}