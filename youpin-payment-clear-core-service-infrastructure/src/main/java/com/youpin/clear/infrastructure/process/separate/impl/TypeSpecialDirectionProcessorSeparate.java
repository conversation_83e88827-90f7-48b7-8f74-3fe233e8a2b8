package com.youpin.clear.infrastructure.process.separate.impl;


import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 特殊处理
 */
@Slf4j
@Service
public class TypeSpecialDirectionProcessorSeparate extends DefaultSeparateFundingDirectionProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.TYPE_SPECIAL, DirectionEnum.INVARIANT);
    }

    @Override
    public void doProcess(List<BillMember> billMemberList) {
        for (BillMember billMember : billMemberList) {
            //是否分账成功
            if (Boolean.TRUE.equals(billMember.getIsSuccess())) {
                continue;
            }
            AccountAggregate accountAggregate = billMember.getAccountAggregate();
            //资金分账抽象
            BillItemMember billItemMember = billMember.getBillItemMember();
            //资金类型
            Integer typeId = billItemMember.getTypeId();

            AccountBalanceDTO accountBalanceDTO = null;

            if (typeId.equals(ClearConstants.CONSTANT_INTEGER_191) || typeId.equals(ClearConstants.CONSTANT_INTEGER_1002) || typeId.equals(ClearConstants.CONSTANT_INTEGER_146)) {
                accountBalanceDTO = getAccountBalanceDTOByDef(billMember);
            } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_201)) {
                accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount().negate()).build();
            } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_202)) {
                accountBalanceDTO = AccountBalanceDTO.builder().balance1(billItemMember.getAmount()).build();
            }
            if (null == accountBalanceDTO) {
                log.error("[分账处理] 特殊处理 金额计算异常 billItemMember:{}", JSON.toJSONString(billItemMember));
                billMember.setIsSuccess(Boolean.FALSE);
                return;
            }
            //金额校验
            if (accountBalanceDTO.getTotalAmountAbs().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
                log.error("[分账处理]  金额校验失败 billItemMember:{}", JSON.toJSONString(billItemMember));
                throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
            }
            List<UserAccountRecordMember> userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTO, billItemMember, accountAggregate, billItemMember.getPayChannelEnum());
            billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
            //标记成功
            billMember.setIsSuccess(Boolean.TRUE);
        }
    }

    AccountBalanceDTO getAccountBalanceDTOByDef(BillMember billMember) {
        BillItemMember billItemMember = billMember.getBillItemMember();
        DoNetPayChannelEnum doNetPayChannelEnum = billItemMember.getPayChannelEnum();
        CollectTypeEnum collectTypeEnum = billMember.getBillItemMember().getCollectTypeEnum();
        switch (doNetPayChannelEnum) {
            case PurchaseBalance:
                return getPurchaseAccountBalanceDTOByCollectTypeEnumDef(collectTypeEnum, billMember);
            case Balance:
            default:
                return getAccountBalanceDTOByCollectTypeEnumDef(collectTypeEnum, billMember);
        }
    }

    AccountBalanceDTO getAccountBalanceDTOByCollectTypeEnumDef(CollectTypeEnum collectTypeEnum, BillMember billMember) {
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        BillItemMember billItemMember = billMember.getBillItemMember();
        switch (collectTypeEnum) {
            case DEFAULT:
                accountBalanceDTO.setBalance1(billItemMember.getAmount());
                log.warn("[分账处理] 没有收单类型 默认 余额1 :{}", JSON.toJSONString(billItemMember));
                break;
            case OWN:
                accountBalanceDTO.setBalance1(billItemMember.getAmount());
                break;
            case SUPERVISION:
                accountBalanceDTO.setBalance2(billItemMember.getAmount());
                break;
        }
        return accountBalanceDTO;
    }

    AccountBalanceDTO getPurchaseAccountBalanceDTOByCollectTypeEnumDef(CollectTypeEnum collectTypeEnum, BillMember billMember) {
        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
        BillItemMember billItemMember = billMember.getBillItemMember();
        switch (collectTypeEnum) {
            case DEFAULT:
                accountBalanceDTO.setPurchaseBalanceTransfer1(billItemMember.getAmount());
                log.warn("[分账处理] 没有收单类型 默认 转入1 :{}", JSON.toJSONString(billItemMember));
                break;
            case OWN:
                accountBalanceDTO.setPurchaseBalanceTransfer1(billItemMember.getAmount());
                break;
            case SUPERVISION:
                accountBalanceDTO.setPurchaseBalanceTransfer2(billItemMember.getAmount());
                break;
        }
        return accountBalanceDTO;
    }


}
