package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898UserAssetsRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898UserAssetsRecord row);

    int insertSelective(UU898UserAssetsRecord row);

    UU898UserAssetsRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898UserAssetsRecord row);

    int updateByPrimaryKey(UU898UserAssetsRecord row);
}