package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsPayOrderNoRelate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAssetsPayOrderNoRelateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAssetsPayOrderNoRelate row);

    int insertSelective(UserAssetsPayOrderNoRelate row);

    UserAssetsPayOrderNoRelate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAssetsPayOrderNoRelate row);

    int updateByPrimaryKey(UserAssetsPayOrderNoRelate row);
}