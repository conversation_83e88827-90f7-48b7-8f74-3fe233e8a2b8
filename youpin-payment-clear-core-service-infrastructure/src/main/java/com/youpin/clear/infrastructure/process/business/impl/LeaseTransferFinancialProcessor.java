package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租赁过户
 */
@Slf4j
@Service
public class LeaseTransferFinancialProcessor extends DefaultSubBusFinancialProcessor {
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.SELL_LEASE_TRANSFER);
    }
    
    /**
     * 结算特殊处理
     */
    @Override
    public void specialSettlement(FinancialProcessorDTO dto) {
        super.specialSettlement(dto);
        log(Level.INFO, "租赁过户-补贴资金处理", dto);
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_174.getTypeId())) {
                //变量 私密交易平台户
                abstractUserAssetsRecordDTO.setUserId(getPlatformUserId(UserAssetsTypeEnum.TYPE_174));
                break;
            }
        }
    }

}
