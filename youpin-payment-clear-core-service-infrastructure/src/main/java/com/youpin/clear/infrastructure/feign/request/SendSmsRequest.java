package com.youpin.clear.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendSmsRequest {

    /**
     * 授权信息
     */
    private AuthorizationInfo authorization;
    /**
     * 请求Id, 幂等用（一小时内不能重复）
     */
    private String requestId;

    /**
     * 用户ID 非必须
     */
    private Long userId;

    /**
     * 短信模版code，由短信网关侧定义
     */
    private String templateCode;

    /**
     * 多个以逗号隔开，最多20个。
     */
    private String[] mobiles;

    /**
     * 短信参数，替换短信模板中的占位符。 Map<String, String>
     */
    private Map<String, String> params;
}
