package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordGateway;
import com.youpin.clear.infrastructure.converter.UU898UserAssetsRecordConvertor;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class UU898UserAssetsRecordGatewayImpl implements UU898UserAssetsRecordGateway {


    @Autowired
    UU898UserAssetsRecordExtMapper userAssetsRecordExtMapper;


    @Autowired
    UU898UserAssetsRecordMapper userAssetsRecordMapper;

    @Override
    public List<UU898UserAssetsRecordDTO> queryUserAssetsRecordDTOList(Long userId, String serialNo, String orderNo, String payOrderNo, Integer typeId, Integer status) {
        if ((serialNo == null || serialNo.isBlank()) && (orderNo == null || orderNo.isBlank()) && (payOrderNo == null || payOrderNo.isBlank())) {
            return Collections.emptyList();
        }
        List<UU898UserAssetsRecord> userAssetsRecordList = userAssetsRecordExtMapper.queryUserAssetsRecordDTOList(userId, serialNo, orderNo, payOrderNo, typeId, status);
        if (userAssetsRecordList == null || userAssetsRecordList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAssetsRecordList, UU898UserAssetsRecordDTO::new);
    }


    @Override
    public List<UU898UserAssetsRecordDTO> queryUserAssetsRecordDTOList(Long userId, String orderNo, String payOrderNo) {
        if (orderNo == null || orderNo.isBlank() ) {
            return Collections.emptyList();
        }
        List<UU898UserAssetsRecord> userAssetsRecordList = userAssetsRecordExtMapper.queryUserAssetsRecordDTOList(userId, null, orderNo, payOrderNo, null, null);
        if (userAssetsRecordList == null || userAssetsRecordList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAssetsRecordList, UU898UserAssetsRecordDTO::new);
    }

    @Override
    public Long countByAddTime(Long lastId, LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return 0L;
        }
        return userAssetsRecordExtMapper.countByAddTime(lastId, startTime, endTime);
    }

    @Override
    public List<Long> selectByAddTimePage(Long lastId, LocalDateTime startTime, LocalDateTime endTime, Long pageIndex, Long pageSize) {
        if (startTime == null || endTime == null || null == pageIndex || null == pageSize) {
            return Collections.emptyList();
        }
        return userAssetsRecordExtMapper.selectByAddTimePage(lastId, startTime, endTime, pageIndex, pageSize);
    }

    @Override
    public List<Long> selectByIdInterval(Long startId, Long endId, LocalDateTime startTime, LocalDateTime endTime, Long pageIndex, Long pageSize) {
        if (startId == null || endId == null || startTime == null || endTime == null || null == pageIndex || null == pageSize) {
            return Collections.emptyList();
        }
        return userAssetsRecordExtMapper.selectByIdInterval(startId, endId, startTime, endTime, pageIndex, pageSize);
    }

    @Override
    public Long selectMaxId() {
        return userAssetsRecordExtMapper.selectMaxId();
    }

    @Override
    public UU898UserAssetsRecordDTO selectById(Long id) {
        if (id == null) {
            return null;
        }
        UU898UserAssetsRecord userAssetsRecord = userAssetsRecordMapper.selectByPrimaryKey(id);
        return BeanUtilsWrapper.convert(userAssetsRecord, UU898UserAssetsRecordDTO::new);

    }

    @Override
    public Long insert(UU898UserAssetsRecordDTO userAssetsRecordDTO) {
        UU898UserAssetsRecord assetsRecord = UU898UserAssetsRecordConvertor.MAPPER.toUU898UserAssetsRecord(userAssetsRecordDTO);
        userAssetsRecordMapper.insertSelective(assetsRecord);
        userAssetsRecordDTO.setId(assetsRecord.getId());
        return assetsRecord.getId();
    }

    @Override
    public UU898UserAssetsRecordDTO selectByTradeNo(String tradeNo) {
        UU898UserAssetsRecord userAssetsRecord = userAssetsRecordExtMapper.selectByTradeNo(tradeNo);
        if (userAssetsRecord == null) {
            return null;
        }
        return BeanUtilsWrapper.convert(userAssetsRecord, UU898UserAssetsRecordDTO::new);
    }

    @Override
    public Integer update(UU898UserAssetsRecordDTO userAssetsRecordDTO) {
        UU898UserAssetsRecord assetsRecord = UU898UserAssetsRecordConvertor.MAPPER.toUU898UserAssetsRecord(userAssetsRecordDTO);
        return userAssetsRecordMapper.updateByPrimaryKeySelective(assetsRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer deleteByTest(Long userId, String serialNo, String orderNo, String payOrderNo, Integer typeId) {
        if (orderNo == null) {
            return null;
        }
        return userAssetsRecordExtMapper.deleteByTest(userId, serialNo, orderNo, payOrderNo, typeId);
    }

    @Override
    public Long selectMinIdByUserIdAndTypeIdTime(Long userId, List<Integer> typeIdList, LocalDateTime endTime){
        return userAssetsRecordExtMapper.selectMinIdByUserIdAndTypeIdTime(userId, typeIdList, endTime);
    }



    @Override
    public List<Long> selectIdListByUserIdAndTypeIdTimePage(Long minId, Long userId, List<Integer> typeIdList, LocalDateTime endTime, Integer pageSize) {
        if (userId == null || typeIdList == null || typeIdList.isEmpty() || pageSize == null || endTime == null) {
            return Collections.emptyList();
        }
        List<Long> idList = userAssetsRecordExtMapper.selectIdListByUserIdAndTypeIdTimePage(minId == null ? 0L : minId, userId, typeIdList, endTime, pageSize);
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        return idList;
    }

}
