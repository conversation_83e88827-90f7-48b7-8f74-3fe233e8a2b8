package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserAccountRecordOrderRelateExtMapper {

    List<UserAccountRecordOrderRelate> getUserAccountRecordOrderRelateByOrderNo(@Param("orderNo") String orderNo);

    List<UserAccountRecordOrderRelate> getUserAccountRecordOrderRelateByPayOrderNo(@Param("payOrderNo") String payOrderNo);

    Integer countByUserAssetsRecordId(@Param("userAssetsRecordId") Long userAssetsRecordId);

    UserAccountRecordOrderRelate getByUserAssetsRecordId(@Param("userAssetsRecordId") Long userAssetsRecordId);

    void deleteByUserId(@Param("userId") Long userId);


}