package com.youpin.clear.infrastructure.helper;

import com.youpin.clear.common.enums.UserAssetsRecordAttrEnum;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MowMoneyHandleHelper {


    public static UserSubAccountRecordMember toUserSubAccountRecordMember(ClearUserAssetsRecordDTO clearUserAssetsRecordDTO, AccountInfoMember targetAccountMember, BigDecimal changeBalance) {

        return UserSubAccountRecordMember.builder().accountRecordNo(String.valueOf(clearUserAssetsRecordDTO.getUserAssetsRecordId())).userId(clearUserAssetsRecordDTO.getUserId()).typeId(clearUserAssetsRecordDTO.getTypeId()).userAssetsRecordId(clearUserAssetsRecordDTO.getId()).serialNo(clearUserAssetsRecordDTO.getSerialNo()).orderNo(clearUserAssetsRecordDTO.getOrderNo()).payOrderNo(clearUserAssetsRecordDTO.getPayOrderNo()).userAccountNo(targetAccountMember.getUserAccountNo()).accountType(targetAccountMember.getAccountType()).balanceChange(changeBalance).balanceIsChange(UserAssetsRecordAttrEnum.BalanceIsChange.getCode()).status(clearUserAssetsRecordDTO.getStatus()).payChannel(clearUserAssetsRecordDTO.getPayChannel()).finishTime(clearUserAssetsRecordDTO.getCompleteTime()).createTime(clearUserAssetsRecordDTO.getAddTime()).updateTime(LocalDateTime.now()).build();
    }


    public static AccountInfoMember originInitTargetAccountMember(AccountInfoMember originAccountMember, BigDecimal changeBalance) {
        return AccountInfoMember.builder().id(originAccountMember.getId()).userId(originAccountMember.getUserId()).accountType(originAccountMember.getAccountType()).userAccountNo(originAccountMember.getUserAccountNo()).balance(originAccountMember.getBalance().add(changeBalance)).frozenBalance(originAccountMember.getFrozenBalance()).lastAccountRecordId(originAccountMember.getLastAccountRecordId()).ext(originAccountMember.getExt()).build();
    }
}
