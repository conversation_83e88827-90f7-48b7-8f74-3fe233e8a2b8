package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAccountReconciliationRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAccountReconciliationRecord row);

    int insertSelective(UserAccountReconciliationRecord row);

    UserAccountReconciliationRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAccountReconciliationRecord row);

    int updateByPrimaryKey(UserAccountReconciliationRecord row);
}