package com.youpin.clear.infrastructure.dataobject;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户二级账户明细
 *
 * <AUTHOR>
 * @TableName user_sub_account_record
 */
@Data
public class UserSubAccountRecord {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 账户明细id
     */
    private String accountRecordNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;

    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;

    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 用户账户号
     */
    private String userAccountNo;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 变动金额
     */
    private BigDecimal balanceChange;

    /**
     *
     */
    private BigDecimal frozenBalanceChange;

    /**
     * 余额是否变动 0不变,1变动
     */
    private Integer balanceIsChange;

    /**
     * 0 失败 1 成功 2 进行中
     */
    private Integer status;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}