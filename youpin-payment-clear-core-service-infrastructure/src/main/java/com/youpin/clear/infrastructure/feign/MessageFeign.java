package com.youpin.clear.infrastructure.feign;

import com.uu898.youpin.commons.base.model.Result;
import com.youpin.clear.infrastructure.feign.request.SendSmsRequest;
import com.youpin.clear.infrastructure.feign.response.SendSmsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 */
@FeignClient(name = "youpin-infra-notification-center-service", contextId = "bff-payment-infra-notification-client")
public interface MessageFeign {

    /**
     * 发送短信
     */
    @PostMapping({"/notifiy/v1/sms/send"})
    Result<SendSmsResponse> sendSms(SendSmsRequest request);
}
