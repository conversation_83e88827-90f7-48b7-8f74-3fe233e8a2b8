package com.youpin.clear.infrastructure.mapper.uu898.ext;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface UU898UserSubAccountFlowRecordExtMapper {

    List<UU898UserSubAccountFlowRecord> listSubAccountRecord(@Param("userId") Long userId,
                                                             @Param("accountNo") String accountNo,
                                                             @Param("orderNo") String orderNo,
                                                             @Param("payOrderNo") String payOrderNo,
                                                             @Param("journalTypeList") List<Integer> journalTypeList);


    List<UU898UserSubAccountFlowRecord> listSubAccountRecordByTime(@Param("userId") Long userId,
                                                                   @Param("accountNo") String accountNo,
                                                                   @Param("journalTypeList") List<Integer> journalTypeList,
                                                                   @Param("startCreateTime") LocalDateTime startCreateTime,
                                                                   @Param("endCreateTime") LocalDateTime endCreateTime);

}