package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsOrderNoRelate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAssetsOrderNoRelateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAssetsOrderNoRelate row);

    int insertSelective(UserAssetsOrderNoRelate row);

    UserAssetsOrderNoRelate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAssetsOrderNoRelate row);

    int updateByPrimaryKey(UserAssetsOrderNoRelate row);
}