package com.youpin.clear.infrastructure.converter;

import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserAccountRecordOrderRelateConvertor {

    UserAccountRecordOrderRelateConvertor MAPPER = Mappers.getMapper(UserAccountRecordOrderRelateConvertor.class);

    @Mapping(target = "updateTime", source = "userAccountRecordMember.finishTime")
    UserAccountRecordOrderRelate toUserAccountRecordOrderRelate(UserAccountRecordMember userAccountRecordMember);

    List<UserAccountRecordOrderRelate> toUserAccountRecordOrderRelateList(List<UserAccountRecordMember> userAccountRecordMemberList);

}