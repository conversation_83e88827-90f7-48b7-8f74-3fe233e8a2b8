package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecord;
import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecordOrderRelate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserSubAccountRecordExtMapper {

    /**
     * 根据订单号查询关联关系
     *
     * @param orderNo 订单号
     * @return 返回
     */
    List<Long> getOrderRelateUserIdByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据订单号查询二级账户资金明细
     *
     * @param userId  userId
     * @param orderNo 订单号
     * @return list
     */
    List<UserSubAccountRecord> getUserSubAccountRecordByOrderNoAndUserId(@Param("userId") Long userId, @Param("orderNo") String orderNo);


    /**
     * 批量查询record
     *
     * @param subAccountRecords subAccountRecords
     * @return changeRows
     */
    int batchInsertRecord(@Param("subAccountRecords") List<UserSubAccountRecord> subAccountRecords);

    /**
     * 批量插入关联关系
     *
     * @param recordOrderRelateList list
     * @return 返回
     */
    int batchInsertRecordOrderRelate(@Param("recordOrderRelateList") List<UserSubAccountRecordOrderRelate> recordOrderRelateList);

    /**
     * 更新二级账户资金明细
     *
     * @param record       记录信息
     * @param originStatus 原始状态
     * @return change rows
     */
    int updateUserSubAccountRecord(@Param("record") UserSubAccountRecord record, @Param("originStatus") Integer originStatus);


    Long getMinAccountRecordId(@Param("userId") Long userId);
}
