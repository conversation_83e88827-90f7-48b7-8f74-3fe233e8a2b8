package com.youpin.clear.infrastructure.process.calculate.impl;

import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class AdditionAccountCalculateProcessor extends DefaultAccountCalculateProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.ADDITION);
    }

    @Override
    public void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember) {
        assert accountAggregate != null;
        BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();
        BigDecimal frozenBalanceChange = userAccountRecordMember.getFrozenBalanceChange();
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()));
        long lastAccountRecordId = 0L;
        //------------------------------------------------------------------------------------------------------------------------------------
        if (userAccountRecordMember.isBalanceOrPurchase()) {
            if (userAccountRecordMember.isProcessing() || userAccountRecordMember.isFail()) {
                userAccountRecordSave(userAccountRecordMember);
                return;
            } else {
                //判断数据库中是已经有进行中数据
                UserAccountRecordMember userIdAndUserAssetsRecordProcessing = getByUserIdAndUserAssetsRecordId(userAccountRecordMember);
                if (Objects.nonNull(userIdAndUserAssetsRecordProcessing)) {
                   userAccountRecordUpdateDO(userAccountRecordMember);
                } else {
                    lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
                }
            }
            updateAccountBalance("加", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
            if (userAccountRecordMember.isBalance()) {
                publicBalance.setBalance(publicBalance.getBalance().add(balanceChange));
            } else {
                publicBalance.setPurchaseBalance(publicBalance.getPurchaseBalance().add(balanceChange));
            }
        } else {
            Integer typeId = userAccountRecordMember.getTypeId();
            userAccountRecordSave(userAccountRecordMember);
            log.info("[分账处理] {} {} 渠道金额不进行 加", typeId, DoNetPayChannelEnum.getByPayChannel(userAccountRecordMember.getPayChannel()));
        }
        //------------------------------------------------------------------------------------------------------------------------------------

    }
}
