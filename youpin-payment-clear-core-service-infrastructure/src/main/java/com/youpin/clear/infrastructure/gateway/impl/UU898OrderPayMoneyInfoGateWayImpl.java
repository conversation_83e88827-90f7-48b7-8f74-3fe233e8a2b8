package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.UU898OrderPayMoneyInfoDTO;
import com.youpin.clear.domain.gateway.UU898OrderPayMoneyInfoGateWay;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898OrderPayMoneyInfo;
import com.youpin.clear.infrastructure.mapper.uu898.ext.UU898OrderPayMoneyInfoExtMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class UU898OrderPayMoneyInfoGateWayImpl implements UU898OrderPayMoneyInfoGateWay {


    @Autowired
    UU898OrderPayMoneyInfoExtMapper uu898OrderPayMoneyInfoExtMapper;

    @Override
    public List<UU898OrderPayMoneyInfoDTO> selectByOrderAndUserId(String orderNo, Long userId) {
        if (orderNo == null || orderNo.isEmpty() || userId == null) {
            log.warn("orderNo or userId is null");
            return List.of();
        }
        List<UU898OrderPayMoneyInfo> list = uu898OrderPayMoneyInfoExtMapper.selectByOrderAndUserId(orderNo, userId);
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return BeanUtilsWrapper.convertList(list, UU898OrderPayMoneyInfoDTO::new);
    }
}
