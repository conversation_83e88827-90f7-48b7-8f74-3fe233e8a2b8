package com.youpin.clear.infrastructure.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;


@Component
@RefreshScope
@Data
public class PaymentClearParamsConfig {
    /***
     * 锁释放时间
     */
    @Value("${payment.payOrderLock.leaseTime:3}")
    private long lockLeaseTime;

    /***
     * 获取锁等待时间
     */
    @Value("${payment.payOrderLock.waitTime:5}")
    private long lockWaitTime;


    /**
     * 用户账户是否兜底,默认 兜底 true
     */
    @Value("${payment.clean.accountCoverageFlag:true}")
    private Boolean accountCoverageFlag;


    /**
     * 是否发送对账校验信息
     * 默认 false 不发送
     */
    @Value("${payment.clean.sendCheckInfo:false}")
    private Boolean sendCheckInfo;

    /**
     * 是否打印分账明细日志
     * 默认 true 打印
     */
    @Value("${payment.clean.separate.printLog:true}")
    private Boolean separatePrintLog;

    /**
     * 是否打印分账明细日志2
     * 默认 false 不打印
     */
    @Value("${payment.clean.separate.printLog2:false}")
    private Boolean separatePrintLog2;


    /**
     * 告警失败:最大次数 默认:1
     */
    @Value(value = "${payment.clean.dingtalk.userBalanceAssetsCheck.threshold:1}")
    private Long dingtalkUserBalanceAssetsCheckThreshold;

    /**
     * 告警失败:缓存时间(秒) 默认:60s
     */
    @Value(value = "${payment.clean.dingtalk.userBalanceAssetsCheck.timeout:60}")
    private Long dingtalkUserBalanceAssetsCheckTimeout;


    /**
     * 平台户是否更新账户数据
     * 默认 true 更新  false 不更新
     */
    @Value("${payment.clean.platformAccountUpdate:true}")
    private Boolean platformAccountUpdate;

    /**
     * 平台户是否参与对账
     * 默认 false 不参与 true 参与
     */
    @Value("${payment.clean.platformAccountCheck:false}")
    private Boolean platformAccountCheck;

    /**
     * 资金平台户配置数据 字符串 ,
     */
    @Value("#{'${payment.clean.platformTypeIdList:21,33,95,102,108,117,119,122,129,134,154,158,162,174,176}'.split(',')}")
    private List<Integer> platformTypeIdList;

    /**
     * 判断是否使用缓存 默认不使用 false true 使用
     */
    @Value("${payment.clean.assetsType.useCache:false}")
    private Boolean assetsTypeUseCache;

    /**
     * 消费是否使用分布式锁
     * 默认不使用 false true 使用
     */
    @Value("${payment.clean.consume.useLock:false}")
    private Boolean consumeUseLock;

    /**
     * 是否开启去重判定 默认 false 不开启,true 开启
     */
    @Value("${payment.clean.deduplication.flag:false}")
    private Boolean deduplicationFlag;


    /**
     * 用户资金关系分表默认值 生产预发默认:64 其余环境:2
     * 此参数不能动态加载
     */
    @Value("${payment.clear.user.asset.relate.sharding.modulo:64}")
    private Integer userAssetQueryRelateShardingModulo;


    /**
     * 是否同步资金类记标签缓存 默认不同步 false true 同步
     */
    @Value("${payment.clean.user.assets.tag.sync.cache.flag:false}")
    private Boolean userAssetsTagSyncCacheFlag;

    @Value("${payment.clean.platform.userId.privateTrading:47739}")
    private Long privateTradingPlatformUserId;

    /**
     * 账务处理获取联锁的等待时间 毫秒
     * 默认 2000ms
     */
    @Value("${payment.clean.assets.multiLock.waitTime:2000}")
    private Long assetsMultiLockWaitTime;


    /**
     * 是否开启平台户缓冲记账
     * 默认不开启 false true 开启
     */
    @Value("${payment.clean.platform.account.buffer.bookkeeping.flag:false}")
    private Boolean platformAccountBufferBookkeepingFlag;


    /**
     * 缓冲记账用户ID
     * 默认null
     */
    @Value("#{'${payment.clean.platform.account.buffer.bookkeeping.userIdList:}'.split(',')}")
    private List<Long> platformAccountBufferBookkeepingUserIdList;


    /**
     * 交易服务费订单默认订单类型
     * 0 是非预售   1 是预售
     * 默认 0
     */
    @Value("${payment.clean.transaction.service.fee.order.type:0}")
    private Integer transactionServiceFeeOrderType;


    /**
     * 交易服务费订单记录比例 0.8
     */
    @Value("${payment.clean.transaction.service.fee.ratio:0.8}")
    private BigDecimal transactionServiceFeeRatio;

    /**
     * 交易服务费统计平台户
     */
    @Value("${payment.clean.transaction.service.fee.statement.platform.userId:********}")
    private Long transactionServiceFeeStatementPlatformUserId;

    /**
     * 交易服务费统计平台户数组
     */
    @Value("#{'${payment.clean.transaction.service.fee.statement.platform.userIdList:}'.split(',')}")
    private List<Long> transactionServiceFeeStatementPlatformUserIdList;

    /**
     * 需要划拨金额1到余额2的用户Id数组
     * ********  2737274 3230302
     */
    @Value("#{'${payment.clean.transfer.account.balance.userIdList:********,2737274,3230302}'.split(',')}")
    private List<Long> transferAccountBalanceUserIdList;

    /**
     * 需要划拨金额1到余额2的用户是否不校验重复 默认 false 不校验
     */
    @Value("${payment.clean.transfer.account.balance.userIdList.check.flag:false}")
    private Boolean transferAccountBalanceUserIdListCheckFlag;

    /**
     * 邮件的发件人地址
     */
    @Value("${payment.clean.email.from:}")
    private String emailFrom;

    /**
     * 财务邮件的收件人地址
     */
    @Value("#{'${payment.clean.email.finance.to:}'.split(',')}")
    private List<String> emailFinanceToList;


    /**
     * 营销立减活动账户余额预警 元
     */
    @Value("${payment.clean.activity.account.balance.warning:5000}")
    private BigDecimal activityAccountBalanceWarning;


    /**
     * 出售结算冻结开关 默认 false 关闭 true 开启
     */
    @Value("${payment.clean.sell.frozen.settlement.flag:false}")
    private Boolean sellFrozenSettlementFlag;

    /**
     * 结算类型编号数组
     */
    @Value("#{'${payment.clean.settlement.type.id.list:5,94}'.split(',')}")
    private List<Integer> settlementTypeIdList;

    /**
     * 结算需要减去的类型编号数组
     */
    @Value("#{'${payment.clean.settlement.type.id.list.subtract:181}'.split(',')}")
    private List<Integer> settlementTypeIdListSubtract;
    /**
     * 出售结算入参是否冻结或结算开关 默认 false 关闭 true 开启 按入参来
     */
    @Value("${payment.clean.sell.frozen.settlement.param.flag:false}")
    private Boolean sellFrozenSettlementParamFlag;


    /**
     * 获取资金Id对应的资金字段
     */
    @Value("#{'${payment.clean.query.type.id.money.list:247,248,250}'.split(',')}")
    private List<Integer> queryTypeIdMoneyList;


    /**
     * 余额是否使用仅可提现展示
     * 默认 false 不使用 true 使用
     */
    @Value("${payment.clean.assets.type.money.only.withdrawal.flag:false}")
    private Boolean assetsTypeMoneyOnlyWithdrawalFlag;


    /**
     * 仅可提现金额1-类型是 This_money 的资金逻辑处理
     */
    @Value("#{'${payment.clean.assets.type.money.list:1,2,4}'.split(',')}")
    private List<Integer> assetsTypeMoneyList;

    /**
     * 仅可提现金额1-类型是 Special_Mow_Money 的资金逻辑处理
     */
    @Value("#{'${payment.clean.assets.type.special.money.list:248,270}'.split(',')}")
    private List<Integer> assetsTypeSpecialMoneyList;

}
