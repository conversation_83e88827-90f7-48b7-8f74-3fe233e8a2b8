package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PlatformAccountRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PlatformAccountRecord row);

    int insertSelective(PlatformAccountRecord row);

    PlatformAccountRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PlatformAccountRecord row);

    int updateByPrimaryKey(PlatformAccountRecord row);
}