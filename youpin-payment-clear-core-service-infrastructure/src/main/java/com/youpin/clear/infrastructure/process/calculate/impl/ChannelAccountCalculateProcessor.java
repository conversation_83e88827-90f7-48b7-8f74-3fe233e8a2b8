package com.youpin.clear.infrastructure.process.calculate.impl;

import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class ChannelAccountCalculateProcessor extends DefaultAccountCalculateProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.CHANNEL_FREEZE, DirectionEnum.INVARIANT, DirectionEnum.CHANNEL_UNFREEZE);
    }

    @Override
    public void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember) {
        assert accountAggregate != null;
        BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();
        //------------------------------------------------------------------------------------------------------------------------------------
        //方向
        DirectionEnum directionEnum = userAccountRecordMember.getDirectionEnum();
        Integer typeId = userAccountRecordMember.getTypeId();
        if (Objects.requireNonNull(directionEnum) == DirectionEnum.CHANNEL_FREEZE) {
            balanceChange = balanceChange.negate();
            userAccountRecordMember.setBalanceChange(balanceChange);
        }
        //else if (directionEnum == DirectionEnum.INVARIANT || directionEnum == DirectionEnum.CHANNEL_UNFREEZE) {
        userAccountRecordSave(userAccountRecordMember);
        log.info("[分账处理] {} {} 渠道金额不进行 {}", typeId, DoNetPayChannelEnum.getByPayChannel(userAccountRecordMember.getPayChannel()), directionEnum.getName());
        //------------------------------------------------------------------------------------------------------------------------------------
    }
}
