package com.youpin.clear.infrastructure.dataobject.uu898;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: OrderPayMoneyInfo
 * @author: kk
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UU898OrderPayMoneyInfo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 仅可交易金额
     */
    private BigDecimal onlyTradeMoney;

    /**
     * 可提现金额
     */
    private BigDecimal canWithdrawMoney;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}