package com.youpin.clear.infrastructure.dataobject.uu898;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * 子账户流水记录表
 *
 * @TableName uu898_user_sub_account_flow_record
 */
@Data
public class UU898UserSubAccountFlowRecord {
    /**
     *
     */
    private Long id;

    /**
     * 账户编号
     */
    private String accountNo;

    /**
     * 账户类型，见账户类型配置
     */
    private Integer accountType;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 流水类型，见流水类型配置表
     */
    private Integer journalType;

    /**
     * 变更前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 余额变更值
     */
    private BigDecimal balanceChange;

    /**
     * 变更后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}