package com.youpin.clear.infrastructure.gateway.impl;

import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BaseException;
import com.youpin.clear.common.enums.UU898UserSubAccountType;
import com.youpin.clear.domain.dto.UU898UserSubAccountDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;
import com.youpin.clear.domain.gateway.UU898UserSubAccountGateway;
import com.youpin.clear.infrastructure.converter.UU898SubAccountConvert;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccountFlowRecord;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountFlowRecordMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserSubAccountMapper;
import com.youpin.clear.infrastructure.mapper.uu898.ext.UU898UserSubAccountExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.ext.UU898UserSubAccountFlowRecordExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UU898UserSubAccountGatewayImpl implements UU898UserSubAccountGateway {

    @Resource
    private UU898UserSubAccountExtMapper uu898UserSubAccountExtMapper;

    @Resource
    private UU898UserSubAccountMapper uu898UserSubAccountMapper;

    @Resource
    private UU898UserSubAccountFlowRecordMapper uu898UserSubAccountFlowRecordMapper;

    @Autowired
    private UU898UserSubAccountFlowRecordExtMapper uu898UserSubAccountFlowRecordExtMapper;

    /**
     * 真实的子账户类型（需要存储db的）
     */
    private static final Set<Integer> REAL_SUB_ACCOUNT_TYPE_LIST = Set.of(UU898UserSubAccountType.TRADE.getType());


    /**
     * 添加子账户
     *
     * @param userIds            userIds
     * @param subAccountTypeList 子账户类型
     */
    @Override
    public void addSubAccount(List<Long> userIds, List<Integer> subAccountTypeList) {
        if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(subAccountTypeList)) {
            return;
        }
        LocalDateTime nowTime = LocalDateTime.now();
        //
        List<UU898UserSubAccount> subAccountList = new ArrayList<>(userIds.size());
        // 遍历userId
        userIds.forEach(userId -> {
            // 遍历子账户类型list
            subAccountTypeList.forEach(subAccountType -> {
                UU898UserSubAccount subAccount = builderUU898UserSubAccount(userId, subAccountType, nowTime);
                subAccountList.add(subAccount);
            });
        });
        int count = uu898UserSubAccountExtMapper.batchInsert(subAccountList);
        if (count != subAccountList.size()) {
            log.warn("[UU898UserSubAccountGatewayImpl]:initSubAccount,出现保存数量和实际数量不一致,subAccountList:{}", subAccountList);
        }
    }

    /**
     * 获取用户子账户
     *
     * @param userId userId
     * @return 子账户map
     */
    @Override
    public Map<Integer, UU898UserSubAccountDTO> getUserSubAccountMap(Long userId) {
        // 根据userId查询
        List<UU898UserSubAccount> subAccountList = uu898UserSubAccountExtMapper.selectByUserId(userId);
        // 获取已存在的子账户类型
        List<Integer> existsSubAccountTypes = subAccountList.stream().map(UU898UserSubAccount::getAccountType).collect(Collectors.toList());
        // 遍历所有真实的子账户
        REAL_SUB_ACCOUNT_TYPE_LIST.forEach(subAccountType -> {
            // 不存在
            if (!existsSubAccountTypes.contains(subAccountType)) {
                //
                log.info("[getUserSubAccountMap]:自动补全uu898子账户开始,userId:{},subAccountType:{}", userId, subAccountType);
                // 补全子账户
                UU898UserSubAccount subAccount = builderUU898UserSubAccount(userId, subAccountType, LocalDateTime.now());
                // 插入数据库
                uu898UserSubAccountMapper.insert(subAccount);
                //
                log.info("[getUserSubAccountMap]:自动补全uu898子账户成功,userId:{},subAccountType:{}", userId, subAccountType);
                // 添加到list
                subAccountList.add(subAccount);
            }
        });
        // 定义subAccountDTOMap
        Map<Integer, UU898UserSubAccountDTO> subAccountDTOMap = new HashMap<>();
        // 遍历
        subAccountList.forEach(uU898UserSubAccount -> {
            // 获取code
            UU898UserSubAccountType subAccountType = UU898UserSubAccountType.getByCode(uU898UserSubAccount.getAccountType());
            if (Objects.isNull(subAccountType)) {
                log.error("用户子账户类型不存在,userId={},accountType={}", userId, uU898UserSubAccount.getAccountType());
                throw BaseException.of(Status.SYSTEM);
            }
            subAccountDTOMap.put(subAccountType.getType(), UU898SubAccountConvert.MAPPER.toUU898UserSubAccountDTO(uU898UserSubAccount));
        });
        return subAccountDTOMap;
    }

    /**
     * 保存子账户资金明细
     *
     * @param recordDTO 子账户明细
     */
    @Override
    public void saveSubAccountRecord(UU898UserSubAccountFlowRecordDTO recordDTO) {
        // 返回
        UU898UserSubAccountFlowRecord uu898UserSubAccountFlowRecord = UU898SubAccountConvert.MAPPER.toUU898UserSubAccountFlowRecord(recordDTO);
        // 保存subAccountRecord
        int insertCount = uu898UserSubAccountFlowRecordMapper.insert(uu898UserSubAccountFlowRecord);
        //
        if (insertCount != 1) {
            log.error("[UU898UserSubAccountGatewayImpl]:saveSubAccountRecord,保存子流水失败,recordDTO:{}", recordDTO);
            throw BaseException.of(Status.SYSTEM);
        }
        // 设置id
        recordDTO.setId(uu898UserSubAccountFlowRecord.getId());
        //
        log.info("[UU898UserSubAccountGatewayImpl]:saveSubAccountRecord,保存子流水成功,recordDTO:{}", recordDTO);
    }

    /**
     * 查询子流水
     *
     * @param subAccountDTO   子账户信息
     * @param orderNo         订单编号
     * @param payOrderNo      支付单号
     * @param journalTypeList journalTypeList
     * @return 返回
     */
    @Override
    public List<UU898UserSubAccountFlowRecordDTO> listSubAccountRecord(UU898UserSubAccountDTO subAccountDTO, String orderNo, String payOrderNo, List<Integer> journalTypeList) {
        if (subAccountDTO == null) {
            return new ArrayList<>(0);
        }
        List<UU898UserSubAccountFlowRecord> uu898UserSubAccountFlowRecords = uu898UserSubAccountFlowRecordExtMapper.listSubAccountRecord(subAccountDTO.getUserId(), subAccountDTO.getAccountNo(), orderNo, payOrderNo, journalTypeList);
        if (CollectionUtils.isEmpty(uu898UserSubAccountFlowRecords)) {
            return new ArrayList<>(0);
        }
        return uu898UserSubAccountFlowRecords.stream().map(UU898SubAccountConvert.MAPPER::toUU898UserSubAccountFlowRecordDTO).collect(Collectors.toList());
    }

    /**
     * 查询子流水
     *
     * @param subAccountDTO   子账户信息
     * @param journalTypeList journalTypeList
     * @param startCreateTime startTime
     * @param endCreateTime   endTime
     * @return list
     */
    @Override
    public List<UU898UserSubAccountFlowRecordDTO> listSubAccountRecord(UU898UserSubAccountDTO subAccountDTO, List<Integer> journalTypeList, LocalDateTime startCreateTime, LocalDateTime endCreateTime) {
        if (subAccountDTO == null || CollectionUtils.isEmpty(journalTypeList)) {
            return new ArrayList<>(0);
        }
        List<UU898UserSubAccountFlowRecord> uu898UserSubAccountFlowRecords = uu898UserSubAccountFlowRecordExtMapper.listSubAccountRecordByTime(subAccountDTO.getUserId(), subAccountDTO.getAccountNo(), journalTypeList, startCreateTime, endCreateTime);
        //
        return uu898UserSubAccountFlowRecords.stream().map(UU898SubAccountConvert.MAPPER::toUU898UserSubAccountFlowRecordDTO).collect(Collectors.toList());
    }

    /**
     * 更新子账户
     *
     * @param subAccountDTO 子账户dto
     * @param lastRecord    最后一条流水
     */
    @Override
    public void updateSubAccount(UU898UserSubAccountDTO subAccountDTO, UU898UserSubAccountFlowRecordDTO lastRecord) {
        // 设置子账户的最后流水id
        subAccountDTO.setLastFlowId(lastRecord.getId());
        // 转换
        UU898UserSubAccount uu898UserSubAccount = UU898SubAccountConvert.MAPPER.toUU898UserSubAccount(subAccountDTO);
        // 更新subAccount
        int updateSubAccountCount = uu898UserSubAccountExtMapper.updateSubAccount(uu898UserSubAccount, lastRecord.getBalanceBefore());
        //
        if (updateSubAccountCount != 1) {
            log.error("[UU898UserSubAccountGatewayImpl]:updateSubAccount,更新子账户失败,uu898UserSubAccount:{},record:{}", uu898UserSubAccount, lastRecord);
            throw BaseException.of(Status.SYSTEM);
        }
        log.info("[UU898UserSubAccountGatewayImpl]:updateSubAccount,更新子账户成功,subAccountDTO:{}", subAccountDTO);
    }

    private UU898UserSubAccount builderUU898UserSubAccount(Long userId, Integer subAccountType, LocalDateTime nowTime) {
        //
        UU898UserSubAccount subAccount = new UU898UserSubAccount();
        // 获取accountNo = userId + subAccountType
        String accountNo = userId.toString() + subAccountType.toString();
        subAccount.setAccountNo(accountNo);
        subAccount.setUserId(userId);
        subAccount.setAccountType(subAccountType);
        subAccount.setStatus(1);
        subAccount.setCurrency("CNY");
        subAccount.setBalance(BigDecimal.ZERO);
        subAccount.setLastFlowId(0L);
        subAccount.setExtInfo("");
        subAccount.setCreateTime(nowTime);
        subAccount.setUpdateTime(nowTime);
        return subAccount;
    }

}
