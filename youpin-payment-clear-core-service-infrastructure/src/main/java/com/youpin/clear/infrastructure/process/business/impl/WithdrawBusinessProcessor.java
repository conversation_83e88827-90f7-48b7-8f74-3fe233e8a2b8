package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsRecordAttrEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.infrastructure.utils.AmountUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 提现
 */
@Slf4j
@Service
public class WithdrawBusinessProcessor extends DefaultSubBusFinancialProcessor {

    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.WALLET_RECHARGE_WITHDRAW, SubBusTypeFrontEnum.PURCHASE_RECHARGE_WITHDRAW);
    }


    /**
     * 提现 失败处理
     */
    @Override
    public void withdrawalFail(FinancialProcessorDTO dto) {
        List<AbstractUserAssetsRecordDTO> tempList = new ArrayList<>();
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = dto.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_2.getTypeId())) {
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.None.getCode());
                abstractUserAssetsRecordDTO.setStatus(NetStatusEnum.FAIL.getCode());
                AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_23 = buildAssetRecord(UserAssetsTypeEnum.TYPE_23, dto, abstractUserAssetsRecordDTO.getUserId(), abstractUserAssetsRecordDTO.getPayChannel(), NetStatusEnum.SUCCESS, AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney()));
                abstractUserAssetsRecordDTO_23.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
                tempList.add(abstractUserAssetsRecordDTO_23);
            } else if (abstractUserAssetsRecordDTO.getTypeId().equals(UserAssetsTypeEnum.TYPE_44.getTypeId())) {
                abstractUserAssetsRecordDTO.setAttr(UserAssetsRecordAttrEnum.None.getCode());
                abstractUserAssetsRecordDTO.setStatus(NetStatusEnum.FAIL.getCode());
                AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO_96 = buildAssetRecord(UserAssetsTypeEnum.TYPE_96, dto, abstractUserAssetsRecordDTO.getUserId(), abstractUserAssetsRecordDTO.getPayChannel(), NetStatusEnum.SUCCESS, AmountUtils.convertToCent(abstractUserAssetsRecordDTO.getChangeMoney()));
                abstractUserAssetsRecordDTO_96.setAttr(UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode());
                tempList.add(abstractUserAssetsRecordDTO_96);
            }
        }
        dto.addAbstractUserAssetsRecordDTO(tempList);
    }
}

