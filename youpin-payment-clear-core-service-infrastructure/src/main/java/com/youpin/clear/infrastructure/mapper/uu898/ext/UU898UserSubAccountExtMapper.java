package com.youpin.clear.infrastructure.mapper.uu898.ext;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserSubAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface UU898UserSubAccountExtMapper {

    /**
     * 获取用户子账户
     *
     * @param userId userId
     * @return 子账户list
     */
    List<UU898UserSubAccount> selectByUserId(@Param("userId") Long userId);

    /**
     * 更新子账户
     *
     * @param subAccount    子账户
     * @param beforeBalance 变动前余额
     * @return 返回影响行数
     */
    int updateSubAccount(@Param("subAccount") UU898UserSubAccount subAccount, @Param("beforeBalance") BigDecimal beforeBalance);

    /**
     * 批量插入
     *
     * @param list list
     * @return 返回
     */
    int batchInsert(@Param("list") List<UU898UserSubAccount> list);
}