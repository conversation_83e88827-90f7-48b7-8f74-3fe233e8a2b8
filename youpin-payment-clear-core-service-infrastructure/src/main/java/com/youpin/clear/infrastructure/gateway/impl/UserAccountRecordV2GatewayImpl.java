package com.youpin.clear.infrastructure.gateway.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.AccountInfoMemberExtension;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.factory.SeparateAccountCalculateFactory;
import com.youpin.clear.domain.gateway.UserAccountRecordV2Gateway;
import com.youpin.clear.infrastructure.converter.UserAccountRecordOrderRelateConvertor;
import com.youpin.clear.infrastructure.dataobject.UpdateAccountBalanceDO;
import com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate;
import com.youpin.clear.infrastructure.mapper.UserAccountExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordOrderRelateExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordOrderRelateMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
//@Primary
public class UserAccountRecordV2GatewayImpl implements UserAccountRecordV2Gateway {


    @Autowired
    UserAccountRecordOrderRelateExtMapper userAccountRecordOrderRelateExtMapper;

    @Autowired
    UserAccountRecordOrderRelateMapper userAccountRecordOrderRelateMapper;

    @Autowired
    UserAccountExtMapper userAccountExtMapper;

    @Autowired
    SeparateAccountCalculateFactory separateAccountCalculateFactory;

    @Autowired
    UserAccountRecordExtMapper userAccountRecordExtMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void userAccountRecordMemberTransactional(List<UserAccountRecordMember> userAccountRecordMemberList, Map<Long, AccountAggregate> accountAggregateMap) {
        for (UserAccountRecordMember userAccountRecordMember : userAccountRecordMemberList) {
            PublicBalanceDTO publicBalanceDTO = new PublicBalanceDTO();
            //方向
            DirectionEnum directionEnum = userAccountRecordMember.getDirectionEnum();
            AccountAggregate accountAggregate = accountAggregateMap.get(userAccountRecordMember.getUserId());
            separateAccountCalculateFactory.getProcessor(directionEnum).handle(publicBalanceDTO, accountAggregate, userAccountRecordMember);
            //更新主账号
            if (publicBalanceDTO.getBalance().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0 || publicBalanceDTO.getFrozenBalance().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
                updateAccountInfoMemberBalance(accountAggregate.getAccountByType(AccountTypeEnum.BALANCE), publicBalanceDTO.getBalance(), publicBalanceDTO.getFrozenBalance());
            }
            if (publicBalanceDTO.getPurchaseBalance().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0 || publicBalanceDTO.getFrozenPurchaseBalance().compareTo(BigDecimal.ZERO) != ClearConstants.CONSTANT_INTEGER_0) {
                updateAccountInfoMemberBalance(accountAggregate.getAccountByType(AccountTypeEnum.PURCHASE_BALANCE), publicBalanceDTO.getPurchaseBalance(), publicBalanceDTO.getFrozenPurchaseBalance());
            }
        }
        //保存关系数据
        saveUserAccountRecordOrderRelate(UserAccountRecordOrderRelateConvertor.MAPPER.toUserAccountRecordOrderRelateList(userAccountRecordMemberList));
    }

    private void updateAccountInfoMemberBalance(AccountInfoMember accountInfoMember, BigDecimal balanceChange, BigDecimal frozenBalanceChange) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(accountInfoMember.getAccountType());

        if (balanceChange.compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0 && frozenBalanceChange.compareTo(BigDecimal.ZERO) == ClearConstants.CONSTANT_INTEGER_0) {
            log.error("分账处理异常，请检查数据，accountInfoMember:{},balanceChange:{},frozenBalanceChange:{}", accountInfoMember, balanceChange, frozenBalanceChange);
        }
        String ext = getExt(accountInfoMember.getAccountInfoMemberExtension(), accountTypeEnum);
        UpdateAccountBalanceDO updateAccountBalanceDO = new UpdateAccountBalanceDO();
        updateAccountBalanceDO.setAccountId(accountInfoMember.getId());
        updateAccountBalanceDO.setUserId(accountInfoMember.getUserId());
        updateAccountBalanceDO.setLastAccountRecordId(null);
        updateAccountBalanceDO.setUserAccountNo(accountInfoMember.getUserAccountNo());
        updateAccountBalanceDO.setAccountType(accountTypeEnum.getCode());
        updateAccountBalanceDO.setOriginalBalance(accountInfoMember.getBalance());
        updateAccountBalanceDO.setBalanceChange(balanceChange);
        updateAccountBalanceDO.setOriginalFrozenBalance(accountInfoMember.getFrozenBalance());
        updateAccountBalanceDO.setFrozenBalanceChange(frozenBalanceChange);
        updateAccountBalanceDO.setExt(ext);
        int result = userAccountExtMapper.updateAccountBalance(updateAccountBalanceDO);
        if (result != Constant.CONSTANT_INTEGER_1) {
            log.error("[分账处理] 更新主账号 {} 余额失败,用户id:{}", accountTypeEnum.getName(), accountInfoMember.getUserId());
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
        BigDecimal balanceAfter = accountInfoMember.getBalance().add(balanceChange);
        BigDecimal frozenBalanceAfter = accountInfoMember.getFrozenBalance().add(frozenBalanceChange);

        log.info("[分账处理] 更新主账号 {} 用户id:{},变动之前:{} 变动金额:{},变动之后:{} 冻结之前:{} 冻结变动:{},冻结之后:{}", accountTypeEnum.getName(), accountInfoMember.getUserId(), accountInfoMember.getBalance(), balanceChange, balanceAfter, accountInfoMember.getFrozenBalance(), frozenBalanceChange, frozenBalanceAfter);
        accountInfoMember.setBalance(accountInfoMember.getBalance().add(balanceChange));
        accountInfoMember.setFrozenBalance(accountInfoMember.getFrozenBalance().add(frozenBalanceChange));

    }

    private static String getExt(AccountInfoMemberExtension accountInfoMemberExtension, AccountTypeEnum accountTypeEnum) {
        //判断用户余额2 是否发生变动
        if (accountTypeEnum.equals(AccountTypeEnum.BALANCE_2)) {
            if (!Boolean.TRUE.equals(accountInfoMemberExtension.getBalance2Changed())) {
                accountInfoMemberExtension.setBalance2Changed(Boolean.TRUE);
                return JSON.toJSONString(accountInfoMemberExtension);
            }
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    void saveUserAccountRecordOrderRelate(List<UserAccountRecordOrderRelate> userAccountRecordOrderRelateList) {
        //去重
        userAccountRecordOrderRelateList = userAccountRecordOrderRelateList.stream().distinct().collect(Collectors.toList());
//        log.info("[分账处理] 保存关系数据");
        for (UserAccountRecordOrderRelate row : userAccountRecordOrderRelateList) {
            UserAccountRecordOrderRelate userAccountRecordOrderRelate = userAccountRecordOrderRelateExtMapper.getByUserAssetsRecordId(row.getUserAssetsRecordId());
            if (null == userAccountRecordOrderRelate) {
                userAccountRecordOrderRelateMapper.insertSelective(row);
            } else {
                if ((StringUtils.isNotBlank(row.getOrderNo()) && StringUtils.isBlank(userAccountRecordOrderRelate.getOrderNo())) || (StringUtils.isNotBlank(row.getPayOrderNo()) && StringUtils.isBlank(userAccountRecordOrderRelate.getPayOrderNo()))) {
                    userAccountRecordOrderRelate.setOrderNo(row.getOrderNo());
                    userAccountRecordOrderRelate.setPayOrderNo(row.getPayOrderNo());
                    userAccountRecordOrderRelate.setCreateTime(null);
                    userAccountRecordOrderRelate.setUpdateTime(null);
                    userAccountRecordOrderRelateMapper.updateByPrimaryKeySelective(userAccountRecordOrderRelate);
                    log.info("[分账处理] 关系数据已存在 更新一次:{}", userAccountRecordOrderRelate.getId());
                } else {
                    log.warn("[分账处理] 关系数据已存在 不做处理");
                }
            }
        }
    }


}
