package com.youpin.clear.infrastructure.process.separate.impl;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.CollectTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.member.BillItemMember;
import com.youpin.clear.domain.aggregate.member.BillMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.AccountBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 收入 +
 */
@Slf4j
@Service
public class AdditionSeparateFundingDirectionProcessor extends DefaultSeparateFundingDirectionProcessor {

    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.ADDITION);
    }


    @Override
    public void doProcess(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference) {
        //资金分账抽象
        BillItemMember billItemMember = billMember.getBillItemMember();
        Integer typeId = billItemMember.getTypeId();
        List<UserAccountRecordMember> userAccountRecordMemberList;
        AccountBalanceDTO accountBalanceDTO;
        if (Objects.equals(typeId, ClearConstants.CONSTANT_INTEGER_6)) {
            //不要找关系直接+
            accountBalanceDTO = new AccountBalanceDTO();
            if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.OWN) || billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.DEFAULT)) {
                accountBalanceDTO.setBalance1(billItemMember.getAmount());
            } else if (billItemMember.getCollectTypeEnum().equals(CollectTypeEnum.SUPERVISION)) {
                accountBalanceDTO.setBalance2(billItemMember.getAmount());
            }
        } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_5)) {
            //特殊逻辑
            accountBalanceDTO = findRelationList2(billItemMember, billMemberList);
            if (null == accountBalanceDTO) {
                log.warn("[分账处理] 分账关系金额计算失败2  尝试从入参里面找  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), typeId);
                billMember.setIsSuccess(Boolean.FALSE);
                return;
            }
            // 取出 221
            BigDecimal money = BigDecimal.ZERO;
            BillMember billMember_221 = billMemberListIsReference.stream().filter(billMemberReference -> billMemberReference.getTypeId().equals(ClearConstants.CONSTANT_INTEGER_221)).findFirst().orElse(null);
            if (null != billMember_221) {
                log.info("[分账处理] 1 net 特殊逻辑 5:{}  221:{}", billMember.getBillItemMember().getAmount(), billMember_221.getBillItemMember().getAmount());
                money = billMember_221.getBillItemMember().getAmount();
            }
            if (null != billItemMember.getBillItemMemberExtension().getMarketingReduceAmount1() && billItemMember.getBillItemMemberExtension().getMarketingReduceAmount1().compareTo(BigDecimal.ZERO) > 0) {
                log.info("[分账处理] 2 java 特殊逻辑 5:{}  221:{}", billMember.getBillItemMember().getAmount(), billItemMember.getBillItemMemberExtension().getMarketingReduceAmount1());
                money = billItemMember.getBillItemMemberExtension().getMarketingReduceAmount1();
            }

            if (money.compareTo(BigDecimal.ZERO) > 0 && accountBalanceDTO.getBalance1().compareTo(money) < 0) {
                accountBalanceDTO.setBalance2(accountBalanceDTO.getBalance2().add(accountBalanceDTO.getBalance1()).subtract(money));
                accountBalanceDTO.setBalance1(money);
            }
            if (money.compareTo(BigDecimal.ZERO) > 0) {
                billItemMember.getBillItemMemberExtension().setMarketingReduceAmount1(money);
            }
            //反转金额类型
            accountBalanceDTO = accountBalanceAbs(accountBalanceDTO);

        } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_229)
                || typeId.equals(ClearConstants.CONSTANT_INTEGER_235)
                || typeId.equals(ClearConstants.CONSTANT_INTEGER_231)
                || typeId.equals(ClearConstants.CONSTANT_INTEGER_233)
                || typeId.equals(ClearConstants.CONSTANT_INTEGER_232)
                ||typeId.equals(ClearConstants.CONSTANT_INTEGER_22)
                ||typeId.equals(ClearConstants.CONSTANT_INTEGER_58)
                ||typeId.equals(ClearConstants.CONSTANT_INTEGER_189)
                ||typeId.equals(ClearConstants.CONSTANT_INTEGER_90)
        ) {
            billMember.setIsSuccess(Boolean.FALSE);
            return;
        } else {
            //关系查找
            accountBalanceDTO = findRelationList2(billItemMember, billMemberList);
            if (null == accountBalanceDTO) {
                log.warn("[分账处理] 分账关系金额计算失败1  尝试从入参里面找  用户id:{} 订单号:{} 资金ID:{} 资金类型:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), typeId);
                billMember.setIsSuccess(Boolean.FALSE);
                return;
            }
            //反转金额类型
            accountBalanceDTO = accountBalanceAbs(accountBalanceDTO);
        }

        //金额校验
        if (accountBalanceDTO.getTotalAmountAbs().compareTo(billItemMember.getAmount()) != ClearConstants.CONSTANT_INTEGER_0) {
            log.error("[分账处理] 金额校验失败  用户id:{} 订单号:{} 资金ID:{} 资金类型:{} 计算金额:{} 账单金额:{}", billItemMember.getUserId(), billItemMember.getOrderNo(), billItemMember.getUserAssetsRecordId(), typeId, accountBalanceDTO.getTotalAmount(), billItemMember.getAmount());
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }

        userAccountRecordMemberList = this.toCreateUserAccountRecordMember(accountBalanceDTO, billItemMember, billMember.getAccountAggregate(), billItemMember.getPayChannelEnum());
        billMember.setIsSuccess(Boolean.TRUE);
        billMember.setUserAccountRecordMemberList(userAccountRecordMemberList);
    }

//    public static void main(String[] args) {
//        extracted(new BigDecimal("0.01"), new BigDecimal("0.04"), new BigDecimal("0.01"));
//        extracted(new BigDecimal("0.01"), new BigDecimal("0.04"), new BigDecimal("0.02"));
//        extracted(null, new BigDecimal("0.05"), new BigDecimal("0.01"));
//        extracted( new BigDecimal("0.05"),null, new BigDecimal("0.01"));
//
//    }
//
//    private static void extracted(BigDecimal amount1, BigDecimal amount2, BigDecimal money) {
//
//        AccountBalanceDTO accountBalanceDTO = new AccountBalanceDTO();
//        if (null != amount1 && amount1.compareTo(BigDecimal.ZERO) > 0) {
//            accountBalanceDTO.setBalance1(amount1);
//        }
//        accountBalanceDTO.setBalance2(amount2);
//
//        if (money.compareTo(BigDecimal.ZERO) > 0 && accountBalanceDTO.getBalance1().compareTo(money) < 0) {
//            accountBalanceDTO.setBalance2(accountBalanceDTO.getBalance2().add(accountBalanceDTO.getBalance1()).subtract(money));
//            accountBalanceDTO.setBalance1(money);
//        }
//
//
//
//        System.out.println(accountBalanceDTO.getBalance1() + "--" + accountBalanceDTO.getBalance2());
//        System.out.println( "------------------------------------------------------------------------------" );
//
//    }


}
