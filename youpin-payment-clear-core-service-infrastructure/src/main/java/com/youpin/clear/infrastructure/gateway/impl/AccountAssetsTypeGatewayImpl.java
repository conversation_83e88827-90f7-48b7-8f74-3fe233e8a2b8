package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.constant.RedisKey;
import com.youpin.clear.domain.dto.AccountAssetsTypeDTO;
import com.youpin.clear.domain.gateway.AccountAssetsTypeGateway;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import com.youpin.clear.infrastructure.dataobject.AccountAssetsType;
import com.youpin.clear.infrastructure.dataobject.AccountAssetsTypeRelate;
import com.youpin.clear.infrastructure.mapper.AccountAssetsTypeExtMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountAssetsTypeGatewayImpl implements AccountAssetsTypeGateway {

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;

    @Autowired
    AccountAssetsTypeExtMapper accountAssetsTypeExtMapper;

    @Autowired
    RedissonClient redissonClient;

    @Override
    public Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsType() {
        boolean assetsTypeUseCache = Boolean.TRUE.equals(paymentClearParamsConfig.getAssetsTypeUseCache());
        if (assetsTypeUseCache) {
            return gatAllAccountAssetsTypeCacheMap();
        }
        return gatAllAccountAssetsTypeDBMap();
    }

    @Override
    public Map<Integer, String> gatAccountAssetsTypeByCode(Set<Integer> assetsCodeList) {
        boolean assetsTypeUseCache = Boolean.TRUE.equals(paymentClearParamsConfig.getAssetsTypeUseCache());
        if (assetsTypeUseCache) {
            RMap<Integer, AccountAssetsTypeDTO> accountAssetsTypeDTOCacheRMap = redissonClient.getMap(RedisKey.REDIS_KEY_ASSETS_TYPE_MAP_CACHE);
            //assetsCodeList 转 Set
            Map<Integer, AccountAssetsTypeDTO> all = accountAssetsTypeDTOCacheRMap.getAll(assetsCodeList);
            if (null == all) {
                return null;
            }
            return all.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getAssetsName()));
        }
        List<AccountAssetsType> accountAssetsTypeList = accountAssetsTypeExtMapper.gatAccountAssetsTypeByCode(new ArrayList<>(assetsCodeList));
        if (CollectionUtils.isEmpty(accountAssetsTypeList)) {
            return null;
        }
        return accountAssetsTypeList.stream().collect(Collectors.toMap(AccountAssetsType::getAssetsCode, AccountAssetsType::getAssetsName, (a, b) -> b));
    }


    @Override
    public Map<String, List<Integer>> gatAllAccountAssetsTypeRelate() {
        boolean assetsTypeUseCache = Boolean.TRUE.equals(paymentClearParamsConfig.getAssetsTypeUseCache());
        if (assetsTypeUseCache) {
            return gatAllAccountAssetsTypeRelateCacheMap();
        }
        return gatAllAccountAssetsTypeRelateDBMap();
    }

    @Override
    public void initAccountAssetsTypeCache() {
        log.info("[初始化基础数据缓存] 开始");
        List<AccountAssetsType> accountAssetsTypeList = accountAssetsTypeExtMapper.gatAllAccountAssetsType();
        log.info("基础数据,开始查询数据库 DB size :{}", accountAssetsTypeList.size());
        if (CollectionUtils.isNotEmpty(accountAssetsTypeList)) {
            RMap<Integer, AccountAssetsTypeDTO> accountAssetsTypeDTOCacheRMap = redissonClient.getMap(RedisKey.REDIS_KEY_ASSETS_TYPE_MAP_CACHE);
            log.info("基础数据,缓存 size :{}", accountAssetsTypeDTOCacheRMap.size());
            Set<Integer> accountAssetsTypeDTOCacheRMap_keySet = accountAssetsTypeDTOCacheRMap.readAllMap().keySet();
            for (AccountAssetsType accountAssetsType : accountAssetsTypeList) {
                accountAssetsTypeDTOCacheRMap_keySet.remove(accountAssetsType.getAssetsCode());
                accountAssetsTypeDTOCacheRMap.put(accountAssetsType.getAssetsCode(), BeanUtilsWrapper.convert(accountAssetsType, AccountAssetsTypeDTO::new));
            }
            if (!accountAssetsTypeDTOCacheRMap_keySet.isEmpty()) {
                //需要清理历史数据
                for (Integer key : accountAssetsTypeDTOCacheRMap_keySet) {
                    accountAssetsTypeDTOCacheRMap.remove(key);
                }
            }
        }
        log.info("[初始化基础数据缓存] 结束");
        log.info("[初始化基础数据关系缓存] 开始");
        List<AccountAssetsTypeRelate> accountAssetsTypeRelateList = accountAssetsTypeExtMapper.gatAllAccountAssetsTypeRelate();
        log.info("基础数据关系,开始查询数据库 DB size :{}", accountAssetsTypeRelateList.size());
        if (CollectionUtils.isNotEmpty(accountAssetsTypeRelateList)) {
            RMap<String, List<Integer>> allAccountAssetsTypeRelateCacheRMap = redissonClient.getMap(RedisKey.REDIS_KEY_ASSETS_TYPE_RELATE_MAP_CACHE);
            log.info("基础数据关系,缓存 size :{}", allAccountAssetsTypeRelateCacheRMap.size());
            Set<String> allAccountAssetsTypeRelateCacheRMap_keySet = allAccountAssetsTypeRelateCacheRMap.readAllMap().keySet();
            for (AccountAssetsTypeRelate accountAssetsTypeRelate : accountAssetsTypeRelateList) {
                allAccountAssetsTypeRelateCacheRMap_keySet.remove(accountAssetsTypeRelate.getRelateType() + "_" + accountAssetsTypeRelate.getAssetsCode());
                if (StringUtils.isBlank(accountAssetsTypeRelate.getRelateCode())) {
                    continue;
                }
                String[] split = accountAssetsTypeRelate.getRelateCode().split(ClearConstants.COMMA);
                List<Integer> assetsTypeList = Arrays.stream(split).filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toList());
                allAccountAssetsTypeRelateCacheRMap.put(accountAssetsTypeRelate.getRelateType() + "_" + accountAssetsTypeRelate.getAssetsCode(), assetsTypeList);
            }
            if (!allAccountAssetsTypeRelateCacheRMap_keySet.isEmpty()) {
                //需要清理历史数据
                for (String key : allAccountAssetsTypeRelateCacheRMap_keySet) {
                    allAccountAssetsTypeRelateCacheRMap.remove(key);
                }
            }
        }
        log.info("[初始化基础数据关系缓存] 结束");
    }


    private Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsTypeCacheMap() {
        Map<Integer, AccountAssetsTypeDTO> map;
        try {
            RMap<Integer, AccountAssetsTypeDTO> rMap = redissonClient.getMap(RedisKey.REDIS_KEY_ASSETS_TYPE_MAP_CACHE);
            map = rMap.readAllMap();
            if (null == map || map.isEmpty()) {
                map = gatAllAccountAssetsTypeDBMap();
                if (null == map || map.isEmpty()) {
                    log.error("获取缓存异常,数据库查询为null");
                } else {
                    log.info("基础数据 redis 缓存无效,查询数据库{} ", map.size());
                    rMap.putAllAsync(map);
                    log.info("基础数据 redis 缓存无效,初始化Redis完成 ");
                }
            }
        } catch (Exception e) {
            log.error("获取缓存异常,使用数据库兜底", e);
            return gatAllAccountAssetsTypeDBMap();
        }
        if (null == map || map.isEmpty()) {
            log.error("获取缓存异常,数据为null, 使用数据库兜底");
            return gatAllAccountAssetsTypeDBMap();
        }
        return map;
    }


    private Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsTypeDBMap() {
        Map<Integer, AccountAssetsTypeDTO> accountAssetsTypeDTOMap = new HashMap<>();
        List<AccountAssetsType> accountAssetsTypeList = accountAssetsTypeExtMapper.gatAllAccountAssetsType();
        if (CollectionUtils.isEmpty(accountAssetsTypeList)) {
            return null;
        }
        for (AccountAssetsType accountAssetsType : accountAssetsTypeList) {
            accountAssetsTypeDTOMap.put(accountAssetsType.getAssetsCode(), BeanUtilsWrapper.convert(accountAssetsType, AccountAssetsTypeDTO::new));
        }
        if (accountAssetsTypeList.size() != accountAssetsTypeDTOMap.size()) {
            log.error("基础数据,数据库数据有误.");
        }
        return accountAssetsTypeDTOMap;
    }


    private Map<String, List<Integer>> gatAllAccountAssetsTypeRelateCacheMap() {
        Map<String, List<Integer>> map;
        try {
            RMap<String, List<Integer>> rmap = redissonClient.getMap(RedisKey.REDIS_KEY_ASSETS_TYPE_RELATE_MAP_CACHE);
            map = rmap.readAllMap();
            if (null == map || map.isEmpty()) {
                map = gatAllAccountAssetsTypeRelateDBMap();
                log.info("基础数据关系 redis 缓存无效,查询数据库 ");
                if (null == map || map.isEmpty()) {
                    log.error("基础数据关系 获取缓存异常,数据库查询为null");
                } else {
                    log.info("基础数据关系 redis 缓存无效,查询数据库{} ", map.size());
                    rmap.putAllAsync(map);
                    log.info("基础数据关系 redis 缓存无效,初始化Redis完成 ");

                }
            }
        } catch (Exception e) {
            log.error("基础数据关系 获取缓存异常,使用数据库兜底", e);
            return gatAllAccountAssetsTypeRelateDBMap();
        }
        if (null == map || map.isEmpty()) {
            log.error("基础数据关系 获取缓存异常,数据为null, 使用数据库兜底");
            return gatAllAccountAssetsTypeRelateDBMap();
        }
        return map;
    }

    private Map<String, List<Integer>> gatAllAccountAssetsTypeRelateDBMap() {
        Map<String, List<Integer>> map = new HashMap<>();
        List<AccountAssetsTypeRelate> accountAssetsTypeRelateList = accountAssetsTypeExtMapper.gatAllAccountAssetsTypeRelate();
        if (CollectionUtils.isEmpty(accountAssetsTypeRelateList)) {
            return null;
        }
        for (AccountAssetsTypeRelate accountAssetsTypeRelate : accountAssetsTypeRelateList) {
            if (StringUtils.isBlank(accountAssetsTypeRelate.getRelateCode())) {
                continue;
            }
            String[] split = accountAssetsTypeRelate.getRelateCode().split(ClearConstants.COMMA);
            List<Integer> assetsTypeList = Arrays.stream(split).filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toList());
            map.put(accountAssetsTypeRelate.getRelateType() + "_" + accountAssetsTypeRelate.getAssetsCode(), assetsTypeList);
        }
        if (accountAssetsTypeRelateList.size() != map.size()) {
            log.error("基础数据关系,数据库数据有误.");
        }
        return map;
    }

}
