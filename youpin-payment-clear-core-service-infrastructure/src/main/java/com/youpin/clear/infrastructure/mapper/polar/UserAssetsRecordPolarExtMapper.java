package com.youpin.clear.infrastructure.mapper.polar;

import com.youpin.clear.infrastructure.dataobject.polar.UserAssetsRecordPolar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface UserAssetsRecordPolarExtMapper {


    List<UserAssetsRecordPolar> selectPageByUserId(@Param("userId") Long userId,
                                                   @Param("pageIndex") Long pageIndex,
                                                   @Param("pageSize") Integer pageSize,
                                                   @Param("typeIdList") List<Integer> typeIdList,
                                                   @Param("id") Long id,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

}