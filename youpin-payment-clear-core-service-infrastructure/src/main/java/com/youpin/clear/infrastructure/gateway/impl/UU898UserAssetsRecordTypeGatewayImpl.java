package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.UU898UserAssetsRecordTypeDTO;
import com.youpin.clear.domain.gateway.UU898UserAssetsRecordTypeGateway;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecordType;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordTypeExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordTypeMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UU898UserAssetsRecordTypeGatewayImpl implements UU898UserAssetsRecordTypeGateway {

    @Resource
    private UU898UserAssetsRecordTypeExtMapper uu898UserAssetsRecordTypeExtMapper;

    @Resource
    private UU898UserAssetsRecordTypeMapper uu898UserAssetsRecordTypeMapper;


    @Override
    public UU898UserAssetsRecordTypeDTO queryById(Long id) {
        UU898UserAssetsRecordType userAssetsRecordTypes = uu898UserAssetsRecordTypeMapper.selectByPrimaryKey(id);
        return BeanUtilsWrapper.convert(userAssetsRecordTypes, UU898UserAssetsRecordTypeDTO::new);
    }

    @Override
    public List<UU898UserAssetsRecordTypeDTO> queryByOperateTypeList(List<Integer> operateTypeIds) {
        List<UU898UserAssetsRecordType> userAssetsRecordTypes = uu898UserAssetsRecordTypeExtMapper.selectByOperateTypeList(operateTypeIds);
        return BeanUtilsWrapper.convertList(userAssetsRecordTypes, UU898UserAssetsRecordTypeDTO::new);
    }
}
