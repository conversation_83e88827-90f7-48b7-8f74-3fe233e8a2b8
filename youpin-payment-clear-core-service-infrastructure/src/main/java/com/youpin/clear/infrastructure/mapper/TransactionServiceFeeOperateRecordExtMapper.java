package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.TransactionServiceFeeOperateRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface TransactionServiceFeeOperateRecordExtMapper {

    TransactionServiceFeeOperateRecord selectBySerialNo(@Param("serialNo") String serialNo);

    List<TransactionServiceFeeOperateRecord> selectPageByStatus(@Param("status") Integer status);

    BigDecimal reportFinanceSummary(@Param("operationDate") Integer operationDate, @Param("status") Integer status);


}