package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898NewOrderPayPurchaseExtend;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UU898NewOrderPayPurchaseExtendMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UU898NewOrderPayPurchaseExtend row);

    int insertSelective(UU898NewOrderPayPurchaseExtend row);

    UU898NewOrderPayPurchaseExtend selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UU898NewOrderPayPurchaseExtend row);

    int updateByPrimaryKey(UU898NewOrderPayPurchaseExtend row);
}