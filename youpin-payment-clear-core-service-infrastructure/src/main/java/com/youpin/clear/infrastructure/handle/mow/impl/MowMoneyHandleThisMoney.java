package com.youpin.clear.infrastructure.handle.mow.impl;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.gateway.UserSubAccountRecordGateway;
import com.youpin.clear.domain.handle.mow.MowMoneyHandle;
import com.youpin.clear.infrastructure.helper.MowMoneyHandleHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 *This_Money
 */
@Slf4j
@Service
public class MowMoneyHandleThisMoney implements MowMoneyHandle {

    @Autowired
    UserAccountGateway userAccountGateway;


    @Autowired
    UserSubAccountRecordGateway userSubAccountRecordGateway;


    @Override
    public List<Integer> support() {
        return List.of(MowMoneyEnum.This_Money.getCode());
    }

    @Override
    public void process(ClearUserAssetsRecordDTO clearUserAssetsRecordDTO, AccountAggregate accountAggregate) {
        //收单类型
        CollectTypeEnum collectTypeEnum = CollectTypeEnum.getCollectTypeEnum(clearUserAssetsRecordDTO.getCollectType());
        //收单类型为null || 非自由
        if (!CollectTypeEnum.OWN.equals(collectTypeEnum)) {
            return;
        }
        Integer typeId = clearUserAssetsRecordDTO.getTypeId();

        //TODO 充值 可以跳过 进行中的逻辑  提现 跳过 成功的逻辑
        if (typeId.equals(UserAssetsTypeEnum.TYPE_1.getTypeId()) && clearUserAssetsRecordDTO.getStatus().equals(NetStatusEnum.PROCESSING.getCode())) {
            log.info("充值: 跳过进行中的逻辑: {}", clearUserAssetsRecordDTO);
            return;
        }

        if (typeId.equals(UserAssetsTypeEnum.TYPE_2.getTypeId()) && clearUserAssetsRecordDTO.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
            log.info("提现: 跳过成功的逻辑: {}", clearUserAssetsRecordDTO);
            return;
        }

        if (typeId.equals(UserAssetsTypeEnum.TYPE_4.getTypeId()) && clearUserAssetsRecordDTO.getThisMoney().compareTo(BigDecimal.ZERO) >= ClearConstants.CONSTANT_INT_0) {
            log.info("非充值退款: 跳出逻辑: {}", clearUserAssetsRecordDTO);
            return;
        }

        //变更前金额
        BigDecimal beforeMoney = clearUserAssetsRecordDTO.getMoney();
        //变化金额
        BigDecimal thisMoney = clearUserAssetsRecordDTO.getThisMoney();
        //变更后金额
        BigDecimal afterMoney = clearUserAssetsRecordDTO.getAfterMoney();
        //判断金额有无发生变化 true 有 false 无
        boolean isChangeMoneyFlag = thisMoney.abs().compareTo(beforeMoney.subtract(afterMoney).abs()) == 0;
        if (!isChangeMoneyFlag) {
            log.warn("金额无发生变化: beforeMoney:{}  thisMoney:{} afterMoney:{} data:{}", beforeMoney, thisMoney, afterMoney, JSON.toJSONString(clearUserAssetsRecordDTO));
            return;
        }
        //原始账户明细
        AccountInfoMember originAccountMember = accountAggregate.getAccountByType(AccountTypeEnum.BALANCE_WITHDRAW_121);
        //复制originAccountMember 到 targetAccountMember
        AccountInfoMember targetAccountMember = MowMoneyHandleHelper.originInitTargetAccountMember(originAccountMember, thisMoney);
        //更新账户
        userAccountGateway.updateAccount(originAccountMember, targetAccountMember);
        //更新原始金额变化
        originAccountMember.setBalance(targetAccountMember.getBalance());
        //组装资金流水
        UserSubAccountRecordMember userSubAccountRecordMember = MowMoneyHandleHelper.toUserSubAccountRecordMember(clearUserAssetsRecordDTO, targetAccountMember, thisMoney);
        //插入资金流水
        userSubAccountRecordGateway.save(List.of(userSubAccountRecordMember));

    }


}
