package com.youpin.clear.infrastructure.utils;

import com.uu898.youpin.commons.base.enums.Constant;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

public class ModuloOperationUtils {


    /**
     * 模运算
     */
    public static int moduloOperation(Integer number, Integer modulo) {
        if (number == null || modulo == null) {
            throw new RuntimeException("number or modulo is null");
        }
        return number % modulo;
    }
    
    /**
     * 模运算
     */
    public static int moduloOperationBigDecimal(BigDecimal number, Integer modulo) {
        if (number == null || modulo == null) {
            throw new RuntimeException("number or modulo is null");
        }
        return number.remainder(BigDecimal.valueOf(modulo)).intValue();
    }

    /**
     * 纯数字类型的 取数
     */
    public static BigDecimal numberUnsignedHash(String val) {
        if (StringUtils.isBlank(val)) {
            return BigDecimal.ZERO;
        }
        // 直接检查是否为纯数字
        if (!StringUtils.isNumeric(val)) {
            // 提取所有数字字符
            StringBuilder digitsBuilder = new StringBuilder();
            for (char c : val.toCharArray()) {
                if (Character.isDigit(c)) {
                    digitsBuilder.append(c);
                }
            }
            val = digitsBuilder.toString();
            if (val.isEmpty()) {
                return BigDecimal.ZERO;
            }
        }
        try {
            return new BigDecimal(val);
        } catch (NumberFormatException e) {
            // 处理超出范围的情况
            return BigDecimal.ZERO;
        }
    }


    /**
     * 不是纯数字类型的hash 取数
     */
    public static int unsignedHash(String val) {
        if (val == null) {
            return Constant.CONSTANT_INTEGER_0;
        }
        return Objects.hash(val);
    }


}
