package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class MoneyTransferPurchaseProcessor extends DefaultSubBusFinancialProcessor {

    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.MONEY_TRANSFER_PURCHASE, SubBusTypeFrontEnum.PURCHASE_TRANSFER_MONEY);
    }

    @Override
    public void paySuccess(FinancialProcessorDTO dto) {
        super.paySuccess(dto);

        if (!dto.isAbstractUserAssetsRecordDTOListEmpty()) {
            List<AbstractUserAssetsRecordDTO> userAssetsRecordDTOLinkList = dto.getAbstractUserAssetsRecordDTOList();
            for (AbstractUserAssetsRecordDTO dtoLink : userAssetsRecordDTOLinkList) {
                if (dtoLink.getTypeId().equals(UserAssetsTypeEnum.TYPE_98.getTypeId())) {
                    dtoLink.setIsMoneyFromPurchaseMoney(true);
                }
                if (dtoLink.getTypeId().equals(UserAssetsTypeEnum.TYPE_100.getTypeId())) {
                    dtoLink.setIsPurchaseMoneyFromMoney(true);
                }
            }
        }
    }
}

