package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserAccountReconciliationRecordExtMapper {

    UserAccountReconciliationRecord getByUserIdAndAccountType(@Param("userId") Long userId, @Param("accountType") Integer accountType);


    void updateByUserIdAndIdSelective(UserAccountReconciliationRecord row);

    void deleteByUserId(@Param("userId") Long userId);
}
