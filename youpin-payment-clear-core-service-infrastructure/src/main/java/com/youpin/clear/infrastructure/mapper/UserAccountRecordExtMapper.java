package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAccountRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface UserAccountRecordExtMapper {

    List<UserAccountRecord> getUserAccountRecordByOrderNo(@Param("userId") Long userId, @Param("orderNo") String orderNo);

    /**
     * 根据用户ID 资金主键 账户类型
     *
     * @param userId             必传
     * @param userAssetsRecordId 必传
     * @param accountType        非必传
     */
    List<UserAccountRecord> getUserAccountRecordByUserIdAndUserAssetsRecordId(@Param("userId") Long userId, @Param("userAssetsRecordId") Long userAssetsRecordId, @Param("accountType") Integer accountType);

    Integer countUserAccountRecordByUserIdAndUserAssetsRecordId(@Param("userId") Long userId,
                                                                @Param("userAssetsRecordId") Long userAssetsRecordId,
                                                                @Param("balanceChange") BigDecimal balanceChange,
                                                                @Param("accountType") Integer accountType);

    Integer userAccountRecordUpdateStatus(@Param("userId") Long userId, @Param("Id") Long id, @Param("status") Integer status);

    void deleteByUserId(@Param("userId") Long userId);

    int updateByPrimaryKeySelectiveUserId(UserAccountRecord row);


    BigDecimal sumBalanceByIdAndAccountType(@Param("userId") Long userId, @Param("startId") Long startId, @Param("entId") Long entId, @Param("accountType") Integer accountType);

    UserAccountRecord getByUserIdAndId(@Param("userId") Long userId, @Param("id") Long id);

    Long minIdByUserId(@Param("userId") Long userId, @Param("accountType") Integer accountType);

    Long minUserAssetsRecordId(@Param("userId") Long userId);

    List<UserAccountRecord> selectUserAccountRecordById(@Param("userId") Long userId,
                                                        @Param("minUserAssetsRecordId") Long minUserAssetsRecordId,
                                                        @Param("maxUserAssetsRecordId") Long maxUserAssetsRecordId);

    Long selectLastUserAccountRecordByUserIdAndAccountType(@Param("userId") Long userId, @Param("userAssetsRecordId") Long userAssetsRecordId, @Param("accountType") Integer accountType);

    UserAccountRecord selectByUserAssetsRecordId(@Param("userId") Long userId,@Param("userAssetsRecordId") Long userAssetsRecordId,@Param("accountType") Integer accountType);

    List<UserAccountRecord> selectByUserIdAndOrderNo(@Param("userId")Long userId, @Param("orderNo")String orderNo);
}
