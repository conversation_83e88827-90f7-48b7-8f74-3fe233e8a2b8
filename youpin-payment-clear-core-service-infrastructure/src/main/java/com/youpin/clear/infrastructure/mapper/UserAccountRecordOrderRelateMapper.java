package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAccountRecordOrderRelate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAccountRecordOrderRelateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAccountRecordOrderRelate row);

    int insertSelective(UserAccountRecordOrderRelate row);

    UserAccountRecordOrderRelate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAccountRecordOrderRelate row);

    int updateByPrimaryKey(UserAccountRecordOrderRelate row);
}