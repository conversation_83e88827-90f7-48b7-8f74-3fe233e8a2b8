package com.youpin.clear.infrastructure.utils;

import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BusinessException;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 */
public class AmountUtils {


    /**
     * 校验金额是否为空
     *
     * @param amount 金额
     */
    public static void checkEmptyAmount(BigDecimal amount) {
        if (amount == null) {
            throw new BusinessException(Status.SYSTEM.getCode(), "金额不能为空");
        }
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException(Status.SYSTEM.getCode(), "金额不能为0");
        }
    }

    /**
     * 金额元转分
     *
     * @param amount 金额
     * @return long
     */
    public static Long convertToCent(BigDecimal amount) {
        if (null == amount) {
            return null;
        }
        return amount.multiply(new BigDecimal(100)).toBigInteger().longValue();
    }

    /**
     * 金额分转元
     *
     * @param amount 金额
     * @return str
     */
    public static BigDecimal convertToDollarToDecimal(BigDecimal amount) {
        if (null == amount) {
            return BigDecimal.ZERO;
        }
        return amount.divide(new BigDecimal(100), 2, RoundingMode.DOWN);
    }


    public static String convertToString(BigDecimal amount) {
        if (null == amount) {
            return null;
        }

         return convertToDollar(amount.multiply(new BigDecimal(100)).toBigInteger().longValue());
    }

    public static String convertToDollar(Long amount) {
        if (null == amount) {
            return "0";
        }
        return new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    public static boolean checkAmountScale(BigDecimal amount, int expectScale) {
        int scale = amount.scale();
        return scale <= expectScale;
    }

    /**
     * 分转万
     * @param amountYuan
     * @return
     */
    public static String convertTenThousandths(BigDecimal amountYuan) {
        if (null == amountYuan) {
            return null;
        }
        return amountYuan.divide(new BigDecimal(10000)).stripTrailingZeros().toPlainString();
    }


}
