package com.youpin.clear.infrastructure.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PlatformAccountRecordExtMapper {


    List<Long> selectPageSizeByStatusAndCreateTime(@Param("shardIndex") Integer shardIndex, @Param("status") Integer status, @Param("createTime") LocalDateTime createTime, @Param("pageSize") Integer pageSize);

    int updateStatusById(@Param("id") Long id, @Param("status") Integer status);

}