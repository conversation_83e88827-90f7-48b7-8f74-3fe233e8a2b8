package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsTag;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAssetsTagMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAssetsTag row);

    int insertSelective(UserAssetsTag row);

    UserAssetsTag selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAssetsTag row);

    int updateByPrimaryKey(UserAssetsTag row);
}