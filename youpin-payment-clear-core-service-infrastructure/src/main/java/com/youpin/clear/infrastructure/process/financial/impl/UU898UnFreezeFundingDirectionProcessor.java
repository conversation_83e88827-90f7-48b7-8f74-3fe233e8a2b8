package com.youpin.clear.infrastructure.process.financial.impl;

import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.UU898FundingDirectionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 解冻 4
 */
@Slf4j
@Service
public class UU898UnFreezeFundingDirectionProcessor extends UU898DefaultFundingDirection implements UU898FundingDirectionProcessor {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.UNFREEZE);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {

        //数据状态
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());

        //判断是否需要更新账户数据
        boolean isAccountUpdate = netStatusEnum.equals(NetStatusEnum.SUCCESS);

        /*
          余额冻结 + 变动金额
          冻结金额 - 变动金额
         */
        //变动金额
        BigDecimal changeMoney = dto.getChangeMoney();

        if (dto.isBalanceChange()) {

            BigDecimal afterMoney = userAssetsInfoDTO.getMoney().add(changeMoney);
            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);

            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());

            //补充冻结余额信息
            BigDecimal afterBlockMoney = userAssetsInfoDTO.getBlockMoney().subtract(changeMoney);
            //校验余额
            if (afterBlockMoney.compareTo(BigDecimal.ZERO) < 0) {
                log.error("[账务交易][冻结余额解冻] 冻结余额不足 userId:{} 用户余额:{} 需要支付:{} 结果:{}", dto.getUserId(), userAssetsInfoDTO.getBlockMoney(), changeMoney, afterBlockMoney);
                throw new PaymentClearBusinessException(ErrorCode.BLOCK_BALANCE_NOT_ENOUGH);
            }
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), changeMoney.negate(), afterBlockMoney);

            //更新
            insertOrUpdateUserAssetsRecord(dto, true);
            updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, afterBlockMoney, isAccountUpdate, dto.checkAccountBufferBookkeeping());
            //更新对象
            userAssetsInfoDTO.setMoney(afterMoney);
            userAssetsInfoDTO.setBlockMoney(afterBlockMoney);

        } else if (dto.isPayChannelChange()) {
            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, userAssetsInfoDTO.getMoney());
            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
            //补充冻结余额信息
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
            //只需要记录资金明细
            insertOrUpdateUserAssetsRecord(dto, true);

        } else {
            log.error("[账务交易][资金解冻] 处理类型未知  dto:{} ", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR);
        }

    }
}
