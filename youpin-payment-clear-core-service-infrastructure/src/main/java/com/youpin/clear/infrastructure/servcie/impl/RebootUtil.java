package com.youpin.clear.infrastructure.servcie.impl;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RebootUtil {
    /**
     * 组装 发送的信息
     * Text版本
     *
     * @param isAt       是否需要 @所有人
     * @param msgContent 要发送信息的主体
     * @param telList    要 @人的电话号码,如果@单独的几个人，就传一个空list，而不是 null
     */
    public static String setMessage(boolean isAt, String msgContent, List<String> telList) {

        TextRebootModel model = new TextRebootModel();
        AtMobiles atMobiles = new AtMobiles();
        atMobiles.setIsAtAll(isAt);
        atMobiles.setAtMobiles(telList);

        ContentModel contentModel = new ContentModel();
        contentModel.setContent(msgContent);

        model.setAt(atMobiles);
        model.setText(contentModel);

        return JSON.toJSONString(model);
    }

    /**
     * 组装 发送的信息
     * Markdown格式
     *
     * @param isAt       是否需要 @所有人
     * @param title      标题
     * @param msgContent 要发送信息的主体
     * @param telList    要 @人的电话号码,如果@单独的几个人，就传一个空list，而不是 null
     */
    public static String setMarkDown(boolean isAt, String title, String msgContent, List<String> telList) {

        MarkDownRebootModel model = new MarkDownRebootModel();
        AtMobiles atMobiles = new AtMobiles();
        atMobiles.setIsAtAll(isAt);
        atMobiles.setAtMobiles(telList);

        MarkDownModel markDownModel = new MarkDownModel();
        markDownModel.setTitle(title);
        markDownModel.setText(msgContent);

        model.setAt(atMobiles);
        model.setMarkdown(markDownModel);

        return JSON.toJSONString(model);
    }

    /**
     * post 请求，发送给哪一个机器人
     *
     * @param reboot  机器人的token
     * @param message 发送的消息
     */
    public static String sendPost(String reboot, String message) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(reboot);
        httppost.addHeader("Content-Type", "application/json; charset=utf-8");

        StringEntity se = new StringEntity(message, "utf-8");
        httppost.setEntity(se);
        String result = null;
        HttpResponse response = null;
        try {
            response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                result = EntityUtils.toString(response.getEntity(), "utf-8");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 选择加签方式下的加签方法
     *
     * @param secret 密钥，机器人安全设置页面，加签一栏下面显示的SEC开头的字符串
     */
    public static Map<String, String> dingDingSec(String secret) throws Exception {
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), StandardCharsets.UTF_8);
        Map<String, String> map = new HashMap<>();
        map.put("sign", sign);
        map.put("timestamp", timestamp.toString());
        return map;
    }

    /**
     * 加签机器人实现，这里需要注意的是：timestamp和sign需要保持一致
     *
     * @param message 要发送的信息
     * @throws Exception
     */
    public static String sendReboot(String message, String accessToke) throws Exception {
        Map<String, String> map = dingDingSec("钉钉机器人的SEC");
        String sign = map.get("sign");
        String timestamp = map.get("timestamp");
        String robotUrl = accessToke + "&timestamp=" + timestamp + "&sign=" + sign;
        return sendPost(robotUrl, message);
    }

    public static String sendRebootWithSec(String message, String sec, String accessToke) throws Exception {
        Map<String, String> map = dingDingSec(sec);
        String sign = map.get("sign");
        String timestamp = map.get("timestamp");
        String robotUrl = accessToke + "&timestamp=" + timestamp + "&sign=" + sign;
        return sendPost(robotUrl, message);
    }

    /**
     * 关键字机器人：发送消息中需要有对应的关键字才能发送成功
     *
     * @param message 封装的消息
     */
    public static String sendKeyReboot(String message) {
        return sendPost("钉钉机器人的webhook", message);
    }


    @Data
    public static class MarkDownRebootModel {
        /**
         * 此消息类型为固定markdown
         */
        public String msgtype = "markdown";

        public MarkDownModel markdown;

        public AtMobiles at;
    }

    @Data
    public static class MarkDownModel {
        /**
         * 首屏会话透出的展示内容
         */
        private String title;

        /**
         * markdown格式的消息
         */
        private String text;
    }

    @Data
    public static class AtMobiles {

        /**
         * 被@人的手机号
         */
        private List<String> atMobiles;

        /**
         * @所有人时:true,否则为:false
         */
        private Boolean isAtAll;
    }

    @Data
    public static class TextRebootModel {
        /**
         * 此消息类型为固定text
         */
        public String msgtype = "text";

        public ContentModel text;

        public AtMobiles at;
    }

    @Data
    public static class ContentModel {
        /**
         * 消息内容
         */
        private String content;
    }
}
