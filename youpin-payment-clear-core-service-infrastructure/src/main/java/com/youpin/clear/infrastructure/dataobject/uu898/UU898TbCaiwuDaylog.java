package com.youpin.clear.infrastructure.dataobject.uu898;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: tb_caiwu_daylog
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UU898TbCaiwuDaylog implements Serializable {
    /**
     */
    private Long id;

    /**
     */
    private LocalDate ddate;

    /**
     */
    private Integer ttype;

    /**
     */
    private String typename;

    /**
     */
    private BigDecimal allmoney;

    /**
     */
    private BigDecimal alipay;

    /**
     */
    private BigDecimal wechat;

    /**
     */
    private BigDecimal money;

    /**
     */
    private BigDecimal jd;

    /**
     */
    private BigDecimal douyinpay;

    /**
     * 易宝
     */
    private BigDecimal yibao;

    /**
     * 是否展示0,展示,1不展示
     */
    private Integer displaystatus;

    /**
     * 来源,0默认 1其它
     */
    private Integer source;

    private static final long serialVersionUID = 1L;
}