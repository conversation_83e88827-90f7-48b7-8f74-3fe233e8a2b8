package com.youpin.clear.infrastructure.process.business.impl;

import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SpecialLogicFinancialProcessor extends DefaultSubBusFinancialProcessor {

    @Override
    public List<SubBusTypeFrontEnum> support() {
        return List.of(SubBusTypeFrontEnum.SPECIAL_LOGIC);
    }


}
