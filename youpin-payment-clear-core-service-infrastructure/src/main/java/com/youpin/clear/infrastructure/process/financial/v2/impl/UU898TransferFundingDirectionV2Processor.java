package com.youpin.clear.infrastructure.process.financial.v2.impl;

import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.ChangeMoneyDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.process.UU898FundingDirectionV2Processor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 7 划拨（从另一个账户转移到另一个账户）
 */
@Slf4j
@Service
public class UU898TransferFundingDirectionV2Processor extends UU898DefaultFundingDirectionV2 implements UU898FundingDirectionV2Processor {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.TRANSFER);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        //补充余额信息
        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), dto.getChangeMoney(), userAssetsInfoDTO.getMoney());
        //补充求购余额信息
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
        //补充冻结余额信息
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
        //记录资金明细
        insertOrUpdateUserAssetsRecord(dto, true);
        // 判断资金类型
        if (UserAssetsTypeEnum.TYPE_270.getTypeId().equals(dto.getTypeId())) {
            // 减少处理仅可交易子账户
            handleSubAccount(dto, userAssetsInfoDTO.getTradeSubAccountDTO(), dto.getChangeMoney(), false);
        }
    }


}
