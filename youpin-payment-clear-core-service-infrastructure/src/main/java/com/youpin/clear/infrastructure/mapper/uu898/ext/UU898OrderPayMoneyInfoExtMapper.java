package com.youpin.clear.infrastructure.mapper.uu898.ext;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898OrderPayMoneyInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UU898OrderPayMoneyInfoExtMapper {


    List<UU898OrderPayMoneyInfo> selectByOrderAndUserId(@Param("orderNo") String orderNo, @Param("userId") Long userId);

}