package com.youpin.clear.infrastructure.config;

import com.youpin.clear.common.enums.UU898UserSubAccountType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "uu898-user-account-infra-config")
public class UU898UserAccountInfraConfig {

    /**
     * 默认退款子账户类型
     */
    private List<Integer> defaultRefundSubTypeList = List.of(UU898UserSubAccountType.TRADE.getType(), UU898UserSubAccountType.WITHDRAW.getType());





}
