package com.youpin.clear.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RefreshScope
@Data
@ConfigurationProperties(prefix = "send-message-config")
public class SendMessageConfig {

    /**
     * 调账申请-短信模版
     * 验证码：${code}。后管系统正在发起调账申请（若非本人操作，请删除本短信）
     */
    private String adjustmentApplyVerifySmsTemplateCode = "SMS202412061";

    private String appSecret = "DCB697D92594F11F650C25F751A54D49";

    private String appKey = "youpin-payment-clear-core-service";

    /**
     * 调账申请-验证码手机号
     */
    private String adjustmentVerifyMobile = "17853500638";

    /**
     * 调账申请-验证码有效时间，单位分钟
     */
    private Long adjustmentVerifyCodeEffectiveTime = 30L;

    /**
     * 调账申请-重复发送短信验证间隔，单位秒
     */
    private Long adjustmentRepeatTime = 60L;

}
