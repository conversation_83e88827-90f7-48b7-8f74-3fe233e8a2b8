package com.youpin.clear.infrastructure.process.financial.impl;

import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.infrastructure.mapper.uu898.UU898NewOrderPayPurchaseExtendExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 收入 1
 */
@Slf4j
@Service
public class UU898AdditionFundingDirectionProcessor extends UU898DefaultFundingDirection {


    @Autowired
    UU898NewOrderPayPurchaseExtendExtMapper uu898NewOrderPayPurchaseExtendExtMapper;


    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.ADDITION);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        //数据状态
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());
        if (dto.isBalanceChange()) {
            handleBalanceChange(dto, userAssetsInfoDTO, netStatusEnum);
        } else if (dto.isPurchaseBalanceChange()) {
            handlePurchaseBalanceChange(dto, userAssetsInfoDTO);
        } else if (dto.isPayChannelChange() && !dto.isBalanceRechargeRefund) {
            handlePayChannelChange(dto, userAssetsInfoDTO);
        } else if (dto.isBalanceRechargeRefund) {
            handleBalanceRechargeRefundChange(dto, userAssetsInfoDTO);
        } else {
            log.error("[账务交易][资金+] 处理类型未知  dto:{} ", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR);
        }
    }

    private void handleBalanceRechargeRefundChange(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());
        boolean isAccountUpdate = false;
        boolean assetsRecordUpdateOrInsert = false;
        BigDecimal changeMoney = dto.getChangeMoney();
        BigDecimal afterMoney = userAssetsInfoDTO.getMoney();
        dto.setAttr(UserAssetsRecordAttrEnum.BalanceIsChange.getCode());
        dto.setAssetType(UserAssetsRecordAssetTypeEnum.Balance.getCode());
        switch (netStatusEnum) {
            case PROCESSING:
                isAccountUpdate = true;
                assetsRecordUpdateOrInsert = true;
                afterMoney = userAssetsInfoDTO.getMoney().add(changeMoney);
                setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);
                setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
                setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
                break;
            case FAIL:
                // TODO 充值退款失败  待处理
                break;
            case SUCCESS:
                //兜底逻辑:如果订单不存在,直接发送成功处理
                if (null == dto.getId()) {
                    isAccountUpdate = true;
                    assetsRecordUpdateOrInsert = true;
                    afterMoney = userAssetsInfoDTO.getMoney().add(changeMoney);
                    setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);
                    setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
                    setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
                }
                break;
        }

        insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
        updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, userAssetsInfoDTO.getBlockMoney(), isAccountUpdate, dto.checkAccountBufferBookkeeping());

    }


    // 处理余额变动
    private void handleBalanceChange(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO, NetStatusEnum netStatusEnum) {
        //变动金额
        BigDecimal changeMoney = dto.getChangeMoney();
        //判断是否需要更新账户数据
        boolean isAccountUpdate = netStatusEnum.equals(NetStatusEnum.SUCCESS);
        BigDecimal afterMoney = userAssetsInfoDTO.getMoney().add(changeMoney);
        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
        //需要记录资金明细
        insertOrUpdateUserAssetsRecord(dto, Boolean.TRUE);
        //更新账户金额
        updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, userAssetsInfoDTO.getBlockMoney(), isAccountUpdate, dto.checkAccountBufferBookkeeping());
        //更新账户金额
        userAssetsInfoDTO.setMoney(afterMoney);
    }

    // 处理求购余额变动
    private void handlePurchaseBalanceChange(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        //变动金额
        BigDecimal changeMoney = dto.getChangeMoney();


        BigDecimal afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney().add(changeMoney);
        BigDecimal afterPurchaseMoneyFromMoney = userAssetsInfoDTO.getPurchaseMoneyFromMoney();

        if (dto.getIsMoneyFromPurchaseMoney()) {
            afterPurchaseMoneyFromMoney = afterPurchaseMoneyFromMoney.add(changeMoney);
        }
        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getMoney());
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), changeMoney, afterPurchaseMoney);
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

        insertOrUpdateUserAssetsRecord(dto, Boolean.TRUE);

        if (null != dto.getPurchaseTransferAmount() && dto.getPurchaseTransferAmount().compareTo(BigDecimal.ZERO) > 0) {
            afterPurchaseMoneyFromMoney = afterPurchaseMoneyFromMoney.add(dto.getPurchaseTransferAmount());
        }

        updateUserPurchaseBalance(dto.getId(), userAssetsInfoDTO, afterPurchaseMoney, afterPurchaseMoneyFromMoney, true, dto.checkAccountBufferBookkeeping());
        //需要同步修改
        //TODO 需要测试
        //250退款逻辑 求购转入逻辑
        //增加开关
        if (null != dto.getPurchaseTransferAmountDataID() && null != dto.getPurchaseTransferAmount() && dto.getPurchaseTransferAmount().compareTo(BigDecimal.ZERO) > 0) {
            uu898NewOrderPayPurchaseExtendExtMapper.updatePurchaseMoneyFromMoney(dto.getPurchaseTransferAmountDataID(), dto.getUserId(), dto.getOrderNo(), dto.getPayOrderNo(), dto.getPurchaseTransferAmount(), LocalDateTime.now());
            log.info("250退款逻辑 求购转入逻辑 id:{}  转入金额:{} ", dto.getPurchaseTransferAmountDataID(), dto.getPurchaseTransferAmount());
        }
        userAssetsInfoDTO.setPurchaseMoney(afterPurchaseMoney);
        userAssetsInfoDTO.setPurchaseMoneyFromMoney(afterPurchaseMoneyFromMoney);
    }

    // 处理渠道变动
    private void handlePayChannelChange(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        //变动金额
        BigDecimal changeMoney = dto.getChangeMoney();
        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, userAssetsInfoDTO.getMoney());
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
        //只需要记录资金明细
        insertOrUpdateUserAssetsRecord(dto, Boolean.TRUE);
    }

}


