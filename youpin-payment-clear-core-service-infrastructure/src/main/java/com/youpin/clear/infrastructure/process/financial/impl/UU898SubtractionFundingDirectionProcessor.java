package com.youpin.clear.infrastructure.process.financial.impl;

import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.UU898FundingDirectionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支出 2
 */
@Slf4j
@Service
public class UU898SubtractionFundingDirectionProcessor extends UU898DefaultFundingDirection implements UU898FundingDirectionProcessor {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.SUBTRACTION);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {

        //数据状态
        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(dto.getStatus());

        //判断是否需要更新账户数据
        boolean isAccountUpdate = netStatusEnum.equals(NetStatusEnum.SUCCESS);
        boolean assetsRecordUpdateOrInsert = null == dto.getId();

        BigDecimal changeMoney = dto.getChangeMoney();
        if (dto.isBalanceChange()) {
            //防御编程
            //余额支付没有进行中这状态
            if (netStatusEnum.equals(NetStatusEnum.PROCESSING)) {
                log.error("[账务交易][资金减] 余额支付没有进行中这状态 dto:{}", dto);
                throw new PaymentClearBusinessException(ErrorCode.BALANCE_PAY_NOT_STATUS);
            }


            BigDecimal afterMoney = userAssetsInfoDTO.getMoney().subtract(changeMoney);
            //校验余额
            if (afterMoney.compareTo(BigDecimal.ZERO) < 0) {
                log.error("[账务交易][余额支付] 余额不足 userId:{} 用户余额:{} 需要支付:{} 结果:{}", dto.getUserId(), userAssetsInfoDTO.getMoney(), changeMoney, afterMoney);
                throw new PaymentClearBusinessException(ErrorCode.BALANCE_NOT_ENOUGH);
            }

            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney.negate(), afterMoney);

            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());

            //补充冻结信息
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

            //更新
            insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
            updateUserBalanceAndBlockMoney(dto.getId(), userAssetsInfoDTO, afterMoney, userAssetsInfoDTO.getBlockMoney(), isAccountUpdate, dto.checkAccountBufferBookkeeping());

            //更新对象
            userAssetsInfoDTO.setMoney(afterMoney);

        } else if (dto.isPurchaseBalanceChange()) {
            BigDecimal afterPurchaseMoney = userAssetsInfoDTO.getPurchaseMoney().subtract(changeMoney);
            BigDecimal purchaseMoneyFromMoney = userAssetsInfoDTO.getPurchaseMoneyFromMoney();
            //需要判断求购金额是是支付 还是 退款 还是 转出
            if (dto.getIsPurchaseMoneyFromMoney()) {
                purchaseMoneyFromMoney = purchaseMoneyFromMoney.subtract(changeMoney);
            }
            // 求购余额小于钱包转入求购金额
            if (afterPurchaseMoney.compareTo(purchaseMoneyFromMoney) < 0) {
                // 设置钱包转入求购金额为求购余额
                purchaseMoneyFromMoney = afterPurchaseMoney;
            }
            //校验余额
            if (afterPurchaseMoney.compareTo(BigDecimal.ZERO) < 0) {
                log.error("[账务交易][求购支付] 余额不足 userId:{} 用户余额:{} 需要支付:{} 结果:{}", dto.getUserId(), userAssetsInfoDTO.getPurchaseMoney(), changeMoney, afterPurchaseMoney);
                throw new PaymentClearBusinessException(ErrorCode.PURCHASE_BALANCE_NOT_ENOUGH);
            }
            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getMoney());

            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), changeMoney.negate(), afterPurchaseMoney);

            //补充冻结信息
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

            //更新
            insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
            updateUserPurchaseBalance(dto.getId(), userAssetsInfoDTO, afterPurchaseMoney, purchaseMoneyFromMoney, isAccountUpdate, dto.checkAccountBufferBookkeeping());
            //更新对象
            userAssetsInfoDTO.setPurchaseMoney(afterPurchaseMoney);
            userAssetsInfoDTO.setPurchaseMoneyFromMoney(purchaseMoneyFromMoney);

        } else if (dto.isPayChannelChange()) { //只需要记录资金明细
            //补充余额信息
            setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney.negate(), userAssetsInfoDTO.getMoney());

            //补充求购余额信息
            setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());

            //补充冻结信息
            setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());

            //插入资金明细
            insertOrUpdateUserAssetsRecord(dto, assetsRecordUpdateOrInsert);
        } else {
            log.error("[账务交易][资金减] 处理类型未知  dto:{} ", dto);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_PROCESS_ERROR);
        }
    }


}


