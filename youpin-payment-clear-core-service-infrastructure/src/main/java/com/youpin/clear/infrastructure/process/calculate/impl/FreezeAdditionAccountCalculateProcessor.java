package com.youpin.clear.infrastructure.process.calculate.impl;

import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Slf4j
@Service
public class FreezeAdditionAccountCalculateProcessor extends DefaultAccountCalculateProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.FREEZE_ADDITION);
    }

    @Override
    public void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember) {
        assert accountAggregate != null;
        BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();
        BigDecimal frozenBalanceChange = userAccountRecordMember.getFrozenBalanceChange();
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()));
        long lastAccountRecordId;
        //------------------------------------------------------------------------------------------------------------------------------------
        balanceChange = balanceChange.negate(); //变-号
        userAccountRecordMember.setBalanceChange(balanceChange);
        lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
        if (userAccountRecordMember.isBalanceOrPurchase()) {
            updateAccountBalance("余额冻结+", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
            publicBalance.setBalance(publicBalance.getBalance().add(balanceChange));
        } else {
            updateAccountBalance("余额冻结+", userAccountRecordMember, accountInfoMember, BigDecimal.ZERO, frozenBalanceChange, lastAccountRecordId);
        }
        publicBalance.setFrozenBalance(publicBalance.getFrozenBalance().add(frozenBalanceChange));
        //------------------------------------------------------------------------------------------------------------------------------------
    }
}
