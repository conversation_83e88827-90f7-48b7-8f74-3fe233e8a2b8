package com.youpin.clear.infrastructure.servcie.impl;

import com.youpin.clear.domain.servcie.EmailService;
import com.youpin.clear.infrastructure.config.PaymentClearParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    JavaMailSender mailSender;

    @Autowired
    PaymentClearParamsConfig paymentClearParamsConfig;


    @Override
    public void sendSimpleMessage(List<String> to, String subject, String text) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(paymentClearParamsConfig.getEmailFrom());
        message.setTo(to.toArray(new String[0]));
        message.setSubject(subject);
        message.setText(text);
        mailSender.send(message);
    }

}
