package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: user_assets_tag
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsTag implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 标签code
     */
    private String tagCode;

    /**
     * 标签数值类型
     */
    private BigDecimal tagValueDecimal;

    /**
     * 标签值字符串类型
     */
    private String tagValue;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}