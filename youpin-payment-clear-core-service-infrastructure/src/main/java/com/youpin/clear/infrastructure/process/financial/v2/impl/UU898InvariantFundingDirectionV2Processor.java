package com.youpin.clear.infrastructure.process.financial.v2.impl;

import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.UserAssetsRecordAttrEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.process.UU898FundingDirectionProcessor;
import com.youpin.clear.domain.process.UU898FundingDirectionV2Processor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资金不变-减
 * 资金不变+加
 * 1.目前 只有余额这个渠道
 */
@Slf4j
@Service
public class UU898InvariantFundingDirectionV2Processor extends UU898DefaultFundingDirectionV2 implements UU898FundingDirectionV2Processor {

    @Override
    public List<FundingDirectionEnum> support() {
        return List.of(FundingDirectionEnum.INVARIANT_SUBTRACTION, FundingDirectionEnum.INVARIANT_ADDITION);
    }

    @Override
    public void processAssets(AbstractUserAssetsRecordDTO dto, UserAssetsInfoDTO userAssetsInfoDTO) {
        FundingDirectionEnum fundingDirectionEnum = dto.getFundingDirectionEnum();
        dto.setAttr(UserAssetsRecordAttrEnum.None.getCode());
        //变动金额
        BigDecimal changeMoney = dto.getChangeMoney();
        switch (fundingDirectionEnum) {
            case INVARIANT_SUBTRACTION:
                //变更符号
                changeMoney = changeMoney.negate();
                break;
            case INVARIANT_ADDITION:
                break;
        }
        //补充余额信息
        setBalanceDto(dto, userAssetsInfoDTO.getMoney(), changeMoney, userAssetsInfoDTO.getMoney());
        //补充求购余额信息
        setPurchaseBalanceDto(dto, userAssetsInfoDTO.getPurchaseMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getPurchaseMoney());
        //补充冻结余额信息
        setBlockBalanceDto(dto, userAssetsInfoDTO.getBlockMoney(), BigDecimal.ZERO, userAssetsInfoDTO.getBlockMoney());
        //记录资金明细
        insertOrUpdateUserAssetsRecord(dto, true);
    }


}
