package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserSubAccountRecordOrderRelate;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【user_sub_account_record_order_relate(用户二级账户订单关系表)】的数据库操作Mapper
* @createDate 2025-07-29 15:16:59
* @Entity com.youpin.clear.infrastructure.dataobject.UserSubAccountRecordOrderRelate
*/
@Mapper
public interface UserSubAccountRecordOrderRelateMapper {

    int deleteByPrimaryKey(Long id);

    int insert(UserSubAccountRecordOrderRelate record);

    int insertSelective(UserSubAccountRecordOrderRelate record);

    UserSubAccountRecordOrderRelate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserSubAccountRecordOrderRelate record);

    int updateByPrimaryKey(UserSubAccountRecordOrderRelate record);

}
