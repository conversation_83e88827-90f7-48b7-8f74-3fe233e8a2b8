package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.AdjustmentApply;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AdjustmentApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AdjustmentApply row);

    int insertSelective(AdjustmentApply row);

    AdjustmentApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AdjustmentApply row);

    int updateByPrimaryKeyWithBLOBs(AdjustmentApply row);

    int updateByPrimaryKey(AdjustmentApply row);
}