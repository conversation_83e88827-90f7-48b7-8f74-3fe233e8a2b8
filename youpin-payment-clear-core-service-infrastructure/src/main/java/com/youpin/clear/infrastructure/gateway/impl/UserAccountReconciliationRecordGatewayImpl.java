package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.UserAccountReconciliationRecordDTO;
import com.youpin.clear.domain.gateway.UserAccountReconciliationRecordGateway;
import com.youpin.clear.infrastructure.dataobject.UserAccountReconciliationRecord;
import com.youpin.clear.infrastructure.mapper.UserAccountReconciliationRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountReconciliationRecordMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserAccountReconciliationRecordGatewayImpl implements UserAccountReconciliationRecordGateway {

    @Autowired
    UserAccountReconciliationRecordMapper userAccountReconciliationRecordMapper;

    @Autowired
    UserAccountReconciliationRecordExtMapper userAccountReconciliationRecordExtMapper;


    @Override
    public UserAccountReconciliationRecordDTO getByUserIdAndAccountType(Long userId, Integer accountType) {
        UserAccountReconciliationRecord userAccountReconciliationRecord = userAccountReconciliationRecordExtMapper.getByUserIdAndAccountType(userId, accountType);
        return BeanUtilsWrapper.convert(userAccountReconciliationRecord, UserAccountReconciliationRecordDTO::new);
    }

    @Override
    public void save(UserAccountReconciliationRecordDTO dto) {
        UserAccountReconciliationRecord row = BeanUtilsWrapper.convert(dto, UserAccountReconciliationRecord::new);
        userAccountReconciliationRecordMapper.insertSelective(row);
    }

    @Override
    public void updateByUserIdAndIdSelective(UserAccountReconciliationRecordDTO dto) {
        UserAccountReconciliationRecord row = BeanUtilsWrapper.convert(dto, UserAccountReconciliationRecord::new);
        userAccountReconciliationRecordExtMapper.updateByUserIdAndIdSelective(row);
    }

    @Override
    public void deleteByUserId(Long userId) {
        userAccountReconciliationRecordExtMapper.deleteByUserId(userId);
    }


}
