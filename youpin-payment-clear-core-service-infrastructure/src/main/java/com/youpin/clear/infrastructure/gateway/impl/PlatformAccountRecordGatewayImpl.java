package com.youpin.clear.infrastructure.gateway.impl;

import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;
import com.youpin.clear.domain.gateway.PlatformAccountRecordGateway;
import com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord;
import com.youpin.clear.infrastructure.mapper.PlatformAccountRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.PlatformAccountRecordMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import com.youpin.clear.infrastructure.utils.ShardUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class PlatformAccountRecordGatewayImpl implements PlatformAccountRecordGateway {

    @Autowired
    PlatformAccountRecordMapper platformAccountRecordMapper;

    @Autowired
    PlatformAccountRecordExtMapper platformAccountRecordExtMapper;

    @Autowired
    UU898UserAssetsInfoMapper userAssetsInfoMapper;


    @Override
    public Boolean save(PlatformAccountRecordDTO dto) {
        //用户分片
        dto.setShard(ShardUtil.shard(dto.getUserId()));
        PlatformAccountRecord convert = BeanUtilsWrapper.convert(dto, PlatformAccountRecord::new);
        int i = platformAccountRecordMapper.insertSelective(convert);
        dto.setId(convert.getId());
        return i > 0;
    }

    @Override
    public Boolean updateStatusById(Long id, Integer status) {
        return platformAccountRecordExtMapper.updateStatusById(id, status) > 0;
    }

    @Override
    public List<Long> selectPageSizeByStatusAndCreateTime(Integer shardIndex, Integer status, LocalDateTime createTime, Integer pageSize) {
        List<Long> idList = platformAccountRecordExtMapper.selectPageSizeByStatusAndCreateTime(shardIndex, status, createTime, pageSize);
        if (null == idList || idList.isEmpty()) {
            return Collections.emptyList();
        }
        return idList;
    }

    @Override
    public PlatformAccountRecordDTO selectById(Long id) {
        PlatformAccountRecord platformAccountRecord = platformAccountRecordMapper.selectByPrimaryKey(id);
        return BeanUtilsWrapper.convert(platformAccountRecord, PlatformAccountRecordDTO::new);
    }

}
