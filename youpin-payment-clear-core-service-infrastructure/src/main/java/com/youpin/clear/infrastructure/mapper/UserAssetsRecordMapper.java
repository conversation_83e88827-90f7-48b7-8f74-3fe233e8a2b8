package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAssetsRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAssetsRecord row);

    int insertSelective(UserAssetsRecord row);

    UserAssetsRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAssetsRecord row);

    int updateByPrimaryKey(UserAssetsRecord row);
}