package com.youpin.clear.infrastructure.process.calculate.impl;

import com.alibaba.fastjson.JSON;
import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.AccountInfoMemberExtension;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.AccountCalculateProcessor;
import com.youpin.clear.domain.servcie.IdWorkService;
import com.youpin.clear.infrastructure.dataobject.UpdateAccountBalanceDO;
import com.youpin.clear.infrastructure.dataobject.UserAccountRecord;
import com.youpin.clear.infrastructure.mapper.UserAccountExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordExtMapper;
import com.youpin.clear.infrastructure.mapper.UserAccountRecordMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class DefaultAccountCalculateProcessor implements AccountCalculateProcessor {

    @Autowired
    UserAccountRecordMapper userAccountRecordMapper;

    @Autowired
    UserAccountRecordExtMapper userAccountRecordExtMapper;

    @Autowired
    IdWorkService idWorkService;

    @Autowired
    UserAccountExtMapper userAccountExtMapper;


    private static String getExt(AccountInfoMemberExtension accountInfoMemberExtension, AccountTypeEnum accountTypeEnum) {
        //判断用户余额2 是否发生变动
        if (accountTypeEnum.equals(AccountTypeEnum.BALANCE_2)) {
            if (!Boolean.TRUE.equals(accountInfoMemberExtension.getBalance2Changed())) {
                accountInfoMemberExtension.setBalance2Changed(Boolean.TRUE);
                return JSON.toJSONString(accountInfoMemberExtension);
            }
        }
        return null;
    }


    void updateAccountBalance(String logStr, UserAccountRecordMember userAccountRecordMember, AccountInfoMember accountInfoMember, BigDecimal balanceChange, BigDecimal frozenBalanceChange, Long lastAccountRecordId) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType());
        String userAccountNo = userAccountRecordMember.getUserAccountNo();
        //变动金额
        Long accountId = accountInfoMember.getId();
        Long userId = userAccountRecordMember.getUserId();
        Long userAssetsRecordId = userAccountRecordMember.getUserAssetsRecordId();
        String orderNo = userAccountRecordMember.getOrderNo();
        String payOrderNo = userAccountRecordMember.getPayOrderNo();

        //变动之前的金额
        BigDecimal balanceBefore = userAccountRecordMember.getBalanceBefore();
        BigDecimal frozenBalanceBefore = userAccountRecordMember.getFrozenBalanceBefore();
        BigDecimal balanceAfter = userAccountRecordMember.getBalanceAfter();
        BigDecimal frozenBalanceAfter = userAccountRecordMember.getFrozenBalanceAfter();


        if (balanceChange.compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0 && balanceBefore.compareTo(balanceChange.abs()) <= Constant.CONSTANT_NEGATIVE_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_BALANCE_NOT_ENOUGH);
        }
        if (frozenBalanceChange.compareTo(BigDecimal.ZERO) < Constant.CONSTANT_INTEGER_0 && frozenBalanceBefore.compareTo(frozenBalanceChange.abs()) <= Constant.CONSTANT_NEGATIVE_INTEGER_1) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_FROZEN_BALANCE_NOT_ENOUGH);
        }

        log.info("[分账处理] {} 更新分账号余额;   资金Id:{} 用户Id: {} 订单号:{},支付单号:{}, 账户类型: {} 变动之前: {} 变动金额:{},变动之后:{},冻结余额~变动之前: {} 冻结余额~变动金额:{},冻结余额~变动之后:{}", logStr, userAssetsRecordId, userId, orderNo, payOrderNo, accountTypeEnum.getName(), balanceBefore, balanceChange, balanceAfter, frozenBalanceBefore, frozenBalanceChange, frozenBalanceAfter);
        String ext = getExt(accountInfoMember.getAccountInfoMemberExtension(), accountTypeEnum);
        UpdateAccountBalanceDO updateAccountBalanceDO = new UpdateAccountBalanceDO();
        updateAccountBalanceDO.setAccountId(accountId);
        updateAccountBalanceDO.setUserId(userId);
        updateAccountBalanceDO.setLastAccountRecordId(lastAccountRecordId);
        updateAccountBalanceDO.setUserAccountNo(userAccountNo);
        updateAccountBalanceDO.setAccountType(accountTypeEnum.getCode());
        updateAccountBalanceDO.setOriginalBalance(balanceBefore);
        updateAccountBalanceDO.setBalanceChange(balanceChange);
        updateAccountBalanceDO.setOriginalFrozenBalance(frozenBalanceBefore);
        updateAccountBalanceDO.setFrozenBalanceChange(frozenBalanceChange);
        updateAccountBalanceDO.setExt(ext);
        int result = userAccountExtMapper.updateAccountBalance(updateAccountBalanceDO);
        if (result != Constant.CONSTANT_INTEGER_1) {
            log.warn("[分账处理] {} 更新分账号余额失败;   资金Id:{} 用户Id: {} 订单号:{},支付单号:{}, 账户类型: {} 变动之前: {} 变动金额:{},变动之后:{},冻结余额~变动之前: {} 冻结余额~变动金额:{},冻结余额~变动之后:{}", logStr, userAssetsRecordId, userId, orderNo, payOrderNo, accountTypeEnum.getName(), balanceBefore, balanceChange, balanceBefore.add(balanceChange), frozenBalanceBefore, frozenBalanceChange, frozenBalanceBefore.add(frozenBalanceChange));
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_UPDATE_FAIL);
        }
    }

    private List<UserAccountRecordMember> getUserAccountRecordByUserIdAndUserAssetsRecordId(Long userId, Long userAssetsRecordId, Integer accountType) {
        List<UserAccountRecord> userAccountRecordList = userAccountRecordExtMapper.getUserAccountRecordByUserIdAndUserAssetsRecordId(userId, userAssetsRecordId, accountType);
        if (userAccountRecordList.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtilsWrapper.convertList(userAccountRecordList, UserAccountRecordMember::new);
    }

    /**
     *
     */
    public UserAccountRecordMember getByUserIdAndUserAssetsRecordId(UserAccountRecordMember userAccountRecordMember) {
        List<UserAccountRecordMember> userAccountRecordMemberList = getUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember.getUserId(), userAccountRecordMember.getUserAssetsRecordId(), userAccountRecordMember.getAccountType());
        if (Objects.nonNull(userAccountRecordMemberList) && !userAccountRecordMemberList.isEmpty()) {
            UserAccountRecordMember userAccountRecordUpdateStatus = null;
            for (UserAccountRecordMember userAccountRecordMemberItem : userAccountRecordMemberList) {
                if (userAccountRecordMemberItem.getAccountType().equals(userAccountRecordMember.getAccountType()) && userAccountRecordMemberItem.getUserAccountNo().equals(userAccountRecordMember.getUserAccountNo()) && userAccountRecordMemberItem.getBalanceChange().abs().compareTo(userAccountRecordMember.getBalanceChange().abs()) == 0) {

                    if (StringUtils.isNotBlank(userAccountRecordMemberItem.getPayOrderNo())) {
                        if (userAccountRecordMemberItem.getPayOrderNo().equals(userAccountRecordMember.getPayOrderNo())) {
                            userAccountRecordUpdateStatus = userAccountRecordMemberItem;
                            break;
                        }
                    } else {
                        userAccountRecordUpdateStatus = userAccountRecordMemberItem;
                        break;
                    }
                }
            }
            if (Objects.nonNull(userAccountRecordUpdateStatus)) {
                return userAccountRecordUpdateStatus;
            }
        }
        return null;
    }

    public void userAccountRecordUpdateStatus(UserAccountRecordMember userAccountRecordMember) {
        UserAccountRecordMember userAccountRecordUpdateStatus = getByUserIdAndUserAssetsRecordId(userAccountRecordMember);
        if (Objects.nonNull(userAccountRecordUpdateStatus)) {
            if (userAccountRecordUpdateStatus.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
                log.info("[分账处理] 分账记录已存在 状态不是进行中  更新状态 用户id:{} 账户类型:{} 资金ID:{} ", userAccountRecordMember.getUserId(), userAccountRecordMember.getAccountType(), userAccountRecordMember.getUserAssetsRecordId());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_RECORD_EXIST);
            }
            UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordUpdateStatus, UserAccountRecord::new);
            userAccountRecord.setPayOrderNo(userAccountRecordMember.getPayOrderNo());
            userAccountRecord.setStatus(userAccountRecordMember.getStatus());
            userAccountRecord.setFinishTime(userAccountRecordMember.getFinishTime());
            userAccountRecordExtMapper.updateByPrimaryKeySelectiveUserId(userAccountRecord);
        } else {
            // 兜底一下
            userAccountRecordSave(userAccountRecordMember);
        }
    }

    public Long userAccountRecordUpdateDO(UserAccountRecordMember userAccountRecordMember) {
        UserAccountRecordMember userAccountRecordUpdateStatus = getByUserIdAndUserAssetsRecordId(userAccountRecordMember);
        if (Objects.nonNull(userAccountRecordUpdateStatus)) {
            if (userAccountRecordUpdateStatus.getStatus().equals(NetStatusEnum.SUCCESS.getCode())) {
                log.info("[分账处理] 分账记录已存在 状态不是进行中 用户id:{} 账户类型:{} 资金ID:{} ", userAccountRecordMember.getUserId(), userAccountRecordMember.getAccountType(), userAccountRecordMember.getUserAssetsRecordId());
                throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_RECORD_EXIST);
            }
            UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordUpdateStatus, UserAccountRecord::new);
            userAccountRecord.setBalanceBefore(userAccountRecordMember.getBalanceBefore());
            userAccountRecord.setBalanceAfter(userAccountRecordMember.getBalanceAfter());
            userAccountRecord.setPayOrderNo(userAccountRecordMember.getPayOrderNo());
            userAccountRecord.setOrderNo(userAccountRecordMember.getOrderNo());
            userAccountRecord.setStatus(userAccountRecordMember.getStatus());
            userAccountRecord.setFinishTime(userAccountRecordMember.getFinishTime());
            userAccountRecord.setBalanceIsChange(userAccountRecordMember.getBalanceIsChange());
            userAccountRecordExtMapper.updateByPrimaryKeySelectiveUserId(userAccountRecord);
            return userAccountRecord.getId();
        } else {
            // 兜底一下
            return userAccountRecordSave(userAccountRecordMember);
        }
    }

    public Long userAccountRecordSave(UserAccountRecordMember userAccountRecordMember) {
        Boolean flag = countUserAccountRecord(userAccountRecordMember);
        if (flag) {
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_RECORD_EXIST);
        } else {
            UserAccountRecord userAccountRecord = BeanUtilsWrapper.convert(userAccountRecordMember, UserAccountRecord::new);
            userAccountRecord.setAccountRecordNo(idWorkService.getNextIdLeafKey());
            userAccountRecordMapper.insertSelective(userAccountRecord);
            userAccountRecordMember.setId(userAccountRecord.getId());
            return userAccountRecord.getId();
        }
    }

    public Boolean countUserAccountRecord(UserAccountRecordMember userAccountRecordMember) {
        Integer i = userAccountRecordExtMapper
                .countUserAccountRecordByUserIdAndUserAssetsRecordId(userAccountRecordMember.getUserId(),
                        userAccountRecordMember.getUserAssetsRecordId(),
                        userAccountRecordMember.getBalanceChange(),
                        userAccountRecordMember.getAccountType());
        return i > ClearConstants.CONSTANT_INTEGER_0;
    }
}
