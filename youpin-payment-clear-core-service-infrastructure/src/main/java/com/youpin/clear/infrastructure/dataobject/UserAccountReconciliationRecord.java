package com.youpin.clear.infrastructure.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Table: user_account_reconciliation_record
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountReconciliationRecord implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户账户号
     */
    private String userAccountNo;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 最后更新记录ID
     */
    private Long lastAccountRecordId;

    /**
     * 0 默认,1,校验成功,2校验失败
     */
    private Integer checkFlag;

    /**
     * 用户权重
     */
    private Long userWeight;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}