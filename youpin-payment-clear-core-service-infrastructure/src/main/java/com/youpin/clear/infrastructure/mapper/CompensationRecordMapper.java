package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.CompensationRecord;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CompensationRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CompensationRecord row);

    int insertSelective(CompensationRecord row);

    CompensationRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CompensationRecord row);

    int updateByPrimaryKeyWithBLOBs(CompensationRecord row);

    int updateByPrimaryKey(CompensationRecord row);

}