package com.youpin.clear.infrastructure.config.sharding;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.youpin.clear.infrastructure.utils.ModuloOperationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 用户账户订单关系表:
 * user_assets_tread_no_relate
 * user_assets_order_no_relate
 * user_assets_pay_order_no_relate
 * user_sub_account_record_order_relate
 *
 * 分片键:字符串
 * 为null 默认再0片 其余 mod 64 表分片
 */
@Component
@Slf4j
public class PreciseShardingOrderRelate implements PreciseShardingAlgorithm<String> {

    private static final String TABLE_NAME_SEPARATOR = "_";

    private static final String APOLLO_PAYMENT_QUERY_USER_ASSET_RELATE_SHARDING_KEY = "payment.clear.user.asset.relate.sharding.modulo";

    private static final String MODULO_DEFAULT_VALUE = "64";

    Config config;

    public PreciseShardingOrderRelate() {
        // 初始化Apollo配置
        config = ConfigService.getAppConfig();
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> preciseShardingValue) {
        int shardingModuloInt = Integer.parseInt(config.getProperty(APOLLO_PAYMENT_QUERY_USER_ASSET_RELATE_SHARDING_KEY, MODULO_DEFAULT_VALUE));
        int modulo = ModuloOperationUtils.moduloOperationBigDecimal(ModuloOperationUtils.numberUnsignedHash(preciseShardingValue.getValue()), shardingModuloInt);
        return preciseShardingValue.getLogicTableName() + TABLE_NAME_SEPARATOR + modulo;
    }

}