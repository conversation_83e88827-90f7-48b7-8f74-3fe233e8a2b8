package com.youpin.clear.infrastructure.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Table: user_account_record_order_relate
 *
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountRecordOrderRelate implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;
    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 支付单号
     */
    private String payOrderNo;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}