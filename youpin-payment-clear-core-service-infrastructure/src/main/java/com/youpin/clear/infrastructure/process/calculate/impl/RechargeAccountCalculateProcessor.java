package com.youpin.clear.infrastructure.process.calculate.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class RechargeAccountCalculateProcessor extends DefaultAccountCalculateProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.RECHARGE);
    }

    @Override
    public void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember) {
        // 处理充值逻辑
        assert accountAggregate != null;
        BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();
        BigDecimal frozenBalanceChange = userAccountRecordMember.getFrozenBalanceChange();
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()));

        NetStatusEnum netStatusEnum = NetStatusEnum.getNetStatusEnum(userAccountRecordMember.getStatus());
        Integer typeId = userAccountRecordMember.getTypeId();
        // 是否进行中
        boolean isProcessing = netStatusEnum.equals(NetStatusEnum.PROCESSING);
        // 是否失败
        boolean isFail = netStatusEnum.equals(NetStatusEnum.FAIL);
        
        //------------------------------------------------------------------------------------------------------------------------------------
        //充值 进行中 不操作 账户 只存记录
        if (isProcessing || isFail) {
            log.info("[分账处理] 充值 状态为进行中,不操作账户,只存记录, 资金编号:{}", userAccountRecordMember.getUserAssetsRecordId());
            userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
            userAccountRecordSave(userAccountRecordMember);
        } else {
            Long lastAccountRecordId = userAccountRecordUpdateDO(userAccountRecordMember);
            //修改账户金额
            updateAccountBalance("充值+", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
            if (typeId.equals(ClearConstants.CONSTANT_INTEGER_1) || typeId.equals(ClearConstants.CONSTANT_INTEGER_82) || typeId.equals(ClearConstants.CONSTANT_INTEGER_219)) {
                publicBalance.setBalance(publicBalance.getBalance().add(balanceChange));
            } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_43)) {
                publicBalance.setPurchaseBalance(publicBalance.getPurchaseBalance().add(balanceChange));
            }
        }
        //------------------------------------------------------------------------------------------------------------------------------------

    }
}
