package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UpdateAccountBalanceDO;
import com.youpin.clear.infrastructure.dataobject.UserAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface UserAccountExtMapper {

    List<UserAccount> getAccountInfoMember(@Param("userId") Long userId);

    int updateBalanceAccountInfoMember(@Param("id") Long id,
                                       @Param("userId") Long userId,
                                       @Param("accountType") int accountType,
                                       @Param("userAccountNo") String userAccountNo,
                                       @Param("originalBalance") BigDecimal originalBalance,
                                       @Param("balanceChange") BigDecimal balanceChange,
                                       @Param("ext") String ext,
                                       @Param("lastAccountRecordId") Long lastAccountRecordId);

    int updateFrozenBalanceAccountInfoMember(@Param("id") Long id, @Param("userId") Long userId,
                                             @Param("accountType") int accountType,
                                             @Param("userAccountNo") String userAccountNo,
                                             @Param("originalFrozenBalance") BigDecimal originalFrozenBalance,
                                             @Param("frozenBalanceChange") BigDecimal frozenBalanceChange,
                                             @Param("lastAccountRecordId") Long lastAccountRecordId
    );

    void deleteByUserId(@Param("userId") Long userId);

    void updateByIdAndUserIdSelective(UserAccount userAccount);

    int updateAccountBalance(UpdateAccountBalanceDO updateAccountBalanceDO);


    /**
     * 更新账户信息
     *
     * @param originAccount 原账户信息
     * @param targetAccount 目标账户信息
     * @return 返回
     */
    int updateAccount(@Param("originAccount") UserAccount originAccount, @Param("targetAccount") UserAccount targetAccount);

    List<UserAccount> getAccountAggregatesMapByType(@Param("userId") Long userId, @Param("accountTypeCodeList") List<Integer> accountTypeCodeList);
}