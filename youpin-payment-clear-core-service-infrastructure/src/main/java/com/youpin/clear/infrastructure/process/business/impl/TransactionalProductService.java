package com.youpin.clear.infrastructure.process.business.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.enums.*;
import com.youpin.clear.domain.dto.*;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.factory.UU898FundingDirectionFactory;
import com.youpin.clear.domain.factory.UU898FundingDirectionV2Factory;
import com.youpin.clear.domain.gateway.UU898UserSubAccountGateway;
import com.youpin.clear.domain.process.UU898FundingDirectionV2Processor;
import com.youpin.clear.infrastructure.dataobject.PlatformAccountRecord;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsInfo;
import com.youpin.clear.infrastructure.helper.UU898UserAccountHelper;
import com.youpin.clear.infrastructure.helper.UserAssetsAmountCheckerHelper;
import com.youpin.clear.infrastructure.mapper.PlatformAccountRecordMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsInfoExtMapper;
import com.youpin.clear.infrastructure.mapper.uu898.UU898UserAssetsRecordExtMapper;
import com.youpin.clear.infrastructure.utils.BeanUtilsWrapper;
import com.youpin.clear.infrastructure.utils.ShardUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TransactionalProductService {

    @Autowired
    UU898UserAssetsInfoExtMapper uu898UserAssetsInfoExtMapper;

    @Autowired
    UU898FundingDirectionFactory uu898FundingDirectionFactory;

    @Autowired
    UU898FundingDirectionV2Factory uu898FundingDirectionV2Factory;


    @Autowired
    UU898UserAssetsRecordExtMapper userAssetsRecordExtMapper;


    @Autowired
    PlatformAccountRecordMapper platformAccountRecordMapper;

    @Autowired
    private UU898UserSubAccountGateway uu898UserSubAccountGateway;

    @Autowired
    private UU898UserAccountHelper uu898UserAccountHelper;


    @Autowired
    @Qualifier("asyncTaskExecutor")
    ThreadPoolTaskExecutor asyncTaskExecutor;


    @Transactional(rollbackFor = Exception.class)
    public void accountUpdateTransactional(FinancialProcessorDTO financialProcessorDTO, List<Long> userIdList) {
        //二次幂等校验 判断数据 是否大于 0
        dataIdempotenceCheck(financialProcessorDTO, Constant.CONSTANT_INTEGER_0);
        //查询涉及的用户资金信息
        Map<Long, UserAssetsInfoDTO> uu898UserAssetsInfoMap = getUserAssetsInfoDTOMap(userIdList);
        //资金逻辑加减
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        //处理排序
        if (abstractUserAssetsRecordDTOList.size() > Constant.CONSTANT_INTEGER_1) {
            List<Integer> customOrderList = SubAssetsCustomOrderEnum.getByTypeCode(financialProcessorDTO.getSubBusTypeFrontEnum());
            if (CollectionUtils.isNotEmpty(customOrderList)) {
                UserAssetsAmountCheckerHelper.customSort(abstractUserAssetsRecordDTOList, customOrderList);
            }
        }
        if (BooleanUtils.isTrue(financialProcessorDTO.getUseNewAssetBranch())) {
            log.info("使用新版本资金处理,orderNo={}", financialProcessorDTO.getOrderNo());
            newAssetHandle(abstractUserAssetsRecordDTOList, uu898UserAssetsInfoMap);
        } else {
            log.info("使用旧版本资金处理,orderNo={}", financialProcessorDTO.getOrderNo());
            oldAssetHandle(abstractUserAssetsRecordDTOList, uu898UserAssetsInfoMap);
        }
        //缓冲记账的记录数据
        addPlatformAccountRecordDTOList(financialProcessorDTO);
    }

    private void oldAssetHandle(List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList, Map<Long, UserAssetsInfoDTO> uu898UserAssetsInfoMap) {
        //资金处理
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            //用户账户信息
            UserAssetsInfoDTO userAssetsInfoDTO = uu898UserAssetsInfoMap.get(abstractUserAssetsRecordDTO.getUserId());
            uu898FundingDirectionFactory.getProcessor(abstractUserAssetsRecordDTO.getFundingDirectionEnum()).process(abstractUserAssetsRecordDTO, userAssetsInfoDTO);
        }
    }

    private void newAssetHandle(List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList, Map<Long, UserAssetsInfoDTO> uu898UserAssetsInfoMap) {
        //资金处理
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            // 用户账户信息
            UserAssetsInfoDTO userAssetsInfoDTO = uu898UserAssetsInfoMap.get(abstractUserAssetsRecordDTO.getUserId());
            // 获取处理器
            UU898FundingDirectionV2Processor processor = uu898FundingDirectionV2Factory.getProcessor(abstractUserAssetsRecordDTO.getFundingDirectionEnum());
            // 处理
            processor.process(abstractUserAssetsRecordDTO, userAssetsInfoDTO);
        }
    }


    private void addPlatformAccountRecordDTOList(FinancialProcessorDTO financialProcessorDTO) {
        if (!financialProcessorDTO.checkPlatformAccountBuffer()) {
            return;
        }
        List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList = financialProcessorDTO.getAbstractUserAssetsRecordDTOList();
        for (AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO : abstractUserAssetsRecordDTOList) {
            if (abstractUserAssetsRecordDTO.checkAccountBufferBookkeeping()) {
                PlatformAccountRecordMoneyTypeEnum moneyTypeEnum = PlatformAccountRecordMoneyTypeEnum.UNKNOWN;
                if (abstractUserAssetsRecordDTO.isBalanceChange()) {
                    moneyTypeEnum = PlatformAccountRecordMoneyTypeEnum.BALANCE;
                } else if (abstractUserAssetsRecordDTO.isPurchaseBalanceChange()) {
                    moneyTypeEnum = PlatformAccountRecordMoneyTypeEnum.PURCHASE_BALANCE;
                }
                PlatformAccountRecordDTO platformAccountRecordDTO = PlatformAccountRecordDTO.builder()
                        .status(PlatformAccountRecordStatusEnum.PENDING.getCode())
                        .userId(abstractUserAssetsRecordDTO.getUserId())
                        .shard(ShardUtil.shard(abstractUserAssetsRecordDTO.getUserId()))
                        .userAssetsRecordId(abstractUserAssetsRecordDTO.getId())
                        .treadNo(abstractUserAssetsRecordDTO.getTreadNo())
                        .moneyType(moneyTypeEnum.getCode())
                        .blockMoney(abstractUserAssetsRecordDTO.getThisBlockMoney())
                        .money(abstractUserAssetsRecordDTO.getThisMoney()).build();
                PlatformAccountRecord convertRecord = BeanUtilsWrapper.convert(platformAccountRecordDTO, PlatformAccountRecord::new);
                platformAccountRecordMapper.insertSelective(convertRecord);
                platformAccountRecordDTO.setId(convertRecord.getId());
                abstractUserAssetsRecordDTO.setPlatformAccountRecordId(platformAccountRecordDTO.getId());
                //financialProcessorDTO.addPlatformAccountRecordDTOList(platformAccountRecordDTO);
            }
        }
    }

    private void dataIdempotenceCheck(FinancialProcessorDTO dto, Integer sum) {
        boolean anyMatch = dto.getAbstractUserAssetsRecordDTOList().stream().map(this::getUU898UserAssetsRecords).anyMatch(count -> null != count && count > sum);
        if (anyMatch) {
            log.error("[账务交易] 流水号:{} 订单号:{} 支付单号:{} 二次幂等校验-结果:不通过.存在相同数据 ", dto.getSerialNo(), dto.getOrderNo(), dto.getPayOrderNo());
            throw new PaymentClearBusinessException(FinancialProcessorResultDTO.ALREADY_PROCESSED.getCode(), FinancialProcessorResultDTO.ALREADY_PROCESSED.getMsg());
        }
        log.info("[账务交易] 流水号:{} 订单号:{} 支付单号:{} 二次幂等校验-结果:通过", dto.getSerialNo(), dto.getOrderNo(), dto.getPayOrderNo());
    }

    private Integer getUU898UserAssetsRecords(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO) {
        return userAssetsRecordExtMapper.countUserAssetsRecordDTOList(
                abstractUserAssetsRecordDTO.getUserId(),
                abstractUserAssetsRecordDTO.getSerialNo(),
                abstractUserAssetsRecordDTO.getOrderNo(),
                abstractUserAssetsRecordDTO.getPayOrderNo(),
                abstractUserAssetsRecordDTO.getTypeId(),
                abstractUserAssetsRecordDTO.getStatus());
    }

    private Map<Long, UserAssetsInfoDTO> getUserAssetsInfoDTOMap(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            log.error("[账务交易] 账户不存在集合为null {}", userIdList);
            throw new PaymentClearBusinessException(ErrorCode.USER_ACCOUNT_INFO_IS_NULL);
        }
        List<UU898UserAssetsInfo> uu898UserAssetsInfos = uu898UserAssetsInfoExtMapper.selectByUserIdList(userIdList);
        if (CollectionUtils.isEmpty(uu898UserAssetsInfos) || uu898UserAssetsInfos.size() != userIdList.size()) {
            log.error("[账务交易] 用户账户不存在 {}", userIdList);
            throw new PaymentClearBusinessException(ErrorCode.ACCOUNT_NOT_EXIST);
        }
        Map<Long, UserAssetsInfoDTO> userAssetsInfoDTOMap = uu898UserAssetsInfos.stream().collect(Collectors.toMap(UU898UserAssetsInfo::getUserId, item -> BeanUtilsWrapper.convert(item, UserAssetsInfoDTO::new)));
        // 遍历dto设置子账户信息
        userAssetsInfoDTOMap.forEach((userId, assetsInfoDTO) -> {
            // 设置子账户map
            assetsInfoDTO.setSubAccountDTOMap(uu898UserSubAccountGateway.getUserSubAccountMap(userId));
        });
        return userAssetsInfoDTOMap;
    }

}
