package com.youpin.clear.infrastructure.mapper;

import com.youpin.clear.infrastructure.dataobject.UserAssetsTreadNoRelate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserAssetsTreadNoRelateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserAssetsTreadNoRelate row);

    int insertSelective(UserAssetsTreadNoRelate row);

    UserAssetsTreadNoRelate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserAssetsTreadNoRelate row);

    int updateByPrimaryKey(UserAssetsTreadNoRelate row);
}