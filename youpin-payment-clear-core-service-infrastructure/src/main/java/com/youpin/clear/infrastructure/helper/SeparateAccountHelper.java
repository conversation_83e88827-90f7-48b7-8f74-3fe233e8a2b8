package com.youpin.clear.infrastructure.helper;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserSubAccountDTO;
import com.youpin.clear.domain.dto.UserSubAccountInfoDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class SeparateAccountHelper {


    //


    /**
     * 过滤数据 并累计各个账户类型 和 资金类型的金额
     * 用户 : typeId : 账户类型 : 金额
     */
    public static void filterGetUserSubAccountDTO(List<UserSubAccountRecordMember> userSubAccountRecordMember) {

        Map<Long, Map<Integer, Map<Integer, BigDecimal>>> collectMap = userSubAccountRecordMember.stream()

                .collect(Collectors.groupingBy(UserSubAccountRecordMember::getUserId,

                        Collectors.groupingBy(UserSubAccountRecordMember::getTypeId,

                                Collectors.groupingBy(UserSubAccountRecordMember::getAccountType,
                                        Collectors.reducing(
                                                BigDecimal.ZERO,  // 初始值
                                                item -> item.getBalanceChange().abs(), // 提取金额
                                                BigDecimal::add   // 累加操作
                                        )
                                ))));

        // collectMap  根据 getAccountType 转换成 Map<Long, Map<Integer, UserSubAccountDTO>> 这个结构

        collectMap.forEach((userId, map) -> map.forEach((typeId, map1) -> {
            UserSubAccountDTO userSubAccountDTO = new UserSubAccountDTO();
            userSubAccountDTO.setUserId(userId);
            userSubAccountDTO.setBalance1(new UserSubAccountInfoDTO(BigDecimal.ZERO, map1.get(AccountTypeEnum.BALANCE_TRADER_1.getCode()), map1.get(AccountTypeEnum.BALANCE_WITHDRAW_1.getCode())));
            userSubAccountDTO.setBalance2(new UserSubAccountInfoDTO(BigDecimal.ZERO, map1.get(AccountTypeEnum.BALANCE_TRADER_1.getCode()), map1.get(AccountTypeEnum.BALANCE_WITHDRAW_1.getCode())));
            userSubAccountDTO.setPurchaseBalanceRecharge1(new UserSubAccountInfoDTO(BigDecimal.ZERO, BigDecimal.ZERO, map1.get(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode())));
            userSubAccountDTO.setPurchaseBalanceRecharge2(new UserSubAccountInfoDTO(BigDecimal.ZERO, BigDecimal.ZERO, map1.get(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode())));
            userSubAccountDTO.setPurchaseBalanceTransfer1(new UserSubAccountInfoDTO(BigDecimal.ZERO, map1.get(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_TRADER_1.getCode()), map1.get(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_WITHDRAW_1.getCode())));
            userSubAccountDTO.setPurchaseBalanceTransfer2(new UserSubAccountInfoDTO(BigDecimal.ZERO, map1.get(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_TRADER_2.getCode()), map1.get(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_WITHDRAW_2.getCode())));
        }));
    }


    /**
     * 根据优先级顺序减
     */
    static Map<AccountTypeEnum, BigDecimal> subtractionAccount(BigDecimal amount, boolean isBlockMoney, AccountAggregate accountAggregate, List<AccountTypeEnum> accountTypeEnumList) {
        // 输入验证
        if (null == amount || null == accountAggregate || null == accountTypeEnumList) {
            throw new PaymentClearBusinessException(ErrorCode.AMOUNT_CHECK_FAIL);
        }
        //定义map 账户类型集合和值
        Map<AccountTypeEnum, BigDecimal> accountTypeEnumBigDecimalMap = new HashMap<>();
        //accountTypeEnumList 去重 去null
        accountTypeEnumList = accountTypeEnumList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //转换金额 为绝对值
        BigDecimal absAmount = amount.abs();
        BigDecimal diff = BigDecimal.ZERO;
        for (AccountTypeEnum accountTypeEnum : accountTypeEnumList) {
            AccountInfoMember accountByType = accountAggregate.getAccountByType(accountTypeEnum);
            if (accountByType == null) {
                continue;
            }
            BigDecimal balance = !isBlockMoney ? accountByType.getBalance() : accountByType.getFrozenBalance();
            //获取当前账户可以减去的值
            diff = subtractionGetValue(balance, absAmount);
            if (diff.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            accountTypeEnumBigDecimalMap.put(accountTypeEnum, diff);
            absAmount = absAmount.subtract(diff);
            if (absAmount.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
        }
        return accountTypeEnumBigDecimalMap;
    }

    public static void main(String[] args) {
        List<AccountTypeEnum> accountTypeEnumList = List.of(
                AccountTypeEnum.BALANCE_TRADER_2,
                AccountTypeEnum.BALANCE_TRADER_1,
                AccountTypeEnum.BALANCE_WITHDRAW_2,
                AccountTypeEnum.BALANCE_WITHDRAW_1);

        AccountAggregate accountAggregate = new AccountAggregate(1L,
                Map.of(
                        AccountTypeEnum.BALANCE_TRADER_1, AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_TRADER_1.getCode()).balance(BigDecimal.valueOf(1001)).frozenBalance(BigDecimal.ZERO).build(),
                        AccountTypeEnum.BALANCE_TRADER_2, AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_TRADER_2.getCode()).balance(BigDecimal.valueOf(1002)).frozenBalance(BigDecimal.ZERO).build(),
                        AccountTypeEnum.BALANCE_WITHDRAW_1, AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_WITHDRAW_1.getCode()).balance(BigDecimal.valueOf(1003)).frozenBalance(BigDecimal.ZERO).build(),
                        AccountTypeEnum.BALANCE_WITHDRAW_2, AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_WITHDRAW_2.getCode()).balance(BigDecimal.valueOf(1004)).frozenBalance(BigDecimal.ZERO).build()
                )
        );
        Map<AccountTypeEnum, BigDecimal> accountTypeEnumBigDecimalMap = subtractionAccount(BigDecimal.valueOf(123), false, accountAggregate, accountTypeEnumList);
        System.out.println(accountTypeEnumBigDecimalMap);


        accountTypeEnumBigDecimalMap = subtractionAccount(BigDecimal.valueOf(1213), false, accountAggregate, accountTypeEnumList);
        System.out.println(accountTypeEnumBigDecimalMap);
    }

    /**
     * 减法 得到减去的值
     */
    static BigDecimal subtractionGetValue(BigDecimal balance, BigDecimal subtractAmount) {
        BigDecimal subtract = balance.subtract(subtractAmount);
        if (subtract.compareTo(BigDecimal.ZERO) <= ClearConstants.CONSTANT_INTEGER_0) {
            return balance;
        }
        return subtractAmount;
    }


    /**
     * 根据自定义顺序对对象列表进行排序
     */
    public static void customSort(List<UU898UserAssetsRecordDTO> objectList, List<Integer> customOrderList) {
        if (CollectionUtils.isEmpty(objectList) || CollectionUtils.isEmpty(customOrderList)) {
            return;
        }
        // 创建值到索引的映射
        int maxValue = customOrderList.stream().max(Integer::compare).orElse(0);
        int[] orderMap = new int[maxValue + 1];
        Arrays.fill(orderMap, Integer.MAX_VALUE);
        for (int i = 0; i < customOrderList.size(); i++) {
            orderMap[customOrderList.get(i)] = i;
        }
        // 使用反射获取字段值并排序
        objectList.sort((a, b) -> {
            int valueA = a.getTypeId();
            int valueB = b.getTypeId();
            // 根据自定义顺序排序
            // 检查值是否超出数组范围
            int indexA = valueA <= maxValue ? orderMap[valueA] : Integer.MAX_VALUE;
            int indexB = valueB <= maxValue ? orderMap[valueB] : Integer.MAX_VALUE;
            return Integer.compare(indexA, indexB);
        });
    }


    /**
     * 是否是求购余额变动
     *
     * @return boolean true 是 false 否
     */
    public static boolean isPurchaseBalanceChange(DoNetPayChannelEnum payChannel) {
        if (payChannel == null) {
            return false;
        }
        return payChannel.equals(DoNetPayChannelEnum.PurchaseBalance);
    }

    /**
     * 是否是余额变动
     *
     * @return boolean true 是 false 否
     */
    public static boolean isBalanceChange(DoNetPayChannelEnum payChannel) {
        if (payChannel == null) {
            return false;
        }
        return payChannel.equals(DoNetPayChannelEnum.Balance);
    }

    /**
     * 是否是支付渠道变动
     *
     * @return boolean true 是 false 否
     */
    public static boolean isPayChannelChange(DoNetPayChannelEnum payChannel) {
        if (payChannel == null) {
            return false;
        }
        return (payChannel.equals(DoNetPayChannelEnum.Alipay)
                || payChannel.equals(DoNetPayChannelEnum.DyPay)
                || payChannel.equals(DoNetPayChannelEnum.JD)
                || payChannel.equals(DoNetPayChannelEnum.Wx));
    }
}
