package com.youpin.clear.infrastructure.utils;

import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
@Component
public class RLockUtilsComponent {

    @Autowired
    RedissonClient redissonClient;

    /***
     * 加锁操作
     *
     * @param lockKey   锁的key
     * @param waitTime  锁等待时间，单位秒
     * @param leaseTime 锁释放时间，单位秒
     */
    public void lockHandle(String lockKey, long waitTime, long leaseTime, Consumer<Void> consumer) {
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            // 如果获取锁成功，处理业务逻辑，LOCK_LEASE_TIME后立即释放锁
            if (rLock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                log.info("[RLockUtils] tryLock handleBeginTime:{} lockKey:{} ", stopWatch.getTotalTimeMillis(), lockKey);
                consumer.accept(null);
                stopWatch.stop();
                log.info("[RLockUtils] tryLock handleFinishTime:{} lockKey:{} ", stopWatch.getTotalTimeMillis(), lockKey);
            } else {
                // 获取锁失败
                log.error("[{}] lockHandle tryLock fail lockKey:{} ", ErrorCode.LOCK_PAY_ORDER_TRY_LOCK_FAIL.getMessage(), lockKey);
                throw new PaymentClearBusinessException(ErrorCode.LOCK_PAY_ORDER_TRY_LOCK_FAIL);
            }
        } catch (PaymentClearBusinessException e) {
            log.error("[RLockUtils] 业务异常. error,{}", ExceptionUtils.getStackTrace(e));
            throw e;
        } catch (Exception e) {
            log.error("[RLockUtils] 系统异常. error,{}", ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR);
        } finally {
            // 是否是锁定状态
            if (rLock.isLocked()) {
                // 是否是当前线程的锁
                if (rLock.isHeldByCurrentThread()) {
                    // 释放锁
                    rLock.unlock();
                }
            }
        }
    }

    /**
     * @param lockKeyList 批量锁
     * @param waitTime    锁等待时间 毫秒 后面不再使用锁等待
     */
    public void lockMultipleHandle(List<String> lockKeyList, long waitTime, Consumer<Void> consumer) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        RLock[] lockList = lockKeyList.stream().map(lockKey -> redissonClient.getLock(lockKey)).toArray(RLock[]::new);
        RLock multiLock = redissonClient.getMultiLock(lockList);
//        log.info("[lockMultipleHandle] 加锁开始 lockKeyList:{} ", lockKeyList);
        try {
            // 如果获取锁成功，处理业务逻辑
            if (multiLock.tryLock(waitTime, TimeUnit.MILLISECONDS)) {
                log.info("[lockMultipleHandle] 加锁成功 lockKeyList:{} ", lockKeyList);
                // 所有锁都获取成功，执行业务逻辑
                consumer.accept(null);
            } else {
                //某个锁获取失败，释放之前已经获取的所有锁
                log.warn("[lockMultipleHandle] 加锁失败 lockKeyList:{} ", lockKeyList);
                multiLock = null; //防止释放锁报错
                throw new PaymentClearBusinessException(ErrorCode.LOCK_PAY_ORDER_TRY_LOCK_FAIL);
            }
        } catch (PaymentClearBusinessException e) {
            log.error("[lockMultipleHandle] 业务异常. error,{}", ExceptionUtils.getStackTrace(e));
            throw e;
        } catch (Exception e) {
            log.error("[lockMultipleHandle] 系统异常. error,{}", ExceptionUtils.getStackTrace(e));
            throw new PaymentClearBusinessException(ErrorCode.SYSTEM_ERROR);
        } finally {
            // 释放所有锁
            //不要使用 multiLock.isHeldByCurrentThread() 这个方法
            // 这次的实例是 多重锁 所以不能使用这个方法
            try {
                if (null != multiLock) {
                    multiLock.unlock();
                    log.info("[lockMultipleHandle] 锁已全部释放:{}", lockKeyList);
                }
            } catch (Exception e) {
                log.error("[lockMultipleHandle] 解锁异常. error,{}", ExceptionUtils.getStackTrace(e));
            }
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            long totalTimeMillis = stopWatch.getTotalTimeMillis();
            log.info("[lockMultipleHandle] 处理完成 lockKey:{} 耗时(毫秒):{}", lockKeyList, totalTimeMillis);
            if (totalTimeMillis > 5000) {
                log.warn("[lockMultipleHandle] 处理完成 lockKey:{} 大于5秒", lockKeyList);
            }
        }
    }
}
