package com.youpin.clear.infrastructure.process.calculate.impl;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CashWithdrawalAccountCalculateProcessor extends DefaultAccountCalculateProcessor {
    @Override
    public List<DirectionEnum> support() {
        return List.of(DirectionEnum.CASH_WITHDRAWAL);
    }

    @Override
    public void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember) {
        assert accountAggregate != null;
        BigDecimal balanceChange = userAccountRecordMember.getBalanceChange();
        BigDecimal frozenBalanceChange = userAccountRecordMember.getFrozenBalanceChange();
        AccountInfoMember accountInfoMember = accountAggregate.getAccountByType(AccountTypeEnum.getAccountTypeEnum(userAccountRecordMember.getAccountType()));

        //------------------------------------------------------------------------------------------------------------------------------------
        balanceChange = balanceChange.negate();
        userAccountRecordMember.setBalanceChange(balanceChange);
        //提现 成功 不操作 账户 只更新记录
        if (userAccountRecordMember.isSuccess() || userAccountRecordMember.isFail()) {
            //判断数据库中是已经有进行中数据
            UserAccountRecordMember userIdAndUserAssetsRecordProcessing = getByUserIdAndUserAssetsRecordId(userAccountRecordMember);
            if (Objects.nonNull(userIdAndUserAssetsRecordProcessing)) {
                if (!userIdAndUserAssetsRecordProcessing.getStatus().equals(userAccountRecordMember.getStatus())) {
                    log.info("[分账处理] 资金类型为提现,数据库已经有数据:{},--->更新状态为{}", userIdAndUserAssetsRecordProcessing.getStatus(), userAccountRecordMember.getStatus());
                    userAccountRecordUpdateStatus(userAccountRecordMember);
                }
            } else {
                log.info("[分账处理] 资金类型为提现,数据库中没有进行中的数据 ,当前消费状态:{}", userAccountRecordMember.getStatus());
                throw new PaymentClearBusinessException(ErrorCode.BILL_STATUS_ORDER_ERROR);
            }
        } else {
            //进行中
            //修改账户金额
            userAccountRecordMember.setBalanceIsChange(Constant.CONSTANT_INTEGER_1);
            Long lastAccountRecordId = userAccountRecordSave(userAccountRecordMember);
            updateAccountBalance("提现-", userAccountRecordMember, accountInfoMember, balanceChange, frozenBalanceChange, lastAccountRecordId);
            Integer typeId = userAccountRecordMember.getTypeId();
            if (typeId.equals(ClearConstants.CONSTANT_INTEGER_2)) {
                publicBalance.setBalance(publicBalance.getBalance().add(balanceChange));
            } else if (typeId.equals(ClearConstants.CONSTANT_INTEGER_44)) {
                publicBalance.setPurchaseBalance(publicBalance.getPurchaseBalance().add(balanceChange));
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------------

    }
}
