package com.youpin.clear.infrastructure.converter;


import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsRecordMessage;
import com.youpin.clear.infrastructure.dataobject.uu898.UU898UserAssetsRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface UU898UserAssetsRecordConvertor {
    UU898UserAssetsRecordConvertor MAPPER = Mappers.getMapper(UU898UserAssetsRecordConvertor.class);
    
    UU898UserAssetsRecordDTO toUU898UserAssetsRecordDto(AbstractUserAssetsRecordDTO dto);

    UU898UserAssetsRecord toUU898UserAssetsRecord(UU898UserAssetsRecordDTO userAssetsRecordDTO);

    UserAssetsRecordMessage toUserAssetsRecordMessage(AbstractUserAssetsRecordDTO agg);
}
