package com.youpin.clear.infrastructure.dataobject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Table: user_account_record
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountRecord implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 账户明细id
     */
    private String accountRecordNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;

    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;

    /**
     * 资金明细唯一单号
     */
    private String treadNo;

    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 用户账户号
     */
    private String userAccountNo;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 变动之前账户余额
     */
    private BigDecimal balanceBefore;

    /**
     * 变动金额
     */
    private BigDecimal balanceChange;

    /**
     * 变动之后账户余额
     */
    private BigDecimal balanceAfter;

    /**
     * 余额是否变动 0不变,1变动
     */
    private Integer balanceIsChange;

    /**
     * 0 失败 1 成功 2 进行中
     */
    private Integer status;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     */
    private BigDecimal frozenBalanceBefore;

    /**
     */
    private BigDecimal frozenBalanceChange;

    /**
     */
    private BigDecimal frozenBalanceAfter;

    private static final long serialVersionUID = 1L;
}