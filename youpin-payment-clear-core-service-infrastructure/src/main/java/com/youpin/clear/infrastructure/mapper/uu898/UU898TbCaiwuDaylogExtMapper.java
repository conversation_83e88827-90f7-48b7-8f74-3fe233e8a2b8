package com.youpin.clear.infrastructure.mapper.uu898;

import com.youpin.clear.infrastructure.dataobject.uu898.UU898TbCaiwuDaylog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface UU898TbCaiwuDaylogExtMapper {

    /**
     * 获取当天的 某个资金ID
     */
    List<UU898TbCaiwuDaylog> selectByTtypeAndDdate(@Param("ttype") List<Integer> ttype, @Param("ddate") LocalDate ddate, @Param("displaystatus") Integer displaystatus, @Param("source") Integer source);

    /**
     * 根据ID 修改数据 展示状态
     */
    int updateDisplayStatusById(@Param("id") Long id, @Param("displaystatus") Integer displaystatus);
}