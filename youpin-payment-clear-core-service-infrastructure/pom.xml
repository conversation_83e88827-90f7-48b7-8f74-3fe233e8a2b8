<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.youpin.clear</groupId>
        <artifactId>youpin-payment-clear-core-service</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>youpin-payment-clear-core-service-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>youpin-payment-clear-core-service-infrastructure</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <skipTests>true</skipTests>
        <sharding-jdbc-spring-boot-starter.version>4.0.0-RC1</sharding-jdbc-spring-boot-starter.version>
        <youpin-infra-leaf-sdk-client.version>1.1.0-RELEASE</youpin-infra-leaf-sdk-client.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.youpin.clear</groupId>
            <artifactId>youpin-payment-clear-core-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uu898.youpin</groupId>
            <artifactId>youpin-commons-base</artifactId>
        </dependency>
        <!--   redis     -->
        <dependency>
            <groupId>com.uu898.youpin</groupId>
            <artifactId>youpin-commons-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>2.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client-config-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.youpin</groupId>
            <artifactId>youpin-apollo-config-spring-boot-starter</artifactId>
        </dependency>
        <!--jasypt加密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-apollo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-circuitbreaker-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--    必须放在mybatis前面    -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
        </dependency>
        <!-- no more than 2.3.3-->
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.2.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>1.5.2.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.2.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

        <!--   监控      -->
        <dependency>
            <groupId>com.uu898.youpin</groupId>
            <artifactId>youpin-commons-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>


        <dependency>
            <groupId>youpin-commons</groupId>
            <artifactId>youpin-commons-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.youpin</groupId>
            <artifactId>youpin-infra-leaf-sdk-client</artifactId>
            <version>${youpin-infra-leaf-sdk-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.youpin</groupId>
            <artifactId>youpin-trade-order-query-client</artifactId>
            <version>${youpin-trade-order-query-client.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.youpin</groupId>
            <artifactId>youpin-infra-nacos-warmup</artifactId>
            <version>1.0.5-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.youpin</groupId>
            <artifactId>asset-account-service-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>


    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- mybatis generator 自动生成代码插件 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.4.2</version>
                <configuration>
                    <!--                    <configurationFile>src/main/resources/mybatis-generator/generatorConfig.xml</configurationFile>-->
                    <configurationFile>src/main/resources/mybatis-generator/generatorConfig-uu898.xml
                    </configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                    <tableNames>
                        <!--  user_account, user_account_record, user_account_record_order_relate,compensation_record,user_account_reconciliation_record,account_assets_type,account_assets_type_relate-->
                        <!--  UserAssetsRecord,UserAssetsInfo,UserAssetsRecordType-->
                        <!--  UserAssetsRecordPolar-->
                        <!--  adjustment_apply, adjustment_apply_detail-->
<!--                        user_assets_record,user_assets_order_no_relate,user_assets_pay_order_no_relate,user_assets_id_relate,user_assets_tread_no_relate,user_assets_tag-->
<!--                        adjustment_apply transaction_service_fee_operation_record-->
<!--                        adjustment_apply-->
<!--                          transaction_service_fee_statement_record,transaction_service_fee_operate_record-->
<!--                        tb_caiwu_daylog -->
<!--                        uu898_user_sub_account,-->
<!--                        uu898_user_sub_account_flow_record,-->
                        OrderPayMoneyInfo
                    </tableNames>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.22</version>
                    </dependency>
                    <dependency>
                        <groupId>cn.guizhou001</groupId>
                        <artifactId>mybatis-generator-lombok-plugin</artifactId>
                        <version>1.0-SNAPSHOT</version>
                    </dependency>
                </dependencies>

            </plugin>

        </plugins>
    </build>
</project>
