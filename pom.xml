<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.youpin</groupId>
        <artifactId>youpin-dependencies</artifactId>
        <version>2.1.4.1-RELEASE</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.youpin.clear</groupId>
    <artifactId>youpin-payment-clear-core-service</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>youpin-payment-clear-core-service</name>
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.deploy.skip>true</maven.deploy.skip>
        <skipTests>true</skipTests>
        <rocketmq.version>2.1.1</rocketmq.version>
        <youpin-commons-rocketmq.version>1.0.1-RELEASE</youpin-commons-rocketmq.version>
        <youpin-payment-clear-core-service-client.version>tradeAccountV2-SNAPSHOT</youpin-payment-clear-core-service-client.version>
        <youpin-payment-transaction-gateway-service.version>1.1.5-RELEASE</youpin-payment-transaction-gateway-service.version>
        <youpin-trade-order-query-client.version>3.0.3-RELEASE</youpin-trade-order-query-client.version>
    </properties>

    <dependencies>



    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-adapter</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-client</artifactId>
                <version>${youpin-payment-clear-core-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-common</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-app</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-report</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-domain</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.youpin.clear</groupId>
                <artifactId>youpin-payment-clear-core-service-infrastructure</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>youpin-commons</groupId>
                <artifactId>youpin-commons-rocketmq</artifactId>
                <version>${youpin-commons-rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.youpin.payment.gateway</groupId>
                <artifactId>youpin-payment-transaction-gateway-service-client</artifactId>
                <version>${youpin-payment-transaction-gateway-service.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.uu898.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.uu898.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>youpin-payment-clear-core-service-client</module>
        <module>youpin-payment-clear-core-service-adapter</module>
        <module>youpin-payment-clear-core-service-app</module>
        <module>youpin-payment-clear-core-service-common</module>
        <module>youpin-payment-clear-core-service-domain</module>
        <module>youpin-payment-clear-core-service-infrastructure</module>
        <module>youpin-payment-clear-core-service-start</module>
        <module>youpin-payment-clear-core-service-report</module>
    </modules>


</project>