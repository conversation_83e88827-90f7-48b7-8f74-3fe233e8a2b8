package com.youpin.clear.domain.aggregate.uu898;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 扣款操作结果
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeductionResult {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 总扣款金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 实际扣款金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 各子账户的扣款明细
     */
    private List<SubAccountDeductionDetail> deductionDetails;
    
    /**
     * 生成的流水记录ID列表
     */
    private List<Long> flowRecordIds;
    
    /**
     * 扩展信息
     */
    private String extInfo;
    
    /**
     * 子账户扣款明细
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubAccountDeductionDetail {
        
        /**
         * 子账户类型
         */
        private UserSubAccountType accountType;
        
        /**
         * 扣款前余额
         */
        private BigDecimal balanceBefore;
        
        /**
         * 扣款金额
         */
        private BigDecimal deductionAmount;
        
        /**
         * 扣款后余额
         */
        private BigDecimal balanceAfter;
        
        /**
         * 流水记录ID
         */
        private Long flowRecordId;
    }
}
