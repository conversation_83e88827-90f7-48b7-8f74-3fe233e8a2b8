package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAccountBalanceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long accountId;

    private String userAccountNo;

    private Long userId;

    private Integer accountType;

    private BigDecimal originalBalance = BigDecimal.ZERO;

    /**
     * 账户余额变动(不判断正负) - 正数代表增加余额，负数代表减少余额
     */
    private BigDecimal balanceChange = BigDecimal.ZERO;
    /**
     * 变动之后账户余额
     */
    private BigDecimal balanceAfter = BigDecimal.ZERO;


    private BigDecimal originalFrozenBalance = BigDecimal.ZERO;

    /**
     * 冻结账户余额变动(不判断正负) - 正数代表增加余额，负数代表减少余额
     */
    private BigDecimal frozenBalanceChange = BigDecimal.ZERO;

    /**
     * 变动之后冻结账户余额
     */
    private BigDecimal frozenBalanceAfter;

    private String ext;

    private Long lastAccountRecordId;


    public void calculateBalanceChangeAfter() {
        this.balanceAfter = this.originalBalance.add(this.balanceChange);
    }

    public void calculateFrozenBalanceChangeAfter() {
        this.frozenBalanceAfter = this.originalFrozenBalance.add(this.frozenBalanceChange);
    }

}