package com.youpin.clear.domain.servcie;

import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;

import java.util.List;

/**
 *
 */
public interface RocketMqService {


    void sendDynamicsMQMsg(String rocketMQUtilType, String topic, String jsonStr, Boolean sendAsync);

    void sendDynamicsMQMsg(String rocketMQUtilType, String topic, String tag, String jsonStr, Boolean sendAsync);

    void sendDynamicsMQFifoMsg(String rocketMQUtilType, String topic, String tag, String group, String jsonStr, Boolean sendAsync);

    /**
     * 账单
     */
    void sendBillMessage(List<AbstractUserAssetsRecordDTO> assetsRecordDTOList);

    /**
     * 易宝分账消息
     */
    void sendSeparateMessage(List<AbstractUserAssetsRecordDTO> assetsRecordDTOList);


    void sendAssetsMessage(List<ClearUserAssetsRecordDTO> assetsRecordDTOList);
    
    void sendPlatformUserAssetsRecordMq(List<PlatformAccountRecordDTO> platformAccountRecordDTOList,Boolean sendAsync);
}
