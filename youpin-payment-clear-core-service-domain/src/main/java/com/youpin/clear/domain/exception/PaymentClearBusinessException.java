package com.youpin.clear.domain.exception;


import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.youpin.clear.common.enums.ErrorCode;


public class PaymentClearBusinessException extends BusinessException {

    public PaymentClearBusinessException(ErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public PaymentClearBusinessException(ErrorCode code, String message) {
        super(code.getCode(), message);
    }

    public PaymentClearBusinessException(Integer code, String message) {
        super(code, message);
    }
}
