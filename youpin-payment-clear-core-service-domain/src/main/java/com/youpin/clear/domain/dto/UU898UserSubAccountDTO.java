package com.youpin.clear.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Table: uu898_user_sub_account
 *
 * @author: lizhuangzhuang
 */
@Data
public class UU898UserSubAccountDTO {
    /**
     *
     */
    private Long id;

    /**
     * 账户编号
     */
    private String accountNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 账户类型，见账户类型配置
     */
    private Integer accountType;

    /**
     * 账户状态，见AccountStatusEnum
     */
    private Integer status;

    /**
     * 币种，见币种配置
     */
    private String currency;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 最近动帐流水id
     */
    private Long lastFlowId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展信息
     */
    private String extInfo;
}