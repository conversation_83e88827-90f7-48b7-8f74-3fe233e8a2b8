package com.youpin.clear.domain.gateway;

import com.youpin.clear.client.request.AdjustApplyListQueryRequest;
import com.youpin.clear.domain.dto.AdjustmentApplyDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdjustmentApplyGateway {

    Long saveAdjustmentApply(AdjustmentApplyDTO adjustmentApplyDTO);

    AdjustmentApplyDTO queryById(Long id);

    Long queryCount(AdjustApplyListQueryRequest request);

    List<AdjustmentApplyDTO> queryList(AdjustApplyListQueryRequest request);

    List<AdjustmentApplyDTO> queryByIds(List<Long> ids);

    int updateAdjustmentApply(AdjustmentApplyDTO adjustmentApplyDTO);

    List<AdjustmentApplyDTO> queryByStatus(List<Integer> statusList,Long size);

    boolean existRepeatAdjustmentApply(String orderNo);

    int updateApplyStatus(List<Long> ids,Integer expectStatus,Integer currentStatus);
}
