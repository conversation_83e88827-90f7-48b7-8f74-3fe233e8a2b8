package com.youpin.clear.domain.gateway;

import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import com.youpin.clear.domain.dto.UpdateAccountBalanceDTO;
import com.youpin.clear.domain.dto.UserAccountInfoDTO;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.List;

public interface UserAccountGateway {

    void deleteByUserId(Long userId);

    List<AccountInfoMember> createUserAccount(Long userId);

    void createUserAccount(List<AccountInfoMember> accountInfoMemberList);

    List<AccountInfoMember> getAccountInfoMember(Long userId);

    AccountAggregate getAccountAggregate(Long userId);

    void updateMember(List<AccountInfoMember> updateMemberList);

    AccountAggregate syncUserAccount(Long userId);

    AccountAggregate syncUserAccountUpdate(Long userId);

    UserAccountInfoDTO queryUserAccountInfoDTO(Long userId);

    @Deprecated
    void updateBalanceAccountInfoMember(Long accountInfoId, Long userId, AccountTypeEnum accountTypeEnum, String userAccountNo, BigDecimal balanceBefore, BigDecimal balanceChange, String ext, Long lastAccountRecordId);

    @Deprecated
    void updateFrozenBalanceAccountInfoMember(Long accountInfoId, Long userId, AccountTypeEnum accountTypeEnum, String userAccountNo, BigDecimal originalFrozenBalance, BigDecimal balanceFrozenChange, String ext, Long lastAccountRecordId);

    void updateAccountBalanceList(@Nonnull List<UpdateAccountBalanceDTO> dto);

    void updateAccountBalance(UpdateAccountBalanceDTO dto);

    /**
     * 更新账户
     *
     * @param originAccountMember 原始账户信息
     * @param targetAccountMember 目标账户信息
     * @return 是否成功
     */
    boolean updateAccount(AccountInfoMember originAccountMember, AccountInfoMember targetAccountMember);

    /**
     * 补全账户
     *
     * @param needAddAccountInfoMemberList 需要添加的账户信息
     */
    void completionUserAccount(List<AccountInfoMember> needAddAccountInfoMemberList);

    AccountAggregate getAccountAggregatesMapByType(Long userId, List<Integer> accountTypeCodeList);
}
