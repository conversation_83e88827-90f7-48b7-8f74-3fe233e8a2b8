package com.youpin.clear.domain.aggregate.member;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountInfoMemberExtension implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 余额2是否发生过变化 默认false
     */
    @Builder.Default
    private Boolean balance2Changed = Boolean.FALSE;

}