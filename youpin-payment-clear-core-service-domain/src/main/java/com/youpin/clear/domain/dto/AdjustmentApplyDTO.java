package com.youpin.clear.domain.dto;

import com.uu898.youpin.commons.base.enums.Constant;
import com.youpin.clear.common.enums.AccountPayChannelEnum;
import com.youpin.clear.common.enums.AdjustmentDirectionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Table: adjustment_apply
 *
 * @author: lizhuangzhuang
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdjustmentApplyDTO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 支付订单号
     */
    private String payOrderNo;

    /**
     * 收入方用户id
     */
    private Long incomeUserId;

    /**
     * 增加调账余额
     */
    private BigDecimal incomeAmount;

    /**
     * 收入-资金明细类型
     */
    private Long incomeAssetType;

    /**
     * 收入方调整支付类型
     */
    private Integer incomePayChannel;

    /**
     * 收入-手续费
     */
    private BigDecimal incomeServiceFee;

    /**
     * 支出方用户id
     */
    private Long expenseUserId;

    /**
     * 减少调账余额
     */
    private BigDecimal expenseAmount;

    /**
     * 支出-资金明细类型
     */
    private Long expenseAssetType;

    /**
     * 支出方调整支付类型
     */
    private Integer expensePayChannel;

    /**
     * 支出-手续费
     */
    private BigDecimal expenseServiceFee;

    /**
     * 调账状态
     */
    private Integer status;

    /**
     * 申请人
     */
    private String applyBy;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 审核人
     */
    private String auditBy;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 调账来源
     */
    private Integer adjustSource;

    /**
     * 批次号
     */
    private String batchNo;


    private List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOList;

    private static final long serialVersionUID = 1L;


    public boolean existIncome() {
        return !Objects.equals(this.incomeUserId, Constant.CONSTANT_LONG_0);
    }

    public boolean existExpense() {
        return !Objects.equals(this.expenseUserId, Constant.CONSTANT_LONG_0);
    }

    public Long getOneUserId() {
        if (!Objects.equals(this.incomeUserId, Constant.CONSTANT_LONG_0)) {
            return this.incomeUserId;
        }
        return expenseUserId;
    }

    public List<Long> getRelatedUserIdList() {
        List<Long> userIdList = new ArrayList<>();
        if (this.existIncome()) {
            userIdList.add(this.incomeUserId);
        }

        if (this.existExpense()) {
            userIdList.add(this.expenseUserId);
        }
        return userIdList.stream().distinct().collect(Collectors.toList());
    }

    public boolean balanceChange(Integer direction) {
        if (direction == AdjustmentDirectionEnum.INCOME.getCode()) {
            return incomePayChannel.equals(AccountPayChannelEnum.BALANCE.getCode());
        } else if (direction == AdjustmentDirectionEnum.EXPENSE.getCode()) {
            return expensePayChannel.equals(AccountPayChannelEnum.BALANCE.getCode());
        }
        return false;
    }

    /**
     *
     */
    public void addUU898UserAssetsRecordDTO(UU898UserAssetsRecordDTO dto) {
        if (null == uu898UserAssetsRecordDTOList || uu898UserAssetsRecordDTOList.isEmpty()) {
            uu898UserAssetsRecordDTOList = new ArrayList<>();
        }
        uu898UserAssetsRecordDTOList.add(dto);
    }
}