package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.aggregate.uu898.UserSubAccountFlowRecord;

import java.util.List;

/**
 * 用户子账户流水记录网关接口
 * <AUTHOR>
 */
public interface UserSubAccountFlowRecordGateway {
    
    /**
     * 插入流水记录
     */
    Long insert(UserSubAccountFlowRecord record);
    
    /**
     * 批量插入流水记录
     */
    void batchInsert(List<UserSubAccountFlowRecord> records);
    
    /**
     * 根据ID查询流水记录
     */
    UserSubAccountFlowRecord getById(Long id);
    
    /**
     * 根据用户ID查询流水记录
     */
    List<UserSubAccountFlowRecord> getByUserId(Long userId);
    
    /**
     * 根据订单号查询流水记录
     */
    List<UserSubAccountFlowRecord> getByOrderNo(String orderNo);
    
    /**
     * 根据支付单号查询流水记录
     */
    List<UserSubAccountFlowRecord> getByPayOrderNo(String payOrderNo);
}
