package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountAssetsTypeRelateDTO implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 资金code
     */
    private Integer assetsCode;

    /**
     * 关系类型
     */
    private String relateType;

    /**
     * 关系映射
     */
    private String relateCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}