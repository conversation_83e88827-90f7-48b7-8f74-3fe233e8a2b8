package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.UU898UserSubAccountDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountFlowRecordDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UU898UserSubAccountGateway {

    /**
     * 添加子账户
     *
     * @param userIds            userIds
     * @param subAccountTypeList 子账户类型
     */
    void addSubAccount(List<Long> userIds, List<Integer> subAccountTypeList);

    /**
     * 获取用户子账户
     *
     * @param userId userId
     * @return 子账户map
     */
    Map<Integer, UU898UserSubAccountDTO> getUserSubAccountMap(Long userId);

    /**
     * 保存子账户资金明细
     *
     * @param recordDTO 子账户明细
     */
    void saveSubAccountRecord(UU898UserSubAccountFlowRecordDTO recordDTO);

    /**
     * 查询子流水
     *
     * @param subAccountDTO   子账户信息
     * @param orderNo         订单编号
     * @param payOrderNo      支付单号
     * @param journalTypeList journalTypeList
     * @return 返回
     */
    List<UU898UserSubAccountFlowRecordDTO> listSubAccountRecord(UU898UserSubAccountDTO subAccountDTO, String orderNo, String payOrderNo, List<Integer> journalTypeList);

    /**
     * 查询子流水
     *
     * @param subAccountDTO   子账户信息
     * @param journalTypeList journalTypeList
     * @param startCreateTime startTime
     * @param endCreateTime   endTime
     * @return list
     */
    List<UU898UserSubAccountFlowRecordDTO> listSubAccountRecord(UU898UserSubAccountDTO subAccountDTO, List<Integer> journalTypeList, LocalDateTime startCreateTime, LocalDateTime endCreateTime);

    /**
     * 更新子账户
     *
     * @param subAccountDTO 子账户dto
     * @param lastRecord    最后一条流水
     */
    void updateSubAccount(UU898UserSubAccountDTO subAccountDTO, UU898UserSubAccountFlowRecordDTO lastRecord);
}
