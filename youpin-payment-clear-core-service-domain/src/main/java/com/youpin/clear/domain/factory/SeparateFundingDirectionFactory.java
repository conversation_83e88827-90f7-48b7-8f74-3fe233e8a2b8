package com.youpin.clear.domain.factory;

import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.domain.process.SeparateFundingDirectionProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金方向抽象处理器工厂
 */
@Component
public class SeparateFundingDirectionFactory {

    @Resource
    List<SeparateFundingDirectionProcessor> directionProcessorProcessorList;

    Map<DirectionEnum, SeparateFundingDirectionProcessor> maps = new HashMap<>();

    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(directionProcessorProcessorList)) {
            directionProcessorProcessorList.forEach(processor -> {
                List<DirectionEnum> supportTypeList = processor.support();
                supportTypeList.forEach(subBusType -> maps.put(subBusType, processor));
            });
        }
    }

    public SeparateFundingDirectionProcessor getProcessor(DirectionEnum typeEnum) {
        if (typeEnum == null) {
            return null;
        }
        return maps.getOrDefault(typeEnum, null);
    }
}
