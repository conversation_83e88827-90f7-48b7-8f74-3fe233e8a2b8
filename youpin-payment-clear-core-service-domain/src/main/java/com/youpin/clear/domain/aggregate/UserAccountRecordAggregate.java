package com.youpin.clear.domain.aggregate;

import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.UserAssetsTypeEnum;
import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAccountRecordAggregate implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属用户的唯一标识
     */
    private Long userId;
    /**
     * 分账方向
     */
    private FundingDirectionEnum directionEnum;

    /**
     * 资金类型ID枚举
     */
    private UserAssetsTypeEnum userAssetsTypeEnum;

    /**
     * 渠道
     */
    private DoNetPayChannelEnum netPayChannelEnum;
    /**
     * 状态
     */
    private NetStatusEnum netStatusEnum;

    /**
     * 分账记录列表
     */
    private List<UserSubAccountRecordMember> userSubAccountRecordMemberList;

    /**
     * 是否分账成功 默认 false
     */
    private Boolean isSuccess = Boolean.FALSE;


    /**
     * 是否是参考资金类型数据 默认 false
     */
    private Boolean isReference = Boolean.FALSE;


    /**
     * 入参关联加
     *
     */
    private List<Integer> associatedAdditionList;

    /**
     * DB关联加
     */
    private List<Integer> dbAssociatedAdditionList;


    /**
     * 入参关联减
     */
    private List<Integer> associatedSubtractionList;

    /**
     * DB关联减
     */
    private List<Integer> dbAssociatedSubtractionList;


}
