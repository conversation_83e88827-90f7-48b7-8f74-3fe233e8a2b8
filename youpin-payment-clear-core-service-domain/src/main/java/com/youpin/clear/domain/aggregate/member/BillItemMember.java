package com.youpin.clear.domain.aggregate.member;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.enums.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillItemMember implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属用户的唯一标识
     */
    private Long userId;

    /**
     * 资金类型ID枚举
     */
    private Integer typeId;

    /**
     * 资金名字
     */
    private String assetsName;


    /**
     * 分账方向
     */
    private DirectionEnum directionEnum;


    /**
     * 资金类型
     */
    private AssetsTypeEnum assetsTypeEnum;

    /**
     * (无符号)金额
     */
    @Builder.Default
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 原始金额
     */
    @Builder.Default
    private BigDecimal originalAmount = BigDecimal.ZERO;

    /**
     * 资金关系
     */
    private List<Integer> typeRelationList;

    /**
     * 支付渠道：0.余额，1.微信，2.支付宝，3.积分，4.额度，5押金卡，6临时额度，7固定额度，8求购余额，9支付宝原路退还
     */
    private DoNetPayChannelEnum payChannelEnum;

    /**
     * 三方渠道 才用到这个字段
     * 收单类型 0,默认  1(自有) 2(监管)
     */
    @Builder.Default
    private CollectTypeEnum collectTypeEnum = CollectTypeEnum.DEFAULT;

    /**
     * 原资金明细id
     */
    private Long userAssetsRecordId;
    /**
     * 当前资金明细记录的唯一单号
     */
    private String treadNo;

    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 状态，0.失败，1.成功，2.进行中
     */
    private NetStatusEnum netStatusEnum;


    /**
     * 是否租赁订单
     */
    @Builder.Default
    private Boolean isLeaseOrder = Boolean.FALSE;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 入参关联加
     */
    private List<Integer> associatedAdditionList;

    /**
     * DB关联加
     */
    private List<Integer> dbAssociatedAdditionList;

    /**
     * 特殊关系
     */
    private List<Integer> specialRelationList;

    /**
     * 金额类型
     */
    private AmountTypeEnum amountTypeEnum;

    /**
     * 扩展
     */
    private BillItemMemberExtension billItemMemberExtension;


    public BillItemMemberExtension getBillItemMemberExtension() {
        if (null == billItemMemberExtension) {
            if (StringUtils.isNotBlank(ext)) {
                return JSON.parseObject(ext, BillItemMemberExtension.class);
            }
        }
        return this.billItemMemberExtension;
    }

}
