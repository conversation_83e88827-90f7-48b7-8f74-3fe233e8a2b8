package com.youpin.clear.domain.aggregate;

import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountAggregate {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 账户明细
     */
    private Map<AccountTypeEnum, AccountInfoMember> accountInfoMemberMap;
    
    /**
     * 更据类型获取账户信息
     */
    public AccountInfoMember getAccountByType(AccountTypeEnum typeEnum) {
        if (accountInfoMemberMap == null || accountInfoMemberMap.isEmpty()) {
            return null;
        }
        return accountInfoMemberMap.getOrDefault(typeEnum, null);
    }
}
