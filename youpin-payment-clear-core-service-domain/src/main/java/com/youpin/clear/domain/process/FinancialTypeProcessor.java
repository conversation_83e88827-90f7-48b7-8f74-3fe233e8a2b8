package com.youpin.clear.domain.process;

import com.youpin.clear.domain.dto.FinancialProcessorDTO;

public interface FinancialTypeProcessor {


    /**
     * 支付进行中
     */
    void payIng(FinancialProcessorDTO dto);

    /**
     * 支付成功
     */
    void paySuccess(FinancialProcessorDTO dto);

    /**
     * 支付失败
     */
    void payFail(FinancialProcessorDTO dto);


    /**
     * 退款进行中
     */
    void refundIng(FinancialProcessorDTO dto);

    /**
     * 退款成功
     */
    void refundSuccess(FinancialProcessorDTO dto);

    /**
     * 退款失败
     */
    void refundFail(FinancialProcessorDTO dto);

    /**
     * 结算
     */
    void settlement(FinancialProcessorDTO dto);

    /**
     * 特殊资金处理
     */
    void specialSettlement(FinancialProcessorDTO dto);


    /**
     * 提现成功
     */
    void withdrawalSuccess(FinancialProcessorDTO dto);

    /**
     * 提现失败
     */
    void withdrawalFail(FinancialProcessorDTO dto);

    /**
     * 提现进行中
     */
    void withdrawalIng(FinancialProcessorDTO dto);

}
