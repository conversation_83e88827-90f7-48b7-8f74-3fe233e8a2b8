package com.youpin.clear.domain.process;

import com.youpin.clear.domain.aggregate.uu898.DeductionResult;
import com.youpin.clear.domain.aggregate.uu898.DeductionStrategyConfig;
import com.youpin.clear.domain.aggregate.uu898.DeductionStrategyType;
import com.youpin.clear.domain.aggregate.uu898.UserAccount;

import java.math.BigDecimal;
import java.util.List;

/**
 * 扣款策略接口
 * <AUTHOR>
 */
public interface DeductionStrategy {
    
    /**
     * 支持的策略类型
     */
    List<DeductionStrategyType> supportedTypes();
    
    /**
     * 执行扣款操作
     * 
     * @param userAccount 用户账户信息
     * @param amount 扣款金额
     * @param config 策略配置
     * @param businessInfo 业务信息（订单号、支付单号等）
     * @return 扣款结果
     */
    DeductionResult executeDeduction(UserAccount userAccount, 
                                   BigDecimal amount, 
                                   DeductionStrategyConfig config,
                                   DeductionBusinessInfo businessInfo);
    
    /**
     * 预检查是否可以执行扣款
     * 
     * @param userAccount 用户账户信息
     * @param amount 扣款金额
     * @param config 策略配置
     * @return 检查结果
     */
    DeductionCheckResult preCheck(UserAccount userAccount, 
                                BigDecimal amount, 
                                DeductionStrategyConfig config);
    
    /**
     * 计算扣款方案（不实际执行）
     * 
     * @param userAccount 用户账户信息
     * @param amount 扣款金额
     * @param config 策略配置
     * @return 扣款方案
     */
    DeductionResult calculateDeductionPlan(UserAccount userAccount, 
                                         BigDecimal amount, 
                                         DeductionStrategyConfig config);
}
