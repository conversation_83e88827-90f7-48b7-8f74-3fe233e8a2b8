package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSubAccountInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 冻结金额
     */
    private BigDecimal blockBalance = BigDecimal.ZERO;

    /**
     * 可交易金额
     */
    private BigDecimal traderBalance = BigDecimal.ZERO;
    
    /**
     * 可提现金额
     */
    private BigDecimal withdrawBalance = BigDecimal.ZERO;


}
