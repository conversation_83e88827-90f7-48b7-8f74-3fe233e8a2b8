package com.youpin.clear.domain.process;

import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.domain.aggregate.member.BillMember;

import java.util.List;

/**
 * 资金方向抽象处理器
 */
public interface SeparateFundingDirectionProcessor {

    List<DirectionEnum> support();

//    void process(BillMember billMember, List<BillMember> billMemberList);

    void process(BillMember billMember, List<BillMember> billMemberList, List<BillMember> billMemberListIsReference);

    void process(List<BillMember> billMemberList);


}
