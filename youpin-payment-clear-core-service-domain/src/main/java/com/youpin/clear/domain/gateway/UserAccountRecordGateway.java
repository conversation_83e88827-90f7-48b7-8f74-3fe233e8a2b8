package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface UserAccountRecordGateway {
    void deleteByUserId(Long userId);

    Long userAccountRecordSave(UserAccountRecordMember userAccountRecordMember);

    Long userAccountRecordUpdateDO(UserAccountRecordMember userAccountRecordMember);


    void userAccountRecordUpdateStatus(UserAccountRecordMember userAccountRecordMember);

    @Deprecated
    void userAccountRecordMemberTransactional(List<UserAccountRecordMember> userAccountRecordMemberList, Map<Long, AccountAggregate> accountAggregateMap);

    List<UserAccountRecordMember> getUserAccountRecordByUserIdAndUserAssetsRecordId(Long userId, Long userAssetsRecordId, Integer accountType);

    Boolean countUserAccountRecord(Long userId,
                                   Long userAssetsRecordId,
                                   BigDecimal balanceChange,
                                   Integer accountType);

    List<UserAccountRecordMember> getUserAccountRecordByOrderNoOrPayOrderNo(String orderNo, String payOrderNo);

    BigDecimal sumBalanceByIdAndAccountType(Long userId, Long startId, Long entId, Integer accountType);

    UserAccountRecordMember getByUserIdAndId(Long userId, Long id);

    Long minIdByUserId(Long userId, Integer accountType);

    Long minUserAssetsRecordId(Long userId);


    void updateUserAccountRecord(UserAccountRecordMember member);

    List<UserAccountRecordMember> selectUserAccountRecordById(Long userId, Long minUserAssetsRecordId, Long maxUserAssetsRecordId);

    /**
     * 查找最近一次 余额二的变动
     */
    UserAccountRecordMember selectLastUserAccountRecordByUserIdAndAccountType(Long userId,Long userAssetsRecordId, Integer accountType);

    void insertSelective(UserAccountRecordMember userAccountRecordMember);
}
