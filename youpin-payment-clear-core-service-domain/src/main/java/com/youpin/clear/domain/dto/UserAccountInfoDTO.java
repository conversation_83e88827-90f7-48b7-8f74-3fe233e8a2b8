package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    private BigDecimal balance = BigDecimal.ZERO;

    /**
     * 可用余额
     */
    private BigDecimal balance1 = BigDecimal.ZERO;

    private BigDecimal balance2 = BigDecimal.ZERO;
    /**
     * 余额1冻结
     */
    private BigDecimal balanceBlock1 = BigDecimal.ZERO;
    /**
     * 余额2冻结
     */
    private BigDecimal balanceBlock2 = BigDecimal.ZERO;

    /**
     * 求购
     */
    private BigDecimal purchaseBalance = BigDecimal.ZERO;

    /**
     * 求购充值余额1
     */
    private BigDecimal purchaseBalanceRecharge1 = BigDecimal.ZERO;
    /**
     * 求购充值余额2
     */
    private BigDecimal purchaseBalanceRecharge2 = BigDecimal.ZERO;
    /**
     * 求购转入余额1
     */
    private BigDecimal purchaseBalanceTransfer1 = BigDecimal.ZERO;
    /**
     * 求购转入余额2
     */
    private BigDecimal purchaseBalanceTransfer2 = BigDecimal.ZERO;

    /**
     * 是否展示 余额2
     * 默认不展示 false  true 展示
     */
    private Boolean showBalance2 = false;


    /**
     * 余额1可提现金额
     */
    private BigDecimal balance1Withdraw = BigDecimal.ZERO;
    /**
     * 余额2可提现金额
     */
    private BigDecimal balance2Withdraw = BigDecimal.ZERO;



}
