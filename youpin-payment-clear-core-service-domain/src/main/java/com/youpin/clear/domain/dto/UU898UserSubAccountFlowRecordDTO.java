package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Table: uu898_user_sub_account_flow_record
 *
 * @author: lizhuangzhuang
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UU898UserSubAccountFlowRecordDTO {
    /**
     *
     */
    private Long id;

    /**
     * 账户编号
     */
    private String accountNo;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 流水类型，见流水类型配置表
     */
    private Integer journalType;

    /**
     * 变更前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 余额变更值
     */
    private BigDecimal balanceChange;

    /**
     * 变更后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展信息
     */
    private String extInfo;


    @Data
    public static class ExtendInfo {

        /**
         * 主流水thisMoney
         */
        private BigDecimal assetRecordThisMoney;
    }

}