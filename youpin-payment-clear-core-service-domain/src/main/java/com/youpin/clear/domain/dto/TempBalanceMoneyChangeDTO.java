package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TempBalanceMoneyChangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigDecimal balanceBefore = BigDecimal.ZERO;
    private BigDecimal balanceChange = BigDecimal.ZERO;
    private BigDecimal balanceAfter = BigDecimal.ZERO;

    private BigDecimal frozenBalanceBefore = BigDecimal.ZERO;
    private BigDecimal frozenBalanceChange = BigDecimal.ZERO;
    private BigDecimal frozenBalanceAfter = BigDecimal.ZERO;

    private BigDecimal purchaseMoney = BigDecimal.ZERO;
    private BigDecimal thisPurchaseMoney = BigDecimal.ZERO;
    private BigDecimal afterPurchaseMoney = BigDecimal.ZERO;


}
