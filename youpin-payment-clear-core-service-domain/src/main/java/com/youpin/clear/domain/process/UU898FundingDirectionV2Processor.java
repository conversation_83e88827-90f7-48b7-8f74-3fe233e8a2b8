package com.youpin.clear.domain.process;

import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.domain.dto.AbstractUserAssetsRecordDTO;
import com.youpin.clear.domain.dto.UserAssetsInfoDTO;
import com.youpin.clear.domain.dto.UU898UserSubAccountDTO;

import java.util.List;

public interface UU898FundingDirectionV2Processor {

    List<FundingDirectionEnum> support();

    void process(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO);

    void processAssets(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO);

    /**
     * 校验
     */
    void beforeDataIdempotenceCheck(AbstractUserAssetsRecordDTO abstractUserAssetsRecordDTO, UserAssetsInfoDTO userAssetsInfoDTO);
}
