package com.youpin.clear.domain.factory;

import com.youpin.clear.domain.aggregate.uu898.DeductionStrategyType;
import com.youpin.clear.domain.process.DeductionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 扣款策略工厂
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeductionStrategyFactory {
    
    @Autowired
    private List<DeductionStrategy> deductionStrategies;
    
    private Map<DeductionStrategyType, DeductionStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(deductionStrategies)) {
            deductionStrategies.forEach(strategy -> {
                List<DeductionStrategyType> supportedTypes = strategy.supportedTypes();
                supportedTypes.forEach(type -> {
                    if (strategyMap.containsKey(type)) {
                        log.warn("扣款策略类型 {} 存在多个实现，将被覆盖", type);
                    }
                    strategyMap.put(type, strategy);
                    log.info("注册扣款策略：{} -> {}", type, strategy.getClass().getSimpleName());
                });
            });
        }
        log.info("扣款策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());
    }
    
    /**
     * 根据策略类型获取对应的策略实现
     */
    public DeductionStrategy getStrategy(DeductionStrategyType strategyType) {
        DeductionStrategy strategy = strategyMap.get(strategyType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的扣款策略类型：" + strategyType);
        }
        return strategy;
    }
    
    /**
     * 检查是否支持指定的策略类型
     */
    public boolean isSupported(DeductionStrategyType strategyType) {
        return strategyMap.containsKey(strategyType);
    }
    
    /**
     * 获取所有支持的策略类型
     */
    public Map<DeductionStrategyType, DeductionStrategy> getAllStrategies() {
        return new HashMap<>(strategyMap);
    }
}
