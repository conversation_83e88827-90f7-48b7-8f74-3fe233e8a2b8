package com.youpin.clear.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserAssetsRecordMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 资金明细主键ID
     */
    @JsonProperty("Id")
    @JSONField(name = "Id")
    private Long id;
    /**
     * 所属用户的唯一标识
     */
    @JsonProperty("UserId")
    @JSONField(name = "UserId")
    private Long userId;
    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    @JsonProperty("TypeId")
    @JSONField(name = "TypeId")
    private Integer typeId;
    /**
     * 当前资金明细记录的唯一单号
     */
    @JsonProperty("TreadNo")
    @JSONField(name = "TreadNo")
    private String treadNo;
    /**
     * 变化资产类型，1.账户余额，10.积分
     */
    @JsonProperty("AssetType")
    @JSONField(name = "AssetType")
    private Integer assetType;
    /**
     * 变动之前的账户余额，含义随AssetType字段而变化
     */
    @JsonProperty("Money")
    @JSONField(name = "Money")
    private BigDecimal money;
    /**
     * 本次变动数额，含义随AssetType字段而变化
     */
    @JsonProperty("ThisMoney")
    @JSONField(name = "ThisMoney")
    private BigDecimal thisMoney;
    /**
     * 变动后的账户余额，含义随AssetType字段而变化
     */
    @JsonProperty("AfterMoney")
    @JSONField(name = "AfterMoney")
    private BigDecimal afterMoney;
    /**
     * 服务费金额
     */
    @JsonProperty("ChargeMoney")
    @JSONField(name = "ChargeMoney")
    private BigDecimal chargeMoney;
    /**
     * 变动前的冻结金额
     */
    @JsonProperty("BlockMoney")
    @JSONField(name = "BlockMoney")
    private BigDecimal blockMoney;
    /**
     * 本次冻结金额
     */
    @JsonProperty("ThisBlockMoney")
    @JSONField(name = "ThisBlockMoney")
    private BigDecimal thisBlockMoney;
    /**
     * 变动后冻结金额
     */
    @JsonProperty("AfterBlockMoney")
    @JSONField(name = "AfterBlockMoney")
    private BigDecimal afterBlockMoney;
    /**
     * 变动前的求购金额
     */
    @JsonProperty("PurchaseMoney")
    @JSONField(name = "PurchaseMoney")
    private BigDecimal purchaseMoney;
    /**
     * 本次变动求购金额
     */
    @JsonProperty("ThisPurchaseMoney")
    @JSONField(name = "ThisPurchaseMoney")
    private BigDecimal thisPurchaseMoney;
    /**
     * 变动后的求购金额
     */
    @JsonProperty("AfterPurchaseMoney")
    @JSONField(name = "AfterPurchaseMoney")
    private BigDecimal afterPurchaseMoney;
    /**
     * 关联支付接口返回的流水号
     */
    @JsonProperty("SerialNo")
    @JSONField(name = "SerialNo")
    private String serialNo;
    /**
     * 订单号
     */
    @JsonProperty("OrderNo")
    @JSONField(name = "OrderNo")
    private String orderNo;
    /**
     * 支付单号
     */
    @JsonProperty("PayOrderNo")
    @JSONField(name = "PayOrderNo")
    private String payOrderNo;
    /**
     * 备注信息
     */
    @JsonProperty("Remark")
    @JSONField(name = "Remark")
    private String remark;
    /**
     * 添加时间
     */
    @JsonProperty("AddTime")
    @JSONField(name = "AddTime")
    private String addTime;
    /**
     * 完成时间
     */
    @JsonProperty("CompleteTime")
    @JSONField(name = "CompleteTime")
    private String completeTime;
    /**
     * 特性，二进制保存，从低位至高位依次为：余额是否变动
     */
    @JsonProperty("Attr")
    @JSONField(name = "Attr")
    private Integer attr;
    /**
     * 状态，0.失败，1.成功，2.进行中
     */
    @JsonProperty("Status")
    @JSONField(name = "Status")
    private Integer status;
    /**
     * 支付渠道：0.余额，1.微信，2.支付宝，3.积分，4额度，5押金卡，6临时额度，7固定额度，8求购余额，9支付宝原路退还
     */
    @JsonProperty("PayChannel")
    @JSONField(name = "PayChannel")
    private Integer payChannel;
    /**
     * 提现支付宝账号
     */
    @JsonProperty("AccountName")
    @JSONField(name = "AccountName")
    private String accountName;
    /**
     * 支付等待时间
     */
    @JsonProperty("PayWaitExpireTime")
    @JSONField(name = "PayWaitExpireTime")
    private String payWaitExpireTime;
    /**
     * 生成来源信息
     */
    @JsonProperty("GenSource")
    @JSONField(name = "GenSource")
    private String genSource;
    /**
     * 收单类型 0,默认  1(自有) 2(监管)
     */
    @JsonProperty("CollectType")
    @JSONField(name = "CollectType")
    private Integer collectType;
    /**
     * 是否是租赁订单
     */
    @JsonProperty("IsLeaseOrder")
    @JSONField(name = "IsLeaseOrder")
    private Boolean isLeaseOrder;

    /**
     * 钱包2转出金额
     */
    @JsonProperty("Balance2TransferAmount")
    @JSONField(name = "Balance2TransferAmount")
    private BigDecimal balance2TransferAmount;

    /***
     * 商户id
     */
    private Integer merchantId;

    /**
     * 业务类型：出租，出售
     */
    private Integer businessType;

    /**
     * 子业务类型
     */
    private Integer subBusType;

    /**
     * 缓冲记账的ID
     */
    private Long platformAccountRecordId;

    /**
     * 是否缓冲记账
     */
    private Boolean accountBufferBookkeeping = false;

    /**
     * 资金涉及的 分账扩展信息 需要透传给分账账单
     */
    private AssetsRecordAccountBillExtInfo assetsRecordAccountBillExtInfo;

    /**
     * 子账户明细map
     */
    private List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList;


}