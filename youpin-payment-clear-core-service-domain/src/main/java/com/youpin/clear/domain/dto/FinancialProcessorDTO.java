package com.youpin.clear.domain.dto;

import com.youpin.clear.common.constant.ClearConstants;
import com.youpin.clear.common.enums.FinancialTypeEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.youpin.clear.common.constant.ClearConstants.NOT_SEND_BILL_ACCOUNT_MQ;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FinancialProcessorDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资金类型
     */
    private FinancialTypeEnum financialTypeEnum;

    /**
     * 子业务类型
     */
    private SubBusTypeFrontEnum subBusTypeFrontEnum;

    /**
     * 业务类型：出租，出售
     */
    private Integer businessType;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 流水号
     */
    private String serialNo;
    /**
     * 资金状态
     */
    private NetStatusEnum netStatusEnum;

    /***
     * 商户id
     */
    private Integer merchantId;

    /***
     * 币种
     */
    private String currency = "CNY";

    /**
     * 收单类型 0,默认  1(自有) 2(监管)
     */
    private Integer collectType;

    /**
     * 是否是租赁订单
     */
    private Boolean isLeaseOrder = false;


    /**
     * 渠道是否成功（原路退款失败时有用）
     */
    private Boolean isChannelSuccess = true;


    /**
     * 钱包2转出金额(目前只适用 求购提现 转账是混合使用)
     */
    private BigDecimal balance2TransferAmount = BigDecimal.ZERO;

    /**
     * 业务侧:资金明细
     */
    private List<FinancialProcessorAssetInfoDTO> assetInfoDTOList;

    /**
     * 关联资金记录
     */
    private List<UU898UserAssetsRecordDTO> userAssetsRecordDTOLinkList;

    /**
     * 账务侧:资金明细数据
     */
    private List<AbstractUserAssetsRecordDTO> abstractUserAssetsRecordDTOList;

    /**
     * 是否开启平台户缓冲记账
     * 默认不开启 false true 开启
     */
    private Boolean platformAccountBuffer = false;

    /**
     * 平台户缓冲流水
     */
    private List<PlatformAccountRecordDTO> platformAccountRecordDTOList;


    /**
     * 资金是否硬处理 默认不硬处理
     */
    private Boolean hardProcess = false;

    /**
     * 结算账户类型 0.1
     */
    private Integer settleAccountType;


//    /**
//     * 业务自定义排序集合
//     */
//    private  List<Integer> customOrderTypeIdList = new ArrayList<>();

    private BigDecimal settleFrozeMoney;

    private BigDecimal privateSettleFrozeMoney;

    /**
     * 是否使用新资金分支
     * true：使用
     * false：不使用
     */
    private Boolean useNewAssetBranch;

    /**
     * 是否需要查询关联数据
     */
    public boolean isNeedQueryLinkData() {
        return true;
    }

    /**
     * 涉及的用户id
     */
    public List<Long> getSupportUserIdList() {
        if (null == abstractUserAssetsRecordDTOList || abstractUserAssetsRecordDTOList.isEmpty()) {
            return Collections.emptyList();
        }
        return abstractUserAssetsRecordDTOList.stream().map(AbstractUserAssetsRecordDTO::getUserId).distinct().collect(Collectors.toList());
    }

    /**
     * 获取需要查询关联数据的用户id (不包含平台户)
     * 防止全量用户 都是tmd的缓冲记账用户id 测试一下 如果全是缓冲用户 锁 和 不锁的逻辑 是否都可以
     */
    public List<Long> getSupportUserIdListNotPlatform(List<Long> allUserIds) {
        // 如果全量用户大于1，则需要判断是否存在需要缓存记账的用户id
        if (null != allUserIds && allUserIds.size() > ClearConstants.CONSTANT_INTEGER_1 && Boolean.TRUE.equals(platformAccountBuffer)) {
            List<Long> accountBufferBookkeepingUserIdList = abstractUserAssetsRecordDTOList.stream().filter(AbstractUserAssetsRecordDTO::checkAccountBufferBookkeeping).map(AbstractUserAssetsRecordDTO::getUserId).distinct().collect(Collectors.toList());
            if (allUserIds.size() > accountBufferBookkeepingUserIdList.size()) {
                return allUserIds.stream().filter(e -> !accountBufferBookkeepingUserIdList.contains(e)).collect(Collectors.toList());
            }
        }
        return allUserIds;
    }


    /**
     *
     */
    public void addAbstractUserAssetsRecordDTO(List<AbstractUserAssetsRecordDTO> dtoList) {
        if (null == abstractUserAssetsRecordDTOList || abstractUserAssetsRecordDTOList.isEmpty()) {
            abstractUserAssetsRecordDTOList = new ArrayList<>();
        }
        if (null == dtoList || dtoList.isEmpty()) {
            return;
        }
        abstractUserAssetsRecordDTOList.addAll(dtoList);
    }

    /**
     *
     */
    public void addPlatformAccountRecordDTOList(PlatformAccountRecordDTO dto) {
        if (null == platformAccountRecordDTOList || platformAccountRecordDTOList.isEmpty()) {
            platformAccountRecordDTOList = new ArrayList<>();
        }
        if (null == dto) {
            return;
        }
        platformAccountRecordDTOList.add(dto);
    }

    /**
     * 判断 abstractUserAssetsRecordDTOList 是否为空
     */
    public boolean isAbstractUserAssetsRecordDTOListEmpty() {
        return null == abstractUserAssetsRecordDTOList || abstractUserAssetsRecordDTOList.isEmpty();
    }

    /**
     * 判断 userAssetsRecordDTOLinkList 是否为空
     */
    public boolean isUserAssetsRecordDTOLinkListEmpty() {
        return null == userAssetsRecordDTOLinkList || userAssetsRecordDTOLinkList.isEmpty();
    }

    /**
     * 是否发送账单
     *
     * @return boolean true 发送 false 不发送
     */
    public boolean isSendBill() {
        return !NOT_SEND_BILL_ACCOUNT_MQ.contains(subBusTypeFrontEnum);
    }

    /**
     * 是否开启平台户缓冲记账
     */
    public boolean checkPlatformAccountBuffer() {
        return Boolean.TRUE.equals(platformAccountBuffer);
    }


}
