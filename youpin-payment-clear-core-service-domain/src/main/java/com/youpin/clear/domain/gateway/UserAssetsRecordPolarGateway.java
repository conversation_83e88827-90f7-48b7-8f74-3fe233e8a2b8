package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface UserAssetsRecordPolarGateway {

    List<ClearUserAssetsRecordDTO> selectPageByUserId(Long userId, Long pageIndex, Integer pageSize, List<Integer> typeIdList, Long maxUserAssetsRecordId, LocalDateTime startTime, LocalDateTime endTime);
}
