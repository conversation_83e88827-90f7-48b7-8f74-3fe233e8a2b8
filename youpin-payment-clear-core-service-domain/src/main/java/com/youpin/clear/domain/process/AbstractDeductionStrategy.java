package com.youpin.clear.domain.process;

import com.youpin.clear.domain.aggregate.uu898.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 抽象扣款策略基类
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractDeductionStrategy implements DeductionStrategy {
    
    @Override
    public DeductionCheckResult preCheck(UserAccount userAccount, 
                                       BigDecimal amount, 
                                       DeductionStrategyConfig config) {
        
        // 基础参数校验
        if (userAccount == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return DeductionCheckResult.builder()
                    .passed(false)
                    .errorCode("INVALID_PARAMS")
                    .errorMessage("参数无效")
                    .build();
        }
        
        // 检查金额限制
        if (config.getMinDeductionAmount() != null && 
            amount.compareTo(config.getMinDeductionAmount()) < 0) {
            return DeductionCheckResult.builder()
                    .passed(false)
                    .errorCode("AMOUNT_TOO_SMALL")
                    .errorMessage("扣款金额小于最小限制")
                    .build();
        }
        
        if (config.getMaxDeductionAmount() != null && 
            amount.compareTo(config.getMaxDeductionAmount()) > 0) {
            return DeductionCheckResult.builder()
                    .passed(false)
                    .errorCode("AMOUNT_TOO_LARGE")
                    .errorMessage("扣款金额超过最大限制")
                    .build();
        }
        
        // 检查账户余额
        BigDecimal totalAvailableBalance = calculateTotalAvailableBalance(userAccount, config);
        
        if (amount.compareTo(totalAvailableBalance) > 0 && !config.isAllowPartialDeduction()) {
            return DeductionCheckResult.builder()
                    .passed(false)
                    .errorCode("INSUFFICIENT_BALANCE")
                    .errorMessage("余额不足")
                    .maxDeductibleAmount(totalAvailableBalance)
                    .build();
        }
        
        return DeductionCheckResult.builder()
                .passed(true)
                .maxDeductibleAmount(totalAvailableBalance)
                .build();
    }
    
    @Override
    public DeductionResult calculateDeductionPlan(UserAccount userAccount, 
                                                 BigDecimal amount, 
                                                 DeductionStrategyConfig config) {
        
        List<DeductionResult.SubAccountDeductionDetail> details = new ArrayList<>();
        BigDecimal remainingAmount = amount;
        
        // 按优先级排序账户
        List<DeductionStrategyConfig.AccountPriorityConfig> sortedPriorities = 
                config.getAccountPriorities().stream()
                        .sorted(Comparator.comparingInt(DeductionStrategyConfig.AccountPriorityConfig::getPriority))
                        .collect(Collectors.toList());
        
        for (DeductionStrategyConfig.AccountPriorityConfig priorityConfig : sortedPriorities) {
            if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            
            UserSubAccount subAccount = userAccount.getSubAccountMap().get(priorityConfig.getAccountType());
            if (subAccount == null) {
                continue;
            }
            
            BigDecimal availableBalance = calculateAvailableBalance(subAccount, priorityConfig);
            if (availableBalance.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            
            BigDecimal deductionAmount = calculateDeductionAmount(remainingAmount, availableBalance, priorityConfig);
            
            if (deductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                details.add(DeductionResult.SubAccountDeductionDetail.builder()
                        .accountType(priorityConfig.getAccountType())
                        .balanceBefore(subAccount.getBalance())
                        .deductionAmount(deductionAmount)
                        .balanceAfter(subAccount.getBalance().subtract(deductionAmount))
                        .build());
                
                remainingAmount = remainingAmount.subtract(deductionAmount);
            }
        }
        
        BigDecimal actualAmount = amount.subtract(remainingAmount);
        boolean success = remainingAmount.compareTo(BigDecimal.ZERO) == 0 || config.isAllowPartialDeduction();
        
        return DeductionResult.builder()
                .success(success)
                .totalAmount(amount)
                .actualAmount(actualAmount)
                .deductionDetails(details)
                .errorMessage(success ? null : "余额不足，无法完成全额扣款")
                .build();
    }
    
    /**
     * 计算总可用余额
     */
    protected BigDecimal calculateTotalAvailableBalance(UserAccount userAccount, 
                                                       DeductionStrategyConfig config) {
        return config.getAccountPriorities().stream()
                .map(priorityConfig -> {
                    UserSubAccount subAccount = userAccount.getSubAccountMap().get(priorityConfig.getAccountType());
                    return subAccount != null ? calculateAvailableBalance(subAccount, priorityConfig) : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算单个账户的可用余额
     */
    protected BigDecimal calculateAvailableBalance(UserSubAccount subAccount, 
                                                  DeductionStrategyConfig.AccountPriorityConfig priorityConfig) {
        BigDecimal balance = subAccount.getBalance();
        BigDecimal minReserve = priorityConfig.getMinReserveBalance() != null ? 
                priorityConfig.getMinReserveBalance() : BigDecimal.ZERO;
        
        BigDecimal availableBalance = balance.subtract(minReserve);
        return availableBalance.compareTo(BigDecimal.ZERO) > 0 ? availableBalance : BigDecimal.ZERO;
    }
    
    /**
     * 计算实际扣款金额
     */
    protected BigDecimal calculateDeductionAmount(BigDecimal remainingAmount, 
                                                 BigDecimal availableBalance, 
                                                 DeductionStrategyConfig.AccountPriorityConfig priorityConfig) {
        BigDecimal targetAmount;
        
        if (priorityConfig.isFixedAmount()) {
            // 固定金额策略
            targetAmount = priorityConfig.getRatio();
        } else {
            // 比例策略
            targetAmount = remainingAmount.multiply(priorityConfig.getRatio());
        }
        
        // 不能超过可用余额
        targetAmount = targetAmount.min(availableBalance);
        
        // 不能超过剩余需扣款金额
        targetAmount = targetAmount.min(remainingAmount);
        
        // 检查最大金额限制
        if (priorityConfig.getMaxAmount() != null) {
            targetAmount = targetAmount.min(priorityConfig.getMaxAmount());
        }
        
        return targetAmount;
    }
    
    /**
     * 创建流水记录
     */
    protected UserSubAccountFlowRecord createFlowRecord(UserSubAccount subAccount,
                                                       BigDecimal deductionAmount,
                                                       BigDecimal balanceBefore,
                                                       BigDecimal balanceAfter,
                                                       DeductionBusinessInfo businessInfo) {
        return UserSubAccountFlowRecord.builder()
                .accountNo(subAccount.getAccountNo())
                .accountType(subAccount.getAccountType())
                .userId(subAccount.getUserId())
                .payOrderNo(businessInfo.getPayOrderNo())
                .orderNo(businessInfo.getOrderNo())
                .serialNo(businessInfo.getSerialNo())
                .balanceBefore(balanceBefore)
                .balanceChange(deductionAmount.negate()) // 扣款为负数
                .balanceAfter(balanceAfter)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .valid(true)
                .extInfo(businessInfo.getExtInfo())
                .build();
    }
}
