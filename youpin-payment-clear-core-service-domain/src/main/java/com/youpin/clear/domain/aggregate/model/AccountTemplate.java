package com.youpin.clear.domain.aggregate.model;

import com.youpin.clear.common.enums.AccountTypeEnum;
import com.youpin.clear.domain.aggregate.member.AccountInfoMember;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AccountTemplate {

    /**
     * 开户模板
     */
    public static List<AccountInfoMember> accountTemplate(Long userId) {
        List<AccountInfoMember> accountTemplateList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        //balance
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE.getCode()).userId(userId).createTime(now).updateTime(now).build());
        //balance1
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_1.getCode()).userId(userId).createTime(now).updateTime(now).build());
        //balance11
//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_TRADER_1.getCode()).userId(userId).createTime(now).updateTime(now).build());
//        //balance12
//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_WITHDRAW_1.getCode()).userId(userId).createTime(now).updateTime(now).build());
        //balance2
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_2.getCode()).userId(userId).createTime(now).updateTime(now).build());

//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_TRADER_2.getCode()).userId(userId).createTime(now).updateTime(now).build());
//
//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_WITHDRAW_2.getCode()).userId(userId).createTime(now).updateTime(now).build());

        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE.getCode()).createTime(now).updateTime(now).userId(userId).build());
//
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_1.getCode()).userId(userId).createTime(now).updateTime(now).build());
//
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_RECHARGE_2.getCode()).userId(userId).createTime(now).updateTime(now).build());


        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_1.getCode()).userId(userId).createTime(now).updateTime(now).build());

//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_TRADER_1.getCode()).userId(userId).createTime(now).updateTime(now).build());
//
//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_WITHDRAW_1.getCode()).userId(userId).createTime(now).updateTime(now).build());

        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_2.getCode()).userId(userId).createTime(now).updateTime(now).build());

//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_TRADER_2.getCode()).userId(userId).createTime(now).updateTime(now).build());
//
//        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.PURCHASE_BALANCE_TRANSFER_WITHDRAW_2.getCode()).userId(userId).createTime(now).updateTime(now).build());

        //临时账户
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_WITHDRAW_121.getCode()).userId(userId).createTime(now).updateTime(now).build());
        accountTemplateList.add(AccountInfoMember.builder().accountType(AccountTypeEnum.BALANCE_WITHDRAW_122.getCode()).userId(userId).createTime(now).updateTime(now).build());

        return accountTemplateList;
    }


}
