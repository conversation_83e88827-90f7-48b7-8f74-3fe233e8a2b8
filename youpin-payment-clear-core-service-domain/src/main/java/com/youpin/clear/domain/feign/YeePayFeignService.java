package com.youpin.clear.domain.feign;

import com.uu898.youpin.commons.base.enums.Status;
import com.uu898.youpin.commons.base.exceptions.BusinessException;
import com.uu898.youpin.commons.base.model.Result;
import com.uu898.youpin.commons.utils.JacksonUtils;
import com.youpin.payment.gateway.client.api.YeePayFeign;
import com.youpin.payment.gateway.client.request.yee.PaySystemQueryDTO;
import com.youpin.payment.gateway.client.response.yee.PaySystemQueryRemitOrderQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class YeePayFeignService {

    @Autowired
    YeePayFeign yeePayFeign;


    /**
     * 易宝提现查询
     */
    public PaySystemQueryRemitOrderQueryResponse paySystemQuery(PaySystemQueryDTO req) {
        Result<PaySystemQueryRemitOrderQueryResponse> result;
        try {
            log.info("易宝提现查询 req:{}", req);
            result = yeePayFeign.paySystemQuery(req);
            log.info("易宝提现查询,result={}", JacksonUtils.writeValueAsString(result));
        } catch (Exception e) {
            log.error("易宝提现查询 失败", e);
            throw new BusinessException(Status.SYSTEM.getCode(), "请稍后再试，若有其他问题，请联系客服处理");
        }
        if (Objects.isNull(result) || Objects.isNull(result.getData()) || !Status.OK.getCode().equals(result.getCode())) {
            log.error("易宝提现查询 异常,result={}", JacksonUtils.writeValueAsString(result));
            throw new BusinessException(Status.SYSTEM.getCode(), result != null ? result.getMsg() : "请稍后再试，若有其他问题，请联系客服处理");
        }
        return result.getData();
    }


}
