package com.youpin.clear.domain.service;

import com.youpin.clear.domain.aggregate.uu898.*;
import com.youpin.clear.domain.factory.DeductionStrategyFactory;
import com.youpin.clear.domain.gateway.UserAccountGateway;
import com.youpin.clear.domain.process.DeductionBusinessInfo;
import com.youpin.clear.domain.process.DeductionCheckResult;
import com.youpin.clear.domain.process.DeductionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 用户账户扣款服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserAccountDeductionService {
    
    @Autowired
    private DeductionStrategyFactory strategyFactory;
    
    @Autowired
    private UserAccountGateway userAccountGateway;
    
    /**
     * 执行扣款操作
     */
    public DeductionResult executeDeduction(Long userId, 
                                          BigDecimal amount, 
                                          DeductionStrategyType strategyType,
                                          DeductionBusinessInfo businessInfo) {
        
        log.info("开始执行扣款，用户ID：{}，金额：{}，策略：{}", userId, amount, strategyType);
        
        try {
            // 1. 获取用户账户信息
            UserAccount userAccount = userAccountGateway.getUserAccountWithLock(userId);
            if (userAccount == null) {
                return DeductionResult.builder()
                        .success(false)
                        .errorMessage("用户账户不存在")
                        .totalAmount(amount)
                        .actualAmount(BigDecimal.ZERO)
                        .build();
            }
            
            // 2. 获取扣款策略
            DeductionStrategy strategy = strategyFactory.getStrategy(strategyType);
            
            // 3. 构建策略配置
            DeductionStrategyConfig config = buildDefaultConfig(strategyType);
            
            // 4. 执行扣款
            DeductionResult result = strategy.executeDeduction(userAccount, amount, config, businessInfo);
            
            log.info("扣款执行完成，用户ID：{}，结果：{}", userId, result.isSuccess());
            return result;
            
        } catch (Exception e) {
            log.error("扣款执行异常，用户ID：{}，金额：{}", userId, amount, e);
            return DeductionResult.builder()
                    .success(false)
                    .errorMessage("扣款执行异常：" + e.getMessage())
                    .totalAmount(amount)
                    .actualAmount(BigDecimal.ZERO)
                    .build();
        }
    }
    
    /**
     * 执行扣款操作（使用自定义配置）
     */
    public DeductionResult executeDeduction(Long userId, 
                                          BigDecimal amount, 
                                          DeductionStrategyConfig config,
                                          DeductionBusinessInfo businessInfo) {
        
        log.info("开始执行扣款（自定义配置），用户ID：{}，金额：{}", userId, amount);
        
        try {
            // 1. 获取用户账户信息
            UserAccount userAccount = userAccountGateway.getUserAccountWithLock(userId);
            if (userAccount == null) {
                return DeductionResult.builder()
                        .success(false)
                        .errorMessage("用户账户不存在")
                        .totalAmount(amount)
                        .actualAmount(BigDecimal.ZERO)
                        .build();
            }
            
            // 2. 获取扣款策略
            DeductionStrategy strategy = strategyFactory.getStrategy(config.getStrategyType());
            
            // 3. 执行扣款
            DeductionResult result = strategy.executeDeduction(userAccount, amount, config, businessInfo);
            
            log.info("扣款执行完成，用户ID：{}，结果：{}", userId, result.isSuccess());
            return result;
            
        } catch (Exception e) {
            log.error("扣款执行异常，用户ID：{}，金额：{}", userId, amount, e);
            return DeductionResult.builder()
                    .success(false)
                    .errorMessage("扣款执行异常：" + e.getMessage())
                    .totalAmount(amount)
                    .actualAmount(BigDecimal.ZERO)
                    .build();
        }
    }
    
    /**
     * 预检查扣款是否可行
     */
    public DeductionCheckResult preCheckDeduction(Long userId, 
                                                BigDecimal amount, 
                                                DeductionStrategyType strategyType) {
        
        try {
            // 1. 获取用户账户信息
            UserAccount userAccount = userAccountGateway.getUserAccount(userId);
            if (userAccount == null) {
                return DeductionCheckResult.builder()
                        .passed(false)
                        .errorCode("USER_NOT_FOUND")
                        .errorMessage("用户账户不存在")
                        .build();
            }
            
            // 2. 获取扣款策略
            DeductionStrategy strategy = strategyFactory.getStrategy(strategyType);
            
            // 3. 构建策略配置
            DeductionStrategyConfig config = buildDefaultConfig(strategyType);
            
            // 4. 执行预检查
            return strategy.preCheck(userAccount, amount, config);
            
        } catch (Exception e) {
            log.error("扣款预检查异常，用户ID：{}，金额：{}", userId, amount, e);
            return DeductionCheckResult.builder()
                    .passed(false)
                    .errorCode("CHECK_ERROR")
                    .errorMessage("预检查异常：" + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 计算扣款方案
     */
    public DeductionResult calculateDeductionPlan(Long userId, 
                                                BigDecimal amount, 
                                                DeductionStrategyType strategyType) {
        
        try {
            // 1. 获取用户账户信息
            UserAccount userAccount = userAccountGateway.getUserAccount(userId);
            if (userAccount == null) {
                return DeductionResult.builder()
                        .success(false)
                        .errorMessage("用户账户不存在")
                        .totalAmount(amount)
                        .actualAmount(BigDecimal.ZERO)
                        .build();
            }
            
            // 2. 获取扣款策略
            DeductionStrategy strategy = strategyFactory.getStrategy(strategyType);
            
            // 3. 构建策略配置
            DeductionStrategyConfig config = buildDefaultConfig(strategyType);
            
            // 4. 计算扣款方案
            return strategy.calculateDeductionPlan(userAccount, amount, config);
            
        } catch (Exception e) {
            log.error("计算扣款方案异常，用户ID：{}，金额：{}", userId, amount, e);
            return DeductionResult.builder()
                    .success(false)
                    .errorMessage("计算扣款方案异常：" + e.getMessage())
                    .totalAmount(amount)
                    .actualAmount(BigDecimal.ZERO)
                    .build();
        }
    }
    
    /**
     * 构建默认策略配置
     */
    private DeductionStrategyConfig buildDefaultConfig(DeductionStrategyType strategyType) {
        switch (strategyType) {
            case PURCHASE_DEDUCTION:
                return DeductionStrategyConfig.builder()
                        .strategyType(strategyType)
                        .strategyName("购买商品扣款")
                        .allowPartialDeduction(false)
                        .accountPriorities(Arrays.asList(
                                DeductionStrategyConfig.AccountPriorityConfig.builder()
                                        .accountType(UserSubAccountType.TRADE)
                                        .priority(1)
                                        .ratio(BigDecimal.ONE)
                                        .build(),
                                DeductionStrategyConfig.AccountPriorityConfig.builder()
                                        .accountType(UserSubAccountType.WITHDRAW)
                                        .priority(2)
                                        .ratio(BigDecimal.ONE)
                                        .build()
                        ))
                        .build();
                        
            case REFUND_ADDITION:
                return DeductionStrategyConfig.builder()
                        .strategyType(strategyType)
                        .strategyName("退款加款")
                        .allowPartialDeduction(true)
                        .accountPriorities(Arrays.asList(
                                DeductionStrategyConfig.AccountPriorityConfig.builder()
                                        .accountType(UserSubAccountType.WITHDRAW)
                                        .priority(1)
                                        .ratio(BigDecimal.ONE)
                                        .build(),
                                DeductionStrategyConfig.AccountPriorityConfig.builder()
                                        .accountType(UserSubAccountType.TRADE)
                                        .priority(2)
                                        .ratio(BigDecimal.ONE)
                                        .build()
                        ))
                        .build();
                        
            default:
                throw new IllegalArgumentException("不支持的策略类型：" + strategyType);
        }
    }
}
