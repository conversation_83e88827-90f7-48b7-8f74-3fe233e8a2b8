package com.youpin.clear.domain.aggregate.member;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillItemMemberExtension implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 服务费金额
     */
    private BigDecimal chargeMoney;

    /**
     * 收单类型 0,默认  1(自有) 2(监管)
     */
    private Integer collectType;

    /**
     * 是否是租赁订单
     */
    private Boolean isLeaseOrder;

    /**
     * 钱包2转出金额(目前只适用 求购提现 转账是混合使用)
     */
    private BigDecimal balance2TransferAmount;

    /***
     * 商户id
     */
    private Integer merchantId;

    /**
     * 业务类型：出租，出售
     */
    private Integer businessType;

    /**
     * 子业务类型
     */
    private Integer subBusType;

    /**
     * 营销立减余额1参与金额
     */
    private BigDecimal marketingReduceAmount1;


}
