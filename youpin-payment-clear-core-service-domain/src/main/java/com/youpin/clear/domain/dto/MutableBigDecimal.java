package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MutableBigDecimal {
    private BigDecimal value;

    public void add(BigDecimal other) {
        this.value = this.value.add(other);
    }

    public BigDecimal subtract(BigDecimal other) {
        this.value = this.value.subtract(other);
        return this.value;
    }

    public void multiply(BigDecimal other) {
        this.value = this.value.multiply(other);
    }

    public void divide(BigDecimal other, int scale, RoundingMode roundingMode) {
        this.value = this.value.divide(other, scale, roundingMode);
    }

    @Override
    public String toString() {
        if (this.value == null) {
            return null;
        }
        return this.value.toString();
    }

}
