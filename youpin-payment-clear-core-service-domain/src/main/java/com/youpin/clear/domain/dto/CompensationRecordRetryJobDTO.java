package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompensationRecordRetryJobDTO implements Serializable {
    private LocalDateTime currentTime;
    private Integer maxRetryCount;
    private Integer shardTotal;
    private Integer shardIndex;
    private Long pageIndex;
    private Long pageSize;
    private Integer bizScene;
}
