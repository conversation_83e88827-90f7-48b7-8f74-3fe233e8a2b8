package com.youpin.clear.domain.service;

import com.youpin.clear.domain.aggregate.uu898.DeductionStrategyConfig;
import com.youpin.clear.domain.aggregate.uu898.DeductionStrategyType;
import com.youpin.clear.domain.aggregate.uu898.UserSubAccountType;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 扣款配置服务 - 提供各种预定义的扣款策略配置
 * <AUTHOR>
 */
@Service
public class DeductionConfigService {
    
    /**
     * 获取购买商品的扣款配置
     * 策略：优先扣除可交易余额，不足时扣除可提现余额
     */
    public DeductionStrategyConfig getPurchaseDeductionConfig() {
        return DeductionStrategyConfig.builder()
                .strategyType(DeductionStrategyType.PURCHASE_DEDUCTION)
                .strategyName("购买商品扣款策略")
                .allowPartialDeduction(false) // 不允许部分扣款
                .minDeductionAmount(new BigDecimal("0.01"))
                .maxDeductionAmount(new BigDecimal("999999.99"))
                .accountPriorities(Arrays.asList(
                        // 优先级1：可交易余额，100%扣除
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.TRADE)
                                .priority(1)
                                .ratio(BigDecimal.ONE) // 100%
                                .fixedAmount(false)
                                .minReserveBalance(BigDecimal.ZERO)
                                .build(),
                        // 优先级2：可提现余额，100%扣除
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.WITHDRAW)
                                .priority(2)
                                .ratio(BigDecimal.ONE) // 100%
                                .fixedAmount(false)
                                .minReserveBalance(BigDecimal.ZERO)
                                .build()
                ))
                .build();
    }
    
    /**
     * 获取退款的配置
     * 策略：优先退回可提现余额，然后退回可交易余额
     */
    public DeductionStrategyConfig getRefundConfig() {
        return DeductionStrategyConfig.builder()
                .strategyType(DeductionStrategyType.REFUND_ADDITION)
                .strategyName("退款策略")
                .allowPartialDeduction(true) // 允许部分退款
                .minDeductionAmount(new BigDecimal("0.01"))
                .accountPriorities(Arrays.asList(
                        // 优先级1：可提现余额
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.WITHDRAW)
                                .priority(1)
                                .ratio(BigDecimal.ONE)
                                .fixedAmount(false)
                                .build(),
                        // 优先级2：可交易余额
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.TRADE)
                                .priority(2)
                                .ratio(BigDecimal.ONE)
                                .fixedAmount(false)
                                .build()
                ))
                .build();
    }
    
    /**
     * 获取提现的扣款配置
     * 策略：只能从可提现余额扣除
     */
    public DeductionStrategyConfig getWithdrawDeductionConfig() {
        return DeductionStrategyConfig.builder()
                .strategyType(DeductionStrategyType.WITHDRAW_DEDUCTION)
                .strategyName("提现扣款策略")
                .allowPartialDeduction(false)
                .minDeductionAmount(new BigDecimal("1.00"))
                .maxDeductionAmount(new BigDecimal("50000.00"))
                .accountPriorities(Arrays.asList(
                        // 只能从可提现余额扣除
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.WITHDRAW)
                                .priority(1)
                                .ratio(BigDecimal.ONE)
                                .fixedAmount(false)
                                .minReserveBalance(BigDecimal.ZERO)
                                .build()
                ))
                .build();
    }
    
    /**
     * 获取混合扣款配置
     * 策略：按比例从多个账户扣款
     */
    public DeductionStrategyConfig getMixedDeductionConfig(BigDecimal tradeRatio, BigDecimal withdrawRatio) {
        return DeductionStrategyConfig.builder()
                .strategyType(DeductionStrategyType.CUSTOM_STRATEGY)
                .strategyName("混合扣款策略")
                .allowPartialDeduction(false)
                .accountPriorities(Arrays.asList(
                        // 可交易余额按比例扣除
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.TRADE)
                                .priority(1)
                                .ratio(tradeRatio)
                                .fixedAmount(false)
                                .build(),
                        // 可提现余额按比例扣除
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(UserSubAccountType.WITHDRAW)
                                .priority(1) // 同优先级，同时扣除
                                .ratio(withdrawRatio)
                                .fixedAmount(false)
                                .build()
                ))
                .build();
    }
    
    /**
     * 获取固定金额扣款配置
     * 策略：从指定账户扣除固定金额
     */
    public DeductionStrategyConfig getFixedAmountDeductionConfig(UserSubAccountType accountType, BigDecimal fixedAmount) {
        return DeductionStrategyConfig.builder()
                .strategyType(DeductionStrategyType.CUSTOM_STRATEGY)
                .strategyName("固定金额扣款策略")
                .allowPartialDeduction(true)
                .accountPriorities(Arrays.asList(
                        DeductionStrategyConfig.AccountPriorityConfig.builder()
                                .accountType(accountType)
                                .priority(1)
                                .ratio(fixedAmount) // 在固定金额模式下，ratio字段存储固定金额
                                .fixedAmount(true)
                                .build()
                ))
                .build();
    }
}
