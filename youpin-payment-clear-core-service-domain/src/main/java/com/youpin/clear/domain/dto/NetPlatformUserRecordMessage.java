package com.youpin.clear.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class NetPlatformUserRecordMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 资金明细主键ID
     */
    @JsonProperty("Id")
    @JSONField(name = "Id")
    private Long id;
    /**
     * 所属用户的唯一标识
     */
    @JsonProperty("UserId")
    @JSONField(name = "UserId")
    private Long userId;
    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    @JsonProperty("TypeId")
    @JSONField(name = "TypeId")
    private Integer typeId;
    /**
     * 当前资金明细记录的唯一单号
     */
    @JsonProperty("TreadNo")
    @JSONField(name = "TreadNo")
    private String treadNo;

}
