package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssetsRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入参关联加
     */
    private List<Integer> associatedAdditionList;

    /**
     * DB关联加
     */
    private List<Integer> dbAssociatedAdditionList;

    /**
     * 入参关联减
     */
    private List<Integer> associatedSubtractionList;

    /**
     * DB关联减
     */
    private List<Integer> dbAssociatedSubtractionList;

    public static void main(String[] args) {
        // 转出了10笔
        List<UU898UserSubAccountFlowRecordDTO> type_97_10 = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            UU898UserSubAccountFlowRecordDTO dto = new UU898UserSubAccountFlowRecordDTO();
            dto.setBalanceChange(BigDecimal.ONE);
            dto.setJournalType(97);
            type_97_10.add(dto);
        }
        // 使用了3笔

    }

    public static ChangeMoneyDTO a(List<UU898UserSubAccountFlowRecordDTO> subAccountRecordList, BigDecimal changeMoney) {
        //
        // 获取总增加typeList 余额++++
        List<Integer> addTypeList = List.of(99);
        // 获取总支出typeList 余额----
        List<Integer> subtractTypeList = List.of(97);
        // 获取时间
        LocalDateTime endCreateTime = LocalDateTime.now();
        //
        LocalDateTime startCreateTime = endCreateTime.minusDays(7L);
        // 获取流水typeList
        List<Integer> journalTypeList = new ArrayList<>(addTypeList);
        //
        journalTypeList.addAll(subtractTypeList);
        // 判断是否为空
        if (CollectionUtils.isEmpty(subAccountRecordList)) {
            // 返回可交易=0
            return ChangeMoneyDTO.builder().tradeChangeMoney(BigDecimal.ZERO).changeMoney(changeMoney).build();
        }
        // 获取总增加金额
        BigDecimal totalAddAmount = subAccountRecordList.stream().filter(item -> addTypeList.contains(item.getJournalType())).map(item -> item.getBalanceChange().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取总支出金额
        BigDecimal totalSubtractAmount = subAccountRecordList.stream().filter(item -> subtractTypeList.contains(item.getJournalType())).map(item -> item.getBalanceChange().abs()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 获取 仅可交易可用退款金额 = 仅可交易支出所有金额 - 仅可交易退款所有金额
        BigDecimal tradeRefundableAmount = totalAddAmount.subtract(totalSubtractAmount);
        // 判断 仅可交易可用退款金额 >= changeMoney
        if (tradeRefundableAmount.compareTo(changeMoney) >= 0) {
            // tradeChangeMoney = changeMoney
            BigDecimal tradeChangeMoney = changeMoney.subtract(tradeRefundableAmount);
            tradeChangeMoney = tradeChangeMoney.compareTo(BigDecimal.ZERO) > 0 ? tradeChangeMoney : BigDecimal.ZERO;
            return ChangeMoneyDTO.builder().tradeChangeMoney(tradeChangeMoney).changeMoney(changeMoney).build();
        } else {
            BigDecimal tradeChangeMoney = tradeRefundableAmount.subtract(tradeRefundableAmount);
            tradeChangeMoney = tradeChangeMoney.compareTo(BigDecimal.ZERO) > 0 ? tradeChangeMoney : BigDecimal.ZERO;
            // 返回tradeChangeMoney = 仅可交易可用退款金额
            return ChangeMoneyDTO.builder().tradeChangeMoney(tradeChangeMoney).changeMoney(changeMoney).build();
        }
    }
}
