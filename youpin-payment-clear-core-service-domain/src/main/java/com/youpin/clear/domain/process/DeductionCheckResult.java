package com.youpin.clear.domain.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 扣款预检查结果
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeductionCheckResult {
    
    /**
     * 检查是否通过
     */
    private boolean passed;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 可扣款的最大金额
     */
    private BigDecimal maxDeductibleAmount;
    
    /**
     * 余额不足的账户类型列表
     */
    private List<String> insufficientAccounts;
    
    /**
     * 警告信息列表
     */
    private List<String> warnings;
    
    /**
     * 建议信息
     */
    private String suggestion;
}
