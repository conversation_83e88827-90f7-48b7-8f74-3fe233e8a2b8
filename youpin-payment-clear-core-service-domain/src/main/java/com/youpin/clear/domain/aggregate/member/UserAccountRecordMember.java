package com.youpin.clear.domain.aggregate.member;

import com.alibaba.fastjson.JSON;
import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.common.enums.DoNetPayChannelEnum;
import com.youpin.clear.common.enums.NetStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountRecordMember implements Serializable, Comparable<UserAccountRecordMember> {
    private static final long serialVersionUID = 1L;
    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 账户明细id
     */
    private String accountRecordNo;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;
    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;
    /**
     * 当前资金明细记录的唯一单号
     */
    private String treadNo;
    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 支付单号
     */
    private String payOrderNo;
    /**
     * 用户账户号
     */
    private String userAccountNo;
    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 变动之前账户余额
     */
    private BigDecimal balanceBefore;
    /**
     * 变动金额
     */
    private BigDecimal balanceChange;
    /**
     * 变动之后账户余额
     */
    private BigDecimal balanceAfter;

    /**
     * 余额是否变动 0不变,1变动
     */
    private Integer balanceIsChange;

    /**
     * 冻结变动之前账户余额
     */
    private BigDecimal frozenBalanceBefore;

    /**
     * 冻结变动金额
     */
    private BigDecimal frozenBalanceChange;

    /**
     * 冻结变动之后账户余额
     */
    private BigDecimal frozenBalanceAfter;

    /**
     * 0 失败 1 成功 2 进行中
     */
    private Integer status;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 扩展字段
     */
    private String ext;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 方向
     */
    private DirectionEnum directionEnum;


    private Integer compareSort = 0;

    /**
     * 扩展
     */
    private BillItemMemberExtension billItemMemberExtension;


    public BillItemMemberExtension getBillItemMemberExtension() {
        if (null == billItemMemberExtension) {
            if (StringUtils.isNotBlank(ext)) {
                return JSON.parseObject(ext, BillItemMemberExtension.class);
            }
        }
        return this.billItemMemberExtension;
    }

    @Override
    public int compareTo(UserAccountRecordMember other) {
        return this.compareSort.compareTo(other.compareSort);
    }

    public boolean isProcessing() {
        return status.equals(NetStatusEnum.PROCESSING.getCode());
    }

    public boolean isFail() {
        return status.equals(NetStatusEnum.FAIL.getCode());
    }

    public boolean isSuccess() {
        return status.equals(NetStatusEnum.SUCCESS.getCode());
    }

    public boolean isBalanceOrPurchase() {
        return payChannel.equals(DoNetPayChannelEnum.Balance.getCode()) || payChannel.equals(DoNetPayChannelEnum.PurchaseBalance.getCode());
    }

    public boolean isBalance() {
        return payChannel.equals(DoNetPayChannelEnum.Balance.getCode());
    }


}