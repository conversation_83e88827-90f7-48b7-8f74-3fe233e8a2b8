package com.youpin.clear.domain.factory;

import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.SubBusFinancialProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SubBusFinancialProcessorFactory {

    @Autowired
    List<SubBusFinancialProcessor> processorList;

    Map<SubBusTypeFrontEnum, SubBusFinancialProcessor> maps = new HashMap<>();

    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(processorList)) {
            processorList.forEach(processor -> {
                List<SubBusTypeFrontEnum> supportTypeList = processor.support();
                supportTypeList.forEach(subBusType -> maps.put(subBusType, processor));
            });
        }
    }


    public SubBusFinancialProcessor getProcessor(SubBusTypeFrontEnum typeEnum) {
        if (typeEnum == null) {
            log.error("SubBusTypeFrontEnum is null");
            return null;
        }
        SubBusFinancialProcessor subBusFinancialProcessor = maps.getOrDefault(typeEnum, null);
        if (subBusFinancialProcessor == null) {
            log.error("SubBusFinancialProcessor is null,typeEnum:{}", typeEnum);
            throw new PaymentClearBusinessException(ErrorCode.BUSINESS_FACTORY_NOT_EXIST);
        }
        return subBusFinancialProcessor;
    }
}
