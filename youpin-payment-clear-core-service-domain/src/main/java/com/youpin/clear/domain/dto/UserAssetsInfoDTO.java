package com.youpin.clear.domain.dto;

import com.youpin.clear.common.enums.UU898UserSubAccountType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户唯一标识
     */
    private Long userId;
    /**
     * 账户余额
     */
    private BigDecimal money;
    /**
     * 已支付金额（入账后增加，退款或交易完成后减少）
     */
    private BigDecimal paymentMoney;
    /**
     * 冻结金额
     */
    private BigDecimal blockMoney;
    /**
     * 求购金额
     */
    private BigDecimal purchaseMoney;
    /**
     * 积分
     */
    private Integer integral;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 最后更新相关的资金明细记录ID(在UserAssetsRecord表中)
     */
    private Long lastUserRecordId;
    /**
     * 套现金额
     */
    private BigDecimal cashMoney;
    /**
     * 仅可提现金额
     */
    private BigDecimal onlyWithDrawMoney;
    /**
     * 求购冻结金额
     */
    private BigDecimal purchaseBlockMoney;
    /**
     * 钱包转入的求购金额
     */
    private BigDecimal purchaseMoneyFromMoney;

    /**
     * 子账户信息
     */
    private Map<Integer, UU898UserSubAccountDTO> subAccountDTOMap;


    public UU898UserSubAccountDTO getTradeSubAccountDTO() {
        if (subAccountDTOMap == null || subAccountDTOMap.isEmpty()) {
            return null;
        }
        return subAccountDTOMap.get(UU898UserSubAccountType.TRADE.getType());
    }
}