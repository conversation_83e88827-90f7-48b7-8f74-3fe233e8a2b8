package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSubAccountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    /**
     * 余额1
     */
    private UserSubAccountInfoDTO balance1;

    /**
     * 余额2
     */
    private UserSubAccountInfoDTO balance2;

    /**
     * 求购充值余额1
     */
    private UserSubAccountInfoDTO purchaseBalanceRecharge1;

    /**
     * 求购充值余额2
     */
    private UserSubAccountInfoDTO purchaseBalanceRecharge2;

    /**
     * 求购转入余额1
     */
    private UserSubAccountInfoDTO purchaseBalanceTransfer1;

    /**
     * 求购转入余额2
     */
    private UserSubAccountInfoDTO purchaseBalanceTransfer2;

}

