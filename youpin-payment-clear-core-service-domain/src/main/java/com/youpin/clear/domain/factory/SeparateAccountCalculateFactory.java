package com.youpin.clear.domain.factory;

import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.domain.process.AccountCalculateProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分账账户抽象处理器工厂
 */
@Component
public class SeparateAccountCalculateFactory {

    @Resource
    List<AccountCalculateProcessor> accountCalculateProcessorList;

    Map<DirectionEnum, AccountCalculateProcessor> maps = new HashMap<>();

    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(accountCalculateProcessorList)) {
            accountCalculateProcessorList.forEach(processor -> {
                List<DirectionEnum> supportTypeList = processor.support();
                supportTypeList.forEach(subBusType -> maps.put(subBusType, processor));
            });
        }
    }

    public AccountCalculateProcessor getProcessor(DirectionEnum typeEnum) {
        if (typeEnum == null) {
            return null;
        }
        return maps.getOrDefault(typeEnum, null);
    }
}
