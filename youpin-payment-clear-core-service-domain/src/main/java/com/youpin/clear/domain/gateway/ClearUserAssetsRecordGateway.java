package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.ClearUserAssetsRecordDTO;

import java.util.List;

public interface ClearUserAssetsRecordGateway {

    void insertSelective(ClearUserAssetsRecordDTO dto);

    List<ClearUserAssetsRecordDTO> batchClearUserAssetsRecordDTO(List<ClearUserAssetsRecordDTO> dtoList);

    Long maxUserAssetsRecordId(Long userId);

    List<ClearUserAssetsRecordDTO> selectUserAssetsRecordById(Long userId, Long pageIndex, Integer pageSize, Long userAssetsRecordId);


    ClearUserAssetsRecordDTO selectUserAssetsRecordById(Long userId, Long userAssetsRecordId);

    void updateByPrimaryKeySelectiveByUserId(ClearUserAssetsRecordDTO dto);

}
