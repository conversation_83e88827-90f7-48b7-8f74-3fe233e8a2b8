package com.youpin.clear.domain.aggregate.uu898;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 扣款策略配置
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeductionStrategyConfig {
    
    /**
     * 策略类型
     */
    private DeductionStrategyType strategyType;
    
    /**
     * 策略名称
     */
    private String strategyName;
    
    /**
     * 账户优先级配置列表（按优先级排序）
     */
    private List<AccountPriorityConfig> accountPriorities;
    
    /**
     * 是否允许部分扣款（余额不足时）
     */
    private boolean allowPartialDeduction;
    
    /**
     * 最小扣款金额
     */
    private BigDecimal minDeductionAmount;
    
    /**
     * 最大扣款金额
     */
    private BigDecimal maxDeductionAmount;
    
    /**
     * 扩展配置（JSON格式）
     */
    private String extConfig;
    
    /**
     * 账户优先级配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountPriorityConfig {
        
        /**
         * 子账户类型
         */
        private UserSubAccountType accountType;
        
        /**
         * 优先级（数字越小优先级越高）
         */
        private int priority;
        
        /**
         * 扣款比例（0-1之间，1表示100%）
         * 当为固定金额策略时，此字段表示固定金额
         */
        private BigDecimal ratio;
        
        /**
         * 是否为固定金额策略
         */
        private boolean fixedAmount;
        
        /**
         * 最大扣款金额限制
         */
        private BigDecimal maxAmount;
        
        /**
         * 最小保留余额
         */
        private BigDecimal minReserveBalance;
    }
}
