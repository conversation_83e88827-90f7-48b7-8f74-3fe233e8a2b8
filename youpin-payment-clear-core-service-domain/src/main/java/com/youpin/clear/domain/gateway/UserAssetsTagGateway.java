package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.UserAssetsTagDTO;

import java.math.BigDecimal;
import java.util.List;

public interface UserAssetsTagGateway {

    List<UserAssetsTagDTO> getUserAssetsTagByUserId(Long userId);

    List<UserAssetsTagDTO> selectUserAssetsTagByTableName(String tableName, Long pageIndex, Integer pageSize, Long minId);

    void syncDateCache(List<UserAssetsTagDTO> dtoList);

    void saveUserAssetsTag(UserAssetsTagDTO userAssetsTag);

    UserAssetsTagDTO getUserAssetsTagByTag(Long userId, String tagCode);

    void updateByUserId(UserAssetsTagDTO dto);

    void updateBigDecimalByUserId(Long id, Long userId, String tagCode, BigDecimal changeTagValueDecimal);


    void assetsTagDelete(Long redisKeySuffix, Long userId, String tagCode);

    List<UserAssetsTagDTO> assetsTagCacheGetUserId(Long userId, String tagCode);
}
