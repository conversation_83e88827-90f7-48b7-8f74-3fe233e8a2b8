package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataAccountDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 用户id
     */
    private Long userId;


    /**
     * 余额变动
     */
    private BigDecimal balanceChange;

    /**
     * 余额冻结变动
     */
    private BigDecimal balanceFreezeChange;

    /**
     * 求购余额变动
     */
    private BigDecimal purchaseBalanceChange;

    /**
     * 余额1变动
     */
    private BigDecimal b1Change;

    /**
     * 余额2变动
     */
    private BigDecimal b2Change;

    /**
     * 求购充值1变动
     */
    private BigDecimal pbr1Change;

    /**
     * 求购充值2变动
     */
    private BigDecimal pbr2Change;

    /**
     * 求购转入1变动
     */
    private BigDecimal pbt1Change;

    /**
     * 求购转入2变动
     */
    private BigDecimal pbt2Change;


    /**
     * 余额1转余额2
     */
    private BigDecimal b1ToB2;

    /**
     * 余额1转余额1冻结
     */
    private BigDecimal b1ToBF;

    /**
     * 余额1冻结转余额1
     */
    private BigDecimal bfToB1;


    /**
     * 余额2转余额1
     */
    private BigDecimal b2ToB1;

    /**
     * 余额2转余额2冻结
     */
    private BigDecimal b2ToBF;

    /**
     * 余额2冻结转余额2
     */
    private BigDecimal bfToB2;


    /**
     * 求购充值1 转 求购充值2
     */
    private BigDecimal pBR1ToPBR2;

    /**
     * 求购充值1 转 求购转入1
     */
    private BigDecimal pBR1ToPBT1;


    /**
     * 求购充值1 转 求购转入2
     */
    private BigDecimal pBR1ToPBT2;


    /**
     * 求购充值2 转 求购充值1
     */
    private BigDecimal pBR2ToPBR1;


    /**
     * 求购充值2 转 求购转入1
     */
    private BigDecimal pBR2ToPBT1;

    /**
     * 求购充值2 转 求购转入2
     */
    private BigDecimal pBR2ToPBT2;


}
