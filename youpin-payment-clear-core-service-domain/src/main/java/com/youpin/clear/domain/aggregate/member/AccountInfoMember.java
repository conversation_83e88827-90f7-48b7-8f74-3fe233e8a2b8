package com.youpin.clear.domain.aggregate.member;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountInfoMember implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户账户号
     */
    private String userAccountNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 账户可用余额
     */
    @Builder.Default
    private BigDecimal balance = BigDecimal.ZERO;


    /**
     * 账户冻结余额
     */
    @Builder.Default
    private BigDecimal frozenBalance = BigDecimal.ZERO;

    /**
     * 最后更新记录ID
     */
    private Long lastAccountRecordId;
    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 使用次数
     */
    @Builder.Default
    private Integer useNumber = 0;

    /**
     * 扩展
     */
    private AccountInfoMemberExtension accountInfoMemberExtension;


    public AccountInfoMemberExtension getAccountInfoMemberExtension() {
        if (null == accountInfoMemberExtension) {
            if (StringUtils.isNotBlank(ext)) {
                return JSON.parseObject(ext, AccountInfoMemberExtension.class);
            }
        }
        return new AccountInfoMemberExtension();
    }

}
