package com.youpin.clear.domain.factory;

import com.youpin.clear.domain.handle.mow.MowMoneyHandle;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MowMoneyHandleFactory {

    @Resource
    List<MowMoneyHandle> mowMoneyHandleList;

    Map<Integer, MowMoneyHandle> maps = new HashMap<>();

    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(mowMoneyHandleList)) {
            mowMoneyHandleList.forEach(processor -> {
                List<Integer> supportTypeList = processor.support();
                supportTypeList.forEach(typeId -> maps.put(typeId, processor));
            });
        }
    }

    public MowMoneyHandle getProcessor(Integer typeId) {
        if (typeId == null) {
            return null;
        }
        return maps.getOrDefault(typeId, null);
    }
}
