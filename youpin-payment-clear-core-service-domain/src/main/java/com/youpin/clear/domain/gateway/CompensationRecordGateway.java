package com.youpin.clear.domain.gateway;

import com.youpin.clear.common.enums.BizCompensationSceneEnum;
import com.youpin.clear.domain.dto.CompensationRecordDTO;
import com.youpin.clear.domain.dto.CompensationRecordRetryJobDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface CompensationRecordGateway {

    void saveCompensationRecord(String uniqueKey, String retryMsg, BizCompensationSceneEnum bizSceneEnum);

    /**
     * 根据业务异常记录id查询业务异常记录
     **/
    CompensationRecordDTO queryBizExceptionRecordByPrimaryKey(Long id);

    void updateRecord(CompensationRecordDTO record);

    Long countExceptionRetryJob(CompensationRecordRetryJobDTO dto);

    List<Long> selectExceptionRetryJob(CompensationRecordRetryJobDTO dto);

    void deleteByCountSum(Integer countSum, Integer pageSize);

    void deleteByCreateTime(LocalDateTime createTime, Integer pageSize);

    List<CompensationRecordDTO> selectMaxRetryCount(Integer maxRetryCount, Integer pageIndex, Integer pageSize);

    void deleteByPrimaryKey(Long id);

    void deleteByHandleStatus(Integer handleStatus, Integer pageSize);

    boolean checkUniqueKeyCount(String uniqueKey, Integer bizScene);
}
