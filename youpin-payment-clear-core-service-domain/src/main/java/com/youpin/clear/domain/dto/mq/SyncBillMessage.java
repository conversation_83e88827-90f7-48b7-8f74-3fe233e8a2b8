package com.youpin.clear.domain.dto.mq;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@NoArgsConstructor
@ToString
@AllArgsConstructor
@Builder
public class SyncBillMessage implements Serializable {

    private static final long serialVersionUID = 1966252175173047474L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 外部交易号
     */
    private String outTradeNo;

    /**
     * 流水号
     */
    private String requestNo;

    /**
     * 标题
     */
    private String title;

    /**
     * 业务创建时间
     */
    private String bizCreatedTime;

    private Long userId;

    /**
     * 安心涨-冻结 10001
     * 安心涨-解冻 10002
     * 红锁-冻结 10003
     * 红锁-解冻 10004
     * 批量-解冻 10005
     */
    private Integer typeId;

    private BigDecimal money;

    private Integer billPayChannel;
    /**
     * 1-钱包余额
     * 2-求购账户余额
     * 3-APP支付
     * 4-预授权支付
     * 5-扫码支付
     */
    private Integer billPaySubChannel;

    /**
     * 1-待处理
     * 2-处理中
     * 3-处理完成
     * 4-处理失败
     */
    private Integer billStatus;

    /**
     * 余额
     */
    private BigDecimal balanceAmount;

    /**
     * 求购账户余额
     */
    private BigDecimal purchaseBalanceAmount;


    /**
     * 消息来源 默认 clear_account
     */
    @Builder.Default
    private String fromSystemSource = "clear_account";


    /**
     * 服务费金额
     */
    private BigDecimal chargeMoney;


}
