package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BalanceComputeResultDTO {

    /**
     * 可提现余额变更前
     */
    private BigDecimal withdrawBalanceBefore;

    /**
     * 冻结余额变更前
     */
    private BigDecimal blockBalanceBefore;

    /**
     * 可交易余额变更前
     */
    private BigDecimal tradeBalanceBefore;

    /**
     * 可提现余额变更后
     */
    private BigDecimal withdrawBalanceAfter;

    /**
     * 冻结余额变更后
     */
    private BigDecimal blockBalanceAfter;

    /**
     * 可交易余额变更后
     */
    private BigDecimal tradeBalanceAfter;

    /**
     * 可提现余额变更值
     */
    private BigDecimal withdrawBalanceChange;

    /**
     * 冻结余额变更值
     */
    private BigDecimal blockBalanceChange;

    /**
     * 可交易余额变更值
     */
    private BigDecimal tradeBalanceChange;


    public BigDecimal getBalanceBefore() {
        return withdrawBalanceBefore.add(tradeBalanceBefore);
    }

    public BigDecimal getBalanceAfter() {
        return withdrawBalanceAfter.add(tradeBalanceAfter);
    }

    public BigDecimal getBalanceChange() {
        return withdrawBalanceChange.add(tradeBalanceChange);
    }

}
