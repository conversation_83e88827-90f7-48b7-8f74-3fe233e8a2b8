package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CallSubAccountBusinessRevokeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;
    private String orderNo;
    private String serialNo;
    private BigDecimal amount;

}