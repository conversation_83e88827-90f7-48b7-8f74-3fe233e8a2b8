package com.youpin.clear.domain.process;

import com.youpin.clear.domain.aggregate.UserAccountRecordAggregate;
import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;

import java.util.List;

public interface SeparateAccountAssetsTypeProcessor {
    
    List<Integer> support();

    void process(UserAccountRecordAggregate userAccountRecordAggregate, List<UU898UserAssetsRecordDTO> uu898UserAssetsRecordDTOList);


}
