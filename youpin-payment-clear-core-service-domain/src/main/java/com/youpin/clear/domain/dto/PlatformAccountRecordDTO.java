package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformAccountRecordDTO implements Serializable {
    /**
     * 主键自增id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 分片键
     */
    private Integer shard;

    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;

    /**
     * 资金明细唯一单号
     */
    private String treadNo;

    /**
     * 金额类型:0,余额,1.求购余额
     */
    private Integer moneyType;

    /**
     * 变动金额:+- 元
     */
    private BigDecimal money;

    /**
     * 冻结金额:+- 元
     */
    private BigDecimal blockMoney;

    /**
     * 状态，0.未处理，1.成功，2.失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
