package com.youpin.clear.domain.dto;

import com.youpin.clear.common.constant.ClearConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserAssetsAmountCheckerDTO {

    //支付金额
    private MutableBigDecimal payMoney = new MutableBigDecimal(BigDecimal.ZERO);
    //结算金额
    private MutableBigDecimal settlementMoney = new MutableBigDecimal(BigDecimal.ZERO);
    //交易服务费
    private MutableBigDecimal serviceCharge = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 支出金额
     */
    private MutableBigDecimal expenditureMoney = new MutableBigDecimal(BigDecimal.ZERO);
    /**
     * 收入金额
     */
    private MutableBigDecimal incomeMoney = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 支出冻结金额
     */
    private MutableBigDecimal expenditureBlockMoney = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 收入冻结金额
     */
    private MutableBigDecimal incomeBlockMoney = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 支出求购金额
     */
    private MutableBigDecimal expenditurePurchaseMoney = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 收入求购金额
     */
    private MutableBigDecimal incomePurchaseMoney = new MutableBigDecimal(BigDecimal.ZERO);


    /**
     * 退款金额
     */
    private MutableBigDecimal refundMoney = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 退款服务费
     */
    private MutableBigDecimal serviceChargeRefund = new MutableBigDecimal(BigDecimal.ZERO);

    /**
     * 结算冻结金额
     */
    private MutableBigDecimal settlementFreezeMoney = new MutableBigDecimal(BigDecimal.ZERO);




    static int compareTo(MutableBigDecimal m1, MutableBigDecimal m2) {
        return m1.getValue().compareTo(m2.getValue());
    }


    /**
     * 总支出
     */
    public BigDecimal sumExpenditureMoney() {
        return expenditureMoney.getValue().add(expenditureBlockMoney.getValue()).add(expenditurePurchaseMoney.getValue());
    }

    /**
     * 总收入
     */
    public BigDecimal sumIncomePurchase() {
        return incomeMoney.getValue().add(incomeBlockMoney.getValue()).add(incomePurchaseMoney.getValue());
    }


    /**
     * 结算金额是否大于支付金额
     * payMoney 支付金额
     * settlementMoney 结算金额
     *
     * @return true 通过 false 不通过
     */
    public boolean checkSettlementMoney() {
        return compareTo(settlementMoney, payMoney) <= ClearConstants.CONSTANT_INTEGER_0;
    }

    /**
     * 服务费是否大于支付金额
     * payMoney 支付金额
     * serviceCharge 服务费
     * settlementMoney 结算金额
     *
     * @return true 通过 false 不通过
     */
    public boolean checkServiceCharge() {
        return compareTo(serviceCharge, payMoney) <= ClearConstants.CONSTANT_INTEGER_0 && compareTo(serviceCharge, settlementMoney) <= ClearConstants.CONSTANT_INTEGER_0;
    }

    /**
     * 支出减收入
     */
    public BigDecimal expenditureSubtractIncomeMoney() {
        return expenditureMoney.getValue().subtract(incomeMoney.getValue());
    }
}
