package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.UU898UserAssetsRecordDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface UU898UserAssetsRecordGateway {

    List<UU898UserAssetsRecordDTO> queryUserAssetsRecordDTOList(
            Long userId, String serialNo,
            String orderNo, String payOrderNo,
            Integer typeId, Integer status
    );

    List<UU898UserAssetsRecordDTO> queryUserAssetsRecordDTOList(Long userId, String orderNo, String payOrderNo);

    Long countByAddTime(Long lastId, LocalDateTime startTime, LocalDateTime endTime);

    List<Long> selectByAddTimePage(Long lastId, LocalDateTime startTime, LocalDateTime endTime, Long pageIndex, Long pageSize);

    List<Long> selectByIdInterval(Long startId, Long endId, LocalDateTime startTime, LocalDateTime endTime, Long pageIndex, Long pageSize);

    Long selectMaxId();

    UU898UserAssetsRecordDTO selectById(Long id);

    Long insert(UU898UserAssetsRecordDTO userAssetsRecordDTO);

    UU898UserAssetsRecordDTO selectByTradeNo(String tradeNo);

    Integer update(UU898UserAssetsRecordDTO userAssetsRecordDTO);

    /**
     *单元测试 删除数据使用
     */
    Integer deleteByTest(Long userId, String serialNo, String orderNo, String payOrderNo, Integer typeId);


    Long selectMinIdByUserIdAndTypeIdTime(Long userId, List<Integer> typeIdList, LocalDateTime endTime);

    List<Long> selectIdListByUserIdAndTypeIdTimePage(Long minId, Long userId, List<Integer> typeIdList, LocalDateTime endTime, Integer pageSize);




}
