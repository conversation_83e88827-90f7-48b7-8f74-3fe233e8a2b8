package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.aggregate.member.UserSubAccountRecordMember;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserSubAccountRecordGateway {

    /**
     * 保存二级账户聚合根
     *
     * @param userSubAccountRecordMembers records
     */
    void save(List<UserSubAccountRecordMember> userSubAccountRecordMembers);

    /**
     * 更新二级账户资金明细
     *
     * @param recordMember 二级账户资金明细
     * @return boolean
     */
    boolean updateRecord(UserSubAccountRecordMember recordMember);

    /**
     * 根据订单号查询二级账户资金明细
     *
     * @param orderNo 订单号
     * @return 二级账户资金明细List
     */
    List<UserSubAccountRecordMember> loadSubAccountRecordMember(String orderNo);


    Long getMinAccountRecordId(@NotNull(message = "userId不能为空") Long userId);
}
