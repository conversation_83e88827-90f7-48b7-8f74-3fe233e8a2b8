package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.UserAssetsInfoDTO;

import java.util.List;

public interface UU898UserAssetsInfoGateway {

    UserAssetsInfoDTO getUserAssetsInfo(Long userId);

    List<UserAssetsInfoDTO> getUserAssetsInfo(Long userIdMin, Long userIdMax, Integer easy);

    int updateUserBalance(UserAssetsInfoDTO origin, UserAssetsInfoDTO toUpdate);

    int updateUserPurchaseBalance(UserAssetsInfoDTO origin, UserAssetsInfoDTO toUpdate);

    int updateUserBalanceAndBlockMoney(UserAssetsInfoDTO originDTO, UserAssetsInfoDTO toUpdateDTO);
}
