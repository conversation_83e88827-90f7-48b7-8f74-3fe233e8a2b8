package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.AccountAssetsTypeDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AccountAssetsTypeGateway {

    /**
     * 获取所有的资金类型
     */
    Map<Integer, AccountAssetsTypeDTO> gatAllAccountAssetsType();


    Map<Integer, String> gatAccountAssetsTypeByCode(Set<Integer> assetsCodeList);

    /**
     * 获取所有的资金类型关联关系
     */
    Map<String, List<Integer>>  gatAllAccountAssetsTypeRelate();

    void initAccountAssetsTypeCache();
}
