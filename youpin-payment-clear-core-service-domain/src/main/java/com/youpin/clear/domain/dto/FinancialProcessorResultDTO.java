package com.youpin.clear.domain.dto;

import com.uu898.youpin.commons.base.enums.Status;
import com.youpin.clear.common.enums.AssetsErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FinancialProcessorResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer code;
    private String msg;


    public static FinancialProcessorResultDTO SUCCESS = getInstance(AssetsErrorCode.SUCCESS);

    public static FinancialProcessorResultDTO ERROR = getInstance(AssetsErrorCode.ERROR);


    public static FinancialProcessorResultDTO ALREADY_PROCESSED = getInstance(AssetsErrorCode.ALREADY_PROCESSED);


    public static FinancialProcessorResultDTO getInstance(AssetsErrorCode assetsErrorCode) {
        return new FinancialProcessorResultDTO(assetsErrorCode.getCode(), assetsErrorCode.getMessage());
    }


    /**
     * 校验是否成功
     */
    public boolean checkSuccess() {
        if (null == this.code) {
            return false;
        }
        return Status.OK.getCode().equals(code);
    }

    /**
     * 校验是否失败
     */
    public boolean checkFail() {
        if (null == this.code) {
            return false;
        }
        return !Status.OK.getCode().equals(code);
    }


}
