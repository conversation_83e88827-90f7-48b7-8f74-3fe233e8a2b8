package com.youpin.clear.domain.aggregate.uu898;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 扣款策略类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DeductionStrategyType {
    
    /**
     * 购买商品扣款策略：优先可交易余额，不足时扣可提现余额
     */
    PURCHASE_DEDUCTION("PURCHASE_DEDUCTION", "购买商品扣款"),
    
    /**
     * 退款策略：优先退回可提现余额，不足时退到可交易余额
     */
    REFUND_ADDITION("REFUND_ADDITION", "退款加款"),
    
    /**
     * 原路退回策略：按照原扣款路径反向退回
     */
    ORIGINAL_PATH_REFUND("ORIGINAL_PATH_REFUND", "原路退回"),
    
    /**
     * 提现扣款策略：只能从可提现余额扣除
     */
    WITHDRAW_DEDUCTION("WITHDRAW_DEDUCTION", "提现扣款"),
    
    /**
     * 冻结策略：从可用余额转移到冻结余额
     */
    FREEZE_STRATEGY("FREEZE_STRATEGY", "冻结策略"),
    
    /**
     * 解冻策略：从冻结余额转移到可用余额
     */
    UNFREEZE_STRATEGY("UNFREEZE_STRATEGY", "解冻策略"),
    
    /**
     * 自定义策略：根据配置的优先级和比例进行扣款
     */
    CUSTOM_STRATEGY("CUSTOM_STRATEGY", "自定义策略");
    
    private final String code;
    private final String desc;
    
    public static DeductionStrategyType getByCode(String code) {
        for (DeductionStrategyType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
