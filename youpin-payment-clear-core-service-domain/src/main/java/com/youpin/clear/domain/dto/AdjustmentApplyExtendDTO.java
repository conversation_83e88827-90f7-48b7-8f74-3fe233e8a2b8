package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdjustmentApplyExtendDTO {

    private String smsCode;

    private String uuAccountMsg;

    private String separateAccountMsg;

    private Integer retryHandleCount;

    private Integer verifyCodeCount;

    private Boolean repeatFlag;
}
