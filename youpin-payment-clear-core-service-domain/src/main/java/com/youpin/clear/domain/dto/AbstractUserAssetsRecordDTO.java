package com.youpin.clear.domain.dto;

import com.youpin.clear.client.request.BalanceStrategy;
import com.youpin.clear.common.enums.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.youpin.clear.common.constant.ClearConstants.NOT_SEND_BILL_ACCOUNT_MQ_BY_TYPE_ID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbstractUserAssetsRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 资金明细主键ID
     */
    private Long id;

    /**
     * 所属用户的唯一标识
     */
    private Long userId;

    /**
     * 资金明细类型ID(在UserAssetsRecordType表中)
     */
    private Integer typeId;

    /**
     * 当前资金明细记录的唯一单号
     */
    private String treadNo;

    /**
     * 变化资产类型，1.账户余额，10.积分
     */
    private Integer assetType;

    /**
     * 变动之前的账户余额，含义随AssetType字段而变化
     */
    private BigDecimal money;

    /**
     * 本次变动数额，含义随AssetType字段而变化
     */
    private BigDecimal thisMoney;

    /**
     * 变动后的账户余额，含义随AssetType字段而变化
     */
    private BigDecimal afterMoney;

    /**
     * 服务费金额
     */
    private BigDecimal chargeMoney;

    /**
     * 变动前的冻结金额
     */
    private BigDecimal blockMoney;

    /**
     * 本次冻结金额
     */
    private BigDecimal thisBlockMoney;

    /**
     * 变动后冻结金额
     */
    private BigDecimal afterBlockMoney;

    /**
     * 变动前的求购金额
     */
    private BigDecimal purchaseMoney;

    /**
     * 本次变动求购金额
     */
    private BigDecimal thisPurchaseMoney;

    /**
     * 变动后的求购金额
     */
    private BigDecimal afterPurchaseMoney;

    /**
     * 关联支付接口返回的流水号
     */
    private String serialNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payOrderNo;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 特性，二进制保存，从低位至高位依次为：余额是否变动
     * 0 余额 不变动   1, 余额变动 2 求购变动
     */
    private Integer attr;

    /**
     * 状态，0.失败，1.成功，2.进行中
     */
    private Integer status;

    /**
     * 支付渠道：0.余额，1.微信，2.支付宝，3.积分，4额度，5押金卡，6临时额度，7固定额度，8求购余额，9支付宝原路退还 15易宝
     */
    private Integer payChannel;

    /**
     * 提现支付宝账号
     */
    private String accountName;

    /**
     * 支付等待时间
     */
    private LocalDateTime payWaitExpireTime;

    /**
     * 生成来源信息
     */
    private String genSource;


    /**
     * 收单类型 0,默认  1(自有) 2(监管)
     */
    private Integer collectType;

    /**
     * 是否是租赁订单
     */
    private Boolean isLeaseOrder;

    /**
     * 钱包2转出金额(目前只适用 求购提现 转账是混合使用)
     */
    private BigDecimal balance2TransferAmount;

    /***
     * 商户id
     */
    private Integer merchantId;

    /**
     * 业务类型：出租，出售
     */
    private Integer businessType;

    /**
     * 子业务类型
     */
    private Integer subBusType;

    /**
     * 资金方向
     */
    private FundingDirectionEnum fundingDirectionEnum;

    /**
     * 资产类型
     */
    private UserAssetsTypeEnum userAssetsTypeEnum;

    /**
     * 变动金额 元
     */
    private BigDecimal changeMoney = BigDecimal.ZERO;


    /**
     * 是否开启缓冲记账
     * 默认不开启
     */
    private Boolean accountBufferBookkeeping = false;

    /**
     * 缓冲记账的ID
     */
    private Long platformAccountRecordId;


    /**
     * 资金涉及的 分账扩展信息 需要透传给分账账单
     */
    private AssetsRecordAccountBillExtInfo assetsRecordAccountBillExtInfo;

    /**
     * 转入金额
     */
    private BigDecimal purchaseTransferAmount = BigDecimal.ZERO;

    /**
     * 转入数据ID
     */
    private Long purchaseTransferAmountDataID;

    /**
     * 账户余额策略
     */
    private BalanceStrategy balanceStrategy;

    /**
     * 子账户明细map
     */
    private List<UU898UserSubAccountFlowRecordDTO> subAccountFlowRecordList;


    /**
     * AssetsRelatio 资金关系
     */
    private AssetsRelationDTO assetsRelationDTO;

    /**
     * 调用子账户业务撤销
     */
    private List<CallSubAccountBusinessRevokeDTO> callSubAccountBusinessRevokeDTOList;


    /**
     * 余额变动属性
     * TODO 需要优化 不一定准确
     */
    public Integer balanceChangeAttr() {
        if (isBalanceChange()) {
            return UserAssetsRecordAttrEnum.BalanceIsChange.getCode();
        } else if (isPurchaseBalanceChange()) {
            return UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode();
        } else {
            return UserAssetsRecordAttrEnum.None.getCode();
        }
    }

    /**
     * TODO 需要优化 不一定准确
     */
    public Integer balanceChangeAssetType() {
        if (isBalanceChange()) {
            return UserAssetsRecordAssetTypeEnum.Balance.getCode();
        } else if (isPurchaseBalanceChange()) {
            return UserAssetsRecordAssetTypeEnum.PurchaseMoney.getCode();
        } else {
            return UserAssetsRecordAssetTypeEnum.Balance.getCode();
        }
    }

    /**
     * 是否是求购余额变动
     *
     * @return boolean true 是 false 否
     */
    public boolean isPurchaseBalanceChange() {
        if (this.payChannel == null) {
            return false;
        }
        return this.payChannel.equals(DoNetPayChannelEnum.PurchaseBalance.getCode()) || UserAssetsRecordAttrEnum.PurchaseBalanceIsChange.getCode().equals(attr);
    }

    /**
     * 是否是余额变动
     *
     * @return boolean true 是 false 否
     */
    public boolean isBalanceChange() {
        if (this.payChannel == null) {
            return false;
        }
        return this.payChannel.equals(DoNetPayChannelEnum.Balance.getCode()) || UserAssetsRecordAttrEnum.BalanceIsChange.getCode().equals(attr);
    }


    /**
     * 是否是支付渠道变动
     *
     * @return boolean true 是 false 否
     */
    public boolean isPayChannelChange() {
        if (this.payChannel == null) {
            return false;
        }
        return (this.payChannel.equals(DoNetPayChannelEnum.Alipay.getCode())
                || this.payChannel.equals(DoNetPayChannelEnum.DyPay.getCode())
                || this.payChannel.equals(DoNetPayChannelEnum.JD.getCode())
                || this.payChannel.equals(DoNetPayChannelEnum.Wx.getCode()));
    }


    /**
     * 是否是求购转入余额变动
     */
    public Boolean isPurchaseMoneyFromMoney = false;

    /**
     * 是否是余额转入求购变动
     */
    public Boolean isMoneyFromPurchaseMoney = false;

    /**
     * 是否余额充值退款变动
     */
    public Boolean isBalanceRechargeRefund = false;


    /**
     * 是否发送账单
     *
     * @return boolean true 发送 false 不发送
     */
    public boolean isSendBill() {
        return !NOT_SEND_BILL_ACCOUNT_MQ_BY_TYPE_ID.contains(typeId);
    }

    /**
     * 是否开启缓冲记账
     */
    public boolean checkAccountBufferBookkeeping() {
        return Boolean.TRUE.equals(this.accountBufferBookkeeping);
    }


}




