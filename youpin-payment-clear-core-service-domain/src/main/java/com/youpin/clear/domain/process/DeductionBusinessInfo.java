package com.youpin.clear.domain.process;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 扣款业务信息
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeductionBusinessInfo {
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 支付单号
     */
    private String payOrderNo;
    
    /**
     * 业务流水号
     */
    private String serialNo;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 业务描述
     */
    private String businessDesc;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 扩展信息
     */
    private String extInfo;
}
