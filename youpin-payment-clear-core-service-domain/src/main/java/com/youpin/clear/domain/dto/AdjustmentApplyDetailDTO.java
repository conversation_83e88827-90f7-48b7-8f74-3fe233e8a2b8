package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Table: adjustment_apply_detail
 * @author: lizhuangzhuang
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdjustmentApplyDetailDTO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 金额调整方向,1:增加 2:减少
     */
    private Integer direction;

    /**
     * 调整金额
     */
    private BigDecimal adjustMoney;

    /**
     * 调账账户类型
     */
    private Integer adjustAccountType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}