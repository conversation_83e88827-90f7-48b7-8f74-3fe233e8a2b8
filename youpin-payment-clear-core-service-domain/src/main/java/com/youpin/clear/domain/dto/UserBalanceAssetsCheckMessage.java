package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBalanceAssetsCheckMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    /**
     * 资金类型
     */
    private Integer typeId;

    /**
     * 支付渠道：0.余额，1.微信，2.支付宝，3.积分，4.额度，5押金卡，6临时额度，7固定额度，8求购余额，9支付宝原路退还
     */
    private Integer payChannel;


    /**
     * 0 失败 1 成功 2 进行中
     */
    private Integer status;


    /**
     * 资金明细id
     */
    private Long userAssetsRecordId;

    /**
     * 订单号
     */
    private String orderNo;




}
