package com.youpin.clear.domain.process;


import com.youpin.clear.common.enums.SubBusTypeFrontEnum;
import com.youpin.clear.domain.dto.FinancialProcessorDTO;
import com.youpin.clear.domain.dto.FinancialProcessorResultDTO;

import java.util.List;


public interface SubBusFinancialProcessor {

    /**
     * 支持业务类型
     */
    List<SubBusTypeFrontEnum> support();

    /**
     * 处理
     */
    FinancialProcessorResultDTO process(FinancialProcessorDTO dto);

    /**
     * 前置数据幂等校验
     * 1.订单号 支付单号  流水号 用户 资金类型
     */
    void beforeDataIdempotenceCheck(FinancialProcessorDTO dto);

    /**
     * 1.账户查询 加锁
     * 业务逻辑处理
     * 2.账户更新   开事务
     */
    FinancialProcessorResultDTO accountUpdateLock(FinancialProcessorDTO dto);


}
