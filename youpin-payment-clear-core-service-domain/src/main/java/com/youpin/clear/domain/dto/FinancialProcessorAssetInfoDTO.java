package com.youpin.clear.domain.dto;

import com.youpin.clear.client.request.BalanceStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinancialProcessorAssetInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;



    private Long userId;
    /**
     * 金额（单位分）
     */
    private Long money;

    private Integer typeId;

    /**
     * 渠道 DoNetPayChannelEnum
     */
    private Integer payChannel;

    /**
     * 余额变动策略
     */
    private BalanceStrategy balanceStrategy;



}
