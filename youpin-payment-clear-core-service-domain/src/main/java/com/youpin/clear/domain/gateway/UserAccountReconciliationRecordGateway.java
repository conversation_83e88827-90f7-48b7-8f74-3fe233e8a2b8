package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.UserAccountReconciliationRecordDTO;

public interface UserAccountReconciliationRecordGateway {

    UserAccountReconciliationRecordDTO getByUserIdAndAccountType(Long userId, Integer accountType);

    void save(UserAccountReconciliationRecordDTO dto);

    void updateByUserIdAndIdSelective(UserAccountReconciliationRecordDTO accountReconciliationRecordDTO);

    void deleteByUserId(Long userId);
}
