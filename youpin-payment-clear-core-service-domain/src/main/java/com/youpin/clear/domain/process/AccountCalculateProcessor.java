package com.youpin.clear.domain.process;

import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;
import com.youpin.clear.domain.dto.PublicBalanceDTO;

import java.util.List;

/**
 * 账户处理器
 */
public interface AccountCalculateProcessor {

    List<DirectionEnum> support();

    void handle(PublicBalanceDTO publicBalance, AccountAggregate accountAggregate, UserAccountRecordMember userAccountRecordMember);
    
}
