package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * Table: account_assets_type
 *
 * @author: usa
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountAssetsTypeDTO implements Serializable {
    /**
     * 资金code
     */
    private Integer assetsCode;

    /**
     * 资金名字
     */
    private String assetsName;

    /**
     * 资金方向
     */
    private String direction;

    /**
     * 资金类型
     */
    private String assetsType;


    private static final long serialVersionUID = 1L;
}