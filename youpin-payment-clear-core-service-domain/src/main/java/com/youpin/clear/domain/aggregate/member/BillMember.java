package com.youpin.clear.domain.aggregate.member;

import com.youpin.clear.common.enums.DirectionEnum;
import com.youpin.clear.domain.aggregate.AccountAggregate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BillMember implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属用户的唯一标识
     */
    private Long userId;
    /**
     * 分账方向
     */
    private DirectionEnum directionEnum;

    /**
     * 资金类型ID枚举
     */
    private Integer typeId;

    /**
     * 资金分账抽象
     */
    private BillItemMember billItemMember;

    /**
     * 账户数据
     */
    private AccountAggregate accountAggregate;

    /**
     * 分账记录列表
     */
    private List<UserAccountRecordMember> userAccountRecordMemberList;

    /**
     * 是否分账成功 默认 false
     */
    private Boolean isSuccess = Boolean.FALSE;


    /**
     * 是否是参考资金类型数据 默认 false
     */
    private Boolean isReference = Boolean.FALSE;

//
//    /**
//     * 参考资金类型数据
//     */
//    private List<BillItemMember> billItemMemberIsReference;












}
