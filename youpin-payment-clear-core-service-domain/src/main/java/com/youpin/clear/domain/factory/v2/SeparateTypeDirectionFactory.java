package com.youpin.clear.domain.factory.v2;

import com.youpin.clear.domain.process.SeparateAccountAssetsTypeProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资金Id抽象处理器工厂
 */
@Component
public class SeparateTypeDirectionFactory {

    @Resource
    List<SeparateAccountAssetsTypeProcessor> separateAccountAssetsTypeProcessorList;

    Map<Integer, SeparateAccountAssetsTypeProcessor> maps = new HashMap<>();

    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(separateAccountAssetsTypeProcessorList)) {
            separateAccountAssetsTypeProcessorList.forEach(processor -> {
                List<Integer> supportTypeList = processor.support();
                supportTypeList.forEach(typeId -> maps.put(typeId, processor));
            });
        }
    }

    public SeparateAccountAssetsTypeProcessor getProcessor(Integer typeId) {
        if (typeId == null) {
            return null;
        }
        return maps.getOrDefault(typeId, null);
    }
}
