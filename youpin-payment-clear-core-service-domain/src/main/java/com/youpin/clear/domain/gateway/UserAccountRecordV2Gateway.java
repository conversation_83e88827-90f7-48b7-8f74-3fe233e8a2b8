package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.aggregate.AccountAggregate;
import com.youpin.clear.domain.aggregate.member.UserAccountRecordMember;

import java.util.List;
import java.util.Map;

public interface UserAccountRecordV2Gateway {
    void userAccountRecordMemberTransactional(List<UserAccountRecordMember> userAccountRecordMemberList, Map<Long, AccountAggregate> accountAggregateMap);

}
