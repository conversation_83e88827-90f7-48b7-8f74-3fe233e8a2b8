package com.youpin.clear.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 金额变动
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountBalanceDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 余额1
     */
    @Builder.Default
    private BigDecimal balance1 = BigDecimal.ZERO;

    /**
     * 余额2
     */
    @Builder.Default
    private BigDecimal balance2 = BigDecimal.ZERO;

    /**
     * 求购充值余额1
     */
    @Builder.Default
    private BigDecimal purchaseBalanceRecharge1 = BigDecimal.ZERO;
    /**
     * 求购充值余额2
     */
    @Builder.Default
    private BigDecimal purchaseBalanceRecharge2 = BigDecimal.ZERO;
    /**
     * 求购转入余额1
     */
    @Builder.Default
    private BigDecimal purchaseBalanceTransfer1 = BigDecimal.ZERO;
    /**
     * 求购转入余额2
     */
    @Builder.Default
    private BigDecimal purchaseBalanceTransfer2 = BigDecimal.ZERO;

    /**
     * 余额1 冻结
     */
    @Builder.Default
    private BigDecimal frozenBalance1 = BigDecimal.ZERO;

    /**
     * 余额2 冻结
     */
    @Builder.Default
    private BigDecimal frozenBalance2 = BigDecimal.ZERO;

    /**
     * 求购充值余额1 冻结
     */
    @Builder.Default
    private BigDecimal frozenPurchaseBalanceRecharge1 = BigDecimal.ZERO;
    /**
     * 求购充值余额2 冻结
     */
    @Builder.Default
    private BigDecimal frozenPurchaseBalanceRecharge2 = BigDecimal.ZERO;
    /**
     * 求购转入余额1 冻结
     */
    @Builder.Default
    private BigDecimal frozenPurchaseBalanceTransfer1 = BigDecimal.ZERO;
    /**
     * 求购转入余额2 冻结
     */
    @Builder.Default
    private BigDecimal frozenPurchaseBalanceTransfer2 = BigDecimal.ZERO;


    /**
     * 获取总金额
     */
    public BigDecimal getTotalAmount() {
        return balance1.add(balance2).add(purchaseBalanceRecharge1).add(purchaseBalanceRecharge2).add(purchaseBalanceTransfer1).add(purchaseBalanceTransfer2);
    }

    /**
     * 获取总金额-绝对值
     */
    public BigDecimal getTotalAmountAbs() {
        return balance1.abs().add(balance2.abs()).add(purchaseBalanceRecharge1.abs()).add(purchaseBalanceRecharge2.abs()).add(purchaseBalanceTransfer1.abs()).add(purchaseBalanceTransfer2.abs());
    }

    /**
     * 获取总冻结金额-绝对值
     */
    public BigDecimal getTotalFrozenAmountAbs() {
        return frozenBalance1.abs().add(frozenBalance2.abs()).add(frozenPurchaseBalanceRecharge1.abs()).add(frozenPurchaseBalanceRecharge2.abs()).add(frozenPurchaseBalanceTransfer1.abs()).add(frozenPurchaseBalanceTransfer2.abs());
    }


    public void add(AccountBalanceDTO subtractDto1) {
        if (null != subtractDto1) {
            this.balance1 = this.balance1.add(subtractDto1.balance1);
            this.balance2 = this.balance2.add(subtractDto1.balance2);
            this.purchaseBalanceRecharge1 = this.purchaseBalanceRecharge1.add(subtractDto1.purchaseBalanceRecharge1);
            this.purchaseBalanceRecharge2 = this.purchaseBalanceRecharge2.add(subtractDto1.purchaseBalanceRecharge2);
            this.purchaseBalanceTransfer1 = this.purchaseBalanceTransfer1.add(subtractDto1.purchaseBalanceTransfer1);
            this.purchaseBalanceTransfer2 = this.purchaseBalanceTransfer2.add(subtractDto1.purchaseBalanceTransfer2);
        }
    }
    

}
