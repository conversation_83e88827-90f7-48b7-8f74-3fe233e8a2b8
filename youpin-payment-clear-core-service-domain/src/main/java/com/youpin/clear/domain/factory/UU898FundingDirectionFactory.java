package com.youpin.clear.domain.factory;

import com.youpin.clear.common.enums.ErrorCode;
import com.youpin.clear.common.enums.FundingDirectionEnum;
import com.youpin.clear.domain.exception.PaymentClearBusinessException;
import com.youpin.clear.domain.process.UU898FundingDirectionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UU898FundingDirectionFactory {

    @Autowired
    List<UU898FundingDirectionProcessor> processorList;

    Map<FundingDirectionEnum, UU898FundingDirectionProcessor> maps = new HashMap<>();

    @PostConstruct
    public void init() {
        if (!CollectionUtils.isEmpty(processorList)) {
            processorList.forEach(processor -> {
                List<FundingDirectionEnum> supportTypeList = processor.support();
                supportTypeList.forEach(subBusType -> maps.put(subBusType, processor));
            });
        }
    }

    public UU898FundingDirectionProcessor getProcessor(FundingDirectionEnum typeEnum) {
        if (typeEnum == null) {
            log.error("UU898FundingDirectionFactory FundingDirectionEnum is null");
            return null;
        }
        UU898FundingDirectionProcessor processor = maps.getOrDefault(typeEnum, null);
        if (processor == null) {
            log.error("UU898FundingDirectionProcessor is null,typeEnum:{}", typeEnum);
            throw new PaymentClearBusinessException(ErrorCode.BUSINESS_FACTORY_NOT_EXIST);
        }
        return processor;
    }
}
