package com.youpin.clear.domain.gateway;

import com.youpin.clear.domain.dto.PlatformAccountRecordDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface PlatformAccountRecordGateway {
    Boolean save(PlatformAccountRecordDTO dto);

    Boolean updateStatusById(Long id, Integer status);

    List<Long> selectPageSizeByStatusAndCreateTime(Integer shardIndex, Integer status, LocalDateTime createTime, Integer pageSize);

    PlatformAccountRecordDTO selectById(Long id);
}
