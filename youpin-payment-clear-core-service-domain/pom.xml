<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.youpin.clear</groupId>
        <artifactId>youpin-payment-clear-core-service</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>youpin-payment-clear-core-service-domain</artifactId>
    <packaging>jar</packaging>
    <name>youpin-payment-clear-core-service-domain</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <skipTests>true</skipTests>

    </properties>
    <dependencies>
        <dependency>
            <groupId>com.youpin.clear</groupId>
            <artifactId>youpin-payment-clear-core-service-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.youpin.clear</groupId>
            <artifactId>youpin-payment-clear-core-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uu898.youpin</groupId>
            <artifactId>youpin-commons-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uu898.youpin</groupId>
            <artifactId>youpin-commons-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uu898.youpin</groupId>
            <artifactId>youpin-commons-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.youpin.payment.gateway</groupId>
            <artifactId>youpin-payment-transaction-gateway-service-client</artifactId>
        </dependency>

    </dependencies>
</project>
